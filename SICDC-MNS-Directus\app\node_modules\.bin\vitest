#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/vitest@1.5.3_@types+node@18.19.33_happy-dom@14.12.0_jsdom@24.0.0_sass@1.77.4_terser@5.31.0/node_modules/vitest/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/vitest@1.5.3_@types+node@18.19.33_happy-dom@14.12.0_jsdom@24.0.0_sass@1.77.4_terser@5.31.0/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/vitest@1.5.3_@types+node@18.19.33_happy-dom@14.12.0_jsdom@24.0.0_sass@1.77.4_terser@5.31.0/node_modules/vitest/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/vitest@1.5.3_@types+node@18.19.33_happy-dom@14.12.0_jsdom@24.0.0_sass@1.77.4_terser@5.31.0/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../vitest/vitest.mjs" "$@"
fi
