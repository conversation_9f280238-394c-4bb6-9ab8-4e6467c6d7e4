{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/fullscreen/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const get$5 = fullscreenState => ({ isFullscreen: () => fullscreenState.get() !== null });\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const eq$1 = t => a => t === a;\n    const isString = isType$1('string');\n    const isObject = isType$1('object');\n    const isArray = isType$1('array');\n    const isNull = eq$1(null);\n    const isBoolean = isSimpleType('boolean');\n    const isUndefined = eq$1(undefined);\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const noop = () => {\n    };\n    const compose = (fa, fb) => {\n      return (...args) => {\n        return fa(fb.apply(null, args));\n      };\n    };\n    const compose1 = (fbc, fab) => a => fbc(fab(a));\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    function curry(fn, ...initialArgs) {\n      return (...restArgs) => {\n        const all = initialArgs.concat(restArgs);\n        return fn.apply(null, all);\n      };\n    }\n    const never = constant(false);\n    const always = constant(true);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const nativePush = Array.prototype.push;\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each$1 = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const filter$1 = (xs, pred) => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    const findUntil = (xs, pred, until) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const find$1 = (xs, pred) => {\n      return findUntil(xs, pred, never);\n    };\n    const flatten = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    const bind$3 = (xs, f) => flatten(map(xs, f));\n    const get$4 = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = xs => get$4(xs, 0);\n    const findMap = (arr, f) => {\n      for (let i = 0; i < arr.length; i++) {\n        const r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    const lift2 = (oa, ob, f) => oa.isSome() && ob.isSome() ? Optional.some(f(oa.getOrDie(), ob.getOrDie())) : Optional.none();\n\n    const singleton = doRevoke => {\n      const subject = Cell(Optional.none());\n      const revoke = () => subject.get().each(doRevoke);\n      const clear = () => {\n        revoke();\n        subject.set(Optional.none());\n      };\n      const isSet = () => subject.get().isSome();\n      const get = () => subject.get();\n      const set = s => {\n        revoke();\n        subject.set(Optional.some(s));\n      };\n      return {\n        clear,\n        isSet,\n        get,\n        set\n      };\n    };\n    const unbindable = () => singleton(s => s.unbind());\n    const value = () => {\n      const subject = singleton(noop);\n      const on = f => subject.get().each(f);\n      return {\n        ...subject,\n        on\n      };\n    };\n\n    const first = (fn, rate) => {\n      let timer = null;\n      const cancel = () => {\n        if (!isNull(timer)) {\n          clearTimeout(timer);\n          timer = null;\n        }\n      };\n      const throttle = (...args) => {\n        if (isNull(timer)) {\n          timer = setTimeout(() => {\n            timer = null;\n            fn.apply(null, args);\n          }, rate);\n        }\n      };\n      return {\n        cancel,\n        throttle\n      };\n    };\n\n    const keys = Object.keys;\n    const each = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n\n    const Global = typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const path = (parts, scope) => {\n      let o = scope !== undefined && scope !== null ? scope : Global;\n      for (let i = 0; i < parts.length && o !== undefined && o !== null; ++i) {\n        o = o[parts[i]];\n      }\n      return o;\n    };\n    const resolve = (p, scope) => {\n      const parts = p.split('.');\n      return path(parts, scope);\n    };\n\n    const unsafe = (name, scope) => {\n      return resolve(name, scope);\n    };\n    const getOrDie = (name, scope) => {\n      const actual = unsafe(name, scope);\n      if (actual === undefined || actual === null) {\n        throw new Error(name + ' not available on this browser');\n      }\n      return actual;\n    };\n\n    const getPrototypeOf = Object.getPrototypeOf;\n    const sandHTMLElement = scope => {\n      return getOrDie('HTMLElement', scope);\n    };\n    const isPrototypeOf = x => {\n      const scope = resolve('ownerDocument.defaultView', x);\n      return isObject(x) && (sandHTMLElement(scope).prototype.isPrototypeOf(x) || /^HTML\\w*Element$/.test(getPrototypeOf(x).constructor.name));\n    };\n\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const type = element => element.dom.nodeType;\n    const isType = t => element => type(element) === t;\n    const isHTMLElement = element => isElement(element) && isPrototypeOf(element.dom);\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocument = isType(DOCUMENT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const set$1 = (element, key, value) => {\n      rawSet(element.dom, key, value);\n    };\n    const get$3 = (element, key) => {\n      const v = element.dom.getAttribute(key);\n      return v === null ? undefined : v;\n    };\n    const remove = (element, key) => {\n      element.dom.removeAttribute(key);\n    };\n\n    const supports = element => element.dom.classList !== undefined;\n\n    const has = (element, clazz) => supports(element) && element.dom.classList.contains(clazz);\n\n    const contains = (str, substr, start = 0, end) => {\n      const idx = str.indexOf(substr, start);\n      if (idx !== -1) {\n        return isUndefined(end) ? true : idx + substr.length <= end;\n      } else {\n        return false;\n      }\n    };\n\n    const isSupported$1 = dom => dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    const fromDom = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom,\n      fromPoint\n    };\n\n    const is = (element, selector) => {\n      const dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        const elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n    const bypassSelector = dom => dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;\n    const all$1 = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n\n    const eq = (e1, e2) => e1.dom === e2.dom;\n\n    const owner = element => SugarElement.fromDom(element.dom.ownerDocument);\n    const documentOrOwner = dos => isDocument(dos) ? dos : owner(dos);\n    const parent = element => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const parents = (element, isRoot) => {\n      const stop = isFunction(isRoot) ? isRoot : never;\n      let dom = element.dom;\n      const ret = [];\n      while (dom.parentNode !== null && dom.parentNode !== undefined) {\n        const rawParent = dom.parentNode;\n        const p = SugarElement.fromDom(rawParent);\n        ret.push(p);\n        if (stop(p) === true) {\n          break;\n        } else {\n          dom = rawParent;\n        }\n      }\n      return ret;\n    };\n    const siblings$2 = element => {\n      const filterSelf = elements => filter$1(elements, x => !eq(element, x));\n      return parent(element).map(children).map(filterSelf).getOr([]);\n    };\n    const nextSibling = element => Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);\n    const children = element => map(element.dom.childNodes, SugarElement.fromDom);\n\n    const isShadowRoot = dos => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    const isSupported = constant(supported);\n    const getRootNode = supported ? e => SugarElement.fromDom(e.dom.getRootNode()) : documentOrOwner;\n    const getShadowRoot = e => {\n      const r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    const getShadowHost = e => SugarElement.fromDom(e.dom.host);\n    const getOriginalEventTarget = event => {\n      if (isSupported() && isNonNullable(event.target)) {\n        const el = SugarElement.fromDom(event.target);\n        if (isElement(el) && isOpenShadowHost(el)) {\n          if (event.composed && event.composedPath) {\n            const composedPath = event.composedPath();\n            if (composedPath) {\n              return head(composedPath);\n            }\n          }\n        }\n      }\n      return Optional.from(event.target);\n    };\n    const isOpenShadowHost = element => isNonNullable(element.dom.shadowRoot);\n\n    const inBody = element => {\n      const dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      const doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n    const getBody = doc => {\n      const b = doc.dom.body;\n      if (b === null || b === undefined) {\n        throw new Error('Body is not available yet');\n      }\n      return SugarElement.fromDom(b);\n    };\n\n    const internalSet = (dom, property, value) => {\n      if (!isString(value)) {\n        console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n        throw new Error('CSS value must be a string: ' + value);\n      }\n      if (isSupported$1(dom)) {\n        dom.style.setProperty(property, value);\n      }\n    };\n    const set = (element, property, value) => {\n      const dom = element.dom;\n      internalSet(dom, property, value);\n    };\n    const setAll = (element, css) => {\n      const dom = element.dom;\n      each(css, (v, k) => {\n        internalSet(dom, k, v);\n      });\n    };\n    const get$2 = (element, property) => {\n      const dom = element.dom;\n      const styles = window.getComputedStyle(dom);\n      const r = styles.getPropertyValue(property);\n      return r === '' && !inBody(element) ? getUnsafeProperty(dom, property) : r;\n    };\n    const getUnsafeProperty = (dom, property) => isSupported$1(dom) ? dom.style.getPropertyValue(property) : '';\n\n    const mkEvent = (target, x, y, stop, prevent, kill, raw) => ({\n      target,\n      x,\n      y,\n      stop,\n      prevent,\n      kill,\n      raw\n    });\n    const fromRawEvent = rawEvent => {\n      const target = SugarElement.fromDom(getOriginalEventTarget(rawEvent).getOr(rawEvent.target));\n      const stop = () => rawEvent.stopPropagation();\n      const prevent = () => rawEvent.preventDefault();\n      const kill = compose(prevent, stop);\n      return mkEvent(target, rawEvent.clientX, rawEvent.clientY, stop, prevent, kill, rawEvent);\n    };\n    const handle = (filter, handler) => rawEvent => {\n      if (filter(rawEvent)) {\n        handler(fromRawEvent(rawEvent));\n      }\n    };\n    const binder = (element, event, filter, handler, useCapture) => {\n      const wrapped = handle(filter, handler);\n      element.dom.addEventListener(event, wrapped, useCapture);\n      return { unbind: curry(unbind, element, event, wrapped, useCapture) };\n    };\n    const bind$2 = (element, event, filter, handler) => binder(element, event, filter, handler, false);\n    const unbind = (element, event, handler, useCapture) => {\n      element.dom.removeEventListener(event, handler, useCapture);\n    };\n\n    const filter = always;\n    const bind$1 = (element, event, handler) => bind$2(element, event, filter, handler);\n\n    const cached = f => {\n      let called = false;\n      let r;\n      return (...args) => {\n        if (!called) {\n          called = true;\n          r = f.apply(null, args);\n        }\n        return r;\n      };\n    };\n\n    const DeviceType = (os, browser, userAgent, mediaMatch) => {\n      const isiPad = os.isiOS() && /ipad/i.test(userAgent) === true;\n      const isiPhone = os.isiOS() && !isiPad;\n      const isMobile = os.isiOS() || os.isAndroid();\n      const isTouch = isMobile || mediaMatch('(pointer:coarse)');\n      const isTablet = isiPad || !isiPhone && isMobile && mediaMatch('(min-device-width:768px)');\n      const isPhone = isiPhone || isMobile && !isTablet;\n      const iOSwebview = browser.isSafari() && os.isiOS() && /safari/i.test(userAgent) === false;\n      const isDesktop = !isPhone && !isTablet && !iOSwebview;\n      return {\n        isiPad: constant(isiPad),\n        isiPhone: constant(isiPhone),\n        isTablet: constant(isTablet),\n        isPhone: constant(isPhone),\n        isTouch: constant(isTouch),\n        isAndroid: os.isAndroid,\n        isiOS: os.isiOS,\n        isWebView: constant(iOSwebview),\n        isDesktop: constant(isDesktop)\n      };\n    };\n\n    const firstMatch = (regexes, s) => {\n      for (let i = 0; i < regexes.length; i++) {\n        const x = regexes[i];\n        if (x.test(s)) {\n          return x;\n        }\n      }\n      return undefined;\n    };\n    const find = (regexes, agent) => {\n      const r = firstMatch(regexes, agent);\n      if (!r) {\n        return {\n          major: 0,\n          minor: 0\n        };\n      }\n      const group = i => {\n        return Number(agent.replace(r, '$' + i));\n      };\n      return nu$2(group(1), group(2));\n    };\n    const detect$3 = (versionRegexes, agent) => {\n      const cleanedAgent = String(agent).toLowerCase();\n      if (versionRegexes.length === 0) {\n        return unknown$2();\n      }\n      return find(versionRegexes, cleanedAgent);\n    };\n    const unknown$2 = () => {\n      return nu$2(0, 0);\n    };\n    const nu$2 = (major, minor) => {\n      return {\n        major,\n        minor\n      };\n    };\n    const Version = {\n      nu: nu$2,\n      detect: detect$3,\n      unknown: unknown$2\n    };\n\n    const detectBrowser$1 = (browsers, userAgentData) => {\n      return findMap(userAgentData.brands, uaBrand => {\n        const lcBrand = uaBrand.brand.toLowerCase();\n        return find$1(browsers, browser => {\n          var _a;\n          return lcBrand === ((_a = browser.brand) === null || _a === void 0 ? void 0 : _a.toLowerCase());\n        }).map(info => ({\n          current: info.name,\n          version: Version.nu(parseInt(uaBrand.version, 10), 0)\n        }));\n      });\n    };\n\n    const detect$2 = (candidates, userAgent) => {\n      const agent = String(userAgent).toLowerCase();\n      return find$1(candidates, candidate => {\n        return candidate.search(agent);\n      });\n    };\n    const detectBrowser = (browsers, userAgent) => {\n      return detect$2(browsers, userAgent).map(browser => {\n        const version = Version.detect(browser.versionRegexes, userAgent);\n        return {\n          current: browser.name,\n          version\n        };\n      });\n    };\n    const detectOs = (oses, userAgent) => {\n      return detect$2(oses, userAgent).map(os => {\n        const version = Version.detect(os.versionRegexes, userAgent);\n        return {\n          current: os.name,\n          version\n        };\n      });\n    };\n\n    const normalVersionRegex = /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/;\n    const checkContains = target => {\n      return uastring => {\n        return contains(uastring, target);\n      };\n    };\n    const browsers = [\n      {\n        name: 'Edge',\n        versionRegexes: [/.*?edge\\/ ?([0-9]+)\\.([0-9]+)$/],\n        search: uastring => {\n          return contains(uastring, 'edge/') && contains(uastring, 'chrome') && contains(uastring, 'safari') && contains(uastring, 'applewebkit');\n        }\n      },\n      {\n        name: 'Chromium',\n        brand: 'Chromium',\n        versionRegexes: [\n          /.*?chrome\\/([0-9]+)\\.([0-9]+).*/,\n          normalVersionRegex\n        ],\n        search: uastring => {\n          return contains(uastring, 'chrome') && !contains(uastring, 'chromeframe');\n        }\n      },\n      {\n        name: 'IE',\n        versionRegexes: [\n          /.*?msie\\ ?([0-9]+)\\.([0-9]+).*/,\n          /.*?rv:([0-9]+)\\.([0-9]+).*/\n        ],\n        search: uastring => {\n          return contains(uastring, 'msie') || contains(uastring, 'trident');\n        }\n      },\n      {\n        name: 'Opera',\n        versionRegexes: [\n          normalVersionRegex,\n          /.*?opera\\/([0-9]+)\\.([0-9]+).*/\n        ],\n        search: checkContains('opera')\n      },\n      {\n        name: 'Firefox',\n        versionRegexes: [/.*?firefox\\/\\ ?([0-9]+)\\.([0-9]+).*/],\n        search: checkContains('firefox')\n      },\n      {\n        name: 'Safari',\n        versionRegexes: [\n          normalVersionRegex,\n          /.*?cpu os ([0-9]+)_([0-9]+).*/\n        ],\n        search: uastring => {\n          return (contains(uastring, 'safari') || contains(uastring, 'mobile/')) && contains(uastring, 'applewebkit');\n        }\n      }\n    ];\n    const oses = [\n      {\n        name: 'Windows',\n        search: checkContains('win'),\n        versionRegexes: [/.*?windows\\ nt\\ ?([0-9]+)\\.([0-9]+).*/]\n      },\n      {\n        name: 'iOS',\n        search: uastring => {\n          return contains(uastring, 'iphone') || contains(uastring, 'ipad');\n        },\n        versionRegexes: [\n          /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/,\n          /.*cpu os ([0-9]+)_([0-9]+).*/,\n          /.*cpu iphone os ([0-9]+)_([0-9]+).*/\n        ]\n      },\n      {\n        name: 'Android',\n        search: checkContains('android'),\n        versionRegexes: [/.*?android\\ ?([0-9]+)\\.([0-9]+).*/]\n      },\n      {\n        name: 'macOS',\n        search: checkContains('mac os x'),\n        versionRegexes: [/.*?mac\\ os\\ x\\ ?([0-9]+)_([0-9]+).*/]\n      },\n      {\n        name: 'Linux',\n        search: checkContains('linux'),\n        versionRegexes: []\n      },\n      {\n        name: 'Solaris',\n        search: checkContains('sunos'),\n        versionRegexes: []\n      },\n      {\n        name: 'FreeBSD',\n        search: checkContains('freebsd'),\n        versionRegexes: []\n      },\n      {\n        name: 'ChromeOS',\n        search: checkContains('cros'),\n        versionRegexes: [/.*?chrome\\/([0-9]+)\\.([0-9]+).*/]\n      }\n    ];\n    const PlatformInfo = {\n      browsers: constant(browsers),\n      oses: constant(oses)\n    };\n\n    const edge = 'Edge';\n    const chromium = 'Chromium';\n    const ie = 'IE';\n    const opera = 'Opera';\n    const firefox = 'Firefox';\n    const safari = 'Safari';\n    const unknown$1 = () => {\n      return nu$1({\n        current: undefined,\n        version: Version.unknown()\n      });\n    };\n    const nu$1 = info => {\n      const current = info.current;\n      const version = info.version;\n      const isBrowser = name => () => current === name;\n      return {\n        current,\n        version,\n        isEdge: isBrowser(edge),\n        isChromium: isBrowser(chromium),\n        isIE: isBrowser(ie),\n        isOpera: isBrowser(opera),\n        isFirefox: isBrowser(firefox),\n        isSafari: isBrowser(safari)\n      };\n    };\n    const Browser = {\n      unknown: unknown$1,\n      nu: nu$1,\n      edge: constant(edge),\n      chromium: constant(chromium),\n      ie: constant(ie),\n      opera: constant(opera),\n      firefox: constant(firefox),\n      safari: constant(safari)\n    };\n\n    const windows = 'Windows';\n    const ios = 'iOS';\n    const android = 'Android';\n    const linux = 'Linux';\n    const macos = 'macOS';\n    const solaris = 'Solaris';\n    const freebsd = 'FreeBSD';\n    const chromeos = 'ChromeOS';\n    const unknown = () => {\n      return nu({\n        current: undefined,\n        version: Version.unknown()\n      });\n    };\n    const nu = info => {\n      const current = info.current;\n      const version = info.version;\n      const isOS = name => () => current === name;\n      return {\n        current,\n        version,\n        isWindows: isOS(windows),\n        isiOS: isOS(ios),\n        isAndroid: isOS(android),\n        isMacOS: isOS(macos),\n        isLinux: isOS(linux),\n        isSolaris: isOS(solaris),\n        isFreeBSD: isOS(freebsd),\n        isChromeOS: isOS(chromeos)\n      };\n    };\n    const OperatingSystem = {\n      unknown,\n      nu,\n      windows: constant(windows),\n      ios: constant(ios),\n      android: constant(android),\n      linux: constant(linux),\n      macos: constant(macos),\n      solaris: constant(solaris),\n      freebsd: constant(freebsd),\n      chromeos: constant(chromeos)\n    };\n\n    const detect$1 = (userAgent, userAgentDataOpt, mediaMatch) => {\n      const browsers = PlatformInfo.browsers();\n      const oses = PlatformInfo.oses();\n      const browser = userAgentDataOpt.bind(userAgentData => detectBrowser$1(browsers, userAgentData)).orThunk(() => detectBrowser(browsers, userAgent)).fold(Browser.unknown, Browser.nu);\n      const os = detectOs(oses, userAgent).fold(OperatingSystem.unknown, OperatingSystem.nu);\n      const deviceType = DeviceType(os, browser, userAgent, mediaMatch);\n      return {\n        browser,\n        os,\n        deviceType\n      };\n    };\n    const PlatformDetection = { detect: detect$1 };\n\n    const mediaMatch = query => window.matchMedia(query).matches;\n    let platform = cached(() => PlatformDetection.detect(navigator.userAgent, Optional.from(navigator.userAgentData), mediaMatch));\n    const detect = () => platform();\n\n    const r = (left, top) => {\n      const translate = (x, y) => r(left + x, top + y);\n      return {\n        left,\n        top,\n        translate\n      };\n    };\n    const SugarPosition = r;\n\n    const get$1 = _DOC => {\n      const doc = _DOC !== undefined ? _DOC.dom : document;\n      const x = doc.body.scrollLeft || doc.documentElement.scrollLeft;\n      const y = doc.body.scrollTop || doc.documentElement.scrollTop;\n      return SugarPosition(x, y);\n    };\n\n    const get = _win => {\n      const win = _win === undefined ? window : _win;\n      if (detect().browser.isFirefox()) {\n        return Optional.none();\n      } else {\n        return Optional.from(win.visualViewport);\n      }\n    };\n    const bounds = (x, y, width, height) => ({\n      x,\n      y,\n      width,\n      height,\n      right: x + width,\n      bottom: y + height\n    });\n    const getBounds = _win => {\n      const win = _win === undefined ? window : _win;\n      const doc = win.document;\n      const scroll = get$1(SugarElement.fromDom(doc));\n      return get(win).fold(() => {\n        const html = win.document.documentElement;\n        const width = html.clientWidth;\n        const height = html.clientHeight;\n        return bounds(scroll.left, scroll.top, width, height);\n      }, visualViewport => bounds(Math.max(visualViewport.pageLeft, scroll.left), Math.max(visualViewport.pageTop, scroll.top), visualViewport.width, visualViewport.height));\n    };\n    const bind = (name, callback, _win) => get(_win).map(visualViewport => {\n      const handler = e => callback(fromRawEvent(e));\n      visualViewport.addEventListener(name, handler);\n      return { unbind: () => visualViewport.removeEventListener(name, handler) };\n    }).getOrThunk(() => ({ unbind: noop }));\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const fireFullscreenStateChanged = (editor, state) => {\n      editor.dispatch('FullscreenStateChanged', { state });\n      editor.dispatch('ResizeEditor');\n    };\n\n    const option = name => editor => editor.options.get(name);\n    const register$2 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('fullscreen_native', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const getFullscreenNative = option('fullscreen_native');\n\n    const getFullscreenRoot = editor => {\n      const elem = SugarElement.fromDom(editor.getElement());\n      return getShadowRoot(elem).map(getShadowHost).getOrThunk(() => getBody(owner(elem)));\n    };\n    const getFullscreenElement = root => {\n      if (root.fullscreenElement !== undefined) {\n        return root.fullscreenElement;\n      } else if (root.msFullscreenElement !== undefined) {\n        return root.msFullscreenElement;\n      } else if (root.webkitFullscreenElement !== undefined) {\n        return root.webkitFullscreenElement;\n      } else {\n        return null;\n      }\n    };\n    const getFullscreenchangeEventName = () => {\n      if (document.fullscreenElement !== undefined) {\n        return 'fullscreenchange';\n      } else if (document.msFullscreenElement !== undefined) {\n        return 'MSFullscreenChange';\n      } else if (document.webkitFullscreenElement !== undefined) {\n        return 'webkitfullscreenchange';\n      } else {\n        return 'fullscreenchange';\n      }\n    };\n    const requestFullscreen = sugarElem => {\n      const elem = sugarElem.dom;\n      if (elem.requestFullscreen) {\n        elem.requestFullscreen();\n      } else if (elem.msRequestFullscreen) {\n        elem.msRequestFullscreen();\n      } else if (elem.webkitRequestFullScreen) {\n        elem.webkitRequestFullScreen();\n      }\n    };\n    const exitFullscreen = sugarDoc => {\n      const doc = sugarDoc.dom;\n      if (doc.exitFullscreen) {\n        doc.exitFullscreen();\n      } else if (doc.msExitFullscreen) {\n        doc.msExitFullscreen();\n      } else if (doc.webkitCancelFullScreen) {\n        doc.webkitCancelFullScreen();\n      }\n    };\n    const isFullscreenElement = elem => elem.dom === getFullscreenElement(owner(elem).dom);\n\n    const ancestors$1 = (scope, predicate, isRoot) => filter$1(parents(scope, isRoot), predicate);\n    const siblings$1 = (scope, predicate) => filter$1(siblings$2(scope), predicate);\n\n    const all = selector => all$1(selector);\n    const ancestors = (scope, selector, isRoot) => ancestors$1(scope, e => is(e, selector), isRoot);\n    const siblings = (scope, selector) => siblings$1(scope, e => is(e, selector));\n\n    const attr = 'data-ephox-mobile-fullscreen-style';\n    const siblingStyles = 'display:none!important;';\n    const ancestorPosition = 'position:absolute!important;';\n    const ancestorStyles = 'top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;';\n    const bgFallback = 'background-color:rgb(255,255,255)!important;';\n    const isAndroid = global$1.os.isAndroid();\n    const matchColor = editorBody => {\n      const color = get$2(editorBody, 'background-color');\n      return color !== undefined && color !== '' ? 'background-color:' + color + '!important' : bgFallback;\n    };\n    const clobberStyles = (dom, container, editorBody) => {\n      const gatherSiblings = element => {\n        return siblings(element, '*:not(.tox-silver-sink)');\n      };\n      const clobber = clobberStyle => element => {\n        const styles = get$3(element, 'style');\n        const backup = styles === undefined ? 'no-styles' : styles.trim();\n        if (backup === clobberStyle) {\n          return;\n        } else {\n          set$1(element, attr, backup);\n          setAll(element, dom.parseStyle(clobberStyle));\n        }\n      };\n      const ancestors$1 = ancestors(container, '*');\n      const siblings$1 = bind$3(ancestors$1, gatherSiblings);\n      const bgColor = matchColor(editorBody);\n      each$1(siblings$1, clobber(siblingStyles));\n      each$1(ancestors$1, clobber(ancestorPosition + ancestorStyles + bgColor));\n      const containerStyles = isAndroid === true ? '' : ancestorPosition;\n      clobber(containerStyles + ancestorStyles + bgColor)(container);\n    };\n    const restoreStyles = dom => {\n      const clobberedEls = all('[' + attr + ']');\n      each$1(clobberedEls, element => {\n        const restore = get$3(element, attr);\n        if (restore && restore !== 'no-styles') {\n          setAll(element, dom.parseStyle(restore));\n        } else {\n          remove(element, 'style');\n        }\n        remove(element, attr);\n      });\n    };\n\n    const DOM = global$2.DOM;\n    const getScrollPos = () => getBounds(window);\n    const setScrollPos = pos => window.scrollTo(pos.x, pos.y);\n    const viewportUpdate = get().fold(() => ({\n      bind: noop,\n      unbind: noop\n    }), visualViewport => {\n      const editorContainer = value();\n      const resizeBinder = unbindable();\n      const scrollBinder = unbindable();\n      const refreshScroll = () => {\n        document.body.scrollTop = 0;\n        document.documentElement.scrollTop = 0;\n      };\n      const refreshVisualViewport = () => {\n        window.requestAnimationFrame(() => {\n          editorContainer.on(container => setAll(container, {\n            top: visualViewport.offsetTop + 'px',\n            left: visualViewport.offsetLeft + 'px',\n            height: visualViewport.height + 'px',\n            width: visualViewport.width + 'px'\n          }));\n        });\n      };\n      const update = first(() => {\n        refreshScroll();\n        refreshVisualViewport();\n      }, 50);\n      const bind$1 = element => {\n        editorContainer.set(element);\n        update.throttle();\n        resizeBinder.set(bind('resize', update.throttle));\n        scrollBinder.set(bind('scroll', update.throttle));\n      };\n      const unbind = () => {\n        editorContainer.on(() => {\n          resizeBinder.clear();\n          scrollBinder.clear();\n        });\n        editorContainer.clear();\n      };\n      return {\n        bind: bind$1,\n        unbind\n      };\n    });\n    const toggleFullscreen = (editor, fullscreenState) => {\n      const body = document.body;\n      const documentElement = document.documentElement;\n      const editorContainer = editor.getContainer();\n      const editorContainerS = SugarElement.fromDom(editorContainer);\n      const sinkContainerS = nextSibling(editorContainerS).filter(elm => isHTMLElement(elm) && has(elm, 'tox-silver-sink'));\n      const fullscreenRoot = getFullscreenRoot(editor);\n      const fullscreenInfo = fullscreenState.get();\n      const editorBody = SugarElement.fromDom(editor.getBody());\n      const isTouch = global$1.deviceType.isTouch();\n      const editorContainerStyle = editorContainer.style;\n      const iframe = editor.iframeElement;\n      const iframeStyle = iframe === null || iframe === void 0 ? void 0 : iframe.style;\n      const handleClasses = handler => {\n        handler(body, 'tox-fullscreen');\n        handler(documentElement, 'tox-fullscreen');\n        handler(editorContainer, 'tox-fullscreen');\n        getShadowRoot(editorContainerS).map(root => getShadowHost(root).dom).each(host => {\n          handler(host, 'tox-fullscreen');\n          handler(host, 'tox-shadowhost');\n        });\n      };\n      const cleanup = () => {\n        if (isTouch) {\n          restoreStyles(editor.dom);\n        }\n        handleClasses(DOM.removeClass);\n        viewportUpdate.unbind();\n        Optional.from(fullscreenState.get()).each(info => info.fullscreenChangeHandler.unbind());\n      };\n      if (!fullscreenInfo) {\n        const fullscreenChangeHandler = bind$1(owner(fullscreenRoot), getFullscreenchangeEventName(), _evt => {\n          if (getFullscreenNative(editor)) {\n            if (!isFullscreenElement(fullscreenRoot) && fullscreenState.get() !== null) {\n              toggleFullscreen(editor, fullscreenState);\n            }\n          }\n        });\n        const newFullScreenInfo = {\n          scrollPos: getScrollPos(),\n          containerWidth: editorContainerStyle.width,\n          containerHeight: editorContainerStyle.height,\n          containerTop: editorContainerStyle.top,\n          containerLeft: editorContainerStyle.left,\n          iframeWidth: iframeStyle.width,\n          iframeHeight: iframeStyle.height,\n          fullscreenChangeHandler,\n          sinkCssPosition: sinkContainerS.map(elm => get$2(elm, 'position'))\n        };\n        if (isTouch) {\n          clobberStyles(editor.dom, editorContainerS, editorBody);\n        }\n        iframeStyle.width = iframeStyle.height = '100%';\n        editorContainerStyle.width = editorContainerStyle.height = '';\n        handleClasses(DOM.addClass);\n        sinkContainerS.each(elm => {\n          set(elm, 'position', 'fixed');\n        });\n        viewportUpdate.bind(editorContainerS);\n        editor.on('remove', cleanup);\n        fullscreenState.set(newFullScreenInfo);\n        if (getFullscreenNative(editor)) {\n          requestFullscreen(fullscreenRoot);\n        }\n        fireFullscreenStateChanged(editor, true);\n      } else {\n        fullscreenInfo.fullscreenChangeHandler.unbind();\n        if (getFullscreenNative(editor) && isFullscreenElement(fullscreenRoot)) {\n          exitFullscreen(owner(fullscreenRoot));\n        }\n        iframeStyle.width = fullscreenInfo.iframeWidth;\n        iframeStyle.height = fullscreenInfo.iframeHeight;\n        editorContainerStyle.width = fullscreenInfo.containerWidth;\n        editorContainerStyle.height = fullscreenInfo.containerHeight;\n        editorContainerStyle.top = fullscreenInfo.containerTop;\n        editorContainerStyle.left = fullscreenInfo.containerLeft;\n        lift2(sinkContainerS, fullscreenInfo.sinkCssPosition, (elm, val) => {\n          set(elm, 'position', val);\n        });\n        cleanup();\n        setScrollPos(fullscreenInfo.scrollPos);\n        fullscreenState.set(null);\n        fireFullscreenStateChanged(editor, false);\n        editor.off('remove', cleanup);\n      }\n    };\n\n    const register$1 = (editor, fullscreenState) => {\n      editor.addCommand('mceFullScreen', () => {\n        toggleFullscreen(editor, fullscreenState);\n      });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    const setup = (editor, fullscreenState) => {\n      editor.on('init', () => {\n        editor.on('keydown', e => {\n          if (e.keyCode === global.TAB && !(e.metaKey || e.ctrlKey) && fullscreenState.get()) {\n            e.preventDefault();\n          }\n        });\n      });\n    };\n\n    const makeSetupHandler = (editor, fullscreenState) => api => {\n      api.setActive(fullscreenState.get() !== null);\n      const editorEventCallback = e => api.setActive(e.state);\n      editor.on('FullscreenStateChanged', editorEventCallback);\n      return () => editor.off('FullscreenStateChanged', editorEventCallback);\n    };\n    const register = (editor, fullscreenState) => {\n      const onAction = () => editor.execCommand('mceFullScreen');\n      editor.ui.registry.addToggleMenuItem('fullscreen', {\n        text: 'Fullscreen',\n        icon: 'fullscreen',\n        shortcut: 'Meta+Shift+F',\n        onAction,\n        onSetup: makeSetupHandler(editor, fullscreenState)\n      });\n      editor.ui.registry.addToggleButton('fullscreen', {\n        tooltip: 'Fullscreen',\n        icon: 'fullscreen',\n        onAction,\n        onSetup: makeSetupHandler(editor, fullscreenState),\n        shortcut: 'Meta+Shift+F'\n      });\n    };\n\n    var Plugin = () => {\n      global$3.add('fullscreen', editor => {\n        const fullscreenState = Cell(null);\n        if (editor.inline) {\n          return get$5(fullscreenState);\n        }\n        register$2(editor);\n        register$1(editor, fullscreenState);\n        register(editor, fullscreenState);\n        setup(editor, fullscreenState);\n        editor.addShortcut('Meta+Shift+F', '', 'mceFullScreen');\n        return get$5(fullscreenState);\n      });\n    };\n\n    Plugin();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,QAAM,OAAO,aAAW;AACtB,QAAIA,SAAQ;AACZ,UAAMC,OAAM,MAAM;AAChB,aAAOD;AAAA,IACT;AACA,UAAME,OAAM,OAAK;AACf,MAAAF,SAAQ;AAAA,IACV;AACA,WAAO;AAAA,MACL,KAAAC;AAAA,MACA,KAAAC;AAAA,IACF;AAAA,EACF;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,QAAM,QAAQ,sBAAoB,EAAE,cAAc,MAAM,gBAAgB,IAAI,MAAM,KAAK;AAEvF,QAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,QAAI;AACJ,QAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,aAAO;AAAA,IACT,OAAO;AACL,eAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,IAC7F;AAAA,EACF;AACA,QAAM,SAAS,OAAK;AAClB,UAAM,IAAI,OAAO;AACjB,QAAI,MAAM,MAAM;AACd,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,WAAW,CAAAC,UAAQ,CAAAH,WAAS,OAAOA,MAAK,MAAMG;AACpD,QAAM,eAAe,CAAAA,UAAQ,CAAAH,WAAS,OAAOA,WAAUG;AACvD,QAAM,OAAO,OAAK,OAAK,MAAM;AAC7B,QAAM,WAAW,SAAS,QAAQ;AAClC,QAAM,WAAW,SAAS,QAAQ;AAClC,QAAM,UAAU,SAAS,OAAO;AAChC,QAAM,SAAS,KAAK,IAAI;AACxB,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,cAAc,KAAK,MAAS;AAClC,QAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,QAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,QAAM,aAAa,aAAa,UAAU;AAC1C,QAAM,WAAW,aAAa,QAAQ;AAEtC,QAAM,OAAO,MAAM;AAAA,EACnB;AACA,QAAM,UAAU,CAAC,IAAI,OAAO;AAC1B,WAAO,IAAI,SAAS;AAClB,aAAO,GAAG,GAAG,MAAM,MAAM,IAAI,CAAC;AAAA,IAChC;AAAA,EACF;AACA,QAAM,WAAW,CAAC,KAAK,QAAQ,OAAK,IAAI,IAAI,CAAC,CAAC;AAC9C,QAAM,WAAW,CAAAH,WAAS;AACxB,WAAO,MAAM;AACX,aAAOA;AAAA,IACT;AAAA,EACF;AACA,WAAS,MAAM,OAAO,aAAa;AACjC,WAAO,IAAI,aAAa;AACtB,YAAMI,OAAM,YAAY,OAAO,QAAQ;AACvC,aAAO,GAAG,MAAM,MAAMA,IAAG;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,SAAS,SAAS,IAAI;AAAA,EAE5B,MAAM,SAAS;AAAA,IACb,YAAY,KAAKJ,QAAO;AACtB,WAAK,MAAM;AACX,WAAK,QAAQA;AAAA,IACf;AAAA,IACA,OAAO,KAAKA,QAAO;AACjB,aAAO,IAAI,SAAS,MAAMA,MAAK;AAAA,IACjC;AAAA,IACA,OAAO,OAAO;AACZ,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,KAAK,QAAQ,QAAQ;AACnB,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AACP,aAAO,KAAK;AAAA,IACd;AAAA,IACA,SAAS;AACP,aAAO,CAAC,KAAK;AAAA,IACf;AAAA,IACA,IAAI,QAAQ;AACV,UAAI,KAAK,KAAK;AACZ,eAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,MACzC,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,KAAKK,SAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAOA,QAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IACzC;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IAC1C;AAAA,IACA,OAAO,WAAW;AAChB,UAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,MAAM,aAAa;AACjB,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,GAAG,aAAa;AACd,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B;AAAA,IACA,WAAW,OAAO;AAChB,aAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,IACvC;AAAA,IACA,QAAQ,OAAO;AACb,aAAO,KAAK,MAAM,OAAO,MAAM;AAAA,IACjC;AAAA,IACA,SAAS,SAAS;AAChB,UAAI,CAAC,KAAK,KAAK;AACb,cAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,MAC9F,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,IACA,OAAO,KAAKL,QAAO;AACjB,aAAO,cAAcA,MAAK,IAAI,SAAS,KAAKA,MAAK,IAAI,SAAS,KAAK;AAAA,IACrE;AAAA,IACA,YAAY;AACV,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,iBAAiB;AACf,aAAO,KAAK;AAAA,IACd;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,IACA,UAAU;AACR,aAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,IACpC;AAAA,IACA,WAAW;AACT,aAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,IAC9C;AAAA,EACF;AACA,WAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,QAAM,aAAa,MAAM,UAAU;AACnC,QAAM,MAAM,CAAC,IAAI,MAAM;AACrB,UAAM,MAAM,GAAG;AACf,UAAMM,KAAI,IAAI,MAAM,GAAG;AACvB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAM,IAAI,GAAG,CAAC;AACd,MAAAA,GAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,IACf;AACA,WAAOA;AAAA,EACT;AACA,QAAM,SAAS,CAAC,IAAI,MAAM;AACxB,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,WAAW,CAAC,IAAI,SAAS;AAC7B,UAAMA,KAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,KAAK,GAAG,CAAC,GAAG;AACd,QAAAA,GAAE,KAAK,CAAC;AAAA,MACV;AAAA,IACF;AACA,WAAOA;AAAA,EACT;AACA,QAAM,YAAY,CAAC,IAAI,MAAM,UAAU;AACrC,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,KAAK,GAAG,CAAC,GAAG;AACd,eAAO,SAAS,KAAK,CAAC;AAAA,MACxB,WAAW,MAAM,GAAG,CAAC,GAAG;AACtB;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,QAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,WAAO,UAAU,IAAI,MAAM,KAAK;AAAA,EAClC;AACA,QAAM,UAAU,QAAM;AACpB,UAAMA,KAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,UAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG;AACnB,cAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,MAC7E;AACA,iBAAW,MAAMA,IAAG,GAAG,CAAC,CAAC;AAAA,IAC3B;AACA,WAAOA;AAAA,EACT;AACA,QAAM,SAAS,CAAC,IAAI,MAAM,QAAQ,IAAI,IAAI,CAAC,CAAC;AAC5C,QAAM,QAAQ,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,CAAC,CAAC,IAAI,SAAS,KAAK;AACxF,QAAM,OAAO,QAAM,MAAM,IAAI,CAAC;AAC9B,QAAM,UAAU,CAAC,KAAK,MAAM;AAC1B,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAMA,KAAI,EAAE,IAAI,CAAC,GAAG,CAAC;AACrB,UAAIA,GAAE,OAAO,GAAG;AACd,eAAOA;AAAA,MACT;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AAEA,QAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI,SAAS,KAAK,EAAE,GAAG,SAAS,GAAG,GAAG,SAAS,CAAC,CAAC,IAAI,SAAS,KAAK;AAEzH,QAAM,YAAY,cAAY;AAC5B,UAAM,UAAU,KAAK,SAAS,KAAK,CAAC;AACpC,UAAM,SAAS,MAAM,QAAQ,IAAI,EAAE,KAAK,QAAQ;AAChD,UAAM,QAAQ,MAAM;AAClB,aAAO;AACP,cAAQ,IAAI,SAAS,KAAK,CAAC;AAAA,IAC7B;AACA,UAAM,QAAQ,MAAM,QAAQ,IAAI,EAAE,OAAO;AACzC,UAAML,OAAM,MAAM,QAAQ,IAAI;AAC9B,UAAMC,OAAM,OAAK;AACf,aAAO;AACP,cAAQ,IAAI,SAAS,KAAK,CAAC,CAAC;AAAA,IAC9B;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAAD;AAAA,MACA,KAAAC;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,MAAM,UAAU,OAAK,EAAE,OAAO,CAAC;AAClD,QAAM,QAAQ,MAAM;AAClB,UAAM,UAAU,UAAU,IAAI;AAC9B,UAAM,KAAK,OAAK,QAAQ,IAAI,EAAE,KAAK,CAAC;AACpC,WAAO;AAAA,MACL,GAAG;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,QAAM,QAAQ,CAAC,IAAI,SAAS;AAC1B,QAAI,QAAQ;AACZ,UAAM,SAAS,MAAM;AACnB,UAAI,CAAC,OAAO,KAAK,GAAG;AAClB,qBAAa,KAAK;AAClB,gBAAQ;AAAA,MACV;AAAA,IACF;AACA,UAAM,WAAW,IAAI,SAAS;AAC5B,UAAI,OAAO,KAAK,GAAG;AACjB,gBAAQ,WAAW,MAAM;AACvB,kBAAQ;AACR,aAAG,MAAM,MAAM,IAAI;AAAA,QACrB,GAAG,IAAI;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO,OAAO;AACpB,QAAM,OAAO,CAAC,KAAK,MAAM;AACvB,UAAM,QAAQ,KAAK,GAAG;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,IAAI,IAAI,CAAC;AACf,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AAEA,QAAM,SAAS,OAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAEjF,QAAM,OAAO,CAAC,OAAO,UAAU;AAC7B,QAAI,IAAI,UAAU,UAAa,UAAU,OAAO,QAAQ;AACxD,aAAS,IAAI,GAAG,IAAI,MAAM,UAAU,MAAM,UAAa,MAAM,MAAM,EAAE,GAAG;AACtE,UAAI,EAAE,MAAM,CAAC,CAAC;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,QAAM,UAAU,CAAC,GAAG,UAAU;AAC5B,UAAM,QAAQ,EAAE,MAAM,GAAG;AACzB,WAAO,KAAK,OAAO,KAAK;AAAA,EAC1B;AAEA,QAAM,SAAS,CAAC,MAAM,UAAU;AAC9B,WAAO,QAAQ,MAAM,KAAK;AAAA,EAC5B;AACA,QAAM,WAAW,CAAC,MAAM,UAAU;AAChC,UAAM,SAAS,OAAO,MAAM,KAAK;AACjC,QAAI,WAAW,UAAa,WAAW,MAAM;AAC3C,YAAM,IAAI,MAAM,OAAO,gCAAgC;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AAEA,QAAM,iBAAiB,OAAO;AAC9B,QAAM,kBAAkB,WAAS;AAC/B,WAAO,SAAS,eAAe,KAAK;AAAA,EACtC;AACA,QAAM,gBAAgB,OAAK;AACzB,UAAM,QAAQ,QAAQ,6BAA6B,CAAC;AACpD,WAAO,SAAS,CAAC,MAAM,gBAAgB,KAAK,EAAE,UAAU,cAAc,CAAC,KAAK,mBAAmB,KAAK,eAAe,CAAC,EAAE,YAAY,IAAI;AAAA,EACxI;AAEA,QAAM,WAAW;AACjB,QAAM,oBAAoB;AAC1B,QAAM,UAAU;AAChB,QAAM,OAAO;AAEb,QAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,QAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,QAAM,gBAAgB,aAAW,UAAU,OAAO,KAAK,cAAc,QAAQ,GAAG;AAChF,QAAM,YAAY,OAAO,OAAO;AAChC,QAAM,SAAS,OAAO,IAAI;AAC1B,QAAM,aAAa,OAAO,QAAQ;AAClC,QAAM,qBAAqB,OAAO,iBAAiB;AAEnD,QAAM,SAAS,CAAC,KAAK,KAAKF,WAAU;AAClC,QAAI,SAASA,MAAK,KAAK,UAAUA,MAAK,KAAK,SAASA,MAAK,GAAG;AAC1D,UAAI,aAAa,KAAKA,SAAQ,EAAE;AAAA,IAClC,OAAO;AACL,cAAQ,MAAM,uCAAuC,KAAK,aAAaA,QAAO,eAAe,GAAG;AAChG,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,SAAS,KAAKA,WAAU;AACrC,WAAO,QAAQ,KAAK,KAAKA,MAAK;AAAA,EAChC;AACA,QAAM,QAAQ,CAAC,SAAS,QAAQ;AAC9B,UAAM,IAAI,QAAQ,IAAI,aAAa,GAAG;AACtC,WAAO,MAAM,OAAO,SAAY;AAAA,EAClC;AACA,QAAM,SAAS,CAAC,SAAS,QAAQ;AAC/B,YAAQ,IAAI,gBAAgB,GAAG;AAAA,EACjC;AAEA,QAAM,WAAW,aAAW,QAAQ,IAAI,cAAc;AAEtD,QAAM,MAAM,CAAC,SAAS,UAAU,SAAS,OAAO,KAAK,QAAQ,IAAI,UAAU,SAAS,KAAK;AAEzF,QAAM,WAAW,CAAC,KAAK,QAAQ,QAAQ,GAAG,QAAQ;AAChD,UAAM,MAAM,IAAI,QAAQ,QAAQ,KAAK;AACrC,QAAI,QAAQ,IAAI;AACd,aAAO,YAAY,GAAG,IAAI,OAAO,MAAM,OAAO,UAAU;AAAA,IAC1D,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,gBAAgB,SAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM,gBAAgB;AAE7F,QAAM,WAAW,CAAC,MAAM,UAAU;AAChC,UAAM,MAAM,SAAS;AACrB,UAAM,MAAM,IAAI,cAAc,KAAK;AACnC,QAAI,YAAY;AAChB,QAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,YAAM,UAAU;AAChB,cAAQ,MAAM,SAAS,IAAI;AAC3B,YAAM,IAAI,MAAM,OAAO;AAAA,IACzB;AACA,WAAO,QAAQ,IAAI,WAAW,CAAC,CAAC;AAAA,EAClC;AACA,QAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,UAAM,MAAM,SAAS;AACrB,UAAM,OAAO,IAAI,cAAc,GAAG;AAClC,WAAO,QAAQ,IAAI;AAAA,EACrB;AACA,QAAM,WAAW,CAAC,MAAM,UAAU;AAChC,UAAM,MAAM,SAAS;AACrB,UAAM,OAAO,IAAI,eAAe,IAAI;AACpC,WAAO,QAAQ,IAAI;AAAA,EACrB;AACA,QAAM,UAAU,UAAQ;AACtB,QAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,WAAO,EAAE,KAAK,KAAK;AAAA,EACrB;AACA,QAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,OAAO;AAChG,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,KAAK,CAAC,SAAS,aAAa;AAChC,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI,aAAa,SAAS;AAC5B,aAAO;AAAA,IACT,OAAO;AACL,YAAM,OAAO;AACb,UAAI,KAAK,YAAY,QAAW;AAC9B,eAAO,KAAK,QAAQ,QAAQ;AAAA,MAC9B,WAAW,KAAK,sBAAsB,QAAW;AAC/C,eAAO,KAAK,kBAAkB,QAAQ;AAAA,MACxC,WAAW,KAAK,0BAA0B,QAAW;AACnD,eAAO,KAAK,sBAAsB,QAAQ;AAAA,MAC5C,WAAW,KAAK,uBAAuB,QAAW;AAChD,eAAO,KAAK,mBAAmB,QAAQ;AAAA,MACzC,OAAO;AACL,cAAM,IAAI,MAAM,gCAAgC;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,SAAO,IAAI,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,qBAAqB,IAAI,sBAAsB;AACvJ,QAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,UAAM,OAAO,UAAU,SAAY,WAAW,MAAM;AACpD,WAAO,eAAe,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,iBAAiB,QAAQ,GAAG,aAAa,OAAO;AAAA,EAC9F;AAEA,QAAM,KAAK,CAAC,IAAI,OAAO,GAAG,QAAQ,GAAG;AAErC,QAAM,QAAQ,aAAW,aAAa,QAAQ,QAAQ,IAAI,aAAa;AACvE,QAAM,kBAAkB,SAAO,WAAW,GAAG,IAAI,MAAM,MAAM,GAAG;AAChE,QAAM,SAAS,aAAW,SAAS,KAAK,QAAQ,IAAI,UAAU,EAAE,IAAI,aAAa,OAAO;AACxF,QAAM,UAAU,CAAC,SAAS,WAAW;AACnC,UAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,QAAI,MAAM,QAAQ;AAClB,UAAM,MAAM,CAAC;AACb,WAAO,IAAI,eAAe,QAAQ,IAAI,eAAe,QAAW;AAC9D,YAAM,YAAY,IAAI;AACtB,YAAM,IAAI,aAAa,QAAQ,SAAS;AACxC,UAAI,KAAK,CAAC;AACV,UAAI,KAAK,CAAC,MAAM,MAAM;AACpB;AAAA,MACF,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAa,aAAW;AAC5B,UAAM,aAAa,cAAY,SAAS,UAAU,OAAK,CAAC,GAAG,SAAS,CAAC,CAAC;AACtE,WAAO,OAAO,OAAO,EAAE,IAAI,QAAQ,EAAE,IAAI,UAAU,EAAE,MAAM,CAAC,CAAC;AAAA,EAC/D;AACA,QAAM,cAAc,aAAW,SAAS,KAAK,QAAQ,IAAI,WAAW,EAAE,IAAI,aAAa,OAAO;AAC9F,QAAM,WAAW,aAAW,IAAI,QAAQ,IAAI,YAAY,aAAa,OAAO;AAE5E,QAAM,eAAe,SAAO,mBAAmB,GAAG,KAAK,cAAc,IAAI,IAAI,IAAI;AACjF,QAAM,YAAY,WAAW,QAAQ,UAAU,YAAY,KAAK,WAAW,KAAK,UAAU,WAAW;AACrG,QAAM,cAAc,SAAS,SAAS;AACtC,QAAM,cAAc,YAAY,OAAK,aAAa,QAAQ,EAAE,IAAI,YAAY,CAAC,IAAI;AACjF,QAAM,gBAAgB,OAAK;AACzB,UAAMM,KAAI,YAAY,CAAC;AACvB,WAAO,aAAaA,EAAC,IAAI,SAAS,KAAKA,EAAC,IAAI,SAAS,KAAK;AAAA,EAC5D;AACA,QAAM,gBAAgB,OAAK,aAAa,QAAQ,EAAE,IAAI,IAAI;AAC1D,QAAM,yBAAyB,WAAS;AACtC,QAAI,YAAY,KAAK,cAAc,MAAM,MAAM,GAAG;AAChD,YAAM,KAAK,aAAa,QAAQ,MAAM,MAAM;AAC5C,UAAI,UAAU,EAAE,KAAK,iBAAiB,EAAE,GAAG;AACzC,YAAI,MAAM,YAAY,MAAM,cAAc;AACxC,gBAAM,eAAe,MAAM,aAAa;AACxC,cAAI,cAAc;AAChB,mBAAO,KAAK,YAAY;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,KAAK,MAAM,MAAM;AAAA,EACnC;AACA,QAAM,mBAAmB,aAAW,cAAc,QAAQ,IAAI,UAAU;AAExE,QAAM,SAAS,aAAW;AACxB,UAAM,MAAM,OAAO,OAAO,IAAI,QAAQ,IAAI,aAAa,QAAQ;AAC/D,QAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,aAAO;AAAA,IACT;AACA,UAAM,MAAM,IAAI;AAChB,WAAO,cAAc,aAAa,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG,SAAS,QAAQ,aAAa,CAAC;AAAA,EACpH;AACA,QAAM,UAAU,SAAO;AACrB,UAAM,IAAI,IAAI,IAAI;AAClB,QAAI,MAAM,QAAQ,MAAM,QAAW;AACjC,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AACA,WAAO,aAAa,QAAQ,CAAC;AAAA,EAC/B;AAEA,QAAM,cAAc,CAAC,KAAK,UAAUN,WAAU;AAC5C,QAAI,CAAC,SAASA,MAAK,GAAG;AACpB,cAAQ,MAAM,sCAAsC,UAAU,aAAaA,QAAO,eAAe,GAAG;AACpG,YAAM,IAAI,MAAM,iCAAiCA,MAAK;AAAA,IACxD;AACA,QAAI,cAAc,GAAG,GAAG;AACtB,UAAI,MAAM,YAAY,UAAUA,MAAK;AAAA,IACvC;AAAA,EACF;AACA,QAAM,MAAM,CAAC,SAAS,UAAUA,WAAU;AACxC,UAAM,MAAM,QAAQ;AACpB,gBAAY,KAAK,UAAUA,MAAK;AAAA,EAClC;AACA,QAAM,SAAS,CAAC,SAAS,QAAQ;AAC/B,UAAM,MAAM,QAAQ;AACpB,SAAK,KAAK,CAAC,GAAG,MAAM;AAClB,kBAAY,KAAK,GAAG,CAAC;AAAA,IACvB,CAAC;AAAA,EACH;AACA,QAAM,QAAQ,CAAC,SAAS,aAAa;AACnC,UAAM,MAAM,QAAQ;AACpB,UAAM,SAAS,OAAO,iBAAiB,GAAG;AAC1C,UAAMM,KAAI,OAAO,iBAAiB,QAAQ;AAC1C,WAAOA,OAAM,MAAM,CAAC,OAAO,OAAO,IAAI,kBAAkB,KAAK,QAAQ,IAAIA;AAAA,EAC3E;AACA,QAAM,oBAAoB,CAAC,KAAK,aAAa,cAAc,GAAG,IAAI,IAAI,MAAM,iBAAiB,QAAQ,IAAI;AAEzG,QAAM,UAAU,CAAC,QAAQ,GAAG,GAAG,MAAM,SAAS,MAAM,SAAS;AAAA,IAC3D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,cAAY;AAC/B,UAAM,SAAS,aAAa,QAAQ,uBAAuB,QAAQ,EAAE,MAAM,SAAS,MAAM,CAAC;AAC3F,UAAM,OAAO,MAAM,SAAS,gBAAgB;AAC5C,UAAM,UAAU,MAAM,SAAS,eAAe;AAC9C,UAAM,OAAO,QAAQ,SAAS,IAAI;AAClC,WAAO,QAAQ,QAAQ,SAAS,SAAS,SAAS,SAAS,MAAM,SAAS,MAAM,QAAQ;AAAA,EAC1F;AACA,QAAM,SAAS,CAACC,SAAQ,YAAY,cAAY;AAC9C,QAAIA,QAAO,QAAQ,GAAG;AACpB,cAAQ,aAAa,QAAQ,CAAC;AAAA,IAChC;AAAA,EACF;AACA,QAAM,SAAS,CAAC,SAAS,OAAOA,SAAQ,SAAS,eAAe;AAC9D,UAAM,UAAU,OAAOA,SAAQ,OAAO;AACtC,YAAQ,IAAI,iBAAiB,OAAO,SAAS,UAAU;AACvD,WAAO,EAAE,QAAQ,MAAM,QAAQ,SAAS,OAAO,SAAS,UAAU,EAAE;AAAA,EACtE;AACA,QAAM,SAAS,CAAC,SAAS,OAAOA,SAAQ,YAAY,OAAO,SAAS,OAAOA,SAAQ,SAAS,KAAK;AACjG,QAAM,SAAS,CAAC,SAAS,OAAO,SAAS,eAAe;AACtD,YAAQ,IAAI,oBAAoB,OAAO,SAAS,UAAU;AAAA,EAC5D;AAEA,QAAM,SAAS;AACf,QAAM,SAAS,CAAC,SAAS,OAAO,YAAY,OAAO,SAAS,OAAO,QAAQ,OAAO;AAElF,QAAM,SAAS,OAAK;AAClB,QAAI,SAAS;AACb,QAAID;AACJ,WAAO,IAAI,SAAS;AAClB,UAAI,CAAC,QAAQ;AACX,iBAAS;AACT,QAAAA,KAAI,EAAE,MAAM,MAAM,IAAI;AAAA,MACxB;AACA,aAAOA;AAAA,IACT;AAAA,EACF;AAEA,QAAM,aAAa,CAAC,IAAI,SAAS,WAAWE,gBAAe;AACzD,UAAM,SAAS,GAAG,MAAM,KAAK,QAAQ,KAAK,SAAS,MAAM;AACzD,UAAM,WAAW,GAAG,MAAM,KAAK,CAAC;AAChC,UAAM,WAAW,GAAG,MAAM,KAAK,GAAG,UAAU;AAC5C,UAAM,UAAU,YAAYA,YAAW,kBAAkB;AACzD,UAAM,WAAW,UAAU,CAAC,YAAY,YAAYA,YAAW,0BAA0B;AACzF,UAAM,UAAU,YAAY,YAAY,CAAC;AACzC,UAAM,aAAa,QAAQ,SAAS,KAAK,GAAG,MAAM,KAAK,UAAU,KAAK,SAAS,MAAM;AACrF,UAAM,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;AAC5C,WAAO;AAAA,MACL,QAAQ,SAAS,MAAM;AAAA,MACvB,UAAU,SAAS,QAAQ;AAAA,MAC3B,UAAU,SAAS,QAAQ;AAAA,MAC3B,SAAS,SAAS,OAAO;AAAA,MACzB,SAAS,SAAS,OAAO;AAAA,MACzB,WAAW,GAAG;AAAA,MACd,OAAO,GAAG;AAAA,MACV,WAAW,SAAS,UAAU;AAAA,MAC9B,WAAW,SAAS,SAAS;AAAA,IAC/B;AAAA,EACF;AAEA,QAAM,aAAa,CAAC,SAAS,MAAM;AACjC,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAM,IAAI,QAAQ,CAAC;AACnB,UAAI,EAAE,KAAK,CAAC,GAAG;AACb,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,OAAO,CAAC,SAAS,UAAU;AAC/B,UAAMF,KAAI,WAAW,SAAS,KAAK;AACnC,QAAI,CAACA,IAAG;AACN,aAAO;AAAA,QACL,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,QAAQ,OAAK;AACjB,aAAO,OAAO,MAAM,QAAQA,IAAG,MAAM,CAAC,CAAC;AAAA,IACzC;AACA,WAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EAChC;AACA,QAAM,WAAW,CAAC,gBAAgB,UAAU;AAC1C,UAAM,eAAe,OAAO,KAAK,EAAE,YAAY;AAC/C,QAAI,eAAe,WAAW,GAAG;AAC/B,aAAO,UAAU;AAAA,IACnB;AACA,WAAO,KAAK,gBAAgB,YAAY;AAAA,EAC1C;AACA,QAAM,YAAY,MAAM;AACtB,WAAO,KAAK,GAAG,CAAC;AAAA,EAClB;AACA,QAAM,OAAO,CAAC,OAAO,UAAU;AAC7B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU;AAAA,IACd,IAAI;AAAA,IACJ,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAEA,QAAM,kBAAkB,CAACG,WAAU,kBAAkB;AACnD,WAAO,QAAQ,cAAc,QAAQ,aAAW;AAC9C,YAAM,UAAU,QAAQ,MAAM,YAAY;AAC1C,aAAO,OAAOA,WAAU,aAAW;AACjC,YAAI;AACJ,eAAO,cAAc,KAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAAA,MAC/F,CAAC,EAAE,IAAI,WAAS;AAAA,QACd,SAAS,KAAK;AAAA,QACd,SAAS,QAAQ,GAAG,SAAS,QAAQ,SAAS,EAAE,GAAG,CAAC;AAAA,MACtD,EAAE;AAAA,IACJ,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,CAAC,YAAY,cAAc;AAC1C,UAAM,QAAQ,OAAO,SAAS,EAAE,YAAY;AAC5C,WAAO,OAAO,YAAY,eAAa;AACrC,aAAO,UAAU,OAAO,KAAK;AAAA,IAC/B,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,CAACA,WAAU,cAAc;AAC7C,WAAO,SAASA,WAAU,SAAS,EAAE,IAAI,aAAW;AAClD,YAAM,UAAU,QAAQ,OAAO,QAAQ,gBAAgB,SAAS;AAChE,aAAO;AAAA,QACL,SAAS,QAAQ;AAAA,QACjB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,WAAW,CAACC,OAAM,cAAc;AACpC,WAAO,SAASA,OAAM,SAAS,EAAE,IAAI,QAAM;AACzC,YAAM,UAAU,QAAQ,OAAO,GAAG,gBAAgB,SAAS;AAC3D,aAAO;AAAA,QACL,SAAS,GAAG;AAAA,QACZ;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,qBAAqB;AAC3B,QAAM,gBAAgB,YAAU;AAC9B,WAAO,cAAY;AACjB,aAAO,SAAS,UAAU,MAAM;AAAA,IAClC;AAAA,EACF;AACA,QAAM,WAAW;AAAA,IACf;AAAA,MACE,MAAM;AAAA,MACN,gBAAgB,CAAC,gCAAgC;AAAA,MACjD,QAAQ,cAAY;AAClB,eAAO,SAAS,UAAU,OAAO,KAAK,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,aAAa;AAAA,MACxI;AAAA,IACF;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,MACP,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAQ,cAAY;AAClB,eAAO,SAAS,UAAU,QAAQ,KAAK,CAAC,SAAS,UAAU,aAAa;AAAA,MAC1E;AAAA,IACF;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAQ,cAAY;AAClB,eAAO,SAAS,UAAU,MAAM,KAAK,SAAS,UAAU,SAAS;AAAA,MACnE;AAAA,IACF;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAQ,cAAc,OAAO;AAAA,IAC/B;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,gBAAgB,CAAC,qCAAqC;AAAA,MACtD,QAAQ,cAAc,SAAS;AAAA,IACjC;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAQ,cAAY;AAClB,gBAAQ,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,SAAS,MAAM,SAAS,UAAU,aAAa;AAAA,MAC5G;AAAA,IACF;AAAA,EACF;AACA,QAAM,OAAO;AAAA,IACX;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,cAAc,KAAK;AAAA,MAC3B,gBAAgB,CAAC,uCAAuC;AAAA,IAC1D;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,cAAY;AAClB,eAAO,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,MAAM;AAAA,MAClE;AAAA,MACA,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,cAAc,SAAS;AAAA,MAC/B,gBAAgB,CAAC,mCAAmC;AAAA,IACtD;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,cAAc,UAAU;AAAA,MAChC,gBAAgB,CAAC,qCAAqC;AAAA,IACxD;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,cAAc,OAAO;AAAA,MAC7B,gBAAgB,CAAC;AAAA,IACnB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,cAAc,OAAO;AAAA,MAC7B,gBAAgB,CAAC;AAAA,IACnB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,cAAc,SAAS;AAAA,MAC/B,gBAAgB,CAAC;AAAA,IACnB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,cAAc,MAAM;AAAA,MAC5B,gBAAgB,CAAC,iCAAiC;AAAA,IACpD;AAAA,EACF;AACA,QAAM,eAAe;AAAA,IACnB,UAAU,SAAS,QAAQ;AAAA,IAC3B,MAAM,SAAS,IAAI;AAAA,EACrB;AAEA,QAAM,OAAO;AACb,QAAM,WAAW;AACjB,QAAM,KAAK;AACX,QAAM,QAAQ;AACd,QAAM,UAAU;AAChB,QAAM,SAAS;AACf,QAAM,YAAY,MAAM;AACtB,WAAO,KAAK;AAAA,MACV,SAAS;AAAA,MACT,SAAS,QAAQ,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACH;AACA,QAAM,OAAO,UAAQ;AACnB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,KAAK;AACrB,UAAM,YAAY,UAAQ,MAAM,YAAY;AAC5C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,QAAQ,UAAU,IAAI;AAAA,MACtB,YAAY,UAAU,QAAQ;AAAA,MAC9B,MAAM,UAAU,EAAE;AAAA,MAClB,SAAS,UAAU,KAAK;AAAA,MACxB,WAAW,UAAU,OAAO;AAAA,MAC5B,UAAU,UAAU,MAAM;AAAA,IAC5B;AAAA,EACF;AACA,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,IAAI;AAAA,IACJ,MAAM,SAAS,IAAI;AAAA,IACnB,UAAU,SAAS,QAAQ;AAAA,IAC3B,IAAI,SAAS,EAAE;AAAA,IACf,OAAO,SAAS,KAAK;AAAA,IACrB,SAAS,SAAS,OAAO;AAAA,IACzB,QAAQ,SAAS,MAAM;AAAA,EACzB;AAEA,QAAM,UAAU;AAChB,QAAM,MAAM;AACZ,QAAM,UAAU;AAChB,QAAM,QAAQ;AACd,QAAM,QAAQ;AACd,QAAM,UAAU;AAChB,QAAM,UAAU;AAChB,QAAM,WAAW;AACjB,QAAM,UAAU,MAAM;AACpB,WAAO,GAAG;AAAA,MACR,SAAS;AAAA,MACT,SAAS,QAAQ,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACH;AACA,QAAM,KAAK,UAAQ;AACjB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,KAAK;AACrB,UAAM,OAAO,UAAQ,MAAM,YAAY;AACvC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,WAAW,KAAK,OAAO;AAAA,MACvB,OAAO,KAAK,GAAG;AAAA,MACf,WAAW,KAAK,OAAO;AAAA,MACvB,SAAS,KAAK,KAAK;AAAA,MACnB,SAAS,KAAK,KAAK;AAAA,MACnB,WAAW,KAAK,OAAO;AAAA,MACvB,WAAW,KAAK,OAAO;AAAA,MACvB,YAAY,KAAK,QAAQ;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,IACA,SAAS,SAAS,OAAO;AAAA,IACzB,KAAK,SAAS,GAAG;AAAA,IACjB,SAAS,SAAS,OAAO;AAAA,IACzB,OAAO,SAAS,KAAK;AAAA,IACrB,OAAO,SAAS,KAAK;AAAA,IACrB,SAAS,SAAS,OAAO;AAAA,IACzB,SAAS,SAAS,OAAO;AAAA,IACzB,UAAU,SAAS,QAAQ;AAAA,EAC7B;AAEA,QAAM,WAAW,CAAC,WAAW,kBAAkBF,gBAAe;AAC5D,UAAMC,YAAW,aAAa,SAAS;AACvC,UAAMC,QAAO,aAAa,KAAK;AAC/B,UAAM,UAAU,iBAAiB,KAAK,mBAAiB,gBAAgBD,WAAU,aAAa,CAAC,EAAE,QAAQ,MAAM,cAAcA,WAAU,SAAS,CAAC,EAAE,KAAK,QAAQ,SAAS,QAAQ,EAAE;AACnL,UAAM,KAAK,SAASC,OAAM,SAAS,EAAE,KAAK,gBAAgB,SAAS,gBAAgB,EAAE;AACrF,UAAM,aAAa,WAAW,IAAI,SAAS,WAAWF,WAAU;AAChE,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,oBAAoB,EAAE,QAAQ,SAAS;AAE7C,QAAM,aAAa,WAAS,OAAO,WAAW,KAAK,EAAE;AACrD,MAAI,WAAW,OAAO,MAAM,kBAAkB,OAAO,UAAU,WAAW,SAAS,KAAK,UAAU,aAAa,GAAG,UAAU,CAAC;AAC7H,QAAM,SAAS,MAAM,SAAS;AAE9B,QAAM,IAAI,CAAC,MAAM,QAAQ;AACvB,UAAM,YAAY,CAAC,GAAG,MAAM,EAAE,OAAO,GAAG,MAAM,CAAC;AAC/C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAAgB;AAEtB,QAAM,QAAQ,UAAQ;AACpB,UAAM,MAAM,SAAS,SAAY,KAAK,MAAM;AAC5C,UAAM,IAAI,IAAI,KAAK,cAAc,IAAI,gBAAgB;AACrD,UAAM,IAAI,IAAI,KAAK,aAAa,IAAI,gBAAgB;AACpD,WAAO,cAAc,GAAG,CAAC;AAAA,EAC3B;AAEA,QAAM,MAAM,UAAQ;AAClB,UAAM,MAAM,SAAS,SAAY,SAAS;AAC1C,QAAI,OAAO,EAAE,QAAQ,UAAU,GAAG;AAChC,aAAO,SAAS,KAAK;AAAA,IACvB,OAAO;AACL,aAAO,SAAS,KAAK,IAAI,cAAc;AAAA,IACzC;AAAA,EACF;AACA,QAAM,SAAS,CAAC,GAAG,GAAG,OAAO,YAAY;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,EACd;AACA,QAAM,YAAY,UAAQ;AACxB,UAAM,MAAM,SAAS,SAAY,SAAS;AAC1C,UAAM,MAAM,IAAI;AAChB,UAAM,SAAS,MAAM,aAAa,QAAQ,GAAG,CAAC;AAC9C,WAAO,IAAI,GAAG,EAAE,KAAK,MAAM;AACzB,YAAM,OAAO,IAAI,SAAS;AAC1B,YAAM,QAAQ,KAAK;AACnB,YAAM,SAAS,KAAK;AACpB,aAAO,OAAO,OAAO,MAAM,OAAO,KAAK,OAAO,MAAM;AAAA,IACtD,GAAG,oBAAkB,OAAO,KAAK,IAAI,eAAe,UAAU,OAAO,IAAI,GAAG,KAAK,IAAI,eAAe,SAAS,OAAO,GAAG,GAAG,eAAe,OAAO,eAAe,MAAM,CAAC;AAAA,EACxK;AACA,QAAM,OAAO,CAAC,MAAM,UAAU,SAAS,IAAI,IAAI,EAAE,IAAI,oBAAkB;AACrE,UAAM,UAAU,OAAK,SAAS,aAAa,CAAC,CAAC;AAC7C,mBAAe,iBAAiB,MAAM,OAAO;AAC7C,WAAO,EAAE,QAAQ,MAAM,eAAe,oBAAoB,MAAM,OAAO,EAAE;AAAA,EAC3E,CAAC,EAAE,WAAW,OAAO,EAAE,QAAQ,KAAK,EAAE;AAEtC,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,sBAAsB;AAEhE,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAEvD,QAAM,6BAA6B,CAAC,QAAQ,UAAU;AACpD,WAAO,SAAS,0BAA0B,EAAE,MAAM,CAAC;AACnD,WAAO,SAAS,cAAc;AAAA,EAChC;AAEA,QAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,QAAM,aAAa,YAAU;AAC3B,UAAM,iBAAiB,OAAO,QAAQ;AACtC,mBAAe,qBAAqB;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,QAAM,sBAAsB,OAAO,mBAAmB;AAEtD,QAAM,oBAAoB,YAAU;AAClC,UAAM,OAAO,aAAa,QAAQ,OAAO,WAAW,CAAC;AACrD,WAAO,cAAc,IAAI,EAAE,IAAI,aAAa,EAAE,WAAW,MAAM,QAAQ,MAAM,IAAI,CAAC,CAAC;AAAA,EACrF;AACA,QAAM,uBAAuB,UAAQ;AACnC,QAAI,KAAK,sBAAsB,QAAW;AACxC,aAAO,KAAK;AAAA,IACd,WAAW,KAAK,wBAAwB,QAAW;AACjD,aAAO,KAAK;AAAA,IACd,WAAW,KAAK,4BAA4B,QAAW;AACrD,aAAO,KAAK;AAAA,IACd,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,+BAA+B,MAAM;AACzC,QAAI,SAAS,sBAAsB,QAAW;AAC5C,aAAO;AAAA,IACT,WAAW,SAAS,wBAAwB,QAAW;AACrD,aAAO;AAAA,IACT,WAAW,SAAS,4BAA4B,QAAW;AACzD,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,oBAAoB,eAAa;AACrC,UAAM,OAAO,UAAU;AACvB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB;AAAA,IACzB,WAAW,KAAK,qBAAqB;AACnC,WAAK,oBAAoB;AAAA,IAC3B,WAAW,KAAK,yBAAyB;AACvC,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AACA,QAAM,iBAAiB,cAAY;AACjC,UAAM,MAAM,SAAS;AACrB,QAAI,IAAI,gBAAgB;AACtB,UAAI,eAAe;AAAA,IACrB,WAAW,IAAI,kBAAkB;AAC/B,UAAI,iBAAiB;AAAA,IACvB,WAAW,IAAI,wBAAwB;AACrC,UAAI,uBAAuB;AAAA,IAC7B;AAAA,EACF;AACA,QAAM,sBAAsB,UAAQ,KAAK,QAAQ,qBAAqB,MAAM,IAAI,EAAE,GAAG;AAErF,QAAM,cAAc,CAAC,OAAO,WAAW,WAAW,SAAS,QAAQ,OAAO,MAAM,GAAG,SAAS;AAC5F,QAAM,aAAa,CAAC,OAAO,cAAc,SAAS,WAAW,KAAK,GAAG,SAAS;AAE9E,QAAM,MAAM,cAAY,MAAM,QAAQ;AACtC,QAAM,YAAY,CAAC,OAAO,UAAU,WAAW,YAAY,OAAO,OAAK,GAAG,GAAG,QAAQ,GAAG,MAAM;AAC9F,QAAM,WAAW,CAAC,OAAO,aAAa,WAAW,OAAO,OAAK,GAAG,GAAG,QAAQ,CAAC;AAE5E,QAAM,OAAO;AACb,QAAM,gBAAgB;AACtB,QAAM,mBAAmB;AACzB,QAAM,iBAAiB;AACvB,QAAM,aAAa;AACnB,QAAM,YAAY,SAAS,GAAG,UAAU;AACxC,QAAM,aAAa,gBAAc;AAC/B,UAAM,QAAQ,MAAM,YAAY,kBAAkB;AAClD,WAAO,UAAU,UAAa,UAAU,KAAK,sBAAsB,QAAQ,eAAe;AAAA,EAC5F;AACA,QAAM,gBAAgB,CAAC,KAAK,WAAW,eAAe;AACpD,UAAM,iBAAiB,aAAW;AAChC,aAAO,SAAS,SAAS,yBAAyB;AAAA,IACpD;AACA,UAAM,UAAU,kBAAgB,aAAW;AACzC,YAAM,SAAS,MAAM,SAAS,OAAO;AACrC,YAAM,SAAS,WAAW,SAAY,cAAc,OAAO,KAAK;AAChE,UAAI,WAAW,cAAc;AAC3B;AAAA,MACF,OAAO;AACL,cAAM,SAAS,MAAM,MAAM;AAC3B,eAAO,SAAS,IAAI,WAAW,YAAY,CAAC;AAAA,MAC9C;AAAA,IACF;AACA,UAAMG,eAAc,UAAU,WAAW,GAAG;AAC5C,UAAMC,cAAa,OAAOD,cAAa,cAAc;AACrD,UAAM,UAAU,WAAW,UAAU;AACrC,WAAOC,aAAY,QAAQ,aAAa,CAAC;AACzC,WAAOD,cAAa,QAAQ,mBAAmB,iBAAiB,OAAO,CAAC;AACxE,UAAM,kBAAkB,cAAc,OAAO,KAAK;AAClD,YAAQ,kBAAkB,iBAAiB,OAAO,EAAE,SAAS;AAAA,EAC/D;AACA,QAAM,gBAAgB,SAAO;AAC3B,UAAM,eAAe,IAAI,MAAM,OAAO,GAAG;AACzC,WAAO,cAAc,aAAW;AAC9B,YAAM,UAAU,MAAM,SAAS,IAAI;AACnC,UAAI,WAAW,YAAY,aAAa;AACtC,eAAO,SAAS,IAAI,WAAW,OAAO,CAAC;AAAA,MACzC,OAAO;AACL,eAAO,SAAS,OAAO;AAAA,MACzB;AACA,aAAO,SAAS,IAAI;AAAA,IACtB,CAAC;AAAA,EACH;AAEA,QAAM,MAAM,SAAS;AACrB,QAAM,eAAe,MAAM,UAAU,MAAM;AAC3C,QAAM,eAAe,SAAO,OAAO,SAAS,IAAI,GAAG,IAAI,CAAC;AACxD,QAAM,iBAAiB,IAAI,EAAE,KAAK,OAAO;AAAA,IACvC,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,IAAI,oBAAkB;AACpB,UAAM,kBAAkB,MAAM;AAC9B,UAAM,eAAe,WAAW;AAChC,UAAM,eAAe,WAAW;AAChC,UAAM,gBAAgB,MAAM;AAC1B,eAAS,KAAK,YAAY;AAC1B,eAAS,gBAAgB,YAAY;AAAA,IACvC;AACA,UAAM,wBAAwB,MAAM;AAClC,aAAO,sBAAsB,MAAM;AACjC,wBAAgB,GAAG,eAAa,OAAO,WAAW;AAAA,UAChD,KAAK,eAAe,YAAY;AAAA,UAChC,MAAM,eAAe,aAAa;AAAA,UAClC,QAAQ,eAAe,SAAS;AAAA,UAChC,OAAO,eAAe,QAAQ;AAAA,QAChC,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AACA,UAAM,SAAS,MAAM,MAAM;AACzB,oBAAc;AACd,4BAAsB;AAAA,IACxB,GAAG,EAAE;AACL,UAAME,UAAS,aAAW;AACxB,sBAAgB,IAAI,OAAO;AAC3B,aAAO,SAAS;AAChB,mBAAa,IAAI,KAAK,UAAU,OAAO,QAAQ,CAAC;AAChD,mBAAa,IAAI,KAAK,UAAU,OAAO,QAAQ,CAAC;AAAA,IAClD;AACA,UAAMC,UAAS,MAAM;AACnB,sBAAgB,GAAG,MAAM;AACvB,qBAAa,MAAM;AACnB,qBAAa,MAAM;AAAA,MACrB,CAAC;AACD,sBAAgB,MAAM;AAAA,IACxB;AACA,WAAO;AAAA,MACL,MAAMD;AAAA,MACN,QAAAC;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,mBAAmB,CAAC,QAAQ,oBAAoB;AACpD,UAAM,OAAO,SAAS;AACtB,UAAM,kBAAkB,SAAS;AACjC,UAAM,kBAAkB,OAAO,aAAa;AAC5C,UAAM,mBAAmB,aAAa,QAAQ,eAAe;AAC7D,UAAM,iBAAiB,YAAY,gBAAgB,EAAE,OAAO,SAAO,cAAc,GAAG,KAAK,IAAI,KAAK,iBAAiB,CAAC;AACpH,UAAM,iBAAiB,kBAAkB,MAAM;AAC/C,UAAM,iBAAiB,gBAAgB,IAAI;AAC3C,UAAM,aAAa,aAAa,QAAQ,OAAO,QAAQ,CAAC;AACxD,UAAM,UAAU,SAAS,WAAW,QAAQ;AAC5C,UAAM,uBAAuB,gBAAgB;AAC7C,UAAM,SAAS,OAAO;AACtB,UAAM,cAAc,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AAC3E,UAAM,gBAAgB,aAAW;AAC/B,cAAQ,MAAM,gBAAgB;AAC9B,cAAQ,iBAAiB,gBAAgB;AACzC,cAAQ,iBAAiB,gBAAgB;AACzC,oBAAc,gBAAgB,EAAE,IAAI,UAAQ,cAAc,IAAI,EAAE,GAAG,EAAE,KAAK,UAAQ;AAChF,gBAAQ,MAAM,gBAAgB;AAC9B,gBAAQ,MAAM,gBAAgB;AAAA,MAChC,CAAC;AAAA,IACH;AACA,UAAM,UAAU,MAAM;AACpB,UAAI,SAAS;AACX,sBAAc,OAAO,GAAG;AAAA,MAC1B;AACA,oBAAc,IAAI,WAAW;AAC7B,qBAAe,OAAO;AACtB,eAAS,KAAK,gBAAgB,IAAI,CAAC,EAAE,KAAK,UAAQ,KAAK,wBAAwB,OAAO,CAAC;AAAA,IACzF;AACA,QAAI,CAAC,gBAAgB;AACnB,YAAM,0BAA0B,OAAO,MAAM,cAAc,GAAG,6BAA6B,GAAG,UAAQ;AACpG,YAAI,oBAAoB,MAAM,GAAG;AAC/B,cAAI,CAAC,oBAAoB,cAAc,KAAK,gBAAgB,IAAI,MAAM,MAAM;AAC1E,6BAAiB,QAAQ,eAAe;AAAA,UAC1C;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,oBAAoB;AAAA,QACxB,WAAW,aAAa;AAAA,QACxB,gBAAgB,qBAAqB;AAAA,QACrC,iBAAiB,qBAAqB;AAAA,QACtC,cAAc,qBAAqB;AAAA,QACnC,eAAe,qBAAqB;AAAA,QACpC,aAAa,YAAY;AAAA,QACzB,cAAc,YAAY;AAAA,QAC1B;AAAA,QACA,iBAAiB,eAAe,IAAI,SAAO,MAAM,KAAK,UAAU,CAAC;AAAA,MACnE;AACA,UAAI,SAAS;AACX,sBAAc,OAAO,KAAK,kBAAkB,UAAU;AAAA,MACxD;AACA,kBAAY,QAAQ,YAAY,SAAS;AACzC,2BAAqB,QAAQ,qBAAqB,SAAS;AAC3D,oBAAc,IAAI,QAAQ;AAC1B,qBAAe,KAAK,SAAO;AACzB,YAAI,KAAK,YAAY,OAAO;AAAA,MAC9B,CAAC;AACD,qBAAe,KAAK,gBAAgB;AACpC,aAAO,GAAG,UAAU,OAAO;AAC3B,sBAAgB,IAAI,iBAAiB;AACrC,UAAI,oBAAoB,MAAM,GAAG;AAC/B,0BAAkB,cAAc;AAAA,MAClC;AACA,iCAA2B,QAAQ,IAAI;AAAA,IACzC,OAAO;AACL,qBAAe,wBAAwB,OAAO;AAC9C,UAAI,oBAAoB,MAAM,KAAK,oBAAoB,cAAc,GAAG;AACtE,uBAAe,MAAM,cAAc,CAAC;AAAA,MACtC;AACA,kBAAY,QAAQ,eAAe;AACnC,kBAAY,SAAS,eAAe;AACpC,2BAAqB,QAAQ,eAAe;AAC5C,2BAAqB,SAAS,eAAe;AAC7C,2BAAqB,MAAM,eAAe;AAC1C,2BAAqB,OAAO,eAAe;AAC3C,YAAM,gBAAgB,eAAe,iBAAiB,CAAC,KAAK,QAAQ;AAClE,YAAI,KAAK,YAAY,GAAG;AAAA,MAC1B,CAAC;AACD,cAAQ;AACR,mBAAa,eAAe,SAAS;AACrC,sBAAgB,IAAI,IAAI;AACxB,iCAA2B,QAAQ,KAAK;AACxC,aAAO,IAAI,UAAU,OAAO;AAAA,IAC9B;AAAA,EACF;AAEA,QAAM,aAAa,CAAC,QAAQ,oBAAoB;AAC9C,WAAO,WAAW,iBAAiB,MAAM;AACvC,uBAAiB,QAAQ,eAAe;AAAA,IAC1C,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,iBAAiB;AAEzD,QAAM,QAAQ,CAAC,QAAQ,oBAAoB;AACzC,WAAO,GAAG,QAAQ,MAAM;AACtB,aAAO,GAAG,WAAW,OAAK;AACxB,YAAI,EAAE,YAAY,OAAO,OAAO,EAAE,EAAE,WAAW,EAAE,YAAY,gBAAgB,IAAI,GAAG;AAClF,YAAE,eAAe;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,QAAM,mBAAmB,CAAC,QAAQ,oBAAoB,SAAO;AAC3D,QAAI,UAAU,gBAAgB,IAAI,MAAM,IAAI;AAC5C,UAAM,sBAAsB,OAAK,IAAI,UAAU,EAAE,KAAK;AACtD,WAAO,GAAG,0BAA0B,mBAAmB;AACvD,WAAO,MAAM,OAAO,IAAI,0BAA0B,mBAAmB;AAAA,EACvE;AACA,QAAM,WAAW,CAAC,QAAQ,oBAAoB;AAC5C,UAAM,WAAW,MAAM,OAAO,YAAY,eAAe;AACzD,WAAO,GAAG,SAAS,kBAAkB,cAAc;AAAA,MACjD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV;AAAA,MACA,SAAS,iBAAiB,QAAQ,eAAe;AAAA,IACnD,CAAC;AACD,WAAO,GAAG,SAAS,gBAAgB,cAAc;AAAA,MAC/C,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,MACA,SAAS,iBAAiB,QAAQ,eAAe;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,MAAM;AACjB,aAAS,IAAI,cAAc,YAAU;AACnC,YAAM,kBAAkB,KAAK,IAAI;AACjC,UAAI,OAAO,QAAQ;AACjB,eAAO,MAAM,eAAe;AAAA,MAC9B;AACA,iBAAW,MAAM;AACjB,iBAAW,QAAQ,eAAe;AAClC,eAAS,QAAQ,eAAe;AAChC,YAAM,QAAQ,eAAe;AAC7B,aAAO,YAAY,gBAAgB,IAAI,eAAe;AACtD,aAAO,MAAM,eAAe;AAAA,IAC9B,CAAC;AAAA,EACH;AAEA,SAAO;AAEX,GAAG;", "names": ["value", "get", "set", "type", "all", "binder", "r", "filter", "mediaMatch", "browsers", "oses", "ancestors$1", "siblings$1", "bind$1", "unbind"]}