{"version": 3, "sources": ["../../../../node_modules/.pnpm/nanoid@5.0.7/node_modules/nanoid/non-secure/index.js"], "sourcesContent": ["let urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\nexport let customAlphabet = (alphabet, defaultSize = 21) => {\n  return (size = defaultSize) => {\n    let id = ''\n    let i = size\n    while (i--) {\n      id += alphabet[(Math.random() * alphabet.length) | 0]\n    }\n    return id\n  }\n}\nexport let nanoid = (size = 21) => {\n  let id = ''\n  let i = size\n  while (i--) {\n    id += urlAlphabet[(Math.random() * 64) | 0]\n  }\n  return id\n}\n"], "mappings": ";;;AAAA,IAAI,cACF;AACK,IAAI,iBAAiB,CAAC,UAAU,cAAc,OAAO;AAC1D,SAAO,CAAC,OAAO,gBAAgB;AAC7B,QAAI,KAAK;AACT,QAAI,IAAI;AACR,WAAO,KAAK;AACV,YAAM,SAAU,KAAK,OAAO,IAAI,SAAS,SAAU,CAAC;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AACF;AACO,IAAI,SAAS,CAAC,OAAO,OAAO;AACjC,MAAI,KAAK;AACT,MAAI,IAAI;AACR,SAAO,KAAK;AACV,UAAM,YAAa,KAAK,OAAO,IAAI,KAAM,CAAC;AAAA,EAC5C;AACA,SAAO;AACT;", "names": []}