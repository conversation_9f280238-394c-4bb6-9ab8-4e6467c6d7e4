#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/packages/extensions-sdk/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/packages/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/node_modules:/mnt/d/Projects/SICDC/node_modules:/mnt/d/Projects/node_modules:/mnt/d/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/packages/extensions-sdk/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/packages/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/node_modules:/mnt/d/Projects/SICDC/node_modules:/mnt/d/Projects/node_modules:/mnt/d/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@directus/extensions-sdk/cli.js" "$@"
else
  exec node  "$basedir/../@directus/extensions-sdk/cli.js" "$@"
fi
