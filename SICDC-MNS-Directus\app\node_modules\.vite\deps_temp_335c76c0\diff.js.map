{"version": 3, "sources": ["../../../../node_modules/.pnpm/diff@5.2.0/node_modules/diff/lib/index.mjs"], "sourcesContent": ["function Diff() {}\nDiff.prototype = {\n  diff: function diff(oldString, newString) {\n    var _options$timeout;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var callback = options.callback;\n\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    this.options = options;\n    var self = this;\n\n    function done(value) {\n      if (callback) {\n        setTimeout(function () {\n          callback(undefined, value);\n        }, 0);\n        return true;\n      } else {\n        return value;\n      }\n    } // Allow subclasses to massage the input prior to running\n\n\n    oldString = this.castInput(oldString);\n    newString = this.castInput(newString);\n    oldString = this.removeEmpty(this.tokenize(oldString));\n    newString = this.removeEmpty(this.tokenize(newString));\n    var newLen = newString.length,\n        oldLen = oldString.length;\n    var editLength = 1;\n    var maxEditLength = newLen + oldLen;\n\n    if (options.maxEditLength) {\n      maxEditLength = Math.min(maxEditLength, options.maxEditLength);\n    }\n\n    var maxExecutionTime = (_options$timeout = options.timeout) !== null && _options$timeout !== void 0 ? _options$timeout : Infinity;\n    var abortAfterTimestamp = Date.now() + maxExecutionTime;\n    var bestPath = [{\n      oldPos: -1,\n      lastComponent: undefined\n    }]; // Seed editLength = 0, i.e. the content starts with the same values\n\n    var newPos = this.extractCommon(bestPath[0], newString, oldString, 0);\n\n    if (bestPath[0].oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n      // Identity per the equality and tokenizer\n      return done([{\n        value: this.join(newString),\n        count: newString.length\n      }]);\n    } // Once we hit the right edge of the edit graph on some diagonal k, we can\n    // definitely reach the end of the edit graph in no more than k edits, so\n    // there's no point in considering any moves to diagonal k+1 any more (from\n    // which we're guaranteed to need at least k+1 more edits).\n    // Similarly, once we've reached the bottom of the edit graph, there's no\n    // point considering moves to lower diagonals.\n    // We record this fact by setting minDiagonalToConsider and\n    // maxDiagonalToConsider to some finite value once we've hit the edge of\n    // the edit graph.\n    // This optimization is not faithful to the original algorithm presented in\n    // Myers's paper, which instead pointlessly extends D-paths off the end of\n    // the edit graph - see page 7 of Myers's paper which notes this point\n    // explicitly and illustrates it with a diagram. This has major performance\n    // implications for some common scenarios. For instance, to compute a diff\n    // where the new text simply appends d characters on the end of the\n    // original text of length n, the true Myers algorithm will take O(n+d^2)\n    // time while this optimization needs only O(n+d) time.\n\n\n    var minDiagonalToConsider = -Infinity,\n        maxDiagonalToConsider = Infinity; // Main worker method. checks all permutations of a given edit length for acceptance.\n\n    function execEditLength() {\n      for (var diagonalPath = Math.max(minDiagonalToConsider, -editLength); diagonalPath <= Math.min(maxDiagonalToConsider, editLength); diagonalPath += 2) {\n        var basePath = void 0;\n        var removePath = bestPath[diagonalPath - 1],\n            addPath = bestPath[diagonalPath + 1];\n\n        if (removePath) {\n          // No one else is going to attempt to use this value, clear it\n          bestPath[diagonalPath - 1] = undefined;\n        }\n\n        var canAdd = false;\n\n        if (addPath) {\n          // what newPos will be after we do an insertion:\n          var addPathNewPos = addPath.oldPos - diagonalPath;\n          canAdd = addPath && 0 <= addPathNewPos && addPathNewPos < newLen;\n        }\n\n        var canRemove = removePath && removePath.oldPos + 1 < oldLen;\n\n        if (!canAdd && !canRemove) {\n          // If this path is a terminal then prune\n          bestPath[diagonalPath] = undefined;\n          continue;\n        } // Select the diagonal that we want to branch from. We select the prior\n        // path whose position in the old string is the farthest from the origin\n        // and does not pass the bounds of the diff graph\n        // TODO: Remove the `+ 1` here to make behavior match Myers algorithm\n        //       and prefer to order removals before insertions.\n\n\n        if (!canRemove || canAdd && removePath.oldPos + 1 < addPath.oldPos) {\n          basePath = self.addToPath(addPath, true, undefined, 0);\n        } else {\n          basePath = self.addToPath(removePath, undefined, true, 1);\n        }\n\n        newPos = self.extractCommon(basePath, newString, oldString, diagonalPath);\n\n        if (basePath.oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n          // If we have hit the end of both strings, then we are done\n          return done(buildValues(self, basePath.lastComponent, newString, oldString, self.useLongestToken));\n        } else {\n          bestPath[diagonalPath] = basePath;\n\n          if (basePath.oldPos + 1 >= oldLen) {\n            maxDiagonalToConsider = Math.min(maxDiagonalToConsider, diagonalPath - 1);\n          }\n\n          if (newPos + 1 >= newLen) {\n            minDiagonalToConsider = Math.max(minDiagonalToConsider, diagonalPath + 1);\n          }\n        }\n      }\n\n      editLength++;\n    } // Performs the length of edit iteration. Is a bit fugly as this has to support the\n    // sync and async mode which is never fun. Loops over execEditLength until a value\n    // is produced, or until the edit length exceeds options.maxEditLength (if given),\n    // in which case it will return undefined.\n\n\n    if (callback) {\n      (function exec() {\n        setTimeout(function () {\n          if (editLength > maxEditLength || Date.now() > abortAfterTimestamp) {\n            return callback();\n          }\n\n          if (!execEditLength()) {\n            exec();\n          }\n        }, 0);\n      })();\n    } else {\n      while (editLength <= maxEditLength && Date.now() <= abortAfterTimestamp) {\n        var ret = execEditLength();\n\n        if (ret) {\n          return ret;\n        }\n      }\n    }\n  },\n  addToPath: function addToPath(path, added, removed, oldPosInc) {\n    var last = path.lastComponent;\n\n    if (last && last.added === added && last.removed === removed) {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: last.count + 1,\n          added: added,\n          removed: removed,\n          previousComponent: last.previousComponent\n        }\n      };\n    } else {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: 1,\n          added: added,\n          removed: removed,\n          previousComponent: last\n        }\n      };\n    }\n  },\n  extractCommon: function extractCommon(basePath, newString, oldString, diagonalPath) {\n    var newLen = newString.length,\n        oldLen = oldString.length,\n        oldPos = basePath.oldPos,\n        newPos = oldPos - diagonalPath,\n        commonCount = 0;\n\n    while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(newString[newPos + 1], oldString[oldPos + 1])) {\n      newPos++;\n      oldPos++;\n      commonCount++;\n    }\n\n    if (commonCount) {\n      basePath.lastComponent = {\n        count: commonCount,\n        previousComponent: basePath.lastComponent\n      };\n    }\n\n    basePath.oldPos = oldPos;\n    return newPos;\n  },\n  equals: function equals(left, right) {\n    if (this.options.comparator) {\n      return this.options.comparator(left, right);\n    } else {\n      return left === right || this.options.ignoreCase && left.toLowerCase() === right.toLowerCase();\n    }\n  },\n  removeEmpty: function removeEmpty(array) {\n    var ret = [];\n\n    for (var i = 0; i < array.length; i++) {\n      if (array[i]) {\n        ret.push(array[i]);\n      }\n    }\n\n    return ret;\n  },\n  castInput: function castInput(value) {\n    return value;\n  },\n  tokenize: function tokenize(value) {\n    return value.split('');\n  },\n  join: function join(chars) {\n    return chars.join('');\n  }\n};\n\nfunction buildValues(diff, lastComponent, newString, oldString, useLongestToken) {\n  // First we convert our linked list of components in reverse order to an\n  // array in the right order:\n  var components = [];\n  var nextComponent;\n\n  while (lastComponent) {\n    components.push(lastComponent);\n    nextComponent = lastComponent.previousComponent;\n    delete lastComponent.previousComponent;\n    lastComponent = nextComponent;\n  }\n\n  components.reverse();\n  var componentPos = 0,\n      componentLen = components.length,\n      newPos = 0,\n      oldPos = 0;\n\n  for (; componentPos < componentLen; componentPos++) {\n    var component = components[componentPos];\n\n    if (!component.removed) {\n      if (!component.added && useLongestToken) {\n        var value = newString.slice(newPos, newPos + component.count);\n        value = value.map(function (value, i) {\n          var oldValue = oldString[oldPos + i];\n          return oldValue.length > value.length ? oldValue : value;\n        });\n        component.value = diff.join(value);\n      } else {\n        component.value = diff.join(newString.slice(newPos, newPos + component.count));\n      }\n\n      newPos += component.count; // Common case\n\n      if (!component.added) {\n        oldPos += component.count;\n      }\n    } else {\n      component.value = diff.join(oldString.slice(oldPos, oldPos + component.count));\n      oldPos += component.count; // Reverse add and remove so removes are output first to match common convention\n      // The diffing algorithm is tied to add then remove output and this is the simplest\n      // route to get the desired output with minimal overhead.\n\n      if (componentPos && components[componentPos - 1].added) {\n        var tmp = components[componentPos - 1];\n        components[componentPos - 1] = components[componentPos];\n        components[componentPos] = tmp;\n      }\n    }\n  } // Special case handle for when one terminal is ignored (i.e. whitespace).\n  // For this case we merge the terminal into the prior string and drop the change.\n  // This is only available for string mode.\n\n\n  var finalComponent = components[componentLen - 1];\n\n  if (componentLen > 1 && typeof finalComponent.value === 'string' && (finalComponent.added || finalComponent.removed) && diff.equals('', finalComponent.value)) {\n    components[componentLen - 2].value += finalComponent.value;\n    components.pop();\n  }\n\n  return components;\n}\n\nvar characterDiff = new Diff();\nfunction diffChars(oldStr, newStr, options) {\n  return characterDiff.diff(oldStr, newStr, options);\n}\n\nfunction generateOptions(options, defaults) {\n  if (typeof options === 'function') {\n    defaults.callback = options;\n  } else if (options) {\n    for (var name in options) {\n      /* istanbul ignore else */\n      if (options.hasOwnProperty(name)) {\n        defaults[name] = options[name];\n      }\n    }\n  }\n\n  return defaults;\n}\n\n//\n// Ranges and exceptions:\n// Latin-1 Supplement, 0080–00FF\n//  - U+00D7  × Multiplication sign\n//  - U+00F7  ÷ Division sign\n// Latin Extended-A, 0100–017F\n// Latin Extended-B, 0180–024F\n// IPA Extensions, 0250–02AF\n// Spacing Modifier Letters, 02B0–02FF\n//  - U+02C7  ˇ &#711;  Caron\n//  - U+02D8  ˘ &#728;  Breve\n//  - U+02D9  ˙ &#729;  Dot Above\n//  - U+02DA  ˚ &#730;  Ring Above\n//  - U+02DB  ˛ &#731;  Ogonek\n//  - U+02DC  ˜ &#732;  Small Tilde\n//  - U+02DD  ˝ &#733;  Double Acute Accent\n// Latin Extended Additional, 1E00–1EFF\n\nvar extendedWordChars = /^[A-Za-z\\xC0-\\u02C6\\u02C8-\\u02D7\\u02DE-\\u02FF\\u1E00-\\u1EFF]+$/;\nvar reWhitespace = /\\S/;\nvar wordDiff = new Diff();\n\nwordDiff.equals = function (left, right) {\n  if (this.options.ignoreCase) {\n    left = left.toLowerCase();\n    right = right.toLowerCase();\n  }\n\n  return left === right || this.options.ignoreWhitespace && !reWhitespace.test(left) && !reWhitespace.test(right);\n};\n\nwordDiff.tokenize = function (value) {\n  // All whitespace symbols except newline group into one token, each newline - in separate token\n  var tokens = value.split(/([^\\S\\r\\n]+|[()[\\]{}'\"\\r\\n]|\\b)/); // Join the boundary splits that we do not consider to be boundaries. This is primarily the extended Latin character set.\n\n  for (var i = 0; i < tokens.length - 1; i++) {\n    // If we have an empty string in the next field and we have only word chars before and after, merge\n    if (!tokens[i + 1] && tokens[i + 2] && extendedWordChars.test(tokens[i]) && extendedWordChars.test(tokens[i + 2])) {\n      tokens[i] += tokens[i + 2];\n      tokens.splice(i + 1, 2);\n      i--;\n    }\n  }\n\n  return tokens;\n};\n\nfunction diffWords(oldStr, newStr, options) {\n  options = generateOptions(options, {\n    ignoreWhitespace: true\n  });\n  return wordDiff.diff(oldStr, newStr, options);\n}\nfunction diffWordsWithSpace(oldStr, newStr, options) {\n  return wordDiff.diff(oldStr, newStr, options);\n}\n\nvar lineDiff = new Diff();\n\nlineDiff.tokenize = function (value) {\n  if (this.options.stripTrailingCr) {\n    // remove one \\r before \\n to match GNU diff's --strip-trailing-cr behavior\n    value = value.replace(/\\r\\n/g, '\\n');\n  }\n\n  var retLines = [],\n      linesAndNewlines = value.split(/(\\n|\\r\\n)/); // Ignore the final empty token that occurs if the string ends with a new line\n\n  if (!linesAndNewlines[linesAndNewlines.length - 1]) {\n    linesAndNewlines.pop();\n  } // Merge the content and line separators into single tokens\n\n\n  for (var i = 0; i < linesAndNewlines.length; i++) {\n    var line = linesAndNewlines[i];\n\n    if (i % 2 && !this.options.newlineIsToken) {\n      retLines[retLines.length - 1] += line;\n    } else {\n      if (this.options.ignoreWhitespace) {\n        line = line.trim();\n      }\n\n      retLines.push(line);\n    }\n  }\n\n  return retLines;\n};\n\nfunction diffLines(oldStr, newStr, callback) {\n  return lineDiff.diff(oldStr, newStr, callback);\n}\nfunction diffTrimmedLines(oldStr, newStr, callback) {\n  var options = generateOptions(callback, {\n    ignoreWhitespace: true\n  });\n  return lineDiff.diff(oldStr, newStr, options);\n}\n\nvar sentenceDiff = new Diff();\n\nsentenceDiff.tokenize = function (value) {\n  return value.split(/(\\S.+?[.!?])(?=\\s+|$)/);\n};\n\nfunction diffSentences(oldStr, newStr, callback) {\n  return sentenceDiff.diff(oldStr, newStr, callback);\n}\n\nvar cssDiff = new Diff();\n\ncssDiff.tokenize = function (value) {\n  return value.split(/([{}:;,]|\\s+)/);\n};\n\nfunction diffCss(oldStr, newStr, callback) {\n  return cssDiff.diff(oldStr, newStr, callback);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar objectPrototypeToString = Object.prototype.toString;\nvar jsonDiff = new Diff(); // Discriminate between two lines of pretty-printed, serialized JSON where one of them has a\n// dangling comma and the other doesn't. Turns out including the dangling comma yields the nicest output:\n\njsonDiff.useLongestToken = true;\njsonDiff.tokenize = lineDiff.tokenize;\n\njsonDiff.castInput = function (value) {\n  var _this$options = this.options,\n      undefinedReplacement = _this$options.undefinedReplacement,\n      _this$options$stringi = _this$options.stringifyReplacer,\n      stringifyReplacer = _this$options$stringi === void 0 ? function (k, v) {\n    return typeof v === 'undefined' ? undefinedReplacement : v;\n  } : _this$options$stringi;\n  return typeof value === 'string' ? value : JSON.stringify(canonicalize(value, null, null, stringifyReplacer), stringifyReplacer, '  ');\n};\n\njsonDiff.equals = function (left, right) {\n  return Diff.prototype.equals.call(jsonDiff, left.replace(/,([\\r\\n])/g, '$1'), right.replace(/,([\\r\\n])/g, '$1'));\n};\n\nfunction diffJson(oldObj, newObj, options) {\n  return jsonDiff.diff(oldObj, newObj, options);\n} // This function handles the presence of circular references by bailing out when encountering an\n// object that is already on the \"stack\" of items being processed. Accepts an optional replacer\n\nfunction canonicalize(obj, stack, replacementStack, replacer, key) {\n  stack = stack || [];\n  replacementStack = replacementStack || [];\n\n  if (replacer) {\n    obj = replacer(key, obj);\n  }\n\n  var i;\n\n  for (i = 0; i < stack.length; i += 1) {\n    if (stack[i] === obj) {\n      return replacementStack[i];\n    }\n  }\n\n  var canonicalizedObj;\n\n  if ('[object Array]' === objectPrototypeToString.call(obj)) {\n    stack.push(obj);\n    canonicalizedObj = new Array(obj.length);\n    replacementStack.push(canonicalizedObj);\n\n    for (i = 0; i < obj.length; i += 1) {\n      canonicalizedObj[i] = canonicalize(obj[i], stack, replacementStack, replacer, key);\n    }\n\n    stack.pop();\n    replacementStack.pop();\n    return canonicalizedObj;\n  }\n\n  if (obj && obj.toJSON) {\n    obj = obj.toJSON();\n  }\n\n  if (_typeof(obj) === 'object' && obj !== null) {\n    stack.push(obj);\n    canonicalizedObj = {};\n    replacementStack.push(canonicalizedObj);\n\n    var sortedKeys = [],\n        _key;\n\n    for (_key in obj) {\n      /* istanbul ignore else */\n      if (obj.hasOwnProperty(_key)) {\n        sortedKeys.push(_key);\n      }\n    }\n\n    sortedKeys.sort();\n\n    for (i = 0; i < sortedKeys.length; i += 1) {\n      _key = sortedKeys[i];\n      canonicalizedObj[_key] = canonicalize(obj[_key], stack, replacementStack, replacer, _key);\n    }\n\n    stack.pop();\n    replacementStack.pop();\n  } else {\n    canonicalizedObj = obj;\n  }\n\n  return canonicalizedObj;\n}\n\nvar arrayDiff = new Diff();\n\narrayDiff.tokenize = function (value) {\n  return value.slice();\n};\n\narrayDiff.join = arrayDiff.removeEmpty = function (value) {\n  return value;\n};\n\nfunction diffArrays(oldArr, newArr, callback) {\n  return arrayDiff.diff(oldArr, newArr, callback);\n}\n\nfunction parsePatch(uniDiff) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var diffstr = uniDiff.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n      delimiters = uniDiff.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n      list = [],\n      i = 0;\n\n  function parseIndex() {\n    var index = {};\n    list.push(index); // Parse diff metadata\n\n    while (i < diffstr.length) {\n      var line = diffstr[i]; // File header found, end parsing diff metadata\n\n      if (/^(\\-\\-\\-|\\+\\+\\+|@@)\\s/.test(line)) {\n        break;\n      } // Diff index\n\n\n      var header = /^(?:Index:|diff(?: -r \\w+)+)\\s+(.+?)\\s*$/.exec(line);\n\n      if (header) {\n        index.index = header[1];\n      }\n\n      i++;\n    } // Parse file headers if they are defined. Unified diff requires them, but\n    // there's no technical issues to have an isolated hunk without file header\n\n\n    parseFileHeader(index);\n    parseFileHeader(index); // Parse hunks\n\n    index.hunks = [];\n\n    while (i < diffstr.length) {\n      var _line = diffstr[i];\n\n      if (/^(Index:|diff|\\-\\-\\-|\\+\\+\\+)\\s/.test(_line)) {\n        break;\n      } else if (/^@@/.test(_line)) {\n        index.hunks.push(parseHunk());\n      } else if (_line && options.strict) {\n        // Ignore unexpected content unless in strict mode\n        throw new Error('Unknown line ' + (i + 1) + ' ' + JSON.stringify(_line));\n      } else {\n        i++;\n      }\n    }\n  } // Parses the --- and +++ headers, if none are found, no lines\n  // are consumed.\n\n\n  function parseFileHeader(index) {\n    var fileHeader = /^(---|\\+\\+\\+)\\s+(.*)$/.exec(diffstr[i]);\n\n    if (fileHeader) {\n      var keyPrefix = fileHeader[1] === '---' ? 'old' : 'new';\n      var data = fileHeader[2].split('\\t', 2);\n      var fileName = data[0].replace(/\\\\\\\\/g, '\\\\');\n\n      if (/^\".*\"$/.test(fileName)) {\n        fileName = fileName.substr(1, fileName.length - 2);\n      }\n\n      index[keyPrefix + 'FileName'] = fileName;\n      index[keyPrefix + 'Header'] = (data[1] || '').trim();\n      i++;\n    }\n  } // Parses a hunk\n  // This assumes that we are at the start of a hunk.\n\n\n  function parseHunk() {\n    var chunkHeaderIndex = i,\n        chunkHeaderLine = diffstr[i++],\n        chunkHeader = chunkHeaderLine.split(/@@ -(\\d+)(?:,(\\d+))? \\+(\\d+)(?:,(\\d+))? @@/);\n    var hunk = {\n      oldStart: +chunkHeader[1],\n      oldLines: typeof chunkHeader[2] === 'undefined' ? 1 : +chunkHeader[2],\n      newStart: +chunkHeader[3],\n      newLines: typeof chunkHeader[4] === 'undefined' ? 1 : +chunkHeader[4],\n      lines: [],\n      linedelimiters: []\n    }; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart += 1;\n    }\n\n    if (hunk.newLines === 0) {\n      hunk.newStart += 1;\n    }\n\n    var addCount = 0,\n        removeCount = 0;\n\n    for (; i < diffstr.length; i++) {\n      // Lines starting with '---' could be mistaken for the \"remove line\" operation\n      // But they could be the header for the next file. Therefore prune such cases out.\n      if (diffstr[i].indexOf('--- ') === 0 && i + 2 < diffstr.length && diffstr[i + 1].indexOf('+++ ') === 0 && diffstr[i + 2].indexOf('@@') === 0) {\n        break;\n      }\n\n      var operation = diffstr[i].length == 0 && i != diffstr.length - 1 ? ' ' : diffstr[i][0];\n\n      if (operation === '+' || operation === '-' || operation === ' ' || operation === '\\\\') {\n        hunk.lines.push(diffstr[i]);\n        hunk.linedelimiters.push(delimiters[i] || '\\n');\n\n        if (operation === '+') {\n          addCount++;\n        } else if (operation === '-') {\n          removeCount++;\n        } else if (operation === ' ') {\n          addCount++;\n          removeCount++;\n        }\n      } else {\n        break;\n      }\n    } // Handle the empty block count case\n\n\n    if (!addCount && hunk.newLines === 1) {\n      hunk.newLines = 0;\n    }\n\n    if (!removeCount && hunk.oldLines === 1) {\n      hunk.oldLines = 0;\n    } // Perform optional sanity checking\n\n\n    if (options.strict) {\n      if (addCount !== hunk.newLines) {\n        throw new Error('Added line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n\n      if (removeCount !== hunk.oldLines) {\n        throw new Error('Removed line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n    }\n\n    return hunk;\n  }\n\n  while (i < diffstr.length) {\n    parseIndex();\n  }\n\n  return list;\n}\n\n// Iterator that traverses in the range of [min, max], stepping\n// by distance from a given start position. I.e. for [0, 4], with\n// start of 2, this will iterate 2, 3, 1, 4, 0.\nfunction distanceIterator (start, minLine, maxLine) {\n  var wantForward = true,\n      backwardExhausted = false,\n      forwardExhausted = false,\n      localOffset = 1;\n  return function iterator() {\n    if (wantForward && !forwardExhausted) {\n      if (backwardExhausted) {\n        localOffset++;\n      } else {\n        wantForward = false;\n      } // Check if trying to fit beyond text length, and if not, check it fits\n      // after offset location (or desired location on first iteration)\n\n\n      if (start + localOffset <= maxLine) {\n        return localOffset;\n      }\n\n      forwardExhausted = true;\n    }\n\n    if (!backwardExhausted) {\n      if (!forwardExhausted) {\n        wantForward = true;\n      } // Check if trying to fit before text beginning, and if not, check it fits\n      // before offset location\n\n\n      if (minLine <= start - localOffset) {\n        return -localOffset++;\n      }\n\n      backwardExhausted = true;\n      return iterator();\n    } // We tried to fit hunk before text beginning and beyond text length, then\n    // hunk can't fit on the text. Return undefined\n\n  };\n}\n\nfunction applyPatch(source, uniDiff) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n\n  if (Array.isArray(uniDiff)) {\n    if (uniDiff.length > 1) {\n      throw new Error('applyPatch only works with a single input.');\n    }\n\n    uniDiff = uniDiff[0];\n  } // Apply the diff to the input\n\n\n  var lines = source.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n      delimiters = source.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n      hunks = uniDiff.hunks,\n      compareLine = options.compareLine || function (lineNumber, line, operation, patchContent) {\n    return line === patchContent;\n  },\n      errorCount = 0,\n      fuzzFactor = options.fuzzFactor || 0,\n      minLine = 0,\n      offset = 0,\n      removeEOFNL,\n      addEOFNL;\n  /**\n   * Checks if the hunk exactly fits on the provided location\n   */\n\n\n  function hunkFits(hunk, toPos) {\n    for (var j = 0; j < hunk.lines.length; j++) {\n      var line = hunk.lines[j],\n          operation = line.length > 0 ? line[0] : ' ',\n          content = line.length > 0 ? line.substr(1) : line;\n\n      if (operation === ' ' || operation === '-') {\n        // Context sanity check\n        if (!compareLine(toPos + 1, lines[toPos], operation, content)) {\n          errorCount++;\n\n          if (errorCount > fuzzFactor) {\n            return false;\n          }\n        }\n\n        toPos++;\n      }\n    }\n\n    return true;\n  } // Search best fit offsets for each hunk based on the previous ones\n\n\n  for (var i = 0; i < hunks.length; i++) {\n    var hunk = hunks[i],\n        maxLine = lines.length - hunk.oldLines,\n        localOffset = 0,\n        toPos = offset + hunk.oldStart - 1;\n    var iterator = distanceIterator(toPos, minLine, maxLine);\n\n    for (; localOffset !== undefined; localOffset = iterator()) {\n      if (hunkFits(hunk, toPos + localOffset)) {\n        hunk.offset = offset += localOffset;\n        break;\n      }\n    }\n\n    if (localOffset === undefined) {\n      return false;\n    } // Set lower text limit to end of the current hunk, so next ones don't try\n    // to fit over already patched text\n\n\n    minLine = hunk.offset + hunk.oldStart + hunk.oldLines;\n  } // Apply patch hunks\n\n\n  var diffOffset = 0;\n\n  for (var _i = 0; _i < hunks.length; _i++) {\n    var _hunk = hunks[_i],\n        _toPos = _hunk.oldStart + _hunk.offset + diffOffset - 1;\n\n    diffOffset += _hunk.newLines - _hunk.oldLines;\n\n    for (var j = 0; j < _hunk.lines.length; j++) {\n      var line = _hunk.lines[j],\n          operation = line.length > 0 ? line[0] : ' ',\n          content = line.length > 0 ? line.substr(1) : line,\n          delimiter = _hunk.linedelimiters && _hunk.linedelimiters[j] || '\\n';\n\n      if (operation === ' ') {\n        _toPos++;\n      } else if (operation === '-') {\n        lines.splice(_toPos, 1);\n        delimiters.splice(_toPos, 1);\n        /* istanbul ignore else */\n      } else if (operation === '+') {\n        lines.splice(_toPos, 0, content);\n        delimiters.splice(_toPos, 0, delimiter);\n        _toPos++;\n      } else if (operation === '\\\\') {\n        var previousOperation = _hunk.lines[j - 1] ? _hunk.lines[j - 1][0] : null;\n\n        if (previousOperation === '+') {\n          removeEOFNL = true;\n        } else if (previousOperation === '-') {\n          addEOFNL = true;\n        }\n      }\n    }\n  } // Handle EOFNL insertion/removal\n\n\n  if (removeEOFNL) {\n    while (!lines[lines.length - 1]) {\n      lines.pop();\n      delimiters.pop();\n    }\n  } else if (addEOFNL) {\n    lines.push('');\n    delimiters.push('\\n');\n  }\n\n  for (var _k = 0; _k < lines.length - 1; _k++) {\n    lines[_k] = lines[_k] + delimiters[_k];\n  }\n\n  return lines.join('');\n} // Wrapper that supports multiple file patches via callbacks.\n\nfunction applyPatches(uniDiff, options) {\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n\n  var currentIndex = 0;\n\n  function processIndex() {\n    var index = uniDiff[currentIndex++];\n\n    if (!index) {\n      return options.complete();\n    }\n\n    options.loadFile(index, function (err, data) {\n      if (err) {\n        return options.complete(err);\n      }\n\n      var updatedContent = applyPatch(data, index, options);\n      options.patched(index, updatedContent, function (err) {\n        if (err) {\n          return options.complete(err);\n        }\n\n        processIndex();\n      });\n    });\n  }\n\n  processIndex();\n}\n\nfunction structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  if (!options) {\n    options = {};\n  }\n\n  if (typeof options.context === 'undefined') {\n    options.context = 4;\n  }\n\n  var diff = diffLines(oldStr, newStr, options);\n\n  if (!diff) {\n    return;\n  }\n\n  diff.push({\n    value: '',\n    lines: []\n  }); // Append an empty value to make cleanup easier\n\n  function contextLines(lines) {\n    return lines.map(function (entry) {\n      return ' ' + entry;\n    });\n  }\n\n  var hunks = [];\n  var oldRangeStart = 0,\n      newRangeStart = 0,\n      curRange = [],\n      oldLine = 1,\n      newLine = 1;\n\n  var _loop = function _loop(i) {\n    var current = diff[i],\n        lines = current.lines || current.value.replace(/\\n$/, '').split('\\n');\n    current.lines = lines;\n\n    if (current.added || current.removed) {\n      var _curRange;\n\n      // If we have previous context, start with that\n      if (!oldRangeStart) {\n        var prev = diff[i - 1];\n        oldRangeStart = oldLine;\n        newRangeStart = newLine;\n\n        if (prev) {\n          curRange = options.context > 0 ? contextLines(prev.lines.slice(-options.context)) : [];\n          oldRangeStart -= curRange.length;\n          newRangeStart -= curRange.length;\n        }\n      } // Output our changes\n\n\n      (_curRange = curRange).push.apply(_curRange, _toConsumableArray(lines.map(function (entry) {\n        return (current.added ? '+' : '-') + entry;\n      }))); // Track the updated file position\n\n\n      if (current.added) {\n        newLine += lines.length;\n      } else {\n        oldLine += lines.length;\n      }\n    } else {\n      // Identical context lines. Track line changes\n      if (oldRangeStart) {\n        // Close out any changes that have been output (or join overlapping)\n        if (lines.length <= options.context * 2 && i < diff.length - 2) {\n          var _curRange2;\n\n          // Overlapping\n          (_curRange2 = curRange).push.apply(_curRange2, _toConsumableArray(contextLines(lines)));\n        } else {\n          var _curRange3;\n\n          // end the range and output\n          var contextSize = Math.min(lines.length, options.context);\n\n          (_curRange3 = curRange).push.apply(_curRange3, _toConsumableArray(contextLines(lines.slice(0, contextSize))));\n\n          var hunk = {\n            oldStart: oldRangeStart,\n            oldLines: oldLine - oldRangeStart + contextSize,\n            newStart: newRangeStart,\n            newLines: newLine - newRangeStart + contextSize,\n            lines: curRange\n          };\n\n          if (i >= diff.length - 2 && lines.length <= options.context) {\n            // EOF is inside this hunk\n            var oldEOFNewline = /\\n$/.test(oldStr);\n            var newEOFNewline = /\\n$/.test(newStr);\n            var noNlBeforeAdds = lines.length == 0 && curRange.length > hunk.oldLines;\n\n            if (!oldEOFNewline && noNlBeforeAdds && oldStr.length > 0) {\n              // special case: old has no eol and no trailing context; no-nl can end up before adds\n              // however, if the old file is empty, do not output the no-nl line\n              curRange.splice(hunk.oldLines, 0, '\\\\ No newline at end of file');\n            }\n\n            if (!oldEOFNewline && !noNlBeforeAdds || !newEOFNewline) {\n              curRange.push('\\\\ No newline at end of file');\n            }\n          }\n\n          hunks.push(hunk);\n          oldRangeStart = 0;\n          newRangeStart = 0;\n          curRange = [];\n        }\n      }\n\n      oldLine += lines.length;\n      newLine += lines.length;\n    }\n  };\n\n  for (var i = 0; i < diff.length; i++) {\n    _loop(i);\n  }\n\n  return {\n    oldFileName: oldFileName,\n    newFileName: newFileName,\n    oldHeader: oldHeader,\n    newHeader: newHeader,\n    hunks: hunks\n  };\n}\nfunction formatPatch(diff) {\n  if (Array.isArray(diff)) {\n    return diff.map(formatPatch).join('\\n');\n  }\n\n  var ret = [];\n\n  if (diff.oldFileName == diff.newFileName) {\n    ret.push('Index: ' + diff.oldFileName);\n  }\n\n  ret.push('===================================================================');\n  ret.push('--- ' + diff.oldFileName + (typeof diff.oldHeader === 'undefined' ? '' : '\\t' + diff.oldHeader));\n  ret.push('+++ ' + diff.newFileName + (typeof diff.newHeader === 'undefined' ? '' : '\\t' + diff.newHeader));\n\n  for (var i = 0; i < diff.hunks.length; i++) {\n    var hunk = diff.hunks[i]; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart -= 1;\n    }\n\n    if (hunk.newLines === 0) {\n      hunk.newStart -= 1;\n    }\n\n    ret.push('@@ -' + hunk.oldStart + ',' + hunk.oldLines + ' +' + hunk.newStart + ',' + hunk.newLines + ' @@');\n    ret.push.apply(ret, hunk.lines);\n  }\n\n  return ret.join('\\n') + '\\n';\n}\nfunction createTwoFilesPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return formatPatch(structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options));\n}\nfunction createPatch(fileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return createTwoFilesPatch(fileName, fileName, oldStr, newStr, oldHeader, newHeader, options);\n}\n\nfunction arrayEqual(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  return arrayStartsWith(a, b);\n}\nfunction arrayStartsWith(array, start) {\n  if (start.length > array.length) {\n    return false;\n  }\n\n  for (var i = 0; i < start.length; i++) {\n    if (start[i] !== array[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction calcLineCount(hunk) {\n  var _calcOldNewLineCount = calcOldNewLineCount(hunk.lines),\n      oldLines = _calcOldNewLineCount.oldLines,\n      newLines = _calcOldNewLineCount.newLines;\n\n  if (oldLines !== undefined) {\n    hunk.oldLines = oldLines;\n  } else {\n    delete hunk.oldLines;\n  }\n\n  if (newLines !== undefined) {\n    hunk.newLines = newLines;\n  } else {\n    delete hunk.newLines;\n  }\n}\nfunction merge(mine, theirs, base) {\n  mine = loadPatch(mine, base);\n  theirs = loadPatch(theirs, base);\n  var ret = {}; // For index we just let it pass through as it doesn't have any necessary meaning.\n  // Leaving sanity checks on this to the API consumer that may know more about the\n  // meaning in their own context.\n\n  if (mine.index || theirs.index) {\n    ret.index = mine.index || theirs.index;\n  }\n\n  if (mine.newFileName || theirs.newFileName) {\n    if (!fileNameChanged(mine)) {\n      // No header or no change in ours, use theirs (and ours if theirs does not exist)\n      ret.oldFileName = theirs.oldFileName || mine.oldFileName;\n      ret.newFileName = theirs.newFileName || mine.newFileName;\n      ret.oldHeader = theirs.oldHeader || mine.oldHeader;\n      ret.newHeader = theirs.newHeader || mine.newHeader;\n    } else if (!fileNameChanged(theirs)) {\n      // No header or no change in theirs, use ours\n      ret.oldFileName = mine.oldFileName;\n      ret.newFileName = mine.newFileName;\n      ret.oldHeader = mine.oldHeader;\n      ret.newHeader = mine.newHeader;\n    } else {\n      // Both changed... figure it out\n      ret.oldFileName = selectField(ret, mine.oldFileName, theirs.oldFileName);\n      ret.newFileName = selectField(ret, mine.newFileName, theirs.newFileName);\n      ret.oldHeader = selectField(ret, mine.oldHeader, theirs.oldHeader);\n      ret.newHeader = selectField(ret, mine.newHeader, theirs.newHeader);\n    }\n  }\n\n  ret.hunks = [];\n  var mineIndex = 0,\n      theirsIndex = 0,\n      mineOffset = 0,\n      theirsOffset = 0;\n\n  while (mineIndex < mine.hunks.length || theirsIndex < theirs.hunks.length) {\n    var mineCurrent = mine.hunks[mineIndex] || {\n      oldStart: Infinity\n    },\n        theirsCurrent = theirs.hunks[theirsIndex] || {\n      oldStart: Infinity\n    };\n\n    if (hunkBefore(mineCurrent, theirsCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(mineCurrent, mineOffset));\n      mineIndex++;\n      theirsOffset += mineCurrent.newLines - mineCurrent.oldLines;\n    } else if (hunkBefore(theirsCurrent, mineCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(theirsCurrent, theirsOffset));\n      theirsIndex++;\n      mineOffset += theirsCurrent.newLines - theirsCurrent.oldLines;\n    } else {\n      // Overlap, merge as best we can\n      var mergedHunk = {\n        oldStart: Math.min(mineCurrent.oldStart, theirsCurrent.oldStart),\n        oldLines: 0,\n        newStart: Math.min(mineCurrent.newStart + mineOffset, theirsCurrent.oldStart + theirsOffset),\n        newLines: 0,\n        lines: []\n      };\n      mergeLines(mergedHunk, mineCurrent.oldStart, mineCurrent.lines, theirsCurrent.oldStart, theirsCurrent.lines);\n      theirsIndex++;\n      mineIndex++;\n      ret.hunks.push(mergedHunk);\n    }\n  }\n\n  return ret;\n}\n\nfunction loadPatch(param, base) {\n  if (typeof param === 'string') {\n    if (/^@@/m.test(param) || /^Index:/m.test(param)) {\n      return parsePatch(param)[0];\n    }\n\n    if (!base) {\n      throw new Error('Must provide a base reference or pass in a patch');\n    }\n\n    return structuredPatch(undefined, undefined, base, param);\n  }\n\n  return param;\n}\n\nfunction fileNameChanged(patch) {\n  return patch.newFileName && patch.newFileName !== patch.oldFileName;\n}\n\nfunction selectField(index, mine, theirs) {\n  if (mine === theirs) {\n    return mine;\n  } else {\n    index.conflict = true;\n    return {\n      mine: mine,\n      theirs: theirs\n    };\n  }\n}\n\nfunction hunkBefore(test, check) {\n  return test.oldStart < check.oldStart && test.oldStart + test.oldLines < check.oldStart;\n}\n\nfunction cloneHunk(hunk, offset) {\n  return {\n    oldStart: hunk.oldStart,\n    oldLines: hunk.oldLines,\n    newStart: hunk.newStart + offset,\n    newLines: hunk.newLines,\n    lines: hunk.lines\n  };\n}\n\nfunction mergeLines(hunk, mineOffset, mineLines, theirOffset, theirLines) {\n  // This will generally result in a conflicted hunk, but there are cases where the context\n  // is the only overlap where we can successfully merge the content here.\n  var mine = {\n    offset: mineOffset,\n    lines: mineLines,\n    index: 0\n  },\n      their = {\n    offset: theirOffset,\n    lines: theirLines,\n    index: 0\n  }; // Handle any leading content\n\n  insertLeading(hunk, mine, their);\n  insertLeading(hunk, their, mine); // Now in the overlap content. Scan through and select the best changes from each.\n\n  while (mine.index < mine.lines.length && their.index < their.lines.length) {\n    var mineCurrent = mine.lines[mine.index],\n        theirCurrent = their.lines[their.index];\n\n    if ((mineCurrent[0] === '-' || mineCurrent[0] === '+') && (theirCurrent[0] === '-' || theirCurrent[0] === '+')) {\n      // Both modified ...\n      mutualChange(hunk, mine, their);\n    } else if (mineCurrent[0] === '+' && theirCurrent[0] === ' ') {\n      var _hunk$lines;\n\n      // Mine inserted\n      (_hunk$lines = hunk.lines).push.apply(_hunk$lines, _toConsumableArray(collectChange(mine)));\n    } else if (theirCurrent[0] === '+' && mineCurrent[0] === ' ') {\n      var _hunk$lines2;\n\n      // Theirs inserted\n      (_hunk$lines2 = hunk.lines).push.apply(_hunk$lines2, _toConsumableArray(collectChange(their)));\n    } else if (mineCurrent[0] === '-' && theirCurrent[0] === ' ') {\n      // Mine removed or edited\n      removal(hunk, mine, their);\n    } else if (theirCurrent[0] === '-' && mineCurrent[0] === ' ') {\n      // Their removed or edited\n      removal(hunk, their, mine, true);\n    } else if (mineCurrent === theirCurrent) {\n      // Context identity\n      hunk.lines.push(mineCurrent);\n      mine.index++;\n      their.index++;\n    } else {\n      // Context mismatch\n      conflict(hunk, collectChange(mine), collectChange(their));\n    }\n  } // Now push anything that may be remaining\n\n\n  insertTrailing(hunk, mine);\n  insertTrailing(hunk, their);\n  calcLineCount(hunk);\n}\n\nfunction mutualChange(hunk, mine, their) {\n  var myChanges = collectChange(mine),\n      theirChanges = collectChange(their);\n\n  if (allRemoves(myChanges) && allRemoves(theirChanges)) {\n    // Special case for remove changes that are supersets of one another\n    if (arrayStartsWith(myChanges, theirChanges) && skipRemoveSuperset(their, myChanges, myChanges.length - theirChanges.length)) {\n      var _hunk$lines3;\n\n      (_hunk$lines3 = hunk.lines).push.apply(_hunk$lines3, _toConsumableArray(myChanges));\n\n      return;\n    } else if (arrayStartsWith(theirChanges, myChanges) && skipRemoveSuperset(mine, theirChanges, theirChanges.length - myChanges.length)) {\n      var _hunk$lines4;\n\n      (_hunk$lines4 = hunk.lines).push.apply(_hunk$lines4, _toConsumableArray(theirChanges));\n\n      return;\n    }\n  } else if (arrayEqual(myChanges, theirChanges)) {\n    var _hunk$lines5;\n\n    (_hunk$lines5 = hunk.lines).push.apply(_hunk$lines5, _toConsumableArray(myChanges));\n\n    return;\n  }\n\n  conflict(hunk, myChanges, theirChanges);\n}\n\nfunction removal(hunk, mine, their, swap) {\n  var myChanges = collectChange(mine),\n      theirChanges = collectContext(their, myChanges);\n\n  if (theirChanges.merged) {\n    var _hunk$lines6;\n\n    (_hunk$lines6 = hunk.lines).push.apply(_hunk$lines6, _toConsumableArray(theirChanges.merged));\n  } else {\n    conflict(hunk, swap ? theirChanges : myChanges, swap ? myChanges : theirChanges);\n  }\n}\n\nfunction conflict(hunk, mine, their) {\n  hunk.conflict = true;\n  hunk.lines.push({\n    conflict: true,\n    mine: mine,\n    theirs: their\n  });\n}\n\nfunction insertLeading(hunk, insert, their) {\n  while (insert.offset < their.offset && insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n    insert.offset++;\n  }\n}\n\nfunction insertTrailing(hunk, insert) {\n  while (insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n  }\n}\n\nfunction collectChange(state) {\n  var ret = [],\n      operation = state.lines[state.index][0];\n\n  while (state.index < state.lines.length) {\n    var line = state.lines[state.index]; // Group additions that are immediately after subtractions and treat them as one \"atomic\" modify change.\n\n    if (operation === '-' && line[0] === '+') {\n      operation = '+';\n    }\n\n    if (operation === line[0]) {\n      ret.push(line);\n      state.index++;\n    } else {\n      break;\n    }\n  }\n\n  return ret;\n}\n\nfunction collectContext(state, matchChanges) {\n  var changes = [],\n      merged = [],\n      matchIndex = 0,\n      contextChanges = false,\n      conflicted = false;\n\n  while (matchIndex < matchChanges.length && state.index < state.lines.length) {\n    var change = state.lines[state.index],\n        match = matchChanges[matchIndex]; // Once we've hit our add, then we are done\n\n    if (match[0] === '+') {\n      break;\n    }\n\n    contextChanges = contextChanges || change[0] !== ' ';\n    merged.push(match);\n    matchIndex++; // Consume any additions in the other block as a conflict to attempt\n    // to pull in the remaining context after this\n\n    if (change[0] === '+') {\n      conflicted = true;\n\n      while (change[0] === '+') {\n        changes.push(change);\n        change = state.lines[++state.index];\n      }\n    }\n\n    if (match.substr(1) === change.substr(1)) {\n      changes.push(change);\n      state.index++;\n    } else {\n      conflicted = true;\n    }\n  }\n\n  if ((matchChanges[matchIndex] || '')[0] === '+' && contextChanges) {\n    conflicted = true;\n  }\n\n  if (conflicted) {\n    return changes;\n  }\n\n  while (matchIndex < matchChanges.length) {\n    merged.push(matchChanges[matchIndex++]);\n  }\n\n  return {\n    merged: merged,\n    changes: changes\n  };\n}\n\nfunction allRemoves(changes) {\n  return changes.reduce(function (prev, change) {\n    return prev && change[0] === '-';\n  }, true);\n}\n\nfunction skipRemoveSuperset(state, removeChanges, delta) {\n  for (var i = 0; i < delta; i++) {\n    var changeContent = removeChanges[removeChanges.length - delta + i].substr(1);\n\n    if (state.lines[state.index + i] !== ' ' + changeContent) {\n      return false;\n    }\n  }\n\n  state.index += delta;\n  return true;\n}\n\nfunction calcOldNewLineCount(lines) {\n  var oldLines = 0;\n  var newLines = 0;\n  lines.forEach(function (line) {\n    if (typeof line !== 'string') {\n      var myCount = calcOldNewLineCount(line.mine);\n      var theirCount = calcOldNewLineCount(line.theirs);\n\n      if (oldLines !== undefined) {\n        if (myCount.oldLines === theirCount.oldLines) {\n          oldLines += myCount.oldLines;\n        } else {\n          oldLines = undefined;\n        }\n      }\n\n      if (newLines !== undefined) {\n        if (myCount.newLines === theirCount.newLines) {\n          newLines += myCount.newLines;\n        } else {\n          newLines = undefined;\n        }\n      }\n    } else {\n      if (newLines !== undefined && (line[0] === '+' || line[0] === ' ')) {\n        newLines++;\n      }\n\n      if (oldLines !== undefined && (line[0] === '-' || line[0] === ' ')) {\n        oldLines++;\n      }\n    }\n  });\n  return {\n    oldLines: oldLines,\n    newLines: newLines\n  };\n}\n\nfunction reversePatch(structuredPatch) {\n  if (Array.isArray(structuredPatch)) {\n    return structuredPatch.map(reversePatch).reverse();\n  }\n\n  return _objectSpread2(_objectSpread2({}, structuredPatch), {}, {\n    oldFileName: structuredPatch.newFileName,\n    oldHeader: structuredPatch.newHeader,\n    newFileName: structuredPatch.oldFileName,\n    newHeader: structuredPatch.oldHeader,\n    hunks: structuredPatch.hunks.map(function (hunk) {\n      return {\n        oldLines: hunk.newLines,\n        oldStart: hunk.newStart,\n        newLines: hunk.oldLines,\n        newStart: hunk.oldStart,\n        linedelimiters: hunk.linedelimiters,\n        lines: hunk.lines.map(function (l) {\n          if (l.startsWith('-')) {\n            return \"+\".concat(l.slice(1));\n          }\n\n          if (l.startsWith('+')) {\n            return \"-\".concat(l.slice(1));\n          }\n\n          return l;\n        })\n      };\n    })\n  });\n}\n\n// See: http://code.google.com/p/google-diff-match-patch/wiki/API\nfunction convertChangesToDMP(changes) {\n  var ret = [],\n      change,\n      operation;\n\n  for (var i = 0; i < changes.length; i++) {\n    change = changes[i];\n\n    if (change.added) {\n      operation = 1;\n    } else if (change.removed) {\n      operation = -1;\n    } else {\n      operation = 0;\n    }\n\n    ret.push([operation, change.value]);\n  }\n\n  return ret;\n}\n\nfunction convertChangesToXML(changes) {\n  var ret = [];\n\n  for (var i = 0; i < changes.length; i++) {\n    var change = changes[i];\n\n    if (change.added) {\n      ret.push('<ins>');\n    } else if (change.removed) {\n      ret.push('<del>');\n    }\n\n    ret.push(escapeHTML(change.value));\n\n    if (change.added) {\n      ret.push('</ins>');\n    } else if (change.removed) {\n      ret.push('</del>');\n    }\n  }\n\n  return ret.join('');\n}\n\nfunction escapeHTML(s) {\n  var n = s;\n  n = n.replace(/&/g, '&amp;');\n  n = n.replace(/</g, '&lt;');\n  n = n.replace(/>/g, '&gt;');\n  n = n.replace(/\"/g, '&quot;');\n  return n;\n}\n\nexport { Diff, applyPatch, applyPatches, canonicalize, convertChangesToDMP, convertChangesToXML, createPatch, createTwoFilesPatch, diffArrays, diffChars, diffCss, diffJson, diffLines, diffSentences, diffTrimmedLines, diffWords, diffWordsWithSpace, formatPatch, merge, parsePatch, reversePatch, structuredPatch };\n"], "mappings": ";;;AAAA,SAAS,OAAO;AAAC;AACjB,KAAK,YAAY;AAAA,EACf,MAAM,SAAS,KAAK,WAAW,WAAW;AACxC,QAAI;AAEJ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,WAAW,QAAQ;AAEvB,QAAI,OAAO,YAAY,YAAY;AACjC,iBAAW;AACX,gBAAU,CAAC;AAAA,IACb;AAEA,SAAK,UAAU;AACf,QAAI,OAAO;AAEX,aAAS,KAAK,OAAO;AACnB,UAAI,UAAU;AACZ,mBAAW,WAAY;AACrB,mBAAS,QAAW,KAAK;AAAA,QAC3B,GAAG,CAAC;AACJ,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAGA,gBAAY,KAAK,UAAU,SAAS;AACpC,gBAAY,KAAK,UAAU,SAAS;AACpC,gBAAY,KAAK,YAAY,KAAK,SAAS,SAAS,CAAC;AACrD,gBAAY,KAAK,YAAY,KAAK,SAAS,SAAS,CAAC;AACrD,QAAI,SAAS,UAAU,QACnB,SAAS,UAAU;AACvB,QAAI,aAAa;AACjB,QAAI,gBAAgB,SAAS;AAE7B,QAAI,QAAQ,eAAe;AACzB,sBAAgB,KAAK,IAAI,eAAe,QAAQ,aAAa;AAAA,IAC/D;AAEA,QAAI,oBAAoB,mBAAmB,QAAQ,aAAa,QAAQ,qBAAqB,SAAS,mBAAmB;AACzH,QAAI,sBAAsB,KAAK,IAAI,IAAI;AACvC,QAAI,WAAW,CAAC;AAAA,MACd,QAAQ;AAAA,MACR,eAAe;AAAA,IACjB,CAAC;AAED,QAAI,SAAS,KAAK,cAAc,SAAS,CAAC,GAAG,WAAW,WAAW,CAAC;AAEpE,QAAI,SAAS,CAAC,EAAE,SAAS,KAAK,UAAU,SAAS,KAAK,QAAQ;AAE5D,aAAO,KAAK,CAAC;AAAA,QACX,OAAO,KAAK,KAAK,SAAS;AAAA,QAC1B,OAAO,UAAU;AAAA,MACnB,CAAC,CAAC;AAAA,IACJ;AAmBA,QAAI,wBAAwB,WACxB,wBAAwB;AAE5B,aAAS,iBAAiB;AACxB,eAAS,eAAe,KAAK,IAAI,uBAAuB,CAAC,UAAU,GAAG,gBAAgB,KAAK,IAAI,uBAAuB,UAAU,GAAG,gBAAgB,GAAG;AACpJ,YAAI,WAAW;AACf,YAAI,aAAa,SAAS,eAAe,CAAC,GACtC,UAAU,SAAS,eAAe,CAAC;AAEvC,YAAI,YAAY;AAEd,mBAAS,eAAe,CAAC,IAAI;AAAA,QAC/B;AAEA,YAAI,SAAS;AAEb,YAAI,SAAS;AAEX,cAAI,gBAAgB,QAAQ,SAAS;AACrC,mBAAS,WAAW,KAAK,iBAAiB,gBAAgB;AAAA,QAC5D;AAEA,YAAI,YAAY,cAAc,WAAW,SAAS,IAAI;AAEtD,YAAI,CAAC,UAAU,CAAC,WAAW;AAEzB,mBAAS,YAAY,IAAI;AACzB;AAAA,QACF;AAOA,YAAI,CAAC,aAAa,UAAU,WAAW,SAAS,IAAI,QAAQ,QAAQ;AAClE,qBAAW,KAAK,UAAU,SAAS,MAAM,QAAW,CAAC;AAAA,QACvD,OAAO;AACL,qBAAW,KAAK,UAAU,YAAY,QAAW,MAAM,CAAC;AAAA,QAC1D;AAEA,iBAAS,KAAK,cAAc,UAAU,WAAW,WAAW,YAAY;AAExE,YAAI,SAAS,SAAS,KAAK,UAAU,SAAS,KAAK,QAAQ;AAEzD,iBAAO,KAAK,YAAY,MAAM,SAAS,eAAe,WAAW,WAAW,KAAK,eAAe,CAAC;AAAA,QACnG,OAAO;AACL,mBAAS,YAAY,IAAI;AAEzB,cAAI,SAAS,SAAS,KAAK,QAAQ;AACjC,oCAAwB,KAAK,IAAI,uBAAuB,eAAe,CAAC;AAAA,UAC1E;AAEA,cAAI,SAAS,KAAK,QAAQ;AACxB,oCAAwB,KAAK,IAAI,uBAAuB,eAAe,CAAC;AAAA,UAC1E;AAAA,QACF;AAAA,MACF;AAEA;AAAA,IACF;AAMA,QAAI,UAAU;AACZ,OAAC,SAAS,OAAO;AACf,mBAAW,WAAY;AACrB,cAAI,aAAa,iBAAiB,KAAK,IAAI,IAAI,qBAAqB;AAClE,mBAAO,SAAS;AAAA,UAClB;AAEA,cAAI,CAAC,eAAe,GAAG;AACrB,iBAAK;AAAA,UACP;AAAA,QACF,GAAG,CAAC;AAAA,MACN,GAAG;AAAA,IACL,OAAO;AACL,aAAO,cAAc,iBAAiB,KAAK,IAAI,KAAK,qBAAqB;AACvE,YAAI,MAAM,eAAe;AAEzB,YAAI,KAAK;AACP,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,SAAS,UAAU,MAAM,OAAO,SAAS,WAAW;AAC7D,QAAI,OAAO,KAAK;AAEhB,QAAI,QAAQ,KAAK,UAAU,SAAS,KAAK,YAAY,SAAS;AAC5D,aAAO;AAAA,QACL,QAAQ,KAAK,SAAS;AAAA,QACtB,eAAe;AAAA,UACb,OAAO,KAAK,QAAQ;AAAA,UACpB;AAAA,UACA;AAAA,UACA,mBAAmB,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,QAAQ,KAAK,SAAS;AAAA,QACtB,eAAe;AAAA,UACb,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,SAAS,cAAc,UAAU,WAAW,WAAW,cAAc;AAClF,QAAI,SAAS,UAAU,QACnB,SAAS,UAAU,QACnB,SAAS,SAAS,QAClB,SAAS,SAAS,cAClB,cAAc;AAElB,WAAO,SAAS,IAAI,UAAU,SAAS,IAAI,UAAU,KAAK,OAAO,UAAU,SAAS,CAAC,GAAG,UAAU,SAAS,CAAC,CAAC,GAAG;AAC9G;AACA;AACA;AAAA,IACF;AAEA,QAAI,aAAa;AACf,eAAS,gBAAgB;AAAA,QACvB,OAAO;AAAA,QACP,mBAAmB,SAAS;AAAA,MAC9B;AAAA,IACF;AAEA,aAAS,SAAS;AAClB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS,OAAO,MAAM,OAAO;AACnC,QAAI,KAAK,QAAQ,YAAY;AAC3B,aAAO,KAAK,QAAQ,WAAW,MAAM,KAAK;AAAA,IAC5C,OAAO;AACL,aAAO,SAAS,SAAS,KAAK,QAAQ,cAAc,KAAK,YAAY,MAAM,MAAM,YAAY;AAAA,IAC/F;AAAA,EACF;AAAA,EACA,aAAa,SAAS,YAAY,OAAO;AACvC,QAAI,MAAM,CAAC;AAEX,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,MAAM,CAAC,GAAG;AACZ,YAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MACnB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,SAAS,UAAU,OAAO;AACnC,WAAO;AAAA,EACT;AAAA,EACA,UAAU,SAAS,SAAS,OAAO;AACjC,WAAO,MAAM,MAAM,EAAE;AAAA,EACvB;AAAA,EACA,MAAM,SAAS,KAAK,OAAO;AACzB,WAAO,MAAM,KAAK,EAAE;AAAA,EACtB;AACF;AAEA,SAAS,YAAYA,OAAM,eAAe,WAAW,WAAW,iBAAiB;AAG/E,MAAI,aAAa,CAAC;AAClB,MAAI;AAEJ,SAAO,eAAe;AACpB,eAAW,KAAK,aAAa;AAC7B,oBAAgB,cAAc;AAC9B,WAAO,cAAc;AACrB,oBAAgB;AAAA,EAClB;AAEA,aAAW,QAAQ;AACnB,MAAI,eAAe,GACf,eAAe,WAAW,QAC1B,SAAS,GACT,SAAS;AAEb,SAAO,eAAe,cAAc,gBAAgB;AAClD,QAAI,YAAY,WAAW,YAAY;AAEvC,QAAI,CAAC,UAAU,SAAS;AACtB,UAAI,CAAC,UAAU,SAAS,iBAAiB;AACvC,YAAI,QAAQ,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK;AAC5D,gBAAQ,MAAM,IAAI,SAAUC,QAAO,GAAG;AACpC,cAAI,WAAW,UAAU,SAAS,CAAC;AACnC,iBAAO,SAAS,SAASA,OAAM,SAAS,WAAWA;AAAA,QACrD,CAAC;AACD,kBAAU,QAAQD,MAAK,KAAK,KAAK;AAAA,MACnC,OAAO;AACL,kBAAU,QAAQA,MAAK,KAAK,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK,CAAC;AAAA,MAC/E;AAEA,gBAAU,UAAU;AAEpB,UAAI,CAAC,UAAU,OAAO;AACpB,kBAAU,UAAU;AAAA,MACtB;AAAA,IACF,OAAO;AACL,gBAAU,QAAQA,MAAK,KAAK,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK,CAAC;AAC7E,gBAAU,UAAU;AAIpB,UAAI,gBAAgB,WAAW,eAAe,CAAC,EAAE,OAAO;AACtD,YAAI,MAAM,WAAW,eAAe,CAAC;AACrC,mBAAW,eAAe,CAAC,IAAI,WAAW,YAAY;AACtD,mBAAW,YAAY,IAAI;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAKA,MAAI,iBAAiB,WAAW,eAAe,CAAC;AAEhD,MAAI,eAAe,KAAK,OAAO,eAAe,UAAU,aAAa,eAAe,SAAS,eAAe,YAAYA,MAAK,OAAO,IAAI,eAAe,KAAK,GAAG;AAC7J,eAAW,eAAe,CAAC,EAAE,SAAS,eAAe;AACrD,eAAW,IAAI;AAAA,EACjB;AAEA,SAAO;AACT;AAEA,IAAI,gBAAgB,IAAI,KAAK;AAC7B,SAAS,UAAU,QAAQ,QAAQ,SAAS;AAC1C,SAAO,cAAc,KAAK,QAAQ,QAAQ,OAAO;AACnD;AAEA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,MAAI,OAAO,YAAY,YAAY;AACjC,aAAS,WAAW;AAAA,EACtB,WAAW,SAAS;AAClB,aAAS,QAAQ,SAAS;AAExB,UAAI,QAAQ,eAAe,IAAI,GAAG;AAChC,iBAAS,IAAI,IAAI,QAAQ,IAAI;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAoBA,IAAI,oBAAoB;AACxB,IAAI,eAAe;AACnB,IAAI,WAAW,IAAI,KAAK;AAExB,SAAS,SAAS,SAAU,MAAM,OAAO;AACvC,MAAI,KAAK,QAAQ,YAAY;AAC3B,WAAO,KAAK,YAAY;AACxB,YAAQ,MAAM,YAAY;AAAA,EAC5B;AAEA,SAAO,SAAS,SAAS,KAAK,QAAQ,oBAAoB,CAAC,aAAa,KAAK,IAAI,KAAK,CAAC,aAAa,KAAK,KAAK;AAChH;AAEA,SAAS,WAAW,SAAU,OAAO;AAEnC,MAAI,SAAS,MAAM,MAAM,iCAAiC;AAE1D,WAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK;AAE1C,QAAI,CAAC,OAAO,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,kBAAkB,KAAK,OAAO,CAAC,CAAC,KAAK,kBAAkB,KAAK,OAAO,IAAI,CAAC,CAAC,GAAG;AACjH,aAAO,CAAC,KAAK,OAAO,IAAI,CAAC;AACzB,aAAO,OAAO,IAAI,GAAG,CAAC;AACtB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,QAAQ,SAAS;AAC1C,YAAU,gBAAgB,SAAS;AAAA,IACjC,kBAAkB;AAAA,EACpB,CAAC;AACD,SAAO,SAAS,KAAK,QAAQ,QAAQ,OAAO;AAC9C;AACA,SAAS,mBAAmB,QAAQ,QAAQ,SAAS;AACnD,SAAO,SAAS,KAAK,QAAQ,QAAQ,OAAO;AAC9C;AAEA,IAAI,WAAW,IAAI,KAAK;AAExB,SAAS,WAAW,SAAU,OAAO;AACnC,MAAI,KAAK,QAAQ,iBAAiB;AAEhC,YAAQ,MAAM,QAAQ,SAAS,IAAI;AAAA,EACrC;AAEA,MAAI,WAAW,CAAC,GACZ,mBAAmB,MAAM,MAAM,WAAW;AAE9C,MAAI,CAAC,iBAAiB,iBAAiB,SAAS,CAAC,GAAG;AAClD,qBAAiB,IAAI;AAAA,EACvB;AAGA,WAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,QAAI,OAAO,iBAAiB,CAAC;AAE7B,QAAI,IAAI,KAAK,CAAC,KAAK,QAAQ,gBAAgB;AACzC,eAAS,SAAS,SAAS,CAAC,KAAK;AAAA,IACnC,OAAO;AACL,UAAI,KAAK,QAAQ,kBAAkB;AACjC,eAAO,KAAK,KAAK;AAAA,MACnB;AAEA,eAAS,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,QAAQ,UAAU;AAC3C,SAAO,SAAS,KAAK,QAAQ,QAAQ,QAAQ;AAC/C;AACA,SAAS,iBAAiB,QAAQ,QAAQ,UAAU;AAClD,MAAI,UAAU,gBAAgB,UAAU;AAAA,IACtC,kBAAkB;AAAA,EACpB,CAAC;AACD,SAAO,SAAS,KAAK,QAAQ,QAAQ,OAAO;AAC9C;AAEA,IAAI,eAAe,IAAI,KAAK;AAE5B,aAAa,WAAW,SAAU,OAAO;AACvC,SAAO,MAAM,MAAM,uBAAuB;AAC5C;AAEA,SAAS,cAAc,QAAQ,QAAQ,UAAU;AAC/C,SAAO,aAAa,KAAK,QAAQ,QAAQ,QAAQ;AACnD;AAEA,IAAI,UAAU,IAAI,KAAK;AAEvB,QAAQ,WAAW,SAAU,OAAO;AAClC,SAAO,MAAM,MAAM,eAAe;AACpC;AAEA,SAAS,QAAQ,QAAQ,QAAQ,UAAU;AACzC,SAAO,QAAQ,KAAK,QAAQ,QAAQ,QAAQ;AAC9C;AAEA,SAAS,QAAQ,KAAK;AACpB;AAEA,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,cAAU,SAAUE,MAAK;AACvB,aAAO,OAAOA;AAAA,IAChB;AAAA,EACF,OAAO;AACL,cAAU,SAAUA,MAAK;AACvB,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAC3H;AAAA,EACF;AAEA,SAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,QAAI;AAAgB,gBAAU,QAAQ,OAAO,SAAU,KAAK;AAC1D,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MACtD,CAAC;AACD,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AAEA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAO,kBAAkB,GAAG;AACtD;AAEA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,IAAI;AAAG,WAAO,MAAM,KAAK,IAAI;AAC9F;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC;AAAG;AACR,MAAI,OAAO,MAAM;AAAU,WAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE;AAAa,QAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM;AAAO,WAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AAAG,WAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI;AAAQ,UAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK;AAAK,SAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAEA,IAAI,0BAA0B,OAAO,UAAU;AAC/C,IAAI,WAAW,IAAI,KAAK;AAGxB,SAAS,kBAAkB;AAC3B,SAAS,WAAW,SAAS;AAE7B,SAAS,YAAY,SAAU,OAAO;AACpC,MAAI,gBAAgB,KAAK,SACrB,uBAAuB,cAAc,sBACrC,wBAAwB,cAAc,mBACtC,oBAAoB,0BAA0B,SAAS,SAAU,GAAG,GAAG;AACzE,WAAO,OAAO,MAAM,cAAc,uBAAuB;AAAA,EAC3D,IAAI;AACJ,SAAO,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU,aAAa,OAAO,MAAM,MAAM,iBAAiB,GAAG,mBAAmB,IAAI;AACvI;AAEA,SAAS,SAAS,SAAU,MAAM,OAAO;AACvC,SAAO,KAAK,UAAU,OAAO,KAAK,UAAU,KAAK,QAAQ,cAAc,IAAI,GAAG,MAAM,QAAQ,cAAc,IAAI,CAAC;AACjH;AAEA,SAAS,SAAS,QAAQ,QAAQ,SAAS;AACzC,SAAO,SAAS,KAAK,QAAQ,QAAQ,OAAO;AAC9C;AAGA,SAAS,aAAa,KAAK,OAAO,kBAAkB,UAAU,KAAK;AACjE,UAAQ,SAAS,CAAC;AAClB,qBAAmB,oBAAoB,CAAC;AAExC,MAAI,UAAU;AACZ,UAAM,SAAS,KAAK,GAAG;AAAA,EACzB;AAEA,MAAI;AAEJ,OAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACpC,QAAI,MAAM,CAAC,MAAM,KAAK;AACpB,aAAO,iBAAiB,CAAC;AAAA,IAC3B;AAAA,EACF;AAEA,MAAI;AAEJ,MAAI,qBAAqB,wBAAwB,KAAK,GAAG,GAAG;AAC1D,UAAM,KAAK,GAAG;AACd,uBAAmB,IAAI,MAAM,IAAI,MAAM;AACvC,qBAAiB,KAAK,gBAAgB;AAEtC,SAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAClC,uBAAiB,CAAC,IAAI,aAAa,IAAI,CAAC,GAAG,OAAO,kBAAkB,UAAU,GAAG;AAAA,IACnF;AAEA,UAAM,IAAI;AACV,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,IAAI,QAAQ;AACrB,UAAM,IAAI,OAAO;AAAA,EACnB;AAEA,MAAI,QAAQ,GAAG,MAAM,YAAY,QAAQ,MAAM;AAC7C,UAAM,KAAK,GAAG;AACd,uBAAmB,CAAC;AACpB,qBAAiB,KAAK,gBAAgB;AAEtC,QAAI,aAAa,CAAC,GACd;AAEJ,SAAK,QAAQ,KAAK;AAEhB,UAAI,IAAI,eAAe,IAAI,GAAG;AAC5B,mBAAW,KAAK,IAAI;AAAA,MACtB;AAAA,IACF;AAEA,eAAW,KAAK;AAEhB,SAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AACzC,aAAO,WAAW,CAAC;AACnB,uBAAiB,IAAI,IAAI,aAAa,IAAI,IAAI,GAAG,OAAO,kBAAkB,UAAU,IAAI;AAAA,IAC1F;AAEA,UAAM,IAAI;AACV,qBAAiB,IAAI;AAAA,EACvB,OAAO;AACL,uBAAmB;AAAA,EACrB;AAEA,SAAO;AACT;AAEA,IAAI,YAAY,IAAI,KAAK;AAEzB,UAAU,WAAW,SAAU,OAAO;AACpC,SAAO,MAAM,MAAM;AACrB;AAEA,UAAU,OAAO,UAAU,cAAc,SAAU,OAAO;AACxD,SAAO;AACT;AAEA,SAAS,WAAW,QAAQ,QAAQ,UAAU;AAC5C,SAAO,UAAU,KAAK,QAAQ,QAAQ,QAAQ;AAChD;AAEA,SAAS,WAAW,SAAS;AAC3B,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,MAAI,UAAU,QAAQ,MAAM,qBAAqB,GAC7C,aAAa,QAAQ,MAAM,sBAAsB,KAAK,CAAC,GACvD,OAAO,CAAC,GACR,IAAI;AAER,WAAS,aAAa;AACpB,QAAI,QAAQ,CAAC;AACb,SAAK,KAAK,KAAK;AAEf,WAAO,IAAI,QAAQ,QAAQ;AACzB,UAAI,OAAO,QAAQ,CAAC;AAEpB,UAAI,wBAAwB,KAAK,IAAI,GAAG;AACtC;AAAA,MACF;AAGA,UAAI,SAAS,2CAA2C,KAAK,IAAI;AAEjE,UAAI,QAAQ;AACV,cAAM,QAAQ,OAAO,CAAC;AAAA,MACxB;AAEA;AAAA,IACF;AAIA,oBAAgB,KAAK;AACrB,oBAAgB,KAAK;AAErB,UAAM,QAAQ,CAAC;AAEf,WAAO,IAAI,QAAQ,QAAQ;AACzB,UAAI,QAAQ,QAAQ,CAAC;AAErB,UAAI,iCAAiC,KAAK,KAAK,GAAG;AAChD;AAAA,MACF,WAAW,MAAM,KAAK,KAAK,GAAG;AAC5B,cAAM,MAAM,KAAK,UAAU,CAAC;AAAA,MAC9B,WAAW,SAAS,QAAQ,QAAQ;AAElC,cAAM,IAAI,MAAM,mBAAmB,IAAI,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC;AAAA,MACzE,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAIA,WAAS,gBAAgB,OAAO;AAC9B,QAAI,aAAa,wBAAwB,KAAK,QAAQ,CAAC,CAAC;AAExD,QAAI,YAAY;AACd,UAAI,YAAY,WAAW,CAAC,MAAM,QAAQ,QAAQ;AAClD,UAAI,OAAO,WAAW,CAAC,EAAE,MAAM,KAAM,CAAC;AACtC,UAAI,WAAW,KAAK,CAAC,EAAE,QAAQ,SAAS,IAAI;AAE5C,UAAI,SAAS,KAAK,QAAQ,GAAG;AAC3B,mBAAW,SAAS,OAAO,GAAG,SAAS,SAAS,CAAC;AAAA,MACnD;AAEA,YAAM,YAAY,UAAU,IAAI;AAChC,YAAM,YAAY,QAAQ,KAAK,KAAK,CAAC,KAAK,IAAI,KAAK;AACnD;AAAA,IACF;AAAA,EACF;AAIA,WAAS,YAAY;AACnB,QAAI,mBAAmB,GACnB,kBAAkB,QAAQ,GAAG,GAC7B,cAAc,gBAAgB,MAAM,4CAA4C;AACpF,QAAI,OAAO;AAAA,MACT,UAAU,CAAC,YAAY,CAAC;AAAA,MACxB,UAAU,OAAO,YAAY,CAAC,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC;AAAA,MACpE,UAAU,CAAC,YAAY,CAAC;AAAA,MACxB,UAAU,OAAO,YAAY,CAAC,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC;AAAA,MACpE,OAAO,CAAC;AAAA,MACR,gBAAgB,CAAC;AAAA,IACnB;AAIA,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,YAAY;AAAA,IACnB;AAEA,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,YAAY;AAAA,IACnB;AAEA,QAAI,WAAW,GACX,cAAc;AAElB,WAAO,IAAI,QAAQ,QAAQ,KAAK;AAG9B,UAAI,QAAQ,CAAC,EAAE,QAAQ,MAAM,MAAM,KAAK,IAAI,IAAI,QAAQ,UAAU,QAAQ,IAAI,CAAC,EAAE,QAAQ,MAAM,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAE,QAAQ,IAAI,MAAM,GAAG;AAC5I;AAAA,MACF;AAEA,UAAI,YAAY,QAAQ,CAAC,EAAE,UAAU,KAAK,KAAK,QAAQ,SAAS,IAAI,MAAM,QAAQ,CAAC,EAAE,CAAC;AAEtF,UAAI,cAAc,OAAO,cAAc,OAAO,cAAc,OAAO,cAAc,MAAM;AACrF,aAAK,MAAM,KAAK,QAAQ,CAAC,CAAC;AAC1B,aAAK,eAAe,KAAK,WAAW,CAAC,KAAK,IAAI;AAE9C,YAAI,cAAc,KAAK;AACrB;AAAA,QACF,WAAW,cAAc,KAAK;AAC5B;AAAA,QACF,WAAW,cAAc,KAAK;AAC5B;AACA;AAAA,QACF;AAAA,MACF,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAGA,QAAI,CAAC,YAAY,KAAK,aAAa,GAAG;AACpC,WAAK,WAAW;AAAA,IAClB;AAEA,QAAI,CAAC,eAAe,KAAK,aAAa,GAAG;AACvC,WAAK,WAAW;AAAA,IAClB;AAGA,QAAI,QAAQ,QAAQ;AAClB,UAAI,aAAa,KAAK,UAAU;AAC9B,cAAM,IAAI,MAAM,sDAAsD,mBAAmB,EAAE;AAAA,MAC7F;AAEA,UAAI,gBAAgB,KAAK,UAAU;AACjC,cAAM,IAAI,MAAM,wDAAwD,mBAAmB,EAAE;AAAA,MAC/F;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,IAAI,QAAQ,QAAQ;AACzB,eAAW;AAAA,EACb;AAEA,SAAO;AACT;AAKA,SAAS,iBAAkB,OAAO,SAAS,SAAS;AAClD,MAAI,cAAc,MACd,oBAAoB,OACpB,mBAAmB,OACnB,cAAc;AAClB,SAAO,SAAS,WAAW;AACzB,QAAI,eAAe,CAAC,kBAAkB;AACpC,UAAI,mBAAmB;AACrB;AAAA,MACF,OAAO;AACL,sBAAc;AAAA,MAChB;AAIA,UAAI,QAAQ,eAAe,SAAS;AAClC,eAAO;AAAA,MACT;AAEA,yBAAmB;AAAA,IACrB;AAEA,QAAI,CAAC,mBAAmB;AACtB,UAAI,CAAC,kBAAkB;AACrB,sBAAc;AAAA,MAChB;AAIA,UAAI,WAAW,QAAQ,aAAa;AAClC,eAAO,CAAC;AAAA,MACV;AAEA,0BAAoB;AACpB,aAAO,SAAS;AAAA,IAClB;AAAA,EAGF;AACF;AAEA,SAAS,WAAW,QAAQ,SAAS;AACnC,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,MAAI,OAAO,YAAY,UAAU;AAC/B,cAAU,WAAW,OAAO;AAAA,EAC9B;AAEA,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,QAAI,QAAQ,SAAS,GAAG;AACtB,YAAM,IAAI,MAAM,4CAA4C;AAAA,IAC9D;AAEA,cAAU,QAAQ,CAAC;AAAA,EACrB;AAGA,MAAI,QAAQ,OAAO,MAAM,qBAAqB,GAC1C,aAAa,OAAO,MAAM,sBAAsB,KAAK,CAAC,GACtD,QAAQ,QAAQ,OAChB,cAAc,QAAQ,eAAe,SAAU,YAAYC,OAAMC,YAAW,cAAc;AAC5F,WAAOD,UAAS;AAAA,EAClB,GACI,aAAa,GACb,aAAa,QAAQ,cAAc,GACnC,UAAU,GACV,SAAS,GACT,aACA;AAMJ,WAAS,SAASE,OAAMC,QAAO;AAC7B,aAASC,KAAI,GAAGA,KAAIF,MAAK,MAAM,QAAQE,MAAK;AAC1C,UAAIJ,QAAOE,MAAK,MAAME,EAAC,GACnBH,aAAYD,MAAK,SAAS,IAAIA,MAAK,CAAC,IAAI,KACxCK,WAAUL,MAAK,SAAS,IAAIA,MAAK,OAAO,CAAC,IAAIA;AAEjD,UAAIC,eAAc,OAAOA,eAAc,KAAK;AAE1C,YAAI,CAAC,YAAYE,SAAQ,GAAG,MAAMA,MAAK,GAAGF,YAAWI,QAAO,GAAG;AAC7D;AAEA,cAAI,aAAa,YAAY;AAC3B,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,QAAAF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAGA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,OAAO,MAAM,CAAC,GACd,UAAU,MAAM,SAAS,KAAK,UAC9B,cAAc,GACd,QAAQ,SAAS,KAAK,WAAW;AACrC,QAAI,WAAW,iBAAiB,OAAO,SAAS,OAAO;AAEvD,WAAO,gBAAgB,QAAW,cAAc,SAAS,GAAG;AAC1D,UAAI,SAAS,MAAM,QAAQ,WAAW,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,gBAAgB,QAAW;AAC7B,aAAO;AAAA,IACT;AAIA,cAAU,KAAK,SAAS,KAAK,WAAW,KAAK;AAAA,EAC/C;AAGA,MAAI,aAAa;AAEjB,WAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,MAAM;AACxC,QAAI,QAAQ,MAAM,EAAE,GAChB,SAAS,MAAM,WAAW,MAAM,SAAS,aAAa;AAE1D,kBAAc,MAAM,WAAW,MAAM;AAErC,aAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,UAAI,OAAO,MAAM,MAAM,CAAC,GACpB,YAAY,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KACxC,UAAU,KAAK,SAAS,IAAI,KAAK,OAAO,CAAC,IAAI,MAC7C,YAAY,MAAM,kBAAkB,MAAM,eAAe,CAAC,KAAK;AAEnE,UAAI,cAAc,KAAK;AACrB;AAAA,MACF,WAAW,cAAc,KAAK;AAC5B,cAAM,OAAO,QAAQ,CAAC;AACtB,mBAAW,OAAO,QAAQ,CAAC;AAAA,MAE7B,WAAW,cAAc,KAAK;AAC5B,cAAM,OAAO,QAAQ,GAAG,OAAO;AAC/B,mBAAW,OAAO,QAAQ,GAAG,SAAS;AACtC;AAAA,MACF,WAAW,cAAc,MAAM;AAC7B,YAAI,oBAAoB,MAAM,MAAM,IAAI,CAAC,IAAI,MAAM,MAAM,IAAI,CAAC,EAAE,CAAC,IAAI;AAErE,YAAI,sBAAsB,KAAK;AAC7B,wBAAc;AAAA,QAChB,WAAW,sBAAsB,KAAK;AACpC,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,MAAI,aAAa;AACf,WAAO,CAAC,MAAM,MAAM,SAAS,CAAC,GAAG;AAC/B,YAAM,IAAI;AACV,iBAAW,IAAI;AAAA,IACjB;AAAA,EACF,WAAW,UAAU;AACnB,UAAM,KAAK,EAAE;AACb,eAAW,KAAK,IAAI;AAAA,EACtB;AAEA,WAAS,KAAK,GAAG,KAAK,MAAM,SAAS,GAAG,MAAM;AAC5C,UAAM,EAAE,IAAI,MAAM,EAAE,IAAI,WAAW,EAAE;AAAA,EACvC;AAEA,SAAO,MAAM,KAAK,EAAE;AACtB;AAEA,SAAS,aAAa,SAAS,SAAS;AACtC,MAAI,OAAO,YAAY,UAAU;AAC/B,cAAU,WAAW,OAAO;AAAA,EAC9B;AAEA,MAAI,eAAe;AAEnB,WAAS,eAAe;AACtB,QAAI,QAAQ,QAAQ,cAAc;AAElC,QAAI,CAAC,OAAO;AACV,aAAO,QAAQ,SAAS;AAAA,IAC1B;AAEA,YAAQ,SAAS,OAAO,SAAU,KAAK,MAAM;AAC3C,UAAI,KAAK;AACP,eAAO,QAAQ,SAAS,GAAG;AAAA,MAC7B;AAEA,UAAI,iBAAiB,WAAW,MAAM,OAAO,OAAO;AACpD,cAAQ,QAAQ,OAAO,gBAAgB,SAAUG,MAAK;AACpD,YAAIA,MAAK;AACP,iBAAO,QAAQ,SAASA,IAAG;AAAA,QAC7B;AAEA,qBAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,eAAa;AACf;AAEA,SAAS,gBAAgB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,SAAS;AAChG,MAAI,CAAC,SAAS;AACZ,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,OAAO,QAAQ,YAAY,aAAa;AAC1C,YAAQ,UAAU;AAAA,EACpB;AAEA,MAAIT,QAAO,UAAU,QAAQ,QAAQ,OAAO;AAE5C,MAAI,CAACA,OAAM;AACT;AAAA,EACF;AAEA,EAAAA,MAAK,KAAK;AAAA,IACR,OAAO;AAAA,IACP,OAAO,CAAC;AAAA,EACV,CAAC;AAED,WAAS,aAAa,OAAO;AAC3B,WAAO,MAAM,IAAI,SAAU,OAAO;AAChC,aAAO,MAAM;AAAA,IACf,CAAC;AAAA,EACH;AAEA,MAAI,QAAQ,CAAC;AACb,MAAI,gBAAgB,GAChB,gBAAgB,GAChB,WAAW,CAAC,GACZ,UAAU,GACV,UAAU;AAEd,MAAI,QAAQ,SAASU,OAAMC,IAAG;AAC5B,QAAI,UAAUX,MAAKW,EAAC,GAChB,QAAQ,QAAQ,SAAS,QAAQ,MAAM,QAAQ,OAAO,EAAE,EAAE,MAAM,IAAI;AACxE,YAAQ,QAAQ;AAEhB,QAAI,QAAQ,SAAS,QAAQ,SAAS;AACpC,UAAI;AAGJ,UAAI,CAAC,eAAe;AAClB,YAAI,OAAOX,MAAKW,KAAI,CAAC;AACrB,wBAAgB;AAChB,wBAAgB;AAEhB,YAAI,MAAM;AACR,qBAAW,QAAQ,UAAU,IAAI,aAAa,KAAK,MAAM,MAAM,CAAC,QAAQ,OAAO,CAAC,IAAI,CAAC;AACrF,2BAAiB,SAAS;AAC1B,2BAAiB,SAAS;AAAA,QAC5B;AAAA,MACF;AAGA,OAAC,YAAY,UAAU,KAAK,MAAM,WAAW,mBAAmB,MAAM,IAAI,SAAU,OAAO;AACzF,gBAAQ,QAAQ,QAAQ,MAAM,OAAO;AAAA,MACvC,CAAC,CAAC,CAAC;AAGH,UAAI,QAAQ,OAAO;AACjB,mBAAW,MAAM;AAAA,MACnB,OAAO;AACL,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF,OAAO;AAEL,UAAI,eAAe;AAEjB,YAAI,MAAM,UAAU,QAAQ,UAAU,KAAKA,KAAIX,MAAK,SAAS,GAAG;AAC9D,cAAI;AAGJ,WAAC,aAAa,UAAU,KAAK,MAAM,YAAY,mBAAmB,aAAa,KAAK,CAAC,CAAC;AAAA,QACxF,OAAO;AACL,cAAI;AAGJ,cAAI,cAAc,KAAK,IAAI,MAAM,QAAQ,QAAQ,OAAO;AAExD,WAAC,aAAa,UAAU,KAAK,MAAM,YAAY,mBAAmB,aAAa,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;AAE5G,cAAI,OAAO;AAAA,YACT,UAAU;AAAA,YACV,UAAU,UAAU,gBAAgB;AAAA,YACpC,UAAU;AAAA,YACV,UAAU,UAAU,gBAAgB;AAAA,YACpC,OAAO;AAAA,UACT;AAEA,cAAIW,MAAKX,MAAK,SAAS,KAAK,MAAM,UAAU,QAAQ,SAAS;AAE3D,gBAAI,gBAAgB,MAAM,KAAK,MAAM;AACrC,gBAAI,gBAAgB,MAAM,KAAK,MAAM;AACrC,gBAAI,iBAAiB,MAAM,UAAU,KAAK,SAAS,SAAS,KAAK;AAEjE,gBAAI,CAAC,iBAAiB,kBAAkB,OAAO,SAAS,GAAG;AAGzD,uBAAS,OAAO,KAAK,UAAU,GAAG,8BAA8B;AAAA,YAClE;AAEA,gBAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,eAAe;AACvD,uBAAS,KAAK,8BAA8B;AAAA,YAC9C;AAAA,UACF;AAEA,gBAAM,KAAK,IAAI;AACf,0BAAgB;AAChB,0BAAgB;AAChB,qBAAW,CAAC;AAAA,QACd;AAAA,MACF;AAEA,iBAAW,MAAM;AACjB,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,KAAK;AACpC,UAAM,CAAC;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,YAAYA,OAAM;AACzB,MAAI,MAAM,QAAQA,KAAI,GAAG;AACvB,WAAOA,MAAK,IAAI,WAAW,EAAE,KAAK,IAAI;AAAA,EACxC;AAEA,MAAI,MAAM,CAAC;AAEX,MAAIA,MAAK,eAAeA,MAAK,aAAa;AACxC,QAAI,KAAK,YAAYA,MAAK,WAAW;AAAA,EACvC;AAEA,MAAI,KAAK,qEAAqE;AAC9E,MAAI,KAAK,SAASA,MAAK,eAAe,OAAOA,MAAK,cAAc,cAAc,KAAK,MAAOA,MAAK,UAAU;AACzG,MAAI,KAAK,SAASA,MAAK,eAAe,OAAOA,MAAK,cAAc,cAAc,KAAK,MAAOA,MAAK,UAAU;AAEzG,WAAS,IAAI,GAAG,IAAIA,MAAK,MAAM,QAAQ,KAAK;AAC1C,QAAI,OAAOA,MAAK,MAAM,CAAC;AAIvB,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,YAAY;AAAA,IACnB;AAEA,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,YAAY;AAAA,IACnB;AAEA,QAAI,KAAK,SAAS,KAAK,WAAW,MAAM,KAAK,WAAW,OAAO,KAAK,WAAW,MAAM,KAAK,WAAW,KAAK;AAC1G,QAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,EAChC;AAEA,SAAO,IAAI,KAAK,IAAI,IAAI;AAC1B;AACA,SAAS,oBAAoB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,SAAS;AACpG,SAAO,YAAY,gBAAgB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW,OAAO,CAAC;AAC7G;AACA,SAAS,YAAY,UAAU,QAAQ,QAAQ,WAAW,WAAW,SAAS;AAC5E,SAAO,oBAAoB,UAAU,UAAU,QAAQ,QAAQ,WAAW,WAAW,OAAO;AAC9F;AAEA,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,GAAG,CAAC;AAC7B;AACA,SAAS,gBAAgB,OAAO,OAAO;AACrC,MAAI,MAAM,SAAS,MAAM,QAAQ;AAC/B,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,MAAM,CAAC,MAAM,MAAM,CAAC,GAAG;AACzB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,uBAAuB,oBAAoB,KAAK,KAAK,GACrD,WAAW,qBAAqB,UAChC,WAAW,qBAAqB;AAEpC,MAAI,aAAa,QAAW;AAC1B,SAAK,WAAW;AAAA,EAClB,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AAEA,MAAI,aAAa,QAAW;AAC1B,SAAK,WAAW;AAAA,EAClB,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AACF;AACA,SAAS,MAAM,MAAM,QAAQ,MAAM;AACjC,SAAO,UAAU,MAAM,IAAI;AAC3B,WAAS,UAAU,QAAQ,IAAI;AAC/B,MAAI,MAAM,CAAC;AAIX,MAAI,KAAK,SAAS,OAAO,OAAO;AAC9B,QAAI,QAAQ,KAAK,SAAS,OAAO;AAAA,EACnC;AAEA,MAAI,KAAK,eAAe,OAAO,aAAa;AAC1C,QAAI,CAAC,gBAAgB,IAAI,GAAG;AAE1B,UAAI,cAAc,OAAO,eAAe,KAAK;AAC7C,UAAI,cAAc,OAAO,eAAe,KAAK;AAC7C,UAAI,YAAY,OAAO,aAAa,KAAK;AACzC,UAAI,YAAY,OAAO,aAAa,KAAK;AAAA,IAC3C,WAAW,CAAC,gBAAgB,MAAM,GAAG;AAEnC,UAAI,cAAc,KAAK;AACvB,UAAI,cAAc,KAAK;AACvB,UAAI,YAAY,KAAK;AACrB,UAAI,YAAY,KAAK;AAAA,IACvB,OAAO;AAEL,UAAI,cAAc,YAAY,KAAK,KAAK,aAAa,OAAO,WAAW;AACvE,UAAI,cAAc,YAAY,KAAK,KAAK,aAAa,OAAO,WAAW;AACvE,UAAI,YAAY,YAAY,KAAK,KAAK,WAAW,OAAO,SAAS;AACjE,UAAI,YAAY,YAAY,KAAK,KAAK,WAAW,OAAO,SAAS;AAAA,IACnE;AAAA,EACF;AAEA,MAAI,QAAQ,CAAC;AACb,MAAI,YAAY,GACZ,cAAc,GACd,aAAa,GACb,eAAe;AAEnB,SAAO,YAAY,KAAK,MAAM,UAAU,cAAc,OAAO,MAAM,QAAQ;AACzE,QAAI,cAAc,KAAK,MAAM,SAAS,KAAK;AAAA,MACzC,UAAU;AAAA,IACZ,GACI,gBAAgB,OAAO,MAAM,WAAW,KAAK;AAAA,MAC/C,UAAU;AAAA,IACZ;AAEA,QAAI,WAAW,aAAa,aAAa,GAAG;AAE1C,UAAI,MAAM,KAAK,UAAU,aAAa,UAAU,CAAC;AACjD;AACA,sBAAgB,YAAY,WAAW,YAAY;AAAA,IACrD,WAAW,WAAW,eAAe,WAAW,GAAG;AAEjD,UAAI,MAAM,KAAK,UAAU,eAAe,YAAY,CAAC;AACrD;AACA,oBAAc,cAAc,WAAW,cAAc;AAAA,IACvD,OAAO;AAEL,UAAI,aAAa;AAAA,QACf,UAAU,KAAK,IAAI,YAAY,UAAU,cAAc,QAAQ;AAAA,QAC/D,UAAU;AAAA,QACV,UAAU,KAAK,IAAI,YAAY,WAAW,YAAY,cAAc,WAAW,YAAY;AAAA,QAC3F,UAAU;AAAA,QACV,OAAO,CAAC;AAAA,MACV;AACA,iBAAW,YAAY,YAAY,UAAU,YAAY,OAAO,cAAc,UAAU,cAAc,KAAK;AAC3G;AACA;AACA,UAAI,MAAM,KAAK,UAAU;AAAA,IAC3B;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,OAAO,MAAM;AAC9B,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG;AAChD,aAAO,WAAW,KAAK,EAAE,CAAC;AAAA,IAC5B;AAEA,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,kDAAkD;AAAA,IACpE;AAEA,WAAO,gBAAgB,QAAW,QAAW,MAAM,KAAK;AAAA,EAC1D;AAEA,SAAO;AACT;AAEA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,MAAM,eAAe,MAAM,gBAAgB,MAAM;AAC1D;AAEA,SAAS,YAAY,OAAO,MAAM,QAAQ;AACxC,MAAI,SAAS,QAAQ;AACnB,WAAO;AAAA,EACT,OAAO;AACL,UAAM,WAAW;AACjB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,WAAW,MAAM,OAAO;AAC/B,SAAO,KAAK,WAAW,MAAM,YAAY,KAAK,WAAW,KAAK,WAAW,MAAM;AACjF;AAEA,SAAS,UAAU,MAAM,QAAQ;AAC/B,SAAO;AAAA,IACL,UAAU,KAAK;AAAA,IACf,UAAU,KAAK;AAAA,IACf,UAAU,KAAK,WAAW;AAAA,IAC1B,UAAU,KAAK;AAAA,IACf,OAAO,KAAK;AAAA,EACd;AACF;AAEA,SAAS,WAAW,MAAM,YAAY,WAAW,aAAa,YAAY;AAGxE,MAAI,OAAO;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,EACT,GACI,QAAQ;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,EACT;AAEA,gBAAc,MAAM,MAAM,KAAK;AAC/B,gBAAc,MAAM,OAAO,IAAI;AAE/B,SAAO,KAAK,QAAQ,KAAK,MAAM,UAAU,MAAM,QAAQ,MAAM,MAAM,QAAQ;AACzE,QAAI,cAAc,KAAK,MAAM,KAAK,KAAK,GACnC,eAAe,MAAM,MAAM,MAAM,KAAK;AAE1C,SAAK,YAAY,CAAC,MAAM,OAAO,YAAY,CAAC,MAAM,SAAS,aAAa,CAAC,MAAM,OAAO,aAAa,CAAC,MAAM,MAAM;AAE9G,mBAAa,MAAM,MAAM,KAAK;AAAA,IAChC,WAAW,YAAY,CAAC,MAAM,OAAO,aAAa,CAAC,MAAM,KAAK;AAC5D,UAAI;AAGJ,OAAC,cAAc,KAAK,OAAO,KAAK,MAAM,aAAa,mBAAmB,cAAc,IAAI,CAAC,CAAC;AAAA,IAC5F,WAAW,aAAa,CAAC,MAAM,OAAO,YAAY,CAAC,MAAM,KAAK;AAC5D,UAAI;AAGJ,OAAC,eAAe,KAAK,OAAO,KAAK,MAAM,cAAc,mBAAmB,cAAc,KAAK,CAAC,CAAC;AAAA,IAC/F,WAAW,YAAY,CAAC,MAAM,OAAO,aAAa,CAAC,MAAM,KAAK;AAE5D,cAAQ,MAAM,MAAM,KAAK;AAAA,IAC3B,WAAW,aAAa,CAAC,MAAM,OAAO,YAAY,CAAC,MAAM,KAAK;AAE5D,cAAQ,MAAM,OAAO,MAAM,IAAI;AAAA,IACjC,WAAW,gBAAgB,cAAc;AAEvC,WAAK,MAAM,KAAK,WAAW;AAC3B,WAAK;AACL,YAAM;AAAA,IACR,OAAO;AAEL,eAAS,MAAM,cAAc,IAAI,GAAG,cAAc,KAAK,CAAC;AAAA,IAC1D;AAAA,EACF;AAGA,iBAAe,MAAM,IAAI;AACzB,iBAAe,MAAM,KAAK;AAC1B,gBAAc,IAAI;AACpB;AAEA,SAAS,aAAa,MAAM,MAAM,OAAO;AACvC,MAAI,YAAY,cAAc,IAAI,GAC9B,eAAe,cAAc,KAAK;AAEtC,MAAI,WAAW,SAAS,KAAK,WAAW,YAAY,GAAG;AAErD,QAAI,gBAAgB,WAAW,YAAY,KAAK,mBAAmB,OAAO,WAAW,UAAU,SAAS,aAAa,MAAM,GAAG;AAC5H,UAAI;AAEJ,OAAC,eAAe,KAAK,OAAO,KAAK,MAAM,cAAc,mBAAmB,SAAS,CAAC;AAElF;AAAA,IACF,WAAW,gBAAgB,cAAc,SAAS,KAAK,mBAAmB,MAAM,cAAc,aAAa,SAAS,UAAU,MAAM,GAAG;AACrI,UAAI;AAEJ,OAAC,eAAe,KAAK,OAAO,KAAK,MAAM,cAAc,mBAAmB,YAAY,CAAC;AAErF;AAAA,IACF;AAAA,EACF,WAAW,WAAW,WAAW,YAAY,GAAG;AAC9C,QAAI;AAEJ,KAAC,eAAe,KAAK,OAAO,KAAK,MAAM,cAAc,mBAAmB,SAAS,CAAC;AAElF;AAAA,EACF;AAEA,WAAS,MAAM,WAAW,YAAY;AACxC;AAEA,SAAS,QAAQ,MAAM,MAAM,OAAO,MAAM;AACxC,MAAI,YAAY,cAAc,IAAI,GAC9B,eAAe,eAAe,OAAO,SAAS;AAElD,MAAI,aAAa,QAAQ;AACvB,QAAI;AAEJ,KAAC,eAAe,KAAK,OAAO,KAAK,MAAM,cAAc,mBAAmB,aAAa,MAAM,CAAC;AAAA,EAC9F,OAAO;AACL,aAAS,MAAM,OAAO,eAAe,WAAW,OAAO,YAAY,YAAY;AAAA,EACjF;AACF;AAEA,SAAS,SAAS,MAAM,MAAM,OAAO;AACnC,OAAK,WAAW;AAChB,OAAK,MAAM,KAAK;AAAA,IACd,UAAU;AAAA,IACV;AAAA,IACA,QAAQ;AAAA,EACV,CAAC;AACH;AAEA,SAAS,cAAc,MAAM,QAAQ,OAAO;AAC1C,SAAO,OAAO,SAAS,MAAM,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ;AACzE,QAAI,OAAO,OAAO,MAAM,OAAO,OAAO;AACtC,SAAK,MAAM,KAAK,IAAI;AACpB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,MAAM,QAAQ;AACpC,SAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ;AACzC,QAAI,OAAO,OAAO,MAAM,OAAO,OAAO;AACtC,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AACF;AAEA,SAAS,cAAc,OAAO;AAC5B,MAAI,MAAM,CAAC,GACP,YAAY,MAAM,MAAM,MAAM,KAAK,EAAE,CAAC;AAE1C,SAAO,MAAM,QAAQ,MAAM,MAAM,QAAQ;AACvC,QAAI,OAAO,MAAM,MAAM,MAAM,KAAK;AAElC,QAAI,cAAc,OAAO,KAAK,CAAC,MAAM,KAAK;AACxC,kBAAY;AAAA,IACd;AAEA,QAAI,cAAc,KAAK,CAAC,GAAG;AACzB,UAAI,KAAK,IAAI;AACb,YAAM;AAAA,IACR,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,OAAO,cAAc;AAC3C,MAAI,UAAU,CAAC,GACX,SAAS,CAAC,GACV,aAAa,GACb,iBAAiB,OACjB,aAAa;AAEjB,SAAO,aAAa,aAAa,UAAU,MAAM,QAAQ,MAAM,MAAM,QAAQ;AAC3E,QAAI,SAAS,MAAM,MAAM,MAAM,KAAK,GAChC,QAAQ,aAAa,UAAU;AAEnC,QAAI,MAAM,CAAC,MAAM,KAAK;AACpB;AAAA,IACF;AAEA,qBAAiB,kBAAkB,OAAO,CAAC,MAAM;AACjD,WAAO,KAAK,KAAK;AACjB;AAGA,QAAI,OAAO,CAAC,MAAM,KAAK;AACrB,mBAAa;AAEb,aAAO,OAAO,CAAC,MAAM,KAAK;AACxB,gBAAQ,KAAK,MAAM;AACnB,iBAAS,MAAM,MAAM,EAAE,MAAM,KAAK;AAAA,MACpC;AAAA,IACF;AAEA,QAAI,MAAM,OAAO,CAAC,MAAM,OAAO,OAAO,CAAC,GAAG;AACxC,cAAQ,KAAK,MAAM;AACnB,YAAM;AAAA,IACR,OAAO;AACL,mBAAa;AAAA,IACf;AAAA,EACF;AAEA,OAAK,aAAa,UAAU,KAAK,IAAI,CAAC,MAAM,OAAO,gBAAgB;AACjE,iBAAa;AAAA,EACf;AAEA,MAAI,YAAY;AACd,WAAO;AAAA,EACT;AAEA,SAAO,aAAa,aAAa,QAAQ;AACvC,WAAO,KAAK,aAAa,YAAY,CAAC;AAAA,EACxC;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,SAAS;AAC3B,SAAO,QAAQ,OAAO,SAAU,MAAM,QAAQ;AAC5C,WAAO,QAAQ,OAAO,CAAC,MAAM;AAAA,EAC/B,GAAG,IAAI;AACT;AAEA,SAAS,mBAAmB,OAAO,eAAe,OAAO;AACvD,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,QAAI,gBAAgB,cAAc,cAAc,SAAS,QAAQ,CAAC,EAAE,OAAO,CAAC;AAE5E,QAAI,MAAM,MAAM,MAAM,QAAQ,CAAC,MAAM,MAAM,eAAe;AACxD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,SAAS;AACf,SAAO;AACT;AAEA,SAAS,oBAAoB,OAAO;AAClC,MAAI,WAAW;AACf,MAAI,WAAW;AACf,QAAM,QAAQ,SAAU,MAAM;AAC5B,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,UAAU,oBAAoB,KAAK,IAAI;AAC3C,UAAI,aAAa,oBAAoB,KAAK,MAAM;AAEhD,UAAI,aAAa,QAAW;AAC1B,YAAI,QAAQ,aAAa,WAAW,UAAU;AAC5C,sBAAY,QAAQ;AAAA,QACtB,OAAO;AACL,qBAAW;AAAA,QACb;AAAA,MACF;AAEA,UAAI,aAAa,QAAW;AAC1B,YAAI,QAAQ,aAAa,WAAW,UAAU;AAC5C,sBAAY,QAAQ;AAAA,QACtB,OAAO;AACL,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF,OAAO;AACL,UAAI,aAAa,WAAc,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM;AAClE;AAAA,MACF;AAEA,UAAI,aAAa,WAAc,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM;AAClE;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,aAAaY,kBAAiB;AACrC,MAAI,MAAM,QAAQA,gBAAe,GAAG;AAClC,WAAOA,iBAAgB,IAAI,YAAY,EAAE,QAAQ;AAAA,EACnD;AAEA,SAAO,eAAe,eAAe,CAAC,GAAGA,gBAAe,GAAG,CAAC,GAAG;AAAA,IAC7D,aAAaA,iBAAgB;AAAA,IAC7B,WAAWA,iBAAgB;AAAA,IAC3B,aAAaA,iBAAgB;AAAA,IAC7B,WAAWA,iBAAgB;AAAA,IAC3B,OAAOA,iBAAgB,MAAM,IAAI,SAAU,MAAM;AAC/C,aAAO;AAAA,QACL,UAAU,KAAK;AAAA,QACf,UAAU,KAAK;AAAA,QACf,UAAU,KAAK;AAAA,QACf,UAAU,KAAK;AAAA,QACf,gBAAgB,KAAK;AAAA,QACrB,OAAO,KAAK,MAAM,IAAI,SAAU,GAAG;AACjC,cAAI,EAAE,WAAW,GAAG,GAAG;AACrB,mBAAO,IAAI,OAAO,EAAE,MAAM,CAAC,CAAC;AAAA,UAC9B;AAEA,cAAI,EAAE,WAAW,GAAG,GAAG;AACrB,mBAAO,IAAI,OAAO,EAAE,MAAM,CAAC,CAAC;AAAA,UAC9B;AAEA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAGA,SAAS,oBAAoB,SAAS;AACpC,MAAI,MAAM,CAAC,GACP,QACA;AAEJ,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,aAAS,QAAQ,CAAC;AAElB,QAAI,OAAO,OAAO;AAChB,kBAAY;AAAA,IACd,WAAW,OAAO,SAAS;AACzB,kBAAY;AAAA,IACd,OAAO;AACL,kBAAY;AAAA,IACd;AAEA,QAAI,KAAK,CAAC,WAAW,OAAO,KAAK,CAAC;AAAA,EACpC;AAEA,SAAO;AACT;AAEA,SAAS,oBAAoB,SAAS;AACpC,MAAI,MAAM,CAAC;AAEX,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,QAAI,SAAS,QAAQ,CAAC;AAEtB,QAAI,OAAO,OAAO;AAChB,UAAI,KAAK,OAAO;AAAA,IAClB,WAAW,OAAO,SAAS;AACzB,UAAI,KAAK,OAAO;AAAA,IAClB;AAEA,QAAI,KAAK,WAAW,OAAO,KAAK,CAAC;AAEjC,QAAI,OAAO,OAAO;AAChB,UAAI,KAAK,QAAQ;AAAA,IACnB,WAAW,OAAO,SAAS;AACzB,UAAI,KAAK,QAAQ;AAAA,IACnB;AAAA,EACF;AAEA,SAAO,IAAI,KAAK,EAAE;AACpB;AAEA,SAAS,WAAW,GAAG;AACrB,MAAI,IAAI;AACR,MAAI,EAAE,QAAQ,MAAM,OAAO;AAC3B,MAAI,EAAE,QAAQ,MAAM,MAAM;AAC1B,MAAI,EAAE,QAAQ,MAAM,MAAM;AAC1B,MAAI,EAAE,QAAQ,MAAM,QAAQ;AAC5B,SAAO;AACT;", "names": ["diff", "value", "obj", "line", "operation", "hunk", "toPos", "j", "content", "err", "_loop", "i", "structuredPatch"]}