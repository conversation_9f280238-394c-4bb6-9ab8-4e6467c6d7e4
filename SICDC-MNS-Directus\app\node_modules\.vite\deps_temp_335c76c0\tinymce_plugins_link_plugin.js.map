{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/link/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const eq = t => a => t === a;\n    const isString = isType('string');\n    const isObject = isType('object');\n    const isArray = isType('array');\n    const isNull = eq(null);\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isArrayOf = (value, pred) => {\n      if (isArray(value)) {\n        for (let i = 0, len = value.length; i < len; ++i) {\n          if (!pred(value[i])) {\n            return false;\n          }\n        }\n        return true;\n      }\n      return false;\n    };\n\n    const noop = () => {\n    };\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const tripleEquals = (a, b) => {\n      return a === b;\n    };\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const nativeIndexOf = Array.prototype.indexOf;\n    const nativePush = Array.prototype.push;\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains = (xs, x) => rawIndexOf(xs, x) > -1;\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each$1 = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const foldl = (xs, f, acc) => {\n      each$1(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    const flatten = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    const bind = (xs, f) => flatten(map(xs, f));\n    const findMap = (arr, f) => {\n      for (let i = 0; i < arr.length; i++) {\n        const r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    const is = (lhs, rhs, comparator = tripleEquals) => lhs.exists(left => comparator(left, rhs));\n    const cat = arr => {\n      const r = [];\n      const push = x => {\n        r.push(x);\n      };\n      for (let i = 0; i < arr.length; i++) {\n        arr[i].each(push);\n      }\n      return r;\n    };\n    const someIf = (b, a) => b ? Optional.some(a) : Optional.none();\n\n    const option = name => editor => editor.options.get(name);\n    const register$1 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('link_assume_external_targets', {\n        processor: value => {\n          const valid = isString(value) || isBoolean(value);\n          if (valid) {\n            if (value === true) {\n              return {\n                value: 1,\n                valid\n              };\n            } else if (value === 'http' || value === 'https') {\n              return {\n                value,\n                valid\n              };\n            } else {\n              return {\n                value: 0,\n                valid\n              };\n            }\n          } else {\n            return {\n              valid: false,\n              message: 'Must be a string or a boolean.'\n            };\n          }\n        },\n        default: false\n      });\n      registerOption('link_context_toolbar', {\n        processor: 'boolean',\n        default: false\n      });\n      registerOption('link_list', { processor: value => isString(value) || isFunction(value) || isArrayOf(value, isObject) });\n      registerOption('link_default_target', { processor: 'string' });\n      registerOption('link_default_protocol', {\n        processor: 'string',\n        default: 'https'\n      });\n      registerOption('link_target_list', {\n        processor: value => isBoolean(value) || isArrayOf(value, isObject),\n        default: true\n      });\n      registerOption('link_rel_list', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('link_class_list', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('link_title', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('allow_unsafe_link_target', {\n        processor: 'boolean',\n        default: false\n      });\n      registerOption('link_quicklink', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const assumeExternalTargets = option('link_assume_external_targets');\n    const hasContextToolbar = option('link_context_toolbar');\n    const getLinkList = option('link_list');\n    const getDefaultLinkTarget = option('link_default_target');\n    const getDefaultLinkProtocol = option('link_default_protocol');\n    const getTargetList = option('link_target_list');\n    const getRelList = option('link_rel_list');\n    const getLinkClassList = option('link_class_list');\n    const shouldShowLinkTitle = option('link_title');\n    const allowUnsafeLinkTarget = option('allow_unsafe_link_target');\n    const useQuickLink = option('link_quicklink');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const getValue = item => isString(item.value) ? item.value : '';\n    const getText = item => {\n      if (isString(item.text)) {\n        return item.text;\n      } else if (isString(item.title)) {\n        return item.title;\n      } else {\n        return '';\n      }\n    };\n    const sanitizeList = (list, extractValue) => {\n      const out = [];\n      global$4.each(list, item => {\n        const text = getText(item);\n        if (item.menu !== undefined) {\n          const items = sanitizeList(item.menu, extractValue);\n          out.push({\n            text,\n            items\n          });\n        } else {\n          const value = extractValue(item);\n          out.push({\n            text,\n            value\n          });\n        }\n      });\n      return out;\n    };\n    const sanitizeWith = (extracter = getValue) => list => Optional.from(list).map(list => sanitizeList(list, extracter));\n    const sanitize = list => sanitizeWith(getValue)(list);\n    const createUi = (name, label) => items => ({\n      name,\n      type: 'listbox',\n      label,\n      items\n    });\n    const ListOptions = {\n      sanitize,\n      sanitizeWith,\n      createUi,\n      getValue\n    };\n\n    const keys = Object.keys;\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n    const objAcc = r => (x, i) => {\n      r[i] = x;\n    };\n    const internalFilter = (obj, pred, onTrue, onFalse) => {\n      each(obj, (x, i) => {\n        (pred(x, i) ? onTrue : onFalse)(x, i);\n      });\n    };\n    const filter = (obj, pred) => {\n      const t = {};\n      internalFilter(obj, pred, objAcc(t), noop);\n      return t;\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n    const hasNonNullableKey = (obj, key) => has(obj, key) && obj[key] !== undefined && obj[key] !== null;\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.URI');\n\n    const isAnchor = elm => isNonNullable(elm) && elm.nodeName.toLowerCase() === 'a';\n    const isLink = elm => isAnchor(elm) && !!getHref(elm);\n    const collectNodesInRange = (rng, predicate) => {\n      if (rng.collapsed) {\n        return [];\n      } else {\n        const contents = rng.cloneContents();\n        const firstChild = contents.firstChild;\n        const walker = new global$3(firstChild, contents);\n        const elements = [];\n        let current = firstChild;\n        do {\n          if (predicate(current)) {\n            elements.push(current);\n          }\n        } while (current = walker.next());\n        return elements;\n      }\n    };\n    const hasProtocol = url => /^\\w+:/i.test(url);\n    const getHref = elm => {\n      var _a, _b;\n      return (_b = (_a = elm.getAttribute('data-mce-href')) !== null && _a !== void 0 ? _a : elm.getAttribute('href')) !== null && _b !== void 0 ? _b : '';\n    };\n    const applyRelTargetRules = (rel, isUnsafe) => {\n      const rules = ['noopener'];\n      const rels = rel ? rel.split(/\\s+/) : [];\n      const toString = rels => global$4.trim(rels.sort().join(' '));\n      const addTargetRules = rels => {\n        rels = removeTargetRules(rels);\n        return rels.length > 0 ? rels.concat(rules) : rules;\n      };\n      const removeTargetRules = rels => rels.filter(val => global$4.inArray(rules, val) === -1);\n      const newRels = isUnsafe ? addTargetRules(rels) : removeTargetRules(rels);\n      return newRels.length > 0 ? toString(newRels) : '';\n    };\n    const trimCaretContainers = text => text.replace(/\\uFEFF/g, '');\n    const getAnchorElement = (editor, selectedElm) => {\n      selectedElm = selectedElm || getLinksInSelection(editor.selection.getRng())[0] || editor.selection.getNode();\n      if (isImageFigure(selectedElm)) {\n        return Optional.from(editor.dom.select('a[href]', selectedElm)[0]);\n      } else {\n        return Optional.from(editor.dom.getParent(selectedElm, 'a[href]'));\n      }\n    };\n    const isInAnchor = (editor, selectedElm) => getAnchorElement(editor, selectedElm).isSome();\n    const getAnchorText = (selection, anchorElm) => {\n      const text = anchorElm.fold(() => selection.getContent({ format: 'text' }), anchorElm => anchorElm.innerText || anchorElm.textContent || '');\n      return trimCaretContainers(text);\n    };\n    const getLinksInSelection = rng => collectNodesInRange(rng, isLink);\n    const getLinks$2 = elements => global$4.grep(elements, isLink);\n    const hasLinks = elements => getLinks$2(elements).length > 0;\n    const hasLinksInSelection = rng => getLinksInSelection(rng).length > 0;\n    const isOnlyTextSelected = editor => {\n      const inlineTextElements = editor.schema.getTextInlineElements();\n      const isElement = elm => elm.nodeType === 1 && !isAnchor(elm) && !has(inlineTextElements, elm.nodeName.toLowerCase());\n      const isInBlockAnchor = getAnchorElement(editor).exists(anchor => anchor.hasAttribute('data-mce-block'));\n      if (isInBlockAnchor) {\n        return false;\n      }\n      const rng = editor.selection.getRng();\n      if (!rng.collapsed) {\n        const elements = collectNodesInRange(rng, isElement);\n        return elements.length === 0;\n      } else {\n        return true;\n      }\n    };\n    const isImageFigure = elm => isNonNullable(elm) && elm.nodeName === 'FIGURE' && /\\bimage\\b/i.test(elm.className);\n    const getLinkAttrs = data => {\n      const attrs = [\n        'title',\n        'rel',\n        'class',\n        'target'\n      ];\n      return foldl(attrs, (acc, key) => {\n        data[key].each(value => {\n          acc[key] = value.length > 0 ? value : null;\n        });\n        return acc;\n      }, { href: data.href });\n    };\n    const handleExternalTargets = (href, assumeExternalTargets) => {\n      if ((assumeExternalTargets === 'http' || assumeExternalTargets === 'https') && !hasProtocol(href)) {\n        return assumeExternalTargets + '://' + href;\n      }\n      return href;\n    };\n    const applyLinkOverrides = (editor, linkAttrs) => {\n      const newLinkAttrs = { ...linkAttrs };\n      if (getRelList(editor).length === 0 && !allowUnsafeLinkTarget(editor)) {\n        const newRel = applyRelTargetRules(newLinkAttrs.rel, newLinkAttrs.target === '_blank');\n        newLinkAttrs.rel = newRel ? newRel : null;\n      }\n      if (Optional.from(newLinkAttrs.target).isNone() && getTargetList(editor) === false) {\n        newLinkAttrs.target = getDefaultLinkTarget(editor);\n      }\n      newLinkAttrs.href = handleExternalTargets(newLinkAttrs.href, assumeExternalTargets(editor));\n      return newLinkAttrs;\n    };\n    const updateLink = (editor, anchorElm, text, linkAttrs) => {\n      text.each(text => {\n        if (has(anchorElm, 'innerText')) {\n          anchorElm.innerText = text;\n        } else {\n          anchorElm.textContent = text;\n        }\n      });\n      editor.dom.setAttribs(anchorElm, linkAttrs);\n      editor.selection.select(anchorElm);\n    };\n    const createLink = (editor, selectedElm, text, linkAttrs) => {\n      const dom = editor.dom;\n      if (isImageFigure(selectedElm)) {\n        linkImageFigure(dom, selectedElm, linkAttrs);\n      } else {\n        text.fold(() => {\n          editor.execCommand('mceInsertLink', false, linkAttrs);\n        }, text => {\n          editor.insertContent(dom.createHTML('a', linkAttrs, dom.encode(text)));\n        });\n      }\n    };\n    const linkDomMutation = (editor, attachState, data) => {\n      const selectedElm = editor.selection.getNode();\n      const anchorElm = getAnchorElement(editor, selectedElm);\n      const linkAttrs = applyLinkOverrides(editor, getLinkAttrs(data));\n      editor.undoManager.transact(() => {\n        if (data.href === attachState.href) {\n          attachState.attach();\n        }\n        anchorElm.fold(() => {\n          createLink(editor, selectedElm, data.text, linkAttrs);\n        }, elm => {\n          editor.focus();\n          updateLink(editor, elm, data.text, linkAttrs);\n        });\n      });\n    };\n    const unlinkSelection = editor => {\n      const dom = editor.dom, selection = editor.selection;\n      const bookmark = selection.getBookmark();\n      const rng = selection.getRng().cloneRange();\n      const startAnchorElm = dom.getParent(rng.startContainer, 'a[href]', editor.getBody());\n      const endAnchorElm = dom.getParent(rng.endContainer, 'a[href]', editor.getBody());\n      if (startAnchorElm) {\n        rng.setStartBefore(startAnchorElm);\n      }\n      if (endAnchorElm) {\n        rng.setEndAfter(endAnchorElm);\n      }\n      selection.setRng(rng);\n      editor.execCommand('unlink');\n      selection.moveToBookmark(bookmark);\n    };\n    const unlinkDomMutation = editor => {\n      editor.undoManager.transact(() => {\n        const node = editor.selection.getNode();\n        if (isImageFigure(node)) {\n          unlinkImageFigure(editor, node);\n        } else {\n          unlinkSelection(editor);\n        }\n        editor.focus();\n      });\n    };\n    const unwrapOptions = data => {\n      const {\n        class: cls,\n        href,\n        rel,\n        target,\n        text,\n        title\n      } = data;\n      return filter({\n        class: cls.getOrNull(),\n        href,\n        rel: rel.getOrNull(),\n        target: target.getOrNull(),\n        text: text.getOrNull(),\n        title: title.getOrNull()\n      }, (v, _k) => isNull(v) === false);\n    };\n    const sanitizeData = (editor, data) => {\n      const getOption = editor.options.get;\n      const uriOptions = {\n        allow_html_data_urls: getOption('allow_html_data_urls'),\n        allow_script_urls: getOption('allow_script_urls'),\n        allow_svg_data_urls: getOption('allow_svg_data_urls')\n      };\n      const href = data.href;\n      return {\n        ...data,\n        href: global$2.isDomSafe(href, 'a', uriOptions) ? href : ''\n      };\n    };\n    const link = (editor, attachState, data) => {\n      const sanitizedData = sanitizeData(editor, data);\n      editor.hasPlugin('rtc', true) ? editor.execCommand('createlink', false, unwrapOptions(sanitizedData)) : linkDomMutation(editor, attachState, sanitizedData);\n    };\n    const unlink = editor => {\n      editor.hasPlugin('rtc', true) ? editor.execCommand('unlink') : unlinkDomMutation(editor);\n    };\n    const unlinkImageFigure = (editor, fig) => {\n      var _a;\n      const img = editor.dom.select('img', fig)[0];\n      if (img) {\n        const a = editor.dom.getParents(img, 'a[href]', fig)[0];\n        if (a) {\n          (_a = a.parentNode) === null || _a === void 0 ? void 0 : _a.insertBefore(img, a);\n          editor.dom.remove(a);\n        }\n      }\n    };\n    const linkImageFigure = (dom, fig, attrs) => {\n      var _a;\n      const img = dom.select('img', fig)[0];\n      if (img) {\n        const a = dom.create('a', attrs);\n        (_a = img.parentNode) === null || _a === void 0 ? void 0 : _a.insertBefore(a, img);\n        a.appendChild(img);\n      }\n    };\n\n    const isListGroup = item => hasNonNullableKey(item, 'items');\n    const findTextByValue = (value, catalog) => findMap(catalog, item => {\n      if (isListGroup(item)) {\n        return findTextByValue(value, item.items);\n      } else {\n        return someIf(item.value === value, item);\n      }\n    });\n    const getDelta = (persistentText, fieldName, catalog, data) => {\n      const value = data[fieldName];\n      const hasPersistentText = persistentText.length > 0;\n      return value !== undefined ? findTextByValue(value, catalog).map(i => ({\n        url: {\n          value: i.value,\n          meta: {\n            text: hasPersistentText ? persistentText : i.text,\n            attach: noop\n          }\n        },\n        text: hasPersistentText ? persistentText : i.text\n      })) : Optional.none();\n    };\n    const findCatalog = (catalogs, fieldName) => {\n      if (fieldName === 'link') {\n        return catalogs.link;\n      } else if (fieldName === 'anchor') {\n        return catalogs.anchor;\n      } else {\n        return Optional.none();\n      }\n    };\n    const init = (initialData, linkCatalog) => {\n      const persistentData = {\n        text: initialData.text,\n        title: initialData.title\n      };\n      const getTitleFromUrlChange = url => {\n        var _a;\n        return someIf(persistentData.title.length <= 0, Optional.from((_a = url.meta) === null || _a === void 0 ? void 0 : _a.title).getOr(''));\n      };\n      const getTextFromUrlChange = url => {\n        var _a;\n        return someIf(persistentData.text.length <= 0, Optional.from((_a = url.meta) === null || _a === void 0 ? void 0 : _a.text).getOr(url.value));\n      };\n      const onUrlChange = data => {\n        const text = getTextFromUrlChange(data.url);\n        const title = getTitleFromUrlChange(data.url);\n        if (text.isSome() || title.isSome()) {\n          return Optional.some({\n            ...text.map(text => ({ text })).getOr({}),\n            ...title.map(title => ({ title })).getOr({})\n          });\n        } else {\n          return Optional.none();\n        }\n      };\n      const onCatalogChange = (data, change) => {\n        const catalog = findCatalog(linkCatalog, change).getOr([]);\n        return getDelta(persistentData.text, change, catalog, data);\n      };\n      const onChange = (getData, change) => {\n        const name = change.name;\n        if (name === 'url') {\n          return onUrlChange(getData());\n        } else if (contains([\n            'anchor',\n            'link'\n          ], name)) {\n          return onCatalogChange(getData(), name);\n        } else if (name === 'text' || name === 'title') {\n          persistentData[name] = getData()[name];\n          return Optional.none();\n        } else {\n          return Optional.none();\n        }\n      };\n      return { onChange };\n    };\n    const DialogChanges = {\n      init,\n      getDelta\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    const delayedConfirm = (editor, message, callback) => {\n      const rng = editor.selection.getRng();\n      global$1.setEditorTimeout(editor, () => {\n        editor.windowManager.confirm(message, state => {\n          editor.selection.setRng(rng);\n          callback(state);\n        });\n      });\n    };\n    const tryEmailTransform = data => {\n      const url = data.href;\n      const suggestMailTo = url.indexOf('@') > 0 && url.indexOf('/') === -1 && url.indexOf('mailto:') === -1;\n      return suggestMailTo ? Optional.some({\n        message: 'The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?',\n        preprocess: oldData => ({\n          ...oldData,\n          href: 'mailto:' + url\n        })\n      }) : Optional.none();\n    };\n    const tryProtocolTransform = (assumeExternalTargets, defaultLinkProtocol) => data => {\n      const url = data.href;\n      const suggestProtocol = assumeExternalTargets === 1 && !hasProtocol(url) || assumeExternalTargets === 0 && /^\\s*www(\\.|\\d\\.)/i.test(url);\n      return suggestProtocol ? Optional.some({\n        message: `The URL you entered seems to be an external link. Do you want to add the required ${ defaultLinkProtocol }:// prefix?`,\n        preprocess: oldData => ({\n          ...oldData,\n          href: defaultLinkProtocol + '://' + url\n        })\n      }) : Optional.none();\n    };\n    const preprocess = (editor, data) => findMap([\n      tryEmailTransform,\n      tryProtocolTransform(assumeExternalTargets(editor), getDefaultLinkProtocol(editor))\n    ], f => f(data)).fold(() => Promise.resolve(data), transform => new Promise(callback => {\n      delayedConfirm(editor, transform.message, state => {\n        callback(state ? transform.preprocess(data) : data);\n      });\n    }));\n    const DialogConfirms = { preprocess };\n\n    const getAnchors = editor => {\n      const anchorNodes = editor.dom.select('a:not([href])');\n      const anchors = bind(anchorNodes, anchor => {\n        const id = anchor.name || anchor.id;\n        return id ? [{\n            text: id,\n            value: '#' + id\n          }] : [];\n      });\n      return anchors.length > 0 ? Optional.some([{\n          text: 'None',\n          value: ''\n        }].concat(anchors)) : Optional.none();\n    };\n    const AnchorListOptions = { getAnchors };\n\n    const getClasses = editor => {\n      const list = getLinkClassList(editor);\n      if (list.length > 0) {\n        return ListOptions.sanitize(list);\n      }\n      return Optional.none();\n    };\n    const ClassListOptions = { getClasses };\n\n    const parseJson = text => {\n      try {\n        return Optional.some(JSON.parse(text));\n      } catch (err) {\n        return Optional.none();\n      }\n    };\n    const getLinks$1 = editor => {\n      const extractor = item => editor.convertURL(item.value || item.url || '', 'href');\n      const linkList = getLinkList(editor);\n      return new Promise(resolve => {\n        if (isString(linkList)) {\n          fetch(linkList).then(res => res.ok ? res.text().then(parseJson) : Promise.reject()).then(resolve, () => resolve(Optional.none()));\n        } else if (isFunction(linkList)) {\n          linkList(output => resolve(Optional.some(output)));\n        } else {\n          resolve(Optional.from(linkList));\n        }\n      }).then(optItems => optItems.bind(ListOptions.sanitizeWith(extractor)).map(items => {\n        if (items.length > 0) {\n          const noneItem = [{\n              text: 'None',\n              value: ''\n            }];\n          return noneItem.concat(items);\n        } else {\n          return items;\n        }\n      }));\n    };\n    const LinkListOptions = { getLinks: getLinks$1 };\n\n    const getRels = (editor, initialTarget) => {\n      const list = getRelList(editor);\n      if (list.length > 0) {\n        const isTargetBlank = is(initialTarget, '_blank');\n        const enforceSafe = allowUnsafeLinkTarget(editor) === false;\n        const safeRelExtractor = item => applyRelTargetRules(ListOptions.getValue(item), isTargetBlank);\n        const sanitizer = enforceSafe ? ListOptions.sanitizeWith(safeRelExtractor) : ListOptions.sanitize;\n        return sanitizer(list);\n      }\n      return Optional.none();\n    };\n    const RelOptions = { getRels };\n\n    const fallbacks = [\n      {\n        text: 'Current window',\n        value: ''\n      },\n      {\n        text: 'New window',\n        value: '_blank'\n      }\n    ];\n    const getTargets = editor => {\n      const list = getTargetList(editor);\n      if (isArray(list)) {\n        return ListOptions.sanitize(list).orThunk(() => Optional.some(fallbacks));\n      } else if (list === false) {\n        return Optional.none();\n      }\n      return Optional.some(fallbacks);\n    };\n    const TargetOptions = { getTargets };\n\n    const nonEmptyAttr = (dom, elem, name) => {\n      const val = dom.getAttrib(elem, name);\n      return val !== null && val.length > 0 ? Optional.some(val) : Optional.none();\n    };\n    const extractFromAnchor = (editor, anchor) => {\n      const dom = editor.dom;\n      const onlyText = isOnlyTextSelected(editor);\n      const text = onlyText ? Optional.some(getAnchorText(editor.selection, anchor)) : Optional.none();\n      const url = anchor.bind(anchorElm => Optional.from(dom.getAttrib(anchorElm, 'href')));\n      const target = anchor.bind(anchorElm => Optional.from(dom.getAttrib(anchorElm, 'target')));\n      const rel = anchor.bind(anchorElm => nonEmptyAttr(dom, anchorElm, 'rel'));\n      const linkClass = anchor.bind(anchorElm => nonEmptyAttr(dom, anchorElm, 'class'));\n      const title = anchor.bind(anchorElm => nonEmptyAttr(dom, anchorElm, 'title'));\n      return {\n        url,\n        text,\n        title,\n        target,\n        rel,\n        linkClass\n      };\n    };\n    const collect = (editor, linkNode) => LinkListOptions.getLinks(editor).then(links => {\n      const anchor = extractFromAnchor(editor, linkNode);\n      return {\n        anchor,\n        catalogs: {\n          targets: TargetOptions.getTargets(editor),\n          rels: RelOptions.getRels(editor, anchor.target),\n          classes: ClassListOptions.getClasses(editor),\n          anchor: AnchorListOptions.getAnchors(editor),\n          link: links\n        },\n        optNode: linkNode,\n        flags: { titleEnabled: shouldShowLinkTitle(editor) }\n      };\n    });\n    const DialogInfo = { collect };\n\n    const handleSubmit = (editor, info) => api => {\n      const data = api.getData();\n      if (!data.url.value) {\n        unlink(editor);\n        api.close();\n        return;\n      }\n      const getChangedValue = key => Optional.from(data[key]).filter(value => !is(info.anchor[key], value));\n      const changedData = {\n        href: data.url.value,\n        text: getChangedValue('text'),\n        target: getChangedValue('target'),\n        rel: getChangedValue('rel'),\n        class: getChangedValue('linkClass'),\n        title: getChangedValue('title')\n      };\n      const attachState = {\n        href: data.url.value,\n        attach: data.url.meta !== undefined && data.url.meta.attach ? data.url.meta.attach : noop\n      };\n      DialogConfirms.preprocess(editor, changedData).then(pData => {\n        link(editor, attachState, pData);\n      });\n      api.close();\n    };\n    const collectData = editor => {\n      const anchorNode = getAnchorElement(editor);\n      return DialogInfo.collect(editor, anchorNode);\n    };\n    const getInitialData = (info, defaultTarget) => {\n      const anchor = info.anchor;\n      const url = anchor.url.getOr('');\n      return {\n        url: {\n          value: url,\n          meta: { original: { value: url } }\n        },\n        text: anchor.text.getOr(''),\n        title: anchor.title.getOr(''),\n        anchor: url,\n        link: url,\n        rel: anchor.rel.getOr(''),\n        target: anchor.target.or(defaultTarget).getOr(''),\n        linkClass: anchor.linkClass.getOr('')\n      };\n    };\n    const makeDialog = (settings, onSubmit, editor) => {\n      const urlInput = [{\n          name: 'url',\n          type: 'urlinput',\n          filetype: 'file',\n          label: 'URL',\n          picker_text: 'Browse links'\n        }];\n      const displayText = settings.anchor.text.map(() => ({\n        name: 'text',\n        type: 'input',\n        label: 'Text to display'\n      })).toArray();\n      const titleText = settings.flags.titleEnabled ? [{\n          name: 'title',\n          type: 'input',\n          label: 'Title'\n        }] : [];\n      const defaultTarget = Optional.from(getDefaultLinkTarget(editor));\n      const initialData = getInitialData(settings, defaultTarget);\n      const catalogs = settings.catalogs;\n      const dialogDelta = DialogChanges.init(initialData, catalogs);\n      const body = {\n        type: 'panel',\n        items: flatten([\n          urlInput,\n          displayText,\n          titleText,\n          cat([\n            catalogs.anchor.map(ListOptions.createUi('anchor', 'Anchors')),\n            catalogs.rels.map(ListOptions.createUi('rel', 'Rel')),\n            catalogs.targets.map(ListOptions.createUi('target', 'Open link in...')),\n            catalogs.link.map(ListOptions.createUi('link', 'Link list')),\n            catalogs.classes.map(ListOptions.createUi('linkClass', 'Class'))\n          ])\n        ])\n      };\n      return {\n        title: 'Insert/Edit Link',\n        size: 'normal',\n        body,\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData,\n        onChange: (api, {name}) => {\n          dialogDelta.onChange(api.getData, { name }).each(newData => {\n            api.setData(newData);\n          });\n        },\n        onSubmit\n      };\n    };\n    const open$1 = editor => {\n      const data = collectData(editor);\n      data.then(info => {\n        const onSubmit = handleSubmit(editor, info);\n        return makeDialog(info, onSubmit, editor);\n      }).then(spec => {\n        editor.windowManager.open(spec);\n      });\n    };\n\n    const register = editor => {\n      editor.addCommand('mceLink', (_ui, value) => {\n        if ((value === null || value === void 0 ? void 0 : value.dialog) === true || !useQuickLink(editor)) {\n          open$1(editor);\n        } else {\n          editor.dispatch('contexttoolbar-show', { toolbarKey: 'quicklink' });\n        }\n      });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    const appendClickRemove = (link, evt) => {\n      document.body.appendChild(link);\n      link.dispatchEvent(evt);\n      document.body.removeChild(link);\n    };\n    const open = url => {\n      const link = document.createElement('a');\n      link.target = '_blank';\n      link.href = url;\n      link.rel = 'noreferrer noopener';\n      const evt = document.createEvent('MouseEvents');\n      evt.initMouseEvent('click', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n      appendClickRemove(link, evt);\n    };\n\n    const isSelectionOnImageWithEmbeddedLink = editor => {\n      const rng = editor.selection.getRng();\n      const node = rng.startContainer;\n      return isLink(node) && rng.startContainer === rng.endContainer && editor.dom.select('img', node).length === 1;\n    };\n    const getLinks = editor => editor.selection.isCollapsed() || isSelectionOnImageWithEmbeddedLink(editor) ? getLinks$2(editor.dom.getParents(editor.selection.getStart())) : getLinksInSelection(editor.selection.getRng());\n    const getSelectedLink = editor => getLinks(editor)[0];\n    const hasOnlyAltModifier = e => {\n      return e.altKey === true && e.shiftKey === false && e.ctrlKey === false && e.metaKey === false;\n    };\n    const gotoLink = (editor, a) => {\n      if (a) {\n        const href = getHref(a);\n        if (/^#/.test(href)) {\n          const targetEl = editor.dom.select(href);\n          if (targetEl.length) {\n            editor.selection.scrollIntoView(targetEl[0], true);\n          }\n        } else {\n          open(a.href);\n        }\n      }\n    };\n    const openDialog = editor => () => {\n      editor.execCommand('mceLink', false, { dialog: true });\n    };\n    const gotoSelectedLink = editor => () => {\n      gotoLink(editor, getSelectedLink(editor));\n    };\n    const setupGotoLinks = editor => {\n      editor.on('click', e => {\n        const links = getLinks$2(editor.dom.getParents(e.target));\n        if (links.length === 1 && global.metaKeyPressed(e)) {\n          e.preventDefault();\n          gotoLink(editor, links[0]);\n        }\n      });\n      editor.on('keydown', e => {\n        if (!e.isDefaultPrevented() && e.keyCode === 13 && hasOnlyAltModifier(e)) {\n          const link = getSelectedLink(editor);\n          if (link) {\n            e.preventDefault();\n            gotoLink(editor, link);\n          }\n        }\n      });\n    };\n    const toggleState = (editor, toggler) => {\n      editor.on('NodeChange', toggler);\n      return () => editor.off('NodeChange', toggler);\n    };\n    const toggleLinkState = editor => api => {\n      const updateState = () => {\n        api.setActive(!editor.mode.isReadOnly() && isInAnchor(editor, editor.selection.getNode()));\n        api.setEnabled(editor.selection.isEditable());\n      };\n      updateState();\n      return toggleState(editor, updateState);\n    };\n    const toggleLinkMenuState = editor => api => {\n      const updateState = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      updateState();\n      return toggleState(editor, updateState);\n    };\n    const hasExactlyOneLinkInSelection = editor => getLinks(editor).length === 1;\n    const toggleGotoLinkState = editor => api => {\n      const updateState = () => api.setEnabled(hasExactlyOneLinkInSelection(editor));\n      updateState();\n      return toggleState(editor, updateState);\n    };\n    const toggleUnlinkState = editor => api => {\n      const hasLinks$1 = parents => hasLinks(parents) || hasLinksInSelection(editor.selection.getRng());\n      const parents = editor.dom.getParents(editor.selection.getStart());\n      const updateEnabled = parents => {\n        api.setEnabled(hasLinks$1(parents) && editor.selection.isEditable());\n      };\n      updateEnabled(parents);\n      return toggleState(editor, e => updateEnabled(e.parents));\n    };\n\n    const setup = editor => {\n      editor.addShortcut('Meta+K', '', () => {\n        editor.execCommand('mceLink');\n      });\n    };\n\n    const setupButtons = editor => {\n      editor.ui.registry.addToggleButton('link', {\n        icon: 'link',\n        tooltip: 'Insert/edit link',\n        onAction: openDialog(editor),\n        onSetup: toggleLinkState(editor),\n        shortcut: 'Meta+K'\n      });\n      editor.ui.registry.addButton('openlink', {\n        icon: 'new-tab',\n        tooltip: 'Open link',\n        onAction: gotoSelectedLink(editor),\n        onSetup: toggleGotoLinkState(editor)\n      });\n      editor.ui.registry.addButton('unlink', {\n        icon: 'unlink',\n        tooltip: 'Remove link',\n        onAction: () => unlink(editor),\n        onSetup: toggleUnlinkState(editor)\n      });\n    };\n    const setupMenuItems = editor => {\n      editor.ui.registry.addMenuItem('openlink', {\n        text: 'Open link',\n        icon: 'new-tab',\n        onAction: gotoSelectedLink(editor),\n        onSetup: toggleGotoLinkState(editor)\n      });\n      editor.ui.registry.addMenuItem('link', {\n        icon: 'link',\n        text: 'Link...',\n        shortcut: 'Meta+K',\n        onSetup: toggleLinkMenuState(editor),\n        onAction: openDialog(editor)\n      });\n      editor.ui.registry.addMenuItem('unlink', {\n        icon: 'unlink',\n        text: 'Remove link',\n        onAction: () => unlink(editor),\n        onSetup: toggleUnlinkState(editor)\n      });\n    };\n    const setupContextMenu = editor => {\n      const inLink = 'link unlink openlink';\n      const noLink = 'link';\n      editor.ui.registry.addContextMenu('link', {\n        update: element => {\n          const isEditable = editor.dom.isEditable(element);\n          if (!isEditable) {\n            return '';\n          }\n          return hasLinks(editor.dom.getParents(element, 'a')) ? inLink : noLink;\n        }\n      });\n    };\n    const setupContextToolbars = editor => {\n      const collapseSelectionToEnd = editor => {\n        editor.selection.collapse(false);\n      };\n      const onSetupLink = buttonApi => {\n        const node = editor.selection.getNode();\n        buttonApi.setEnabled(isInAnchor(editor, node));\n        return noop;\n      };\n      const getLinkText = value => {\n        const anchor = getAnchorElement(editor);\n        const onlyText = isOnlyTextSelected(editor);\n        if (anchor.isNone() && onlyText) {\n          const text = getAnchorText(editor.selection, anchor);\n          return someIf(text.length === 0, value);\n        } else {\n          return Optional.none();\n        }\n      };\n      editor.ui.registry.addContextForm('quicklink', {\n        launch: {\n          type: 'contextformtogglebutton',\n          icon: 'link',\n          tooltip: 'Link',\n          onSetup: toggleLinkState(editor)\n        },\n        label: 'Link',\n        predicate: node => hasContextToolbar(editor) && isInAnchor(editor, node),\n        initValue: () => {\n          const elm = getAnchorElement(editor);\n          return elm.fold(constant(''), getHref);\n        },\n        commands: [\n          {\n            type: 'contextformtogglebutton',\n            icon: 'link',\n            tooltip: 'Link',\n            primary: true,\n            onSetup: buttonApi => {\n              const node = editor.selection.getNode();\n              buttonApi.setActive(isInAnchor(editor, node));\n              return toggleLinkState(editor)(buttonApi);\n            },\n            onAction: formApi => {\n              const value = formApi.getValue();\n              const text = getLinkText(value);\n              const attachState = {\n                href: value,\n                attach: noop\n              };\n              link(editor, attachState, {\n                href: value,\n                text,\n                title: Optional.none(),\n                rel: Optional.none(),\n                target: Optional.from(getDefaultLinkTarget(editor)),\n                class: Optional.none()\n              });\n              collapseSelectionToEnd(editor);\n              formApi.hide();\n            }\n          },\n          {\n            type: 'contextformbutton',\n            icon: 'unlink',\n            tooltip: 'Remove link',\n            onSetup: onSetupLink,\n            onAction: formApi => {\n              unlink(editor);\n              formApi.hide();\n            }\n          },\n          {\n            type: 'contextformbutton',\n            icon: 'new-tab',\n            tooltip: 'Open link',\n            onSetup: onSetupLink,\n            onAction: formApi => {\n              gotoSelectedLink(editor)();\n              formApi.hide();\n            }\n          }\n        ]\n      });\n    };\n\n    var Plugin = () => {\n      global$5.add('link', editor => {\n        register$1(editor);\n        setupButtons(editor);\n        setupMenuItems(editor);\n        setupContextMenu(editor);\n        setupContextToolbars(editor);\n        setupGotoLinks(editor);\n        register(editor);\n        setup(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,QAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,QAAI;AACJ,QAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,aAAO;AAAA,IACT,OAAO;AACL,eAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,IAC7F;AAAA,EACF;AACA,QAAM,SAAS,OAAK;AAClB,UAAM,IAAI,OAAO;AACjB,QAAI,MAAM,MAAM;AACd,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,SAAS,UAAQ,WAAS,OAAO,KAAK,MAAM;AAClD,QAAM,eAAe,UAAQ,WAAS,OAAO,UAAU;AACvD,QAAM,KAAK,OAAK,OAAK,MAAM;AAC3B,QAAM,WAAW,OAAO,QAAQ;AAChC,QAAM,WAAW,OAAO,QAAQ;AAChC,QAAM,UAAU,OAAO,OAAO;AAC9B,QAAM,SAAS,GAAG,IAAI;AACtB,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,QAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,QAAM,aAAa,aAAa,UAAU;AAC1C,QAAM,YAAY,CAAC,OAAO,SAAS;AACjC,QAAI,QAAQ,KAAK,GAAG;AAClB,eAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,EAAE,GAAG;AAChD,YAAI,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG;AACnB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,MAAM;AAAA,EACnB;AACA,QAAM,WAAW,WAAS;AACxB,WAAO,MAAM;AACX,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,eAAe,CAAC,GAAG,MAAM;AAC7B,WAAO,MAAM;AAAA,EACf;AAAA,EAEA,MAAM,SAAS;AAAA,IACb,YAAY,KAAK,OAAO;AACtB,WAAK,MAAM;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,IACA,OAAO,KAAK,OAAO;AACjB,aAAO,IAAI,SAAS,MAAM,KAAK;AAAA,IACjC;AAAA,IACA,OAAO,OAAO;AACZ,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,KAAK,QAAQ,QAAQ;AACnB,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AACP,aAAO,KAAK;AAAA,IACd;AAAA,IACA,SAAS;AACP,aAAO,CAAC,KAAK;AAAA,IACf;AAAA,IACA,IAAI,QAAQ;AACV,UAAI,KAAK,KAAK;AACZ,eAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,MACzC,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IACzC;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IAC1C;AAAA,IACA,OAAO,WAAW;AAChB,UAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,MAAM,aAAa;AACjB,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,GAAG,aAAa;AACd,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B;AAAA,IACA,WAAW,OAAO;AAChB,aAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,IACvC;AAAA,IACA,QAAQ,OAAO;AACb,aAAO,KAAK,MAAM,OAAO,MAAM;AAAA,IACjC;AAAA,IACA,SAAS,SAAS;AAChB,UAAI,CAAC,KAAK,KAAK;AACb,cAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,MAC9F,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,IACA,OAAO,KAAK,OAAO;AACjB,aAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,IACrE;AAAA,IACA,YAAY;AACV,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,iBAAiB;AACf,aAAO,KAAK;AAAA,IACd;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,IACA,UAAU;AACR,aAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,IACpC;AAAA,IACA,WAAW;AACT,aAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,IAC9C;AAAA,EACF;AACA,WAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,QAAM,gBAAgB,MAAM,UAAU;AACtC,QAAM,aAAa,MAAM,UAAU;AACnC,QAAM,aAAa,CAAC,IAAI,MAAM,cAAc,KAAK,IAAI,CAAC;AACtD,QAAM,WAAW,CAAC,IAAI,MAAM,WAAW,IAAI,CAAC,IAAI;AAChD,QAAM,MAAM,CAAC,IAAI,MAAM;AACrB,UAAM,MAAM,GAAG;AACf,UAAM,IAAI,IAAI,MAAM,GAAG;AACvB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,IACf;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC,IAAI,MAAM;AACxB,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,WAAO,IAAI,CAAC,GAAG,MAAM;AACnB,YAAM,EAAE,KAAK,GAAG,CAAC;AAAA,IACnB,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,UAAU,QAAM;AACpB,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,UAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG;AACnB,cAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,MAC7E;AACA,iBAAW,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AACA,QAAM,OAAO,CAAC,IAAI,MAAM,QAAQ,IAAI,IAAI,CAAC,CAAC;AAC1C,QAAM,UAAU,CAAC,KAAK,MAAM;AAC1B,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAM,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;AACrB,UAAI,EAAE,OAAO,GAAG;AACd,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AAEA,QAAM,KAAK,CAAC,KAAK,KAAK,aAAa,iBAAiB,IAAI,OAAO,UAAQ,WAAW,MAAM,GAAG,CAAC;AAC5F,QAAM,MAAM,SAAO;AACjB,UAAM,IAAI,CAAC;AACX,UAAM,OAAO,OAAK;AAChB,QAAE,KAAK,CAAC;AAAA,IACV;AACA,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAI,CAAC,EAAE,KAAK,IAAI;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC,GAAG,MAAM,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAE9D,QAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,QAAM,aAAa,YAAU;AAC3B,UAAM,iBAAiB,OAAO,QAAQ;AACtC,mBAAe,gCAAgC;AAAA,MAC7C,WAAW,WAAS;AAClB,cAAM,QAAQ,SAAS,KAAK,KAAK,UAAU,KAAK;AAChD,YAAI,OAAO;AACT,cAAI,UAAU,MAAM;AAClB,mBAAO;AAAA,cACL,OAAO;AAAA,cACP;AAAA,YACF;AAAA,UACF,WAAW,UAAU,UAAU,UAAU,SAAS;AAChD,mBAAO;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,cACL,OAAO;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,wBAAwB;AAAA,MACrC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,aAAa,EAAE,WAAW,WAAS,SAAS,KAAK,KAAK,WAAW,KAAK,KAAK,UAAU,OAAO,QAAQ,EAAE,CAAC;AACtH,mBAAe,uBAAuB,EAAE,WAAW,SAAS,CAAC;AAC7D,mBAAe,yBAAyB;AAAA,MACtC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,oBAAoB;AAAA,MACjC,WAAW,WAAS,UAAU,KAAK,KAAK,UAAU,OAAO,QAAQ;AAAA,MACjE,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,iBAAiB;AAAA,MAC9B,WAAW;AAAA,MACX,SAAS,CAAC;AAAA,IACZ,CAAC;AACD,mBAAe,mBAAmB;AAAA,MAChC,WAAW;AAAA,MACX,SAAS,CAAC;AAAA,IACZ,CAAC;AACD,mBAAe,cAAc;AAAA,MAC3B,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,4BAA4B;AAAA,MACzC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,kBAAkB;AAAA,MAC/B,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,QAAM,wBAAwB,OAAO,8BAA8B;AACnE,QAAM,oBAAoB,OAAO,sBAAsB;AACvD,QAAM,cAAc,OAAO,WAAW;AACtC,QAAM,uBAAuB,OAAO,qBAAqB;AACzD,QAAM,yBAAyB,OAAO,uBAAuB;AAC7D,QAAM,gBAAgB,OAAO,kBAAkB;AAC/C,QAAM,aAAa,OAAO,eAAe;AACzC,QAAM,mBAAmB,OAAO,iBAAiB;AACjD,QAAM,sBAAsB,OAAO,YAAY;AAC/C,QAAM,wBAAwB,OAAO,0BAA0B;AAC/D,QAAM,eAAe,OAAO,gBAAgB;AAE5C,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE9D,QAAM,WAAW,UAAQ,SAAS,KAAK,KAAK,IAAI,KAAK,QAAQ;AAC7D,QAAM,UAAU,UAAQ;AACtB,QAAI,SAAS,KAAK,IAAI,GAAG;AACvB,aAAO,KAAK;AAAA,IACd,WAAW,SAAS,KAAK,KAAK,GAAG;AAC/B,aAAO,KAAK;AAAA,IACd,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,eAAe,CAAC,MAAM,iBAAiB;AAC3C,UAAM,MAAM,CAAC;AACb,aAAS,KAAK,MAAM,UAAQ;AAC1B,YAAM,OAAO,QAAQ,IAAI;AACzB,UAAI,KAAK,SAAS,QAAW;AAC3B,cAAM,QAAQ,aAAa,KAAK,MAAM,YAAY;AAClD,YAAI,KAAK;AAAA,UACP;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,cAAM,QAAQ,aAAa,IAAI;AAC/B,YAAI,KAAK;AAAA,UACP;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,eAAe,CAAC,YAAY,aAAa,UAAQ,SAAS,KAAK,IAAI,EAAE,IAAI,CAAAA,UAAQ,aAAaA,OAAM,SAAS,CAAC;AACpH,QAAM,WAAW,UAAQ,aAAa,QAAQ,EAAE,IAAI;AACpD,QAAM,WAAW,CAAC,MAAM,UAAU,YAAU;AAAA,IAC1C;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACA,QAAM,cAAc;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,OAAO,OAAO;AACpB,QAAM,iBAAiB,OAAO;AAC9B,QAAM,OAAO,CAAC,KAAK,MAAM;AACvB,UAAM,QAAQ,KAAK,GAAG;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,IAAI,IAAI,CAAC;AACf,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,SAAS,OAAK,CAAC,GAAG,MAAM;AAC5B,MAAE,CAAC,IAAI;AAAA,EACT;AACA,QAAM,iBAAiB,CAAC,KAAK,MAAM,QAAQ,YAAY;AACrD,SAAK,KAAK,CAAC,GAAG,MAAM;AAClB,OAAC,KAAK,GAAG,CAAC,IAAI,SAAS,SAAS,GAAG,CAAC;AAAA,IACtC,CAAC;AAAA,EACH;AACA,QAAM,SAAS,CAAC,KAAK,SAAS;AAC5B,UAAM,IAAI,CAAC;AACX,mBAAe,KAAK,MAAM,OAAO,CAAC,GAAG,IAAI;AACzC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,CAAC,KAAK,QAAQ,eAAe,KAAK,KAAK,GAAG;AACtD,QAAM,oBAAoB,CAAC,KAAK,QAAQ,IAAI,KAAK,GAAG,KAAK,IAAI,GAAG,MAAM,UAAa,IAAI,GAAG,MAAM;AAEhG,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,wBAAwB;AAElE,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,kBAAkB;AAE5D,QAAM,WAAW,SAAO,cAAc,GAAG,KAAK,IAAI,SAAS,YAAY,MAAM;AAC7E,QAAM,SAAS,SAAO,SAAS,GAAG,KAAK,CAAC,CAAC,QAAQ,GAAG;AACpD,QAAM,sBAAsB,CAAC,KAAK,cAAc;AAC9C,QAAI,IAAI,WAAW;AACjB,aAAO,CAAC;AAAA,IACV,OAAO;AACL,YAAM,WAAW,IAAI,cAAc;AACnC,YAAM,aAAa,SAAS;AAC5B,YAAM,SAAS,IAAI,SAAS,YAAY,QAAQ;AAChD,YAAM,WAAW,CAAC;AAClB,UAAI,UAAU;AACd,SAAG;AACD,YAAI,UAAU,OAAO,GAAG;AACtB,mBAAS,KAAK,OAAO;AAAA,QACvB;AAAA,MACF,SAAS,UAAU,OAAO,KAAK;AAC/B,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,cAAc,SAAO,SAAS,KAAK,GAAG;AAC5C,QAAM,UAAU,SAAO;AACrB,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,IAAI,aAAa,eAAe,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,aAAa,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,EACpJ;AACA,QAAM,sBAAsB,CAAC,KAAK,aAAa;AAC7C,UAAM,QAAQ,CAAC,UAAU;AACzB,UAAM,OAAO,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC;AACvC,UAAM,WAAW,CAAAC,UAAQ,SAAS,KAAKA,MAAK,KAAK,EAAE,KAAK,GAAG,CAAC;AAC5D,UAAM,iBAAiB,CAAAA,UAAQ;AAC7B,MAAAA,QAAO,kBAAkBA,KAAI;AAC7B,aAAOA,MAAK,SAAS,IAAIA,MAAK,OAAO,KAAK,IAAI;AAAA,IAChD;AACA,UAAM,oBAAoB,CAAAA,UAAQA,MAAK,OAAO,SAAO,SAAS,QAAQ,OAAO,GAAG,MAAM,EAAE;AACxF,UAAM,UAAU,WAAW,eAAe,IAAI,IAAI,kBAAkB,IAAI;AACxE,WAAO,QAAQ,SAAS,IAAI,SAAS,OAAO,IAAI;AAAA,EAClD;AACA,QAAM,sBAAsB,UAAQ,KAAK,QAAQ,WAAW,EAAE;AAC9D,QAAM,mBAAmB,CAAC,QAAQ,gBAAgB;AAChD,kBAAc,eAAe,oBAAoB,OAAO,UAAU,OAAO,CAAC,EAAE,CAAC,KAAK,OAAO,UAAU,QAAQ;AAC3G,QAAI,cAAc,WAAW,GAAG;AAC9B,aAAO,SAAS,KAAK,OAAO,IAAI,OAAO,WAAW,WAAW,EAAE,CAAC,CAAC;AAAA,IACnE,OAAO;AACL,aAAO,SAAS,KAAK,OAAO,IAAI,UAAU,aAAa,SAAS,CAAC;AAAA,IACnE;AAAA,EACF;AACA,QAAM,aAAa,CAAC,QAAQ,gBAAgB,iBAAiB,QAAQ,WAAW,EAAE,OAAO;AACzF,QAAM,gBAAgB,CAAC,WAAW,cAAc;AAC9C,UAAM,OAAO,UAAU,KAAK,MAAM,UAAU,WAAW,EAAE,QAAQ,OAAO,CAAC,GAAG,CAAAC,eAAaA,WAAU,aAAaA,WAAU,eAAe,EAAE;AAC3I,WAAO,oBAAoB,IAAI;AAAA,EACjC;AACA,QAAM,sBAAsB,SAAO,oBAAoB,KAAK,MAAM;AAClE,QAAM,aAAa,cAAY,SAAS,KAAK,UAAU,MAAM;AAC7D,QAAM,WAAW,cAAY,WAAW,QAAQ,EAAE,SAAS;AAC3D,QAAM,sBAAsB,SAAO,oBAAoB,GAAG,EAAE,SAAS;AACrE,QAAM,qBAAqB,YAAU;AACnC,UAAM,qBAAqB,OAAO,OAAO,sBAAsB;AAC/D,UAAM,YAAY,SAAO,IAAI,aAAa,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,oBAAoB,IAAI,SAAS,YAAY,CAAC;AACpH,UAAM,kBAAkB,iBAAiB,MAAM,EAAE,OAAO,YAAU,OAAO,aAAa,gBAAgB,CAAC;AACvG,QAAI,iBAAiB;AACnB,aAAO;AAAA,IACT;AACA,UAAM,MAAM,OAAO,UAAU,OAAO;AACpC,QAAI,CAAC,IAAI,WAAW;AAClB,YAAM,WAAW,oBAAoB,KAAK,SAAS;AACnD,aAAO,SAAS,WAAW;AAAA,IAC7B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,gBAAgB,SAAO,cAAc,GAAG,KAAK,IAAI,aAAa,YAAY,aAAa,KAAK,IAAI,SAAS;AAC/G,QAAM,eAAe,UAAQ;AAC3B,UAAM,QAAQ;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,OAAO,CAAC,KAAK,QAAQ;AAChC,WAAK,GAAG,EAAE,KAAK,WAAS;AACtB,YAAI,GAAG,IAAI,MAAM,SAAS,IAAI,QAAQ;AAAA,MACxC,CAAC;AACD,aAAO;AAAA,IACT,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC;AAAA,EACxB;AACA,QAAM,wBAAwB,CAAC,MAAMC,2BAA0B;AAC7D,SAAKA,2BAA0B,UAAUA,2BAA0B,YAAY,CAAC,YAAY,IAAI,GAAG;AACjG,aAAOA,yBAAwB,QAAQ;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AACA,QAAM,qBAAqB,CAAC,QAAQ,cAAc;AAChD,UAAM,eAAe,EAAE,GAAG,UAAU;AACpC,QAAI,WAAW,MAAM,EAAE,WAAW,KAAK,CAAC,sBAAsB,MAAM,GAAG;AACrE,YAAM,SAAS,oBAAoB,aAAa,KAAK,aAAa,WAAW,QAAQ;AACrF,mBAAa,MAAM,SAAS,SAAS;AAAA,IACvC;AACA,QAAI,SAAS,KAAK,aAAa,MAAM,EAAE,OAAO,KAAK,cAAc,MAAM,MAAM,OAAO;AAClF,mBAAa,SAAS,qBAAqB,MAAM;AAAA,IACnD;AACA,iBAAa,OAAO,sBAAsB,aAAa,MAAM,sBAAsB,MAAM,CAAC;AAC1F,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC,QAAQ,WAAW,MAAM,cAAc;AACzD,SAAK,KAAK,CAAAC,UAAQ;AAChB,UAAI,IAAI,WAAW,WAAW,GAAG;AAC/B,kBAAU,YAAYA;AAAA,MACxB,OAAO;AACL,kBAAU,cAAcA;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,WAAO,IAAI,WAAW,WAAW,SAAS;AAC1C,WAAO,UAAU,OAAO,SAAS;AAAA,EACnC;AACA,QAAM,aAAa,CAAC,QAAQ,aAAa,MAAM,cAAc;AAC3D,UAAM,MAAM,OAAO;AACnB,QAAI,cAAc,WAAW,GAAG;AAC9B,sBAAgB,KAAK,aAAa,SAAS;AAAA,IAC7C,OAAO;AACL,WAAK,KAAK,MAAM;AACd,eAAO,YAAY,iBAAiB,OAAO,SAAS;AAAA,MACtD,GAAG,CAAAA,UAAQ;AACT,eAAO,cAAc,IAAI,WAAW,KAAK,WAAW,IAAI,OAAOA,KAAI,CAAC,CAAC;AAAA,MACvE,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,kBAAkB,CAAC,QAAQ,aAAa,SAAS;AACrD,UAAM,cAAc,OAAO,UAAU,QAAQ;AAC7C,UAAM,YAAY,iBAAiB,QAAQ,WAAW;AACtD,UAAM,YAAY,mBAAmB,QAAQ,aAAa,IAAI,CAAC;AAC/D,WAAO,YAAY,SAAS,MAAM;AAChC,UAAI,KAAK,SAAS,YAAY,MAAM;AAClC,oBAAY,OAAO;AAAA,MACrB;AACA,gBAAU,KAAK,MAAM;AACnB,mBAAW,QAAQ,aAAa,KAAK,MAAM,SAAS;AAAA,MACtD,GAAG,SAAO;AACR,eAAO,MAAM;AACb,mBAAW,QAAQ,KAAK,KAAK,MAAM,SAAS;AAAA,MAC9C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,kBAAkB,YAAU;AAChC,UAAM,MAAM,OAAO,KAAK,YAAY,OAAO;AAC3C,UAAM,WAAW,UAAU,YAAY;AACvC,UAAM,MAAM,UAAU,OAAO,EAAE,WAAW;AAC1C,UAAM,iBAAiB,IAAI,UAAU,IAAI,gBAAgB,WAAW,OAAO,QAAQ,CAAC;AACpF,UAAM,eAAe,IAAI,UAAU,IAAI,cAAc,WAAW,OAAO,QAAQ,CAAC;AAChF,QAAI,gBAAgB;AAClB,UAAI,eAAe,cAAc;AAAA,IACnC;AACA,QAAI,cAAc;AAChB,UAAI,YAAY,YAAY;AAAA,IAC9B;AACA,cAAU,OAAO,GAAG;AACpB,WAAO,YAAY,QAAQ;AAC3B,cAAU,eAAe,QAAQ;AAAA,EACnC;AACA,QAAM,oBAAoB,YAAU;AAClC,WAAO,YAAY,SAAS,MAAM;AAChC,YAAM,OAAO,OAAO,UAAU,QAAQ;AACtC,UAAI,cAAc,IAAI,GAAG;AACvB,0BAAkB,QAAQ,IAAI;AAAA,MAChC,OAAO;AACL,wBAAgB,MAAM;AAAA,MACxB;AACA,aAAO,MAAM;AAAA,IACf,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,UAAQ;AAC5B,UAAM;AAAA,MACJ,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,OAAO;AAAA,MACZ,OAAO,IAAI,UAAU;AAAA,MACrB;AAAA,MACA,KAAK,IAAI,UAAU;AAAA,MACnB,QAAQ,OAAO,UAAU;AAAA,MACzB,MAAM,KAAK,UAAU;AAAA,MACrB,OAAO,MAAM,UAAU;AAAA,IACzB,GAAG,CAAC,GAAG,OAAO,OAAO,CAAC,MAAM,KAAK;AAAA,EACnC;AACA,QAAM,eAAe,CAAC,QAAQ,SAAS;AACrC,UAAM,YAAY,OAAO,QAAQ;AACjC,UAAM,aAAa;AAAA,MACjB,sBAAsB,UAAU,sBAAsB;AAAA,MACtD,mBAAmB,UAAU,mBAAmB;AAAA,MAChD,qBAAqB,UAAU,qBAAqB;AAAA,IACtD;AACA,UAAM,OAAO,KAAK;AAClB,WAAO;AAAA,MACL,GAAG;AAAA,MACH,MAAM,SAAS,UAAU,MAAM,KAAK,UAAU,IAAI,OAAO;AAAA,IAC3D;AAAA,EACF;AACA,QAAM,OAAO,CAAC,QAAQ,aAAa,SAAS;AAC1C,UAAM,gBAAgB,aAAa,QAAQ,IAAI;AAC/C,WAAO,UAAU,OAAO,IAAI,IAAI,OAAO,YAAY,cAAc,OAAO,cAAc,aAAa,CAAC,IAAI,gBAAgB,QAAQ,aAAa,aAAa;AAAA,EAC5J;AACA,QAAM,SAAS,YAAU;AACvB,WAAO,UAAU,OAAO,IAAI,IAAI,OAAO,YAAY,QAAQ,IAAI,kBAAkB,MAAM;AAAA,EACzF;AACA,QAAM,oBAAoB,CAAC,QAAQ,QAAQ;AACzC,QAAI;AACJ,UAAM,MAAM,OAAO,IAAI,OAAO,OAAO,GAAG,EAAE,CAAC;AAC3C,QAAI,KAAK;AACP,YAAM,IAAI,OAAO,IAAI,WAAW,KAAK,WAAW,GAAG,EAAE,CAAC;AACtD,UAAI,GAAG;AACL,SAAC,KAAK,EAAE,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,KAAK,CAAC;AAC/E,eAAO,IAAI,OAAO,CAAC;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,QAAM,kBAAkB,CAAC,KAAK,KAAK,UAAU;AAC3C,QAAI;AACJ,UAAM,MAAM,IAAI,OAAO,OAAO,GAAG,EAAE,CAAC;AACpC,QAAI,KAAK;AACP,YAAM,IAAI,IAAI,OAAO,KAAK,KAAK;AAC/B,OAAC,KAAK,IAAI,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,GAAG,GAAG;AACjF,QAAE,YAAY,GAAG;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,cAAc,UAAQ,kBAAkB,MAAM,OAAO;AAC3D,QAAM,kBAAkB,CAAC,OAAO,YAAY,QAAQ,SAAS,UAAQ;AACnE,QAAI,YAAY,IAAI,GAAG;AACrB,aAAO,gBAAgB,OAAO,KAAK,KAAK;AAAA,IAC1C,OAAO;AACL,aAAO,OAAO,KAAK,UAAU,OAAO,IAAI;AAAA,IAC1C;AAAA,EACF,CAAC;AACD,QAAM,WAAW,CAAC,gBAAgB,WAAW,SAAS,SAAS;AAC7D,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,oBAAoB,eAAe,SAAS;AAClD,WAAO,UAAU,SAAY,gBAAgB,OAAO,OAAO,EAAE,IAAI,QAAM;AAAA,MACrE,KAAK;AAAA,QACH,OAAO,EAAE;AAAA,QACT,MAAM;AAAA,UACJ,MAAM,oBAAoB,iBAAiB,EAAE;AAAA,UAC7C,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA,MAAM,oBAAoB,iBAAiB,EAAE;AAAA,IAC/C,EAAE,IAAI,SAAS,KAAK;AAAA,EACtB;AACA,QAAM,cAAc,CAAC,UAAU,cAAc;AAC3C,QAAI,cAAc,QAAQ;AACxB,aAAO,SAAS;AAAA,IAClB,WAAW,cAAc,UAAU;AACjC,aAAO,SAAS;AAAA,IAClB,OAAO;AACL,aAAO,SAAS,KAAK;AAAA,IACvB;AAAA,EACF;AACA,QAAM,OAAO,CAAC,aAAa,gBAAgB;AACzC,UAAM,iBAAiB;AAAA,MACrB,MAAM,YAAY;AAAA,MAClB,OAAO,YAAY;AAAA,IACrB;AACA,UAAM,wBAAwB,SAAO;AACnC,UAAI;AACJ,aAAO,OAAO,eAAe,MAAM,UAAU,GAAG,SAAS,MAAM,KAAK,IAAI,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE,MAAM,EAAE,CAAC;AAAA,IACxI;AACA,UAAM,uBAAuB,SAAO;AAClC,UAAI;AACJ,aAAO,OAAO,eAAe,KAAK,UAAU,GAAG,SAAS,MAAM,KAAK,IAAI,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,EAAE,MAAM,IAAI,KAAK,CAAC;AAAA,IAC7I;AACA,UAAM,cAAc,UAAQ;AAC1B,YAAM,OAAO,qBAAqB,KAAK,GAAG;AAC1C,YAAM,QAAQ,sBAAsB,KAAK,GAAG;AAC5C,UAAI,KAAK,OAAO,KAAK,MAAM,OAAO,GAAG;AACnC,eAAO,SAAS,KAAK;AAAA,UACnB,GAAG,KAAK,IAAI,CAAAA,WAAS,EAAE,MAAAA,MAAK,EAAE,EAAE,MAAM,CAAC,CAAC;AAAA,UACxC,GAAG,MAAM,IAAI,CAAAC,YAAU,EAAE,OAAAA,OAAM,EAAE,EAAE,MAAM,CAAC,CAAC;AAAA,QAC7C,CAAC;AAAA,MACH,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AACA,UAAM,kBAAkB,CAAC,MAAM,WAAW;AACxC,YAAM,UAAU,YAAY,aAAa,MAAM,EAAE,MAAM,CAAC,CAAC;AACzD,aAAO,SAAS,eAAe,MAAM,QAAQ,SAAS,IAAI;AAAA,IAC5D;AACA,UAAM,WAAW,CAAC,SAAS,WAAW;AACpC,YAAM,OAAO,OAAO;AACpB,UAAI,SAAS,OAAO;AAClB,eAAO,YAAY,QAAQ,CAAC;AAAA,MAC9B,WAAW,SAAS;AAAA,QAChB;AAAA,QACA;AAAA,MACF,GAAG,IAAI,GAAG;AACV,eAAO,gBAAgB,QAAQ,GAAG,IAAI;AAAA,MACxC,WAAW,SAAS,UAAU,SAAS,SAAS;AAC9C,uBAAe,IAAI,IAAI,QAAQ,EAAE,IAAI;AACrC,eAAO,SAAS,KAAK;AAAA,MACvB,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AACA,WAAO,EAAE,SAAS;AAAA,EACpB;AACA,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,EACF;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE9D,QAAM,iBAAiB,CAAC,QAAQ,SAAS,aAAa;AACpD,UAAM,MAAM,OAAO,UAAU,OAAO;AACpC,aAAS,iBAAiB,QAAQ,MAAM;AACtC,aAAO,cAAc,QAAQ,SAAS,WAAS;AAC7C,eAAO,UAAU,OAAO,GAAG;AAC3B,iBAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,oBAAoB,UAAQ;AAChC,UAAM,MAAM,KAAK;AACjB,UAAM,gBAAgB,IAAI,QAAQ,GAAG,IAAI,KAAK,IAAI,QAAQ,GAAG,MAAM,MAAM,IAAI,QAAQ,SAAS,MAAM;AACpG,WAAO,gBAAgB,SAAS,KAAK;AAAA,MACnC,SAAS;AAAA,MACT,YAAY,cAAY;AAAA,QACtB,GAAG;AAAA,QACH,MAAM,YAAY;AAAA,MACpB;AAAA,IACF,CAAC,IAAI,SAAS,KAAK;AAAA,EACrB;AACA,QAAM,uBAAuB,CAACF,wBAAuB,wBAAwB,UAAQ;AACnF,UAAM,MAAM,KAAK;AACjB,UAAM,kBAAkBA,2BAA0B,KAAK,CAAC,YAAY,GAAG,KAAKA,2BAA0B,KAAK,oBAAoB,KAAK,GAAG;AACvI,WAAO,kBAAkB,SAAS,KAAK;AAAA,MACrC,SAAS,qFAAsF,mBAAoB;AAAA,MACnH,YAAY,cAAY;AAAA,QACtB,GAAG;AAAA,QACH,MAAM,sBAAsB,QAAQ;AAAA,MACtC;AAAA,IACF,CAAC,IAAI,SAAS,KAAK;AAAA,EACrB;AACA,QAAM,aAAa,CAAC,QAAQ,SAAS,QAAQ;AAAA,IAC3C;AAAA,IACA,qBAAqB,sBAAsB,MAAM,GAAG,uBAAuB,MAAM,CAAC;AAAA,EACpF,GAAG,OAAK,EAAE,IAAI,CAAC,EAAE,KAAK,MAAM,QAAQ,QAAQ,IAAI,GAAG,eAAa,IAAI,QAAQ,cAAY;AACtF,mBAAe,QAAQ,UAAU,SAAS,WAAS;AACjD,eAAS,QAAQ,UAAU,WAAW,IAAI,IAAI,IAAI;AAAA,IACpD,CAAC;AAAA,EACH,CAAC,CAAC;AACF,QAAM,iBAAiB,EAAE,WAAW;AAEpC,QAAM,aAAa,YAAU;AAC3B,UAAM,cAAc,OAAO,IAAI,OAAO,eAAe;AACrD,UAAM,UAAU,KAAK,aAAa,YAAU;AAC1C,YAAM,KAAK,OAAO,QAAQ,OAAO;AACjC,aAAO,KAAK,CAAC;AAAA,QACT,MAAM;AAAA,QACN,OAAO,MAAM;AAAA,MACf,CAAC,IAAI,CAAC;AAAA,IACV,CAAC;AACD,WAAO,QAAQ,SAAS,IAAI,SAAS,KAAK,CAAC;AAAA,MACvC,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI,SAAS,KAAK;AAAA,EACxC;AACA,QAAM,oBAAoB,EAAE,WAAW;AAEvC,QAAM,aAAa,YAAU;AAC3B,UAAM,OAAO,iBAAiB,MAAM;AACpC,QAAI,KAAK,SAAS,GAAG;AACnB,aAAO,YAAY,SAAS,IAAI;AAAA,IAClC;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,QAAM,mBAAmB,EAAE,WAAW;AAEtC,QAAM,YAAY,UAAQ;AACxB,QAAI;AACF,aAAO,SAAS,KAAK,KAAK,MAAM,IAAI,CAAC;AAAA,IACvC,SAAS,KAAK;AACZ,aAAO,SAAS,KAAK;AAAA,IACvB;AAAA,EACF;AACA,QAAM,aAAa,YAAU;AAC3B,UAAM,YAAY,UAAQ,OAAO,WAAW,KAAK,SAAS,KAAK,OAAO,IAAI,MAAM;AAChF,UAAM,WAAW,YAAY,MAAM;AACnC,WAAO,IAAI,QAAQ,aAAW;AAC5B,UAAI,SAAS,QAAQ,GAAG;AACtB,cAAM,QAAQ,EAAE,KAAK,SAAO,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,SAAS,IAAI,QAAQ,OAAO,CAAC,EAAE,KAAK,SAAS,MAAM,QAAQ,SAAS,KAAK,CAAC,CAAC;AAAA,MAClI,WAAW,WAAW,QAAQ,GAAG;AAC/B,iBAAS,YAAU,QAAQ,SAAS,KAAK,MAAM,CAAC,CAAC;AAAA,MACnD,OAAO;AACL,gBAAQ,SAAS,KAAK,QAAQ,CAAC;AAAA,MACjC;AAAA,IACF,CAAC,EAAE,KAAK,cAAY,SAAS,KAAK,YAAY,aAAa,SAAS,CAAC,EAAE,IAAI,WAAS;AAClF,UAAI,MAAM,SAAS,GAAG;AACpB,cAAM,WAAW,CAAC;AAAA,UACd,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC;AACH,eAAO,SAAS,OAAO,KAAK;AAAA,MAC9B,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACA,QAAM,kBAAkB,EAAE,UAAU,WAAW;AAE/C,QAAM,UAAU,CAAC,QAAQ,kBAAkB;AACzC,UAAM,OAAO,WAAW,MAAM;AAC9B,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM,gBAAgB,GAAG,eAAe,QAAQ;AAChD,YAAM,cAAc,sBAAsB,MAAM,MAAM;AACtD,YAAM,mBAAmB,UAAQ,oBAAoB,YAAY,SAAS,IAAI,GAAG,aAAa;AAC9F,YAAM,YAAY,cAAc,YAAY,aAAa,gBAAgB,IAAI,YAAY;AACzF,aAAO,UAAU,IAAI;AAAA,IACvB;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,QAAM,aAAa,EAAE,QAAQ;AAE7B,QAAM,YAAY;AAAA,IAChB;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,aAAa,YAAU;AAC3B,UAAM,OAAO,cAAc,MAAM;AACjC,QAAI,QAAQ,IAAI,GAAG;AACjB,aAAO,YAAY,SAAS,IAAI,EAAE,QAAQ,MAAM,SAAS,KAAK,SAAS,CAAC;AAAA,IAC1E,WAAW,SAAS,OAAO;AACzB,aAAO,SAAS,KAAK;AAAA,IACvB;AACA,WAAO,SAAS,KAAK,SAAS;AAAA,EAChC;AACA,QAAM,gBAAgB,EAAE,WAAW;AAEnC,QAAM,eAAe,CAAC,KAAK,MAAM,SAAS;AACxC,UAAM,MAAM,IAAI,UAAU,MAAM,IAAI;AACpC,WAAO,QAAQ,QAAQ,IAAI,SAAS,IAAI,SAAS,KAAK,GAAG,IAAI,SAAS,KAAK;AAAA,EAC7E;AACA,QAAM,oBAAoB,CAAC,QAAQ,WAAW;AAC5C,UAAM,MAAM,OAAO;AACnB,UAAM,WAAW,mBAAmB,MAAM;AAC1C,UAAM,OAAO,WAAW,SAAS,KAAK,cAAc,OAAO,WAAW,MAAM,CAAC,IAAI,SAAS,KAAK;AAC/F,UAAM,MAAM,OAAO,KAAK,eAAa,SAAS,KAAK,IAAI,UAAU,WAAW,MAAM,CAAC,CAAC;AACpF,UAAM,SAAS,OAAO,KAAK,eAAa,SAAS,KAAK,IAAI,UAAU,WAAW,QAAQ,CAAC,CAAC;AACzF,UAAM,MAAM,OAAO,KAAK,eAAa,aAAa,KAAK,WAAW,KAAK,CAAC;AACxE,UAAM,YAAY,OAAO,KAAK,eAAa,aAAa,KAAK,WAAW,OAAO,CAAC;AAChF,UAAM,QAAQ,OAAO,KAAK,eAAa,aAAa,KAAK,WAAW,OAAO,CAAC;AAC5E,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU,CAAC,QAAQ,aAAa,gBAAgB,SAAS,MAAM,EAAE,KAAK,WAAS;AACnF,UAAM,SAAS,kBAAkB,QAAQ,QAAQ;AACjD,WAAO;AAAA,MACL;AAAA,MACA,UAAU;AAAA,QACR,SAAS,cAAc,WAAW,MAAM;AAAA,QACxC,MAAM,WAAW,QAAQ,QAAQ,OAAO,MAAM;AAAA,QAC9C,SAAS,iBAAiB,WAAW,MAAM;AAAA,QAC3C,QAAQ,kBAAkB,WAAW,MAAM;AAAA,QAC3C,MAAM;AAAA,MACR;AAAA,MACA,SAAS;AAAA,MACT,OAAO,EAAE,cAAc,oBAAoB,MAAM,EAAE;AAAA,IACrD;AAAA,EACF,CAAC;AACD,QAAM,aAAa,EAAE,QAAQ;AAE7B,QAAM,eAAe,CAAC,QAAQ,SAAS,SAAO;AAC5C,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,CAAC,KAAK,IAAI,OAAO;AACnB,aAAO,MAAM;AACb,UAAI,MAAM;AACV;AAAA,IACF;AACA,UAAM,kBAAkB,SAAO,SAAS,KAAK,KAAK,GAAG,CAAC,EAAE,OAAO,WAAS,CAAC,GAAG,KAAK,OAAO,GAAG,GAAG,KAAK,CAAC;AACpG,UAAM,cAAc;AAAA,MAClB,MAAM,KAAK,IAAI;AAAA,MACf,MAAM,gBAAgB,MAAM;AAAA,MAC5B,QAAQ,gBAAgB,QAAQ;AAAA,MAChC,KAAK,gBAAgB,KAAK;AAAA,MAC1B,OAAO,gBAAgB,WAAW;AAAA,MAClC,OAAO,gBAAgB,OAAO;AAAA,IAChC;AACA,UAAM,cAAc;AAAA,MAClB,MAAM,KAAK,IAAI;AAAA,MACf,QAAQ,KAAK,IAAI,SAAS,UAAa,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS;AAAA,IACvF;AACA,mBAAe,WAAW,QAAQ,WAAW,EAAE,KAAK,WAAS;AAC3D,WAAK,QAAQ,aAAa,KAAK;AAAA,IACjC,CAAC;AACD,QAAI,MAAM;AAAA,EACZ;AACA,QAAM,cAAc,YAAU;AAC5B,UAAM,aAAa,iBAAiB,MAAM;AAC1C,WAAO,WAAW,QAAQ,QAAQ,UAAU;AAAA,EAC9C;AACA,QAAM,iBAAiB,CAAC,MAAM,kBAAkB;AAC9C,UAAM,SAAS,KAAK;AACpB,UAAM,MAAM,OAAO,IAAI,MAAM,EAAE;AAC/B,WAAO;AAAA,MACL,KAAK;AAAA,QACH,OAAO;AAAA,QACP,MAAM,EAAE,UAAU,EAAE,OAAO,IAAI,EAAE;AAAA,MACnC;AAAA,MACA,MAAM,OAAO,KAAK,MAAM,EAAE;AAAA,MAC1B,OAAO,OAAO,MAAM,MAAM,EAAE;AAAA,MAC5B,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,KAAK,OAAO,IAAI,MAAM,EAAE;AAAA,MACxB,QAAQ,OAAO,OAAO,GAAG,aAAa,EAAE,MAAM,EAAE;AAAA,MAChD,WAAW,OAAO,UAAU,MAAM,EAAE;AAAA,IACtC;AAAA,EACF;AACA,QAAM,aAAa,CAAC,UAAU,UAAU,WAAW;AACjD,UAAM,WAAW,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,aAAa;AAAA,IACf,CAAC;AACH,UAAM,cAAc,SAAS,OAAO,KAAK,IAAI,OAAO;AAAA,MAClD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACT,EAAE,EAAE,QAAQ;AACZ,UAAM,YAAY,SAAS,MAAM,eAAe,CAAC;AAAA,MAC7C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC,IAAI,CAAC;AACR,UAAM,gBAAgB,SAAS,KAAK,qBAAqB,MAAM,CAAC;AAChE,UAAM,cAAc,eAAe,UAAU,aAAa;AAC1D,UAAM,WAAW,SAAS;AAC1B,UAAM,cAAc,cAAc,KAAK,aAAa,QAAQ;AAC5D,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,OAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI;AAAA,UACF,SAAS,OAAO,IAAI,YAAY,SAAS,UAAU,SAAS,CAAC;AAAA,UAC7D,SAAS,KAAK,IAAI,YAAY,SAAS,OAAO,KAAK,CAAC;AAAA,UACpD,SAAS,QAAQ,IAAI,YAAY,SAAS,UAAU,iBAAiB,CAAC;AAAA,UACtE,SAAS,KAAK,IAAI,YAAY,SAAS,QAAQ,WAAW,CAAC;AAAA,UAC3D,SAAS,QAAQ,IAAI,YAAY,SAAS,aAAa,OAAO,CAAC;AAAA,QACjE,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA;AAAA,MACA,UAAU,CAAC,KAAK,EAAC,KAAI,MAAM;AACzB,oBAAY,SAAS,IAAI,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,aAAW;AAC1D,cAAI,QAAQ,OAAO;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,SAAS,YAAU;AACvB,UAAM,OAAO,YAAY,MAAM;AAC/B,SAAK,KAAK,UAAQ;AAChB,YAAM,WAAW,aAAa,QAAQ,IAAI;AAC1C,aAAO,WAAW,MAAM,UAAU,MAAM;AAAA,IAC1C,CAAC,EAAE,KAAK,UAAQ;AACd,aAAO,cAAc,KAAK,IAAI;AAAA,IAChC,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,YAAU;AACzB,WAAO,WAAW,WAAW,CAAC,KAAK,UAAU;AAC3C,WAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY,QAAQ,CAAC,aAAa,MAAM,GAAG;AAClG,eAAO,MAAM;AAAA,MACf,OAAO;AACL,eAAO,SAAS,uBAAuB,EAAE,YAAY,YAAY,CAAC;AAAA,MACpE;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,iBAAiB;AAEzD,QAAM,oBAAoB,CAACG,OAAM,QAAQ;AACvC,aAAS,KAAK,YAAYA,KAAI;AAC9B,IAAAA,MAAK,cAAc,GAAG;AACtB,aAAS,KAAK,YAAYA,KAAI;AAAA,EAChC;AACA,QAAM,OAAO,SAAO;AAClB,UAAMA,QAAO,SAAS,cAAc,GAAG;AACvC,IAAAA,MAAK,SAAS;AACd,IAAAA,MAAK,OAAO;AACZ,IAAAA,MAAK,MAAM;AACX,UAAM,MAAM,SAAS,YAAY,aAAa;AAC9C,QAAI,eAAe,SAAS,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,OAAO,OAAO,OAAO,GAAG,IAAI;AAClG,sBAAkBA,OAAM,GAAG;AAAA,EAC7B;AAEA,QAAM,qCAAqC,YAAU;AACnD,UAAM,MAAM,OAAO,UAAU,OAAO;AACpC,UAAM,OAAO,IAAI;AACjB,WAAO,OAAO,IAAI,KAAK,IAAI,mBAAmB,IAAI,gBAAgB,OAAO,IAAI,OAAO,OAAO,IAAI,EAAE,WAAW;AAAA,EAC9G;AACA,QAAM,WAAW,YAAU,OAAO,UAAU,YAAY,KAAK,mCAAmC,MAAM,IAAI,WAAW,OAAO,IAAI,WAAW,OAAO,UAAU,SAAS,CAAC,CAAC,IAAI,oBAAoB,OAAO,UAAU,OAAO,CAAC;AACxN,QAAM,kBAAkB,YAAU,SAAS,MAAM,EAAE,CAAC;AACpD,QAAM,qBAAqB,OAAK;AAC9B,WAAO,EAAE,WAAW,QAAQ,EAAE,aAAa,SAAS,EAAE,YAAY,SAAS,EAAE,YAAY;AAAA,EAC3F;AACA,QAAM,WAAW,CAAC,QAAQ,MAAM;AAC9B,QAAI,GAAG;AACL,YAAM,OAAO,QAAQ,CAAC;AACtB,UAAI,KAAK,KAAK,IAAI,GAAG;AACnB,cAAM,WAAW,OAAO,IAAI,OAAO,IAAI;AACvC,YAAI,SAAS,QAAQ;AACnB,iBAAO,UAAU,eAAe,SAAS,CAAC,GAAG,IAAI;AAAA,QACnD;AAAA,MACF,OAAO;AACL,aAAK,EAAE,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,YAAU,MAAM;AACjC,WAAO,YAAY,WAAW,OAAO,EAAE,QAAQ,KAAK,CAAC;AAAA,EACvD;AACA,QAAM,mBAAmB,YAAU,MAAM;AACvC,aAAS,QAAQ,gBAAgB,MAAM,CAAC;AAAA,EAC1C;AACA,QAAM,iBAAiB,YAAU;AAC/B,WAAO,GAAG,SAAS,OAAK;AACtB,YAAM,QAAQ,WAAW,OAAO,IAAI,WAAW,EAAE,MAAM,CAAC;AACxD,UAAI,MAAM,WAAW,KAAK,OAAO,eAAe,CAAC,GAAG;AAClD,UAAE,eAAe;AACjB,iBAAS,QAAQ,MAAM,CAAC,CAAC;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,WAAO,GAAG,WAAW,OAAK;AACxB,UAAI,CAAC,EAAE,mBAAmB,KAAK,EAAE,YAAY,MAAM,mBAAmB,CAAC,GAAG;AACxE,cAAMA,QAAO,gBAAgB,MAAM;AACnC,YAAIA,OAAM;AACR,YAAE,eAAe;AACjB,mBAAS,QAAQA,KAAI;AAAA,QACvB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,cAAc,CAAC,QAAQ,YAAY;AACvC,WAAO,GAAG,cAAc,OAAO;AAC/B,WAAO,MAAM,OAAO,IAAI,cAAc,OAAO;AAAA,EAC/C;AACA,QAAM,kBAAkB,YAAU,SAAO;AACvC,UAAM,cAAc,MAAM;AACxB,UAAI,UAAU,CAAC,OAAO,KAAK,WAAW,KAAK,WAAW,QAAQ,OAAO,UAAU,QAAQ,CAAC,CAAC;AACzF,UAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,IAC9C;AACA,gBAAY;AACZ,WAAO,YAAY,QAAQ,WAAW;AAAA,EACxC;AACA,QAAM,sBAAsB,YAAU,SAAO;AAC3C,UAAM,cAAc,MAAM;AACxB,UAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,IAC9C;AACA,gBAAY;AACZ,WAAO,YAAY,QAAQ,WAAW;AAAA,EACxC;AACA,QAAM,+BAA+B,YAAU,SAAS,MAAM,EAAE,WAAW;AAC3E,QAAM,sBAAsB,YAAU,SAAO;AAC3C,UAAM,cAAc,MAAM,IAAI,WAAW,6BAA6B,MAAM,CAAC;AAC7E,gBAAY;AACZ,WAAO,YAAY,QAAQ,WAAW;AAAA,EACxC;AACA,QAAM,oBAAoB,YAAU,SAAO;AACzC,UAAM,aAAa,CAAAC,aAAW,SAASA,QAAO,KAAK,oBAAoB,OAAO,UAAU,OAAO,CAAC;AAChG,UAAM,UAAU,OAAO,IAAI,WAAW,OAAO,UAAU,SAAS,CAAC;AACjE,UAAM,gBAAgB,CAAAA,aAAW;AAC/B,UAAI,WAAW,WAAWA,QAAO,KAAK,OAAO,UAAU,WAAW,CAAC;AAAA,IACrE;AACA,kBAAc,OAAO;AACrB,WAAO,YAAY,QAAQ,OAAK,cAAc,EAAE,OAAO,CAAC;AAAA,EAC1D;AAEA,QAAM,QAAQ,YAAU;AACtB,WAAO,YAAY,UAAU,IAAI,MAAM;AACrC,aAAO,YAAY,SAAS;AAAA,IAC9B,CAAC;AAAA,EACH;AAEA,QAAM,eAAe,YAAU;AAC7B,WAAO,GAAG,SAAS,gBAAgB,QAAQ;AAAA,MACzC,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU,WAAW,MAAM;AAAA,MAC3B,SAAS,gBAAgB,MAAM;AAAA,MAC/B,UAAU;AAAA,IACZ,CAAC;AACD,WAAO,GAAG,SAAS,UAAU,YAAY;AAAA,MACvC,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU,iBAAiB,MAAM;AAAA,MACjC,SAAS,oBAAoB,MAAM;AAAA,IACrC,CAAC;AACD,WAAO,GAAG,SAAS,UAAU,UAAU;AAAA,MACrC,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU,MAAM,OAAO,MAAM;AAAA,MAC7B,SAAS,kBAAkB,MAAM;AAAA,IACnC,CAAC;AAAA,EACH;AACA,QAAM,iBAAiB,YAAU;AAC/B,WAAO,GAAG,SAAS,YAAY,YAAY;AAAA,MACzC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU,iBAAiB,MAAM;AAAA,MACjC,SAAS,oBAAoB,MAAM;AAAA,IACrC,CAAC;AACD,WAAO,GAAG,SAAS,YAAY,QAAQ;AAAA,MACrC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,oBAAoB,MAAM;AAAA,MACnC,UAAU,WAAW,MAAM;AAAA,IAC7B,CAAC;AACD,WAAO,GAAG,SAAS,YAAY,UAAU;AAAA,MACvC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU,MAAM,OAAO,MAAM;AAAA,MAC7B,SAAS,kBAAkB,MAAM;AAAA,IACnC,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,YAAU;AACjC,UAAM,SAAS;AACf,UAAM,SAAS;AACf,WAAO,GAAG,SAAS,eAAe,QAAQ;AAAA,MACxC,QAAQ,aAAW;AACjB,cAAM,aAAa,OAAO,IAAI,WAAW,OAAO;AAChD,YAAI,CAAC,YAAY;AACf,iBAAO;AAAA,QACT;AACA,eAAO,SAAS,OAAO,IAAI,WAAW,SAAS,GAAG,CAAC,IAAI,SAAS;AAAA,MAClE;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,uBAAuB,YAAU;AACrC,UAAM,yBAAyB,CAAAC,YAAU;AACvC,MAAAA,QAAO,UAAU,SAAS,KAAK;AAAA,IACjC;AACA,UAAM,cAAc,eAAa;AAC/B,YAAM,OAAO,OAAO,UAAU,QAAQ;AACtC,gBAAU,WAAW,WAAW,QAAQ,IAAI,CAAC;AAC7C,aAAO;AAAA,IACT;AACA,UAAM,cAAc,WAAS;AAC3B,YAAM,SAAS,iBAAiB,MAAM;AACtC,YAAM,WAAW,mBAAmB,MAAM;AAC1C,UAAI,OAAO,OAAO,KAAK,UAAU;AAC/B,cAAM,OAAO,cAAc,OAAO,WAAW,MAAM;AACnD,eAAO,OAAO,KAAK,WAAW,GAAG,KAAK;AAAA,MACxC,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AACA,WAAO,GAAG,SAAS,eAAe,aAAa;AAAA,MAC7C,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,gBAAgB,MAAM;AAAA,MACjC;AAAA,MACA,OAAO;AAAA,MACP,WAAW,UAAQ,kBAAkB,MAAM,KAAK,WAAW,QAAQ,IAAI;AAAA,MACvE,WAAW,MAAM;AACf,cAAM,MAAM,iBAAiB,MAAM;AACnC,eAAO,IAAI,KAAK,SAAS,EAAE,GAAG,OAAO;AAAA,MACvC;AAAA,MACA,UAAU;AAAA,QACR;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS,eAAa;AACpB,kBAAM,OAAO,OAAO,UAAU,QAAQ;AACtC,sBAAU,UAAU,WAAW,QAAQ,IAAI,CAAC;AAC5C,mBAAO,gBAAgB,MAAM,EAAE,SAAS;AAAA,UAC1C;AAAA,UACA,UAAU,aAAW;AACnB,kBAAM,QAAQ,QAAQ,SAAS;AAC/B,kBAAM,OAAO,YAAY,KAAK;AAC9B,kBAAM,cAAc;AAAA,cAClB,MAAM;AAAA,cACN,QAAQ;AAAA,YACV;AACA,iBAAK,QAAQ,aAAa;AAAA,cACxB,MAAM;AAAA,cACN;AAAA,cACA,OAAO,SAAS,KAAK;AAAA,cACrB,KAAK,SAAS,KAAK;AAAA,cACnB,QAAQ,SAAS,KAAK,qBAAqB,MAAM,CAAC;AAAA,cAClD,OAAO,SAAS,KAAK;AAAA,YACvB,CAAC;AACD,mCAAuB,MAAM;AAC7B,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU,aAAW;AACnB,mBAAO,MAAM;AACb,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU,aAAW;AACnB,6BAAiB,MAAM,EAAE;AACzB,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,MAAM;AACjB,aAAS,IAAI,QAAQ,YAAU;AAC7B,iBAAW,MAAM;AACjB,mBAAa,MAAM;AACnB,qBAAe,MAAM;AACrB,uBAAiB,MAAM;AACvB,2BAAqB,MAAM;AAC3B,qBAAe,MAAM;AACrB,eAAS,MAAM;AACf,YAAM,MAAM;AAAA,IACd,CAAC;AAAA,EACH;AAEA,SAAO;AAEX,GAAG;", "names": ["list", "rels", "anchorElm", "assumeExternalTargets", "text", "title", "link", "parents", "editor"]}