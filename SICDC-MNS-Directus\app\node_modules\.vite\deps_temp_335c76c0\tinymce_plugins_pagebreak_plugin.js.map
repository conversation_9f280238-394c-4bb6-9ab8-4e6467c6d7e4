{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/pagebreak/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const option = name => editor => editor.options.get(name);\n    const register$2 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('pagebreak_separator', {\n        processor: 'string',\n        default: '<!-- pagebreak -->'\n      });\n      registerOption('pagebreak_split_block', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const getSeparatorHtml = option('pagebreak_separator');\n    const shouldSplitBlock = option('pagebreak_split_block');\n\n    const pageBreakClass = 'mce-pagebreak';\n    const getPlaceholderHtml = shouldSplitBlock => {\n      const html = `<img src=\"${ global.transparentSrc }\" class=\"${ pageBreakClass }\" data-mce-resize=\"false\" data-mce-placeholder />`;\n      return shouldSplitBlock ? `<p>${ html }</p>` : html;\n    };\n    const setup$1 = editor => {\n      const separatorHtml = getSeparatorHtml(editor);\n      const shouldSplitBlock$1 = () => shouldSplitBlock(editor);\n      const pageBreakSeparatorRegExp = new RegExp(separatorHtml.replace(/[\\?\\.\\*\\[\\]\\(\\)\\{\\}\\+\\^\\$\\:]/g, a => {\n        return '\\\\' + a;\n      }), 'gi');\n      editor.on('BeforeSetContent', e => {\n        e.content = e.content.replace(pageBreakSeparatorRegExp, getPlaceholderHtml(shouldSplitBlock$1()));\n      });\n      editor.on('PreInit', () => {\n        editor.serializer.addNodeFilter('img', nodes => {\n          let i = nodes.length, node, className;\n          while (i--) {\n            node = nodes[i];\n            className = node.attr('class');\n            if (className && className.indexOf(pageBreakClass) !== -1) {\n              const parentNode = node.parent;\n              if (parentNode && editor.schema.getBlockElements()[parentNode.name] && shouldSplitBlock$1()) {\n                parentNode.type = 3;\n                parentNode.value = separatorHtml;\n                parentNode.raw = true;\n                node.remove();\n                continue;\n              }\n              node.type = 3;\n              node.value = separatorHtml;\n              node.raw = true;\n            }\n          }\n        });\n      });\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mcePageBreak', () => {\n        editor.insertContent(getPlaceholderHtml(shouldSplitBlock(editor)));\n      });\n    };\n\n    const setup = editor => {\n      editor.on('ResolveName', e => {\n        if (e.target.nodeName === 'IMG' && editor.dom.hasClass(e.target, pageBreakClass)) {\n          e.name = 'pagebreak';\n        }\n      });\n    };\n\n    const onSetupEditable = editor => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const register = editor => {\n      const onAction = () => editor.execCommand('mcePageBreak');\n      editor.ui.registry.addButton('pagebreak', {\n        icon: 'page-break',\n        tooltip: 'Page break',\n        onAction,\n        onSetup: onSetupEditable(editor)\n      });\n      editor.ui.registry.addMenuItem('pagebreak', {\n        text: 'Page break',\n        icon: 'page-break',\n        onAction,\n        onSetup: onSetupEditable(editor)\n      });\n    };\n\n    var Plugin = () => {\n      global$1.add('pagebreak', editor => {\n        register$2(editor);\n        register$1(editor);\n        register(editor);\n        setup$1(editor);\n        setup(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAErD,QAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,QAAM,aAAa,YAAU;AAC3B,UAAM,iBAAiB,OAAO,QAAQ;AACtC,mBAAe,uBAAuB;AAAA,MACpC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,yBAAyB;AAAA,MACtC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,OAAO,qBAAqB;AACrD,QAAM,mBAAmB,OAAO,uBAAuB;AAEvD,QAAM,iBAAiB;AACvB,QAAM,qBAAqB,CAAAA,sBAAoB;AAC7C,UAAM,OAAO,aAAc,OAAO,cAAe,YAAa,cAAe;AAC7E,WAAOA,oBAAmB,MAAO,IAAK,SAAS;AAAA,EACjD;AACA,QAAM,UAAU,YAAU;AACxB,UAAM,gBAAgB,iBAAiB,MAAM;AAC7C,UAAM,qBAAqB,MAAM,iBAAiB,MAAM;AACxD,UAAM,2BAA2B,IAAI,OAAO,cAAc,QAAQ,iCAAiC,OAAK;AACtG,aAAO,OAAO;AAAA,IAChB,CAAC,GAAG,IAAI;AACR,WAAO,GAAG,oBAAoB,OAAK;AACjC,QAAE,UAAU,EAAE,QAAQ,QAAQ,0BAA0B,mBAAmB,mBAAmB,CAAC,CAAC;AAAA,IAClG,CAAC;AACD,WAAO,GAAG,WAAW,MAAM;AACzB,aAAO,WAAW,cAAc,OAAO,WAAS;AAC9C,YAAI,IAAI,MAAM,QAAQ,MAAM;AAC5B,eAAO,KAAK;AACV,iBAAO,MAAM,CAAC;AACd,sBAAY,KAAK,KAAK,OAAO;AAC7B,cAAI,aAAa,UAAU,QAAQ,cAAc,MAAM,IAAI;AACzD,kBAAM,aAAa,KAAK;AACxB,gBAAI,cAAc,OAAO,OAAO,iBAAiB,EAAE,WAAW,IAAI,KAAK,mBAAmB,GAAG;AAC3F,yBAAW,OAAO;AAClB,yBAAW,QAAQ;AACnB,yBAAW,MAAM;AACjB,mBAAK,OAAO;AACZ;AAAA,YACF;AACA,iBAAK,OAAO;AACZ,iBAAK,QAAQ;AACb,iBAAK,MAAM;AAAA,UACb;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,QAAM,aAAa,YAAU;AAC3B,WAAO,WAAW,gBAAgB,MAAM;AACtC,aAAO,cAAc,mBAAmB,iBAAiB,MAAM,CAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AAEA,QAAM,QAAQ,YAAU;AACtB,WAAO,GAAG,eAAe,OAAK;AAC5B,UAAI,EAAE,OAAO,aAAa,SAAS,OAAO,IAAI,SAAS,EAAE,QAAQ,cAAc,GAAG;AAChF,UAAE,OAAO;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,kBAAkB,YAAU,SAAO;AACvC,UAAM,cAAc,MAAM;AACxB,UAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,IAC9C;AACA,WAAO,GAAG,cAAc,WAAW;AACnC,gBAAY;AACZ,WAAO,MAAM;AACX,aAAO,IAAI,cAAc,WAAW;AAAA,IACtC;AAAA,EACF;AACA,QAAM,WAAW,YAAU;AACzB,UAAM,WAAW,MAAM,OAAO,YAAY,cAAc;AACxD,WAAO,GAAG,SAAS,UAAU,aAAa;AAAA,MACxC,MAAM;AAAA,MACN,SAAS;AAAA,MACT;AAAA,MACA,SAAS,gBAAgB,MAAM;AAAA,IACjC,CAAC;AACD,WAAO,GAAG,SAAS,YAAY,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,MACA,SAAS,gBAAgB,MAAM;AAAA,IACjC,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,MAAM;AACjB,aAAS,IAAI,aAAa,YAAU;AAClC,iBAAW,MAAM;AACjB,iBAAW,MAAM;AACjB,eAAS,MAAM;AACf,cAAQ,MAAM;AACd,YAAM,MAAM;AAAA,IACd,CAAC;AAAA,EACH;AAEA,SAAO;AAEX,GAAG;", "names": ["shouldSplitBlock"]}