{"version": 3, "sources": ["../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kk/_lib/formatDistance.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kk/_lib/formatLong.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kk/_lib/formatRelative.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kk/_lib/localize.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kk/_lib/match.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kk.mjs"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    regular: {\n      one: \"1 секундтан аз\",\n      singularNominative: \"{{count}} секундтан аз\",\n      singularGenitive: \"{{count}} секундтан аз\",\n      pluralGenitive: \"{{count}} секундтан аз\",\n    },\n    future: {\n      one: \"бір секундтан кейін\",\n      singularNominative: \"{{count}} секундтан кейін\",\n      singularGenitive: \"{{count}} секундтан кейін\",\n      pluralGenitive: \"{{count}} секундтан кейін\",\n    },\n  },\n\n  xSeconds: {\n    regular: {\n      singularNominative: \"{{count}} секунд\",\n      singularGenitive: \"{{count}} секунд\",\n      pluralGenitive: \"{{count}} секунд\",\n    },\n    past: {\n      singularNominative: \"{{count}} секунд бұрын\",\n      singularGenitive: \"{{count}} секунд бұрын\",\n      pluralGenitive: \"{{count}} секунд бұрын\",\n    },\n    future: {\n      singularNominative: \"{{count}} секундтан кейін\",\n      singularGenitive: \"{{count}} секундтан кейін\",\n      pluralGenitive: \"{{count}} секундтан кейін\",\n    },\n  },\n\n  halfAMinute: (options) => {\n    if (options?.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return \"жарты минут ішінде\";\n      } else {\n        return \"жарты минут бұрын\";\n      }\n    }\n\n    return \"жарты минут\";\n  },\n\n  lessThanXMinutes: {\n    regular: {\n      one: \"1 минуттан аз\",\n      singularNominative: \"{{count}} минуттан аз\",\n      singularGenitive: \"{{count}} минуттан аз\",\n      pluralGenitive: \"{{count}} минуттан аз\",\n    },\n    future: {\n      one: \"минуттан кем \",\n      singularNominative: \"{{count}} минуттан кем\",\n      singularGenitive: \"{{count}} минуттан кем\",\n      pluralGenitive: \"{{count}} минуттан кем\",\n    },\n  },\n\n  xMinutes: {\n    regular: {\n      singularNominative: \"{{count}} минут\",\n      singularGenitive: \"{{count}} минут\",\n      pluralGenitive: \"{{count}} минут\",\n    },\n    past: {\n      singularNominative: \"{{count}} минут бұрын\",\n      singularGenitive: \"{{count}} минут бұрын\",\n      pluralGenitive: \"{{count}} минут бұрын\",\n    },\n    future: {\n      singularNominative: \"{{count}} минуттан кейін\",\n      singularGenitive: \"{{count}} минуттан кейін\",\n      pluralGenitive: \"{{count}} минуттан кейін\",\n    },\n  },\n\n  aboutXHours: {\n    regular: {\n      singularNominative: \"шамамен {{count}} сағат\",\n      singularGenitive: \"шамамен {{count}} сағат\",\n      pluralGenitive: \"шамамен {{count}} сағат\",\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} сағаттан кейін\",\n      singularGenitive: \"шамамен {{count}} сағаттан кейін\",\n      pluralGenitive: \"шамамен {{count}} сағаттан кейін\",\n    },\n  },\n\n  xHours: {\n    regular: {\n      singularNominative: \"{{count}} сағат\",\n      singularGenitive: \"{{count}} сағат\",\n      pluralGenitive: \"{{count}} сағат\",\n    },\n  },\n\n  xDays: {\n    regular: {\n      singularNominative: \"{{count}} күн\",\n      singularGenitive: \"{{count}} күн\",\n      pluralGenitive: \"{{count}} күн\",\n    },\n    future: {\n      singularNominative: \"{{count}} күннен кейін\",\n      singularGenitive: \"{{count}} күннен кейін\",\n      pluralGenitive: \"{{count}} күннен кейін\",\n    },\n  },\n\n  aboutXWeeks: {\n    type: \"weeks\",\n    one: \"шамамен 1 апта\",\n    other: \"шамамен {{count}} апта\",\n  },\n\n  xWeeks: {\n    type: \"weeks\",\n    one: \"1 апта\",\n    other: \"{{count}} апта\",\n  },\n\n  aboutXMonths: {\n    regular: {\n      singularNominative: \"шамамен {{count}} ай\",\n      singularGenitive: \"шамамен {{count}} ай\",\n      pluralGenitive: \"шамамен {{count}} ай\",\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} айдан кейін\",\n      singularGenitive: \"шамамен {{count}} айдан кейін\",\n      pluralGenitive: \"шамамен {{count}} айдан кейін\",\n    },\n  },\n\n  xMonths: {\n    regular: {\n      singularNominative: \"{{count}} ай\",\n      singularGenitive: \"{{count}} ай\",\n      pluralGenitive: \"{{count}} ай\",\n    },\n  },\n\n  aboutXYears: {\n    regular: {\n      singularNominative: \"шамамен {{count}} жыл\",\n      singularGenitive: \"шамамен {{count}} жыл\",\n      pluralGenitive: \"шамамен {{count}} жыл\",\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} жылдан кейін\",\n      singularGenitive: \"шамамен {{count}} жылдан кейін\",\n      pluralGenitive: \"шамамен {{count}} жылдан кейін\",\n    },\n  },\n\n  xYears: {\n    regular: {\n      singularNominative: \"{{count}} жыл\",\n      singularGenitive: \"{{count}} жыл\",\n      pluralGenitive: \"{{count}} жыл\",\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан кейін\",\n      singularGenitive: \"{{count}} жылдан кейін\",\n      pluralGenitive: \"{{count}} жылдан кейін\",\n    },\n  },\n\n  overXYears: {\n    regular: {\n      singularNominative: \"{{count}} жылдан астам\",\n      singularGenitive: \"{{count}} жылдан астам\",\n      pluralGenitive: \"{{count}} жылдан астам\",\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан астам\",\n      singularGenitive: \"{{count}} жылдан астам\",\n      pluralGenitive: \"{{count}} жылдан астам\",\n    },\n  },\n\n  almostXYears: {\n    regular: {\n      singularNominative: \"{{count}} жылға жақын\",\n      singularGenitive: \"{{count}} жылға жақын\",\n      pluralGenitive: \"{{count}} жылға жақын\",\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан кейін\",\n      singularGenitive: \"{{count}} жылдан кейін\",\n      pluralGenitive: \"{{count}} жылдан кейін\",\n    },\n  },\n};\n\nfunction declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one && count === 1) return scheme.one;\n\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\n\nexport const formatDistance = (token, count, options) => {\n  const tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === \"function\") return tokenValue(options);\n\n  if (tokenValue.type === \"weeks\") {\n    return count === 1\n      ? tokenValue.one\n      : tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (tokenValue.future) {\n        return declension(tokenValue.future, count);\n      } else {\n        return declension(tokenValue.regular, count) + \" кейін\";\n      }\n    } else {\n      if (tokenValue.past) {\n        return declension(tokenValue.past, count);\n      } else {\n        return declension(tokenValue.regular, count) + \" бұрын\";\n      }\n    }\n  } else {\n    return declension(tokenValue.regular, count);\n  }\n};\n", "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.mjs\";\n\nconst dateFormats = {\n  full: \"EEEE, do MMMM y 'ж.'\",\n  long: \"do MMMM y 'ж.'\",\n  medium: \"d MMM y 'ж.'\",\n  short: \"dd.MM.yyyy\",\n};\n\nconst timeFormats = {\n  full: \"H:mm:ss zzzz\",\n  long: \"H:mm:ss z\",\n  medium: \"H:mm:ss\",\n  short: \"H:mm\",\n};\n\nconst dateTimeFormats = {\n  any: \"{{date}}, {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"any\",\n  }),\n};\n", "import { isSameWeek } from \"../../../isSameWeek.mjs\";\n\nconst accusativeWeekdays = [\n  \"жексенбіде\",\n  \"дүйсенбіде\",\n  \"сейсенбіде\",\n  \"сәрсенбіде\",\n  \"бейсенбіде\",\n  \"жұмада\",\n  \"сенбіде\",\n];\n\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  return \"'өткен \" + weekday + \" сағат' p'-де'\";\n}\n\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  return \"'\" + weekday + \" сағат' p'-де'\";\n}\n\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  return \"'келесі \" + weekday + \" сағат' p'-де'\";\n}\n\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'кеше сағат' p'-де'\",\n  today: \"'бүгін сағат' p'-де'\",\n  tomorrow: \"'ертең сағат' p'-де'\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n", "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"б.з.д.\", \"б.з.\"],\n  abbreviated: [\"б.з.д.\", \"б.з.\"],\n  wide: [\"біздің заманымызға дейін\", \"біздің заманымыз\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ші тоқ.\", \"2-ші тоқ.\", \"3-ші тоқ.\", \"4-ші тоқ.\"],\n  wide: [\"1-ші тоқсан\", \"2-ші тоқсан\", \"3-ші тоқсан\", \"4-ші тоқсан\"],\n};\n\nconst monthValues = {\n  narrow: [\"Қ\", \"А\", \"Н\", \"С\", \"М\", \"М\", \"Ш\", \"Т\", \"Қ\", \"Қ\", \"Қ\", \"Ж\"],\n  abbreviated: [\n    \"қаң\",\n    \"ақп\",\n    \"нау\",\n    \"сәу\",\n    \"мам\",\n    \"мау\",\n    \"шіл\",\n    \"там\",\n    \"қыр\",\n    \"қаз\",\n    \"қар\",\n    \"жел\",\n  ],\n\n  wide: [\n    \"қаңтар\",\n    \"ақпан\",\n    \"наурыз\",\n    \"сәуір\",\n    \"мамыр\",\n    \"маусым\",\n    \"шілде\",\n    \"тамыз\",\n    \"қыркүйек\",\n    \"қазан\",\n    \"қараша\",\n    \"желтоқсан\",\n  ],\n};\nconst formattingMonthValues = {\n  narrow: [\"Қ\", \"А\", \"Н\", \"С\", \"М\", \"М\", \"Ш\", \"Т\", \"Қ\", \"Қ\", \"Қ\", \"Ж\"],\n  abbreviated: [\n    \"қаң\",\n    \"ақп\",\n    \"нау\",\n    \"сәу\",\n    \"мам\",\n    \"мау\",\n    \"шіл\",\n    \"там\",\n    \"қыр\",\n    \"қаз\",\n    \"қар\",\n    \"жел\",\n  ],\n\n  wide: [\n    \"қаңтар\",\n    \"ақпан\",\n    \"наурыз\",\n    \"сәуір\",\n    \"мамыр\",\n    \"маусым\",\n    \"шілде\",\n    \"тамыз\",\n    \"қыркүйек\",\n    \"қазан\",\n    \"қараша\",\n    \"желтоқсан\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Ж\", \"Д\", \"С\", \"С\", \"Б\", \"Ж\", \"С\"],\n  short: [\"жс\", \"дс\", \"сс\", \"ср\", \"бс\", \"жм\", \"сб\"],\n  abbreviated: [\"жс\", \"дс\", \"сс\", \"ср\", \"бс\", \"жм\", \"сб\"],\n  wide: [\n    \"жексенбі\",\n    \"дүйсенбі\",\n    \"сейсенбі\",\n    \"сәрсенбі\",\n    \"бейсенбі\",\n    \"жұма\",\n    \"сенбі\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасы\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күндіз\",\n    evening: \"кеш\",\n    night: \"түн\",\n  },\n  wide: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасы\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күндіз\",\n    evening: \"кеш\",\n    night: \"түн\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасында\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күн\",\n    evening: \"кеш\",\n    night: \"түн\",\n  },\n  wide: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасында\",\n    noon: \"түсте\",\n    morning: \"таңертең\",\n    afternoon: \"күндіз\",\n    evening: \"кеште\",\n    night: \"түнде\",\n  },\n};\n\nconst suffixes = {\n  0: \"-ші\",\n  1: \"-ші\",\n  2: \"-ші\",\n  3: \"-ші\",\n  4: \"-ші\",\n  5: \"-ші\",\n  6: \"-шы\",\n  7: \"-ші\",\n  8: \"-ші\",\n  9: \"-шы\",\n  10: \"-шы\",\n  20: \"-шы\",\n  30: \"-шы\",\n  40: \"-шы\",\n  50: \"-ші\",\n  60: \"-шы\",\n  70: \"-ші\",\n  80: \"-ші\",\n  90: \"-шы\",\n  100: \"-ші\",\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  const mod10 = number % 10;\n  const b = number >= 100 ? 100 : null;\n  const suffix =\n    suffixes[number] || suffixes[mod10] || (b && suffixes[b]) || \"\";\n\n  return number + suffix;\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n", "import { buildMatchFn } from \"../../_lib/buildMatchFn.mjs\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.mjs\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(ші|шы))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^((б )?з\\.?\\s?д\\.?)/i,\n  abbreviated: /^((б )?з\\.?\\s?д\\.?)/i,\n  wide: /^(біздің заманымызға дейін|біздің заманымыз|біздің заманымыздан)/i,\n};\nconst parseEraPatterns = {\n  any: [/^б/i, /^з/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?ші)? тоқ.?/i,\n  wide: /^[1234](-?ші)? тоқсан/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^(қ|а|н|с|м|мау|ш|т|қыр|қаз|қар|ж)/i,\n  abbreviated: /^(қаң|ақп|нау|сәу|мам|мау|шіл|там|қыр|қаз|қар|жел)/i,\n  wide: /^(қаңтар|ақпан|наурыз|сәуір|мамыр|маусым|шілде|тамыз|қыркүйек|қазан|қараша|желтоқсан)/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /^қ/i,\n    /^а/i,\n    /^н/i,\n    /^с/i,\n    /^м/i,\n    /^м/i,\n    /^ш/i,\n    /^т/i,\n    /^қ/i,\n    /^қ/i,\n    /^қ/i,\n    /^ж/i,\n  ],\n\n  abbreviated: [\n    /^қаң/i,\n    /^ақп/i,\n    /^нау/i,\n    /^сәу/i,\n    /^мам/i,\n    /^мау/i,\n    /^шіл/i,\n    /^там/i,\n    /^қыр/i,\n    /^қаз/i,\n    /^қар/i,\n    /^жел/i,\n  ],\n\n  any: [\n    /^қ/i,\n    /^а/i,\n    /^н/i,\n    /^с/i,\n    /^м/i,\n    /^м/i,\n    /^ш/i,\n    /^т/i,\n    /^қ/i,\n    /^қ/i,\n    /^қ/i,\n    /^ж/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^(ж|д|с|с|б|ж|с)/i,\n  short: /^(жс|дс|сс|ср|бс|жм|сб)/i,\n  wide: /^(жексенбі|дүйсенбі|сейсенбі|сәрсенбі|бейсенбі|жұма|сенбі)/i,\n};\nconst parseDayPatterns = {\n  narrow: [/^ж/i, /^д/i, /^с/i, /^с/i, /^б/i, /^ж/i, /^с/i],\n  short: [/^жс/i, /^дс/i, /^сс/i, /^ср/i, /^бс/i, /^жм/i, /^сб/i],\n  any: [\n    /^ж[ек]/i,\n    /^д[үй]/i,\n    /^сe[й]/i,\n    /^сә[р]/i,\n    /^б[ей]/i,\n    /^ж[ұм]/i,\n    /^се[н]/i,\n  ],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow:\n    /^Т\\.?\\s?[ДК]\\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\\.?)/i,\n  wide: /^Т\\.?\\s?[ДК]\\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\\.?)/i,\n  any: /^Т\\.?\\s?[ДК]\\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\\.?)/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^ТД/i,\n    pm: /^ТК/i,\n    midnight: /^түн орта/i,\n    noon: /^күндіз/i,\n    morning: /таң/i,\n    afternoon: /түс/i,\n    evening: /кеш/i,\n    night: /түн/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n", "import { formatDistance } from \"./kk/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./kk/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./kk/_lib/formatRelative.mjs\";\nimport { localize } from \"./kk/_lib/localize.mjs\";\nimport { match } from \"./kk/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Kazakh locale.\n * @language Kazakh\n * @iso-639-2 kaz\n * <AUTHOR> [@drugoi](https://github.com/drugoi)\n */\nexport const kk = {\n  code: \"kk\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default kk;\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAM,uBAAuB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,SAAS;AAAA,MACP,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,UAAU;AAAA,IACR,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,aAAa,CAAC,YAAY;AACxB,QAAI,mCAAS,WAAW;AACtB,UAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,kBAAkB;AAAA,IAChB,SAAS;AAAA,MACP,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,UAAU;AAAA,IACR,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,aAAa;AAAA,IACX,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,QAAQ;AAAA,IACN,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,IACL,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EAEA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EAEA,cAAc;AAAA,IACZ,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,aAAa;AAAA,IACX,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,QAAQ;AAAA,IACN,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,YAAY;AAAA,IACV,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,cAAc;AAAA,IACZ,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF;AACF;AAEA,SAAS,WAAW,QAAQ,OAAO;AAEjC,MAAI,OAAO,OAAO,UAAU;AAAG,WAAO,OAAO;AAE7C,QAAM,QAAQ,QAAQ;AACtB,QAAM,SAAS,QAAQ;AAGvB,MAAI,UAAU,KAAK,WAAW,IAAI;AAChC,WAAO,OAAO,mBAAmB,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAGrE,WAAW,SAAS,KAAK,SAAS,MAAM,SAAS,MAAM,SAAS,KAAK;AACnE,WAAO,OAAO,iBAAiB,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAGnE,OAAO;AACL,WAAO,OAAO,eAAe,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EACjE;AACF;AAEO,IAAM,iBAAiB,CAAC,OAAO,OAAO,YAAY;AACvD,QAAM,aAAa,qBAAqB,KAAK;AAE7C,MAAI,OAAO,eAAe;AAAY,WAAO,WAAW,OAAO;AAE/D,MAAI,WAAW,SAAS,SAAS;AAC/B,WAAO,UAAU,IACb,WAAW,MACX,WAAW,MAAM,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EACzD;AAEA,MAAI,mCAAS,WAAW;AACtB,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,UAAI,WAAW,QAAQ;AACrB,eAAO,WAAW,WAAW,QAAQ,KAAK;AAAA,MAC5C,OAAO;AACL,eAAO,WAAW,WAAW,SAAS,KAAK,IAAI;AAAA,MACjD;AAAA,IACF,OAAO;AACL,UAAI,WAAW,MAAM;AACnB,eAAO,WAAW,WAAW,MAAM,KAAK;AAAA,MAC1C,OAAO;AACL,eAAO,WAAW,WAAW,SAAS,KAAK,IAAI;AAAA,MACjD;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,WAAW,WAAW,SAAS,KAAK;AAAA,EAC7C;AACF;;;ACtPA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEA,IAAM,kBAAkB;AAAA,EACtB,KAAK;AACP;AAEO,IAAM,aAAa;AAAA,EACxB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;;;ACjCA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,SAAS,KAAK;AACrB,QAAM,UAAU,mBAAmB,GAAG;AAEtC,SAAO,YAAY,UAAU;AAC/B;AAEA,SAAS,SAAS,KAAK;AACrB,QAAM,UAAU,mBAAmB,GAAG;AAEtC,SAAO,MAAM,UAAU;AACzB;AAEA,SAAS,SAAS,KAAK;AACrB,QAAM,UAAU,mBAAmB,GAAG;AAEtC,SAAO,aAAa,UAAU;AAChC;AAEA,IAAM,uBAAuB;AAAA,EAC3B,UAAU,CAAC,MAAM,UAAU,YAAY;AACrC,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,WAAW,MAAM,UAAU,OAAO,GAAG;AACvC,aAAO,SAAS,GAAG;AAAA,IACrB,OAAO;AACL,aAAO,SAAS,GAAG;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU,CAAC,MAAM,UAAU,YAAY;AACrC,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,WAAW,MAAM,UAAU,OAAO,GAAG;AACvC,aAAO,SAAS,GAAG;AAAA,IACrB,OAAO;AACL,aAAO,SAAS,GAAG;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACT;AAEO,IAAM,iBAAiB,CAAC,OAAO,MAAM,UAAU,YAAY;AAChE,QAAM,SAAS,qBAAqB,KAAK;AAEzC,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,OAAO,MAAM,UAAU,OAAO;AAAA,EACvC;AAEA,SAAO;AACT;;;AC3DA,IAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,UAAU,MAAM;AAAA,EACzB,aAAa,CAAC,UAAU,MAAM;AAAA,EAC9B,MAAM,CAAC,4BAA4B,kBAAkB;AACvD;AAEA,IAAM,gBAAgB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,aAAa,aAAa,aAAa,WAAW;AAAA,EAChE,MAAM,CAAC,eAAe,eAAe,eAAe,aAAa;AACnE;AAEA,IAAM,cAAc;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,wBAAwB;AAAA,EAC5B,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EACtD,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEA,IAAM,4BAA4B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEA,IAAM,WAAW;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AACP;AAEA,IAAM,gBAAgB,CAAC,aAAa,aAAa;AAC/C,QAAM,SAAS,OAAO,WAAW;AACjC,QAAM,QAAQ,SAAS;AACvB,QAAM,IAAI,UAAU,MAAM,MAAM;AAChC,QAAM,SACJ,SAAS,MAAM,KAAK,SAAS,KAAK,KAAM,KAAK,SAAS,CAAC,KAAM;AAE/D,SAAO,SAAS;AAClB;AAEO,IAAM,WAAW;AAAA,EACtB;AAAA,EAEA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,CAAC,YAAY,UAAU;AAAA,EAC3C,CAAC;AAAA,EAED,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AAAA,EAED,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;;;AC1MA,IAAM,4BAA4B;AAClC,IAAM,4BAA4B;AAElC,IAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAM,mBAAmB;AAAA,EACvB,KAAK,CAAC,OAAO,KAAK;AACpB;AAEA,IAAM,uBAAuB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAM,uBAAuB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AAEA,IAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,KAAK;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AACR;AACA,IAAM,mBAAmB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,OAAO,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EAC9D,KAAK;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,yBAAyB;AAAA,EAC7B,QACE;AAAA,EACF,MAAM;AAAA,EACN,KAAK;AACP;AACA,IAAM,yBAAyB;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEO,IAAM,QAAQ;AAAA,EACnB,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,CAAC,UAAU,SAAS,OAAO,EAAE;AAAA,EAC9C,CAAC;AAAA,EAED,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,CAAC,UAAU,QAAQ;AAAA,EACpC,CAAC;AAAA,EAED,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;;;AC/IO,IAAM,KAAK;AAAA,EAChB,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AACF;AAGA,IAAO,aAAQ;", "names": []}