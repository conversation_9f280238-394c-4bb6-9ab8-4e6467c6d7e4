import{c as t,f as a,d as m,l as o,m as e}from"./index.DUmRo3Ep.entry.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";const s={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"dd/MM/yyyy"},i={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},l={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},r={date:t({formats:s,defaultWidth:"full"}),time:t({formats:i,defaultWidth:"full"}),dateTime:t({formats:l,defaultWidth:"full"})},h={code:"en-AU",formatDistance:a,formatLong:r,formatRelative:m,localize:o,match:e,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{h as default,h as enAU};
