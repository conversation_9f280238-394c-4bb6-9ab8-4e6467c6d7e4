{"version": 3, "sources": ["../../../../node_modules/.pnpm/parse-ms@4.0.0/node_modules/parse-ms/index.js", "../../../../node_modules/.pnpm/pretty-ms@9.0.0/node_modules/pretty-ms/index.js"], "sourcesContent": ["const toZeroIfInfinity = value => Number.isFinite(value) ? value : 0;\n\nfunction parseNumber(milliseconds) {\n\treturn {\n\t\tdays: Math.trunc(milliseconds / 86_400_000),\n\t\thours: Math.trunc(milliseconds / 3_600_000 % 24),\n\t\tminutes: Math.trunc(milliseconds / 60_000 % 60),\n\t\tseconds: Math.trunc(milliseconds / 1000 % 60),\n\t\tmilliseconds: Math.trunc(milliseconds % 1000),\n\t\tmicroseconds: Math.trunc(toZeroIfInfinity(milliseconds * 1000) % 1000),\n\t\tnanoseconds: Math.trunc(toZeroIfInfinity(milliseconds * 1e6) % 1000),\n\t};\n}\n\nfunction parseBigint(milliseconds) {\n\treturn {\n\t\tdays: milliseconds / 86_400_000n,\n\t\thours: milliseconds / 3_600_000n % 24n,\n\t\tminutes: milliseconds / 60_000n % 60n,\n\t\tseconds: milliseconds / 1000n % 60n,\n\t\tmilliseconds: milliseconds % 1000n,\n\t\tmicroseconds: 0n,\n\t\tnanoseconds: 0n,\n\t};\n}\n\nexport default function parseMilliseconds(milliseconds) {\n\tswitch (typeof milliseconds) {\n\t\tcase 'number': {\n\t\t\tif (Number.isFinite(milliseconds)) {\n\t\t\t\treturn parseNumber(milliseconds);\n\t\t\t}\n\n\t\t\tbreak;\n\t\t}\n\n\t\tcase 'bigint': {\n\t\t\treturn parseBigint(milliseconds);\n\t\t}\n\n\t\t// No default\n\t}\n\n\tthrow new TypeError('Expected a finite number or bigint');\n}\n", "import parseMilliseconds from 'parse-ms';\n\nconst isZero = value => value === 0 || value === 0n;\nconst pluralize = (word, count) => (count === 1 || count === 1n) ? word : `${word}s`;\n\nconst SECOND_ROUNDING_EPSILON = 0.000_000_1;\nconst ONE_DAY_IN_MILLISECONDS = 24n * 60n * 60n * 1000n;\n\nexport default function prettyMilliseconds(milliseconds, options) {\n\tconst isBigInt = typeof milliseconds === 'bigint';\n\tif (!isBigInt && !Number.isFinite(milliseconds)) {\n\t\tthrow new TypeError('Expected a finite number or bigint');\n\t}\n\n\toptions = {...options};\n\n\tif (options.colonNotation) {\n\t\toptions.compact = false;\n\t\toptions.formatSubMilliseconds = false;\n\t\toptions.separateMilliseconds = false;\n\t\toptions.verbose = false;\n\t}\n\n\tif (options.compact) {\n\t\toptions.unitCount = 1;\n\t\toptions.secondsDecimalDigits = 0;\n\t\toptions.millisecondsDecimalDigits = 0;\n\t}\n\n\tlet result = [];\n\n\tconst floorDecimals = (value, decimalDigits) => {\n\t\tconst flooredInterimValue = Math.floor((value * (10 ** decimalDigits)) + SECOND_ROUNDING_EPSILON);\n\t\tconst flooredValue = Math.round(flooredInterimValue) / (10 ** decimalDigits);\n\t\treturn flooredValue.toFixed(decimalDigits);\n\t};\n\n\tconst add = (value, long, short, valueString) => {\n\t\tif (\n\t\t\t(result.length === 0 || !options.colonNotation)\n\t\t\t&& isZero(value)\n\t\t\t&& !(options.colonNotation && short === 'm')) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalueString = valueString ?? String(value);\n\t\tif (options.colonNotation) {\n\t\t\tconst wholeDigits = valueString.includes('.') ? valueString.split('.')[0].length : valueString.length;\n\t\t\tconst minLength = result.length > 0 ? 2 : 1;\n\t\t\tvalueString = '0'.repeat(Math.max(0, minLength - wholeDigits)) + valueString;\n\t\t} else {\n\t\t\tvalueString += options.verbose ? ' ' + pluralize(long, value) : short;\n\t\t}\n\n\t\tresult.push(valueString);\n\t};\n\n\tconst parsed = parseMilliseconds(milliseconds);\n\tconst days = BigInt(parsed.days);\n\n\tadd(days / 365n, 'year', 'y');\n\tadd(days % 365n, 'day', 'd');\n\tadd(Number(parsed.hours), 'hour', 'h');\n\tadd(Number(parsed.minutes), 'minute', 'm');\n\n\tif (\n\t\toptions.separateMilliseconds\n\t\t|| options.formatSubMilliseconds\n\t\t|| (!options.colonNotation && milliseconds < 1000)\n\t) {\n\t\tconst seconds = Number(parsed.seconds);\n\t\tconst milliseconds = Number(parsed.milliseconds);\n\t\tconst microseconds = Number(parsed.microseconds);\n\t\tconst nanoseconds = Number(parsed.nanoseconds);\n\n\t\tadd(seconds, 'second', 's');\n\n\t\tif (options.formatSubMilliseconds) {\n\t\t\tadd(milliseconds, 'millisecond', 'ms');\n\t\t\tadd(microseconds, 'microsecond', 'µs');\n\t\t\tadd(nanoseconds, 'nanosecond', 'ns');\n\t\t} else {\n\t\t\tconst millisecondsAndBelow\n\t\t\t\t= milliseconds\n\t\t\t\t+ (microseconds / 1000)\n\t\t\t\t+ (nanoseconds / 1e6);\n\n\t\t\tconst millisecondsDecimalDigits\n\t\t\t\t= typeof options.millisecondsDecimalDigits === 'number'\n\t\t\t\t\t? options.millisecondsDecimalDigits\n\t\t\t\t\t: 0;\n\n\t\t\tconst roundedMilliseconds = millisecondsAndBelow >= 1\n\t\t\t\t? Math.round(millisecondsAndBelow)\n\t\t\t\t: Math.ceil(millisecondsAndBelow);\n\n\t\t\tconst millisecondsString = millisecondsDecimalDigits\n\t\t\t\t? millisecondsAndBelow.toFixed(millisecondsDecimalDigits)\n\t\t\t\t: roundedMilliseconds;\n\n\t\t\tadd(\n\t\t\t\tNumber.parseFloat(millisecondsString),\n\t\t\t\t'millisecond',\n\t\t\t\t'ms',\n\t\t\t\tmillisecondsString,\n\t\t\t);\n\t\t}\n\t} else {\n\t\tconst seconds = (\n\t\t\t(isBigInt ? Number(milliseconds % ONE_DAY_IN_MILLISECONDS) : milliseconds)\n\t\t\t/ 1000\n\t\t) % 60;\n\t\tconst secondsDecimalDigits\n\t\t\t= typeof options.secondsDecimalDigits === 'number'\n\t\t\t\t? options.secondsDecimalDigits\n\t\t\t\t: 1;\n\t\tconst secondsFixed = floorDecimals(seconds, secondsDecimalDigits);\n\t\tconst secondsString = options.keepDecimalsOnWholeSeconds\n\t\t\t? secondsFixed\n\t\t\t: secondsFixed.replace(/\\.0+$/, '');\n\t\tadd(Number.parseFloat(secondsString), 'second', 's', secondsString);\n\t}\n\n\tif (result.length === 0) {\n\t\treturn '0' + (options.verbose ? ' milliseconds' : 'ms');\n\t}\n\n\tconst separator = options.colonNotation ? ':' : ' ';\n\tif (typeof options.unitCount === 'number') {\n\t\tresult = result.slice(0, Math.max(options.unitCount, 1));\n\t}\n\n\treturn result.join(separator);\n}\n"], "mappings": ";;;AAAA,IAAM,mBAAmB,WAAS,OAAO,SAAS,KAAK,IAAI,QAAQ;AAEnE,SAAS,YAAY,cAAc;AAClC,SAAO;AAAA,IACN,MAAM,KAAK,MAAM,eAAe,KAAU;AAAA,IAC1C,OAAO,KAAK,MAAM,eAAe,OAAY,EAAE;AAAA,IAC/C,SAAS,KAAK,MAAM,eAAe,MAAS,EAAE;AAAA,IAC9C,SAAS,KAAK,MAAM,eAAe,MAAO,EAAE;AAAA,IAC5C,cAAc,KAAK,MAAM,eAAe,GAAI;AAAA,IAC5C,cAAc,KAAK,MAAM,iBAAiB,eAAe,GAAI,IAAI,GAAI;AAAA,IACrE,aAAa,KAAK,MAAM,iBAAiB,eAAe,GAAG,IAAI,GAAI;AAAA,EACpE;AACD;AAEA,SAAS,YAAY,cAAc;AAClC,SAAO;AAAA,IACN,MAAM,eAAe;AAAA,IACrB,OAAO,eAAe,WAAa;AAAA,IACnC,SAAS,eAAe,SAAU;AAAA,IAClC,SAAS,eAAe,QAAQ;AAAA,IAChC,cAAc,eAAe;AAAA,IAC7B,cAAc;AAAA,IACd,aAAa;AAAA,EACd;AACD;AAEe,SAAR,kBAAmC,cAAc;AACvD,UAAQ,OAAO,cAAc;AAAA,IAC5B,KAAK,UAAU;AACd,UAAI,OAAO,SAAS,YAAY,GAAG;AAClC,eAAO,YAAY,YAAY;AAAA,MAChC;AAEA;AAAA,IACD;AAAA,IAEA,KAAK,UAAU;AACd,aAAO,YAAY,YAAY;AAAA,IAChC;AAAA,EAGD;AAEA,QAAM,IAAI,UAAU,oCAAoC;AACzD;;;AC1CA,IAAM,SAAS,WAAS,UAAU,KAAK,UAAU;AACjD,IAAM,YAAY,CAAC,MAAM,UAAW,UAAU,KAAK,UAAU,KAAM,OAAO,GAAG,IAAI;AAEjF,IAAM,0BAA0B;AAChC,IAAM,0BAA0B,MAAM,MAAM,MAAM;AAEnC,SAAR,mBAAoC,cAAc,SAAS;AACjE,QAAM,WAAW,OAAO,iBAAiB;AACzC,MAAI,CAAC,YAAY,CAAC,OAAO,SAAS,YAAY,GAAG;AAChD,UAAM,IAAI,UAAU,oCAAoC;AAAA,EACzD;AAEA,YAAU,EAAC,GAAG,QAAO;AAErB,MAAI,QAAQ,eAAe;AAC1B,YAAQ,UAAU;AAClB,YAAQ,wBAAwB;AAChC,YAAQ,uBAAuB;AAC/B,YAAQ,UAAU;AAAA,EACnB;AAEA,MAAI,QAAQ,SAAS;AACpB,YAAQ,YAAY;AACpB,YAAQ,uBAAuB;AAC/B,YAAQ,4BAA4B;AAAA,EACrC;AAEA,MAAI,SAAS,CAAC;AAEd,QAAM,gBAAgB,CAAC,OAAO,kBAAkB;AAC/C,UAAM,sBAAsB,KAAK,MAAO,QAAS,MAAM,gBAAkB,uBAAuB;AAChG,UAAM,eAAe,KAAK,MAAM,mBAAmB,IAAK,MAAM;AAC9D,WAAO,aAAa,QAAQ,aAAa;AAAA,EAC1C;AAEA,QAAM,MAAM,CAAC,OAAO,MAAM,OAAO,gBAAgB;AAChD,SACE,OAAO,WAAW,KAAK,CAAC,QAAQ,kBAC9B,OAAO,KAAK,KACZ,EAAE,QAAQ,iBAAiB,UAAU,MAAM;AAC9C;AAAA,IACD;AAEA,kBAAc,eAAe,OAAO,KAAK;AACzC,QAAI,QAAQ,eAAe;AAC1B,YAAM,cAAc,YAAY,SAAS,GAAG,IAAI,YAAY,MAAM,GAAG,EAAE,CAAC,EAAE,SAAS,YAAY;AAC/F,YAAM,YAAY,OAAO,SAAS,IAAI,IAAI;AAC1C,oBAAc,IAAI,OAAO,KAAK,IAAI,GAAG,YAAY,WAAW,CAAC,IAAI;AAAA,IAClE,OAAO;AACN,qBAAe,QAAQ,UAAU,MAAM,UAAU,MAAM,KAAK,IAAI;AAAA,IACjE;AAEA,WAAO,KAAK,WAAW;AAAA,EACxB;AAEA,QAAM,SAAS,kBAAkB,YAAY;AAC7C,QAAM,OAAO,OAAO,OAAO,IAAI;AAE/B,MAAI,OAAO,MAAM,QAAQ,GAAG;AAC5B,MAAI,OAAO,MAAM,OAAO,GAAG;AAC3B,MAAI,OAAO,OAAO,KAAK,GAAG,QAAQ,GAAG;AACrC,MAAI,OAAO,OAAO,OAAO,GAAG,UAAU,GAAG;AAEzC,MACC,QAAQ,wBACL,QAAQ,yBACP,CAAC,QAAQ,iBAAiB,eAAe,KAC5C;AACD,UAAM,UAAU,OAAO,OAAO,OAAO;AACrC,UAAMA,gBAAe,OAAO,OAAO,YAAY;AAC/C,UAAM,eAAe,OAAO,OAAO,YAAY;AAC/C,UAAM,cAAc,OAAO,OAAO,WAAW;AAE7C,QAAI,SAAS,UAAU,GAAG;AAE1B,QAAI,QAAQ,uBAAuB;AAClC,UAAIA,eAAc,eAAe,IAAI;AACrC,UAAI,cAAc,eAAe,IAAI;AACrC,UAAI,aAAa,cAAc,IAAI;AAAA,IACpC,OAAO;AACN,YAAM,uBACHA,gBACC,eAAe,MACf,cAAc;AAElB,YAAM,4BACH,OAAO,QAAQ,8BAA8B,WAC5C,QAAQ,4BACR;AAEJ,YAAM,sBAAsB,wBAAwB,IACjD,KAAK,MAAM,oBAAoB,IAC/B,KAAK,KAAK,oBAAoB;AAEjC,YAAM,qBAAqB,4BACxB,qBAAqB,QAAQ,yBAAyB,IACtD;AAEH;AAAA,QACC,OAAO,WAAW,kBAAkB;AAAA,QACpC;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAAA,EACD,OAAO;AACN,UAAM,WACJ,WAAW,OAAO,eAAe,uBAAuB,IAAI,gBAC3D,MACC;AACJ,UAAM,uBACH,OAAO,QAAQ,yBAAyB,WACvC,QAAQ,uBACR;AACJ,UAAM,eAAe,cAAc,SAAS,oBAAoB;AAChE,UAAM,gBAAgB,QAAQ,6BAC3B,eACA,aAAa,QAAQ,SAAS,EAAE;AACnC,QAAI,OAAO,WAAW,aAAa,GAAG,UAAU,KAAK,aAAa;AAAA,EACnE;AAEA,MAAI,OAAO,WAAW,GAAG;AACxB,WAAO,OAAO,QAAQ,UAAU,kBAAkB;AAAA,EACnD;AAEA,QAAM,YAAY,QAAQ,gBAAgB,MAAM;AAChD,MAAI,OAAO,QAAQ,cAAc,UAAU;AAC1C,aAAS,OAAO,MAAM,GAAG,KAAK,IAAI,QAAQ,WAAW,CAAC,CAAC;AAAA,EACxD;AAEA,SAAO,OAAO,KAAK,SAAS;AAC7B;", "names": ["milliseconds"]}