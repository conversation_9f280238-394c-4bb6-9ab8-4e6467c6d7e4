{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/table/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const eq$1 = t => a => t === a;\n    const isString = isType$1('string');\n    const isArray = isType$1('array');\n    const isBoolean = isSimpleType('boolean');\n    const isUndefined = eq$1(undefined);\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const noop = () => {\n    };\n    const compose1 = (fbc, fab) => a => fbc(fab(a));\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const identity = x => {\n      return x;\n    };\n    const tripleEquals = (a, b) => {\n      return a === b;\n    };\n    function curry(fn, ...initialArgs) {\n      return (...restArgs) => {\n        const all = initialArgs.concat(restArgs);\n        return fn.apply(null, all);\n      };\n    }\n    const call = f => {\n      f();\n    };\n    const never = constant(false);\n    const always = constant(true);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const keys = Object.keys;\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each$1 = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n    const objAcc = r => (x, i) => {\n      r[i] = x;\n    };\n    const internalFilter = (obj, pred, onTrue, onFalse) => {\n      each$1(obj, (x, i) => {\n        (pred(x, i) ? onTrue : onFalse)(x, i);\n      });\n    };\n    const filter$1 = (obj, pred) => {\n      const t = {};\n      internalFilter(obj, pred, objAcc(t), noop);\n      return t;\n    };\n    const mapToArray = (obj, f) => {\n      const r = [];\n      each$1(obj, (value, name) => {\n        r.push(f(value, name));\n      });\n      return r;\n    };\n    const values = obj => {\n      return mapToArray(obj, identity);\n    };\n    const size = obj => {\n      return keys(obj).length;\n    };\n    const get$4 = (obj, key) => {\n      return has(obj, key) ? Optional.from(obj[key]) : Optional.none();\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n    const hasNonNullableKey = (obj, key) => has(obj, key) && obj[key] !== undefined && obj[key] !== null;\n    const isEmpty$1 = r => {\n      for (const x in r) {\n        if (hasOwnProperty.call(r, x)) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    const nativeIndexOf = Array.prototype.indexOf;\n    const nativePush = Array.prototype.push;\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains = (xs, x) => rawIndexOf(xs, x) > -1;\n    const exists = (xs, pred) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return true;\n        }\n      }\n      return false;\n    };\n    const range = (num, f) => {\n      const r = [];\n      for (let i = 0; i < num; i++) {\n        r.push(f(i));\n      }\n      return r;\n    };\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const eachr = (xs, f) => {\n      for (let i = xs.length - 1; i >= 0; i--) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const partition = (xs, pred) => {\n      const pass = [];\n      const fail = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        const arr = pred(x, i) ? pass : fail;\n        arr.push(x);\n      }\n      return {\n        pass,\n        fail\n      };\n    };\n    const filter = (xs, pred) => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    const foldr = (xs, f, acc) => {\n      eachr(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    const foldl = (xs, f, acc) => {\n      each(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    const findUntil = (xs, pred, until) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const find = (xs, pred) => {\n      return findUntil(xs, pred, never);\n    };\n    const flatten$1 = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    const bind = (xs, f) => flatten$1(map(xs, f));\n    const forall = (xs, pred) => {\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        const x = xs[i];\n        if (pred(x, i) !== true) {\n          return false;\n        }\n      }\n      return true;\n    };\n    const mapToObject = (xs, f) => {\n      const r = {};\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        r[String(x)] = f(x, i);\n      }\n      return r;\n    };\n    const get$3 = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = xs => get$3(xs, 0);\n    const last = xs => get$3(xs, xs.length - 1);\n    const findMap = (arr, f) => {\n      for (let i = 0; i < arr.length; i++) {\n        const r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    const COMMENT = 8;\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom$1(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom$1(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom$1(node);\n    };\n    const fromDom$1 = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom$1);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom: fromDom$1,\n      fromPoint\n    };\n\n    const is$2 = (element, selector) => {\n      const dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        const elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n    const bypassSelector = dom => dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;\n    const all$1 = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n    const one = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? Optional.none() : Optional.from(base.querySelector(selector)).map(SugarElement.fromDom);\n    };\n\n    const eq = (e1, e2) => e1.dom === e2.dom;\n    const is$1 = is$2;\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const name = element => {\n      const r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n    const type = element => element.dom.nodeType;\n    const isType = t => element => type(element) === t;\n    const isComment = element => type(element) === COMMENT || name(element) === '#comment';\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocument = isType(DOCUMENT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n    const isTag = tag => e => isElement(e) && name(e) === tag;\n\n    const owner = element => SugarElement.fromDom(element.dom.ownerDocument);\n    const documentOrOwner = dos => isDocument(dos) ? dos : owner(dos);\n    const parent = element => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const parents = (element, isRoot) => {\n      const stop = isFunction(isRoot) ? isRoot : never;\n      let dom = element.dom;\n      const ret = [];\n      while (dom.parentNode !== null && dom.parentNode !== undefined) {\n        const rawParent = dom.parentNode;\n        const p = SugarElement.fromDom(rawParent);\n        ret.push(p);\n        if (stop(p) === true) {\n          break;\n        } else {\n          dom = rawParent;\n        }\n      }\n      return ret;\n    };\n    const prevSibling = element => Optional.from(element.dom.previousSibling).map(SugarElement.fromDom);\n    const nextSibling = element => Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);\n    const children$3 = element => map(element.dom.childNodes, SugarElement.fromDom);\n    const child$3 = (element, index) => {\n      const cs = element.dom.childNodes;\n      return Optional.from(cs[index]).map(SugarElement.fromDom);\n    };\n    const firstChild = element => child$3(element, 0);\n\n    const isShadowRoot = dos => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    const getRootNode = supported ? e => SugarElement.fromDom(e.dom.getRootNode()) : documentOrOwner;\n    const getShadowRoot = e => {\n      const r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    const getShadowHost = e => SugarElement.fromDom(e.dom.host);\n\n    const inBody = element => {\n      const dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      const doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n\n    var ClosestOrAncestor = (is, ancestor, scope, a, isRoot) => {\n      if (is(scope, a)) {\n        return Optional.some(scope);\n      } else if (isFunction(isRoot) && isRoot(scope)) {\n        return Optional.none();\n      } else {\n        return ancestor(scope, a, isRoot);\n      }\n    };\n\n    const ancestor$1 = (scope, predicate, isRoot) => {\n      let element = scope.dom;\n      const stop = isFunction(isRoot) ? isRoot : never;\n      while (element.parentNode) {\n        element = element.parentNode;\n        const el = SugarElement.fromDom(element);\n        if (predicate(el)) {\n          return Optional.some(el);\n        } else if (stop(el)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const closest$2 = (scope, predicate, isRoot) => {\n      const is = (s, test) => test(s);\n      return ClosestOrAncestor(is, ancestor$1, scope, predicate, isRoot);\n    };\n    const child$2 = (scope, predicate) => {\n      const pred = node => predicate(SugarElement.fromDom(node));\n      const result = find(scope.dom.childNodes, pred);\n      return result.map(SugarElement.fromDom);\n    };\n\n    const ancestor = (scope, selector, isRoot) => ancestor$1(scope, e => is$2(e, selector), isRoot);\n    const child$1 = (scope, selector) => child$2(scope, e => is$2(e, selector));\n    const descendant = (scope, selector) => one(selector, scope);\n    const closest$1 = (scope, selector, isRoot) => {\n      const is = (element, selector) => is$2(element, selector);\n      return ClosestOrAncestor(is, ancestor, scope, selector, isRoot);\n    };\n\n    const closest = target => closest$1(target, '[contenteditable]');\n    const isEditable = (element, assumeEditable = false) => {\n      if (inBody(element)) {\n        return element.dom.isContentEditable;\n      } else {\n        return closest(element).fold(constant(assumeEditable), editable => getRaw$1(editable) === 'true');\n      }\n    };\n    const getRaw$1 = element => element.dom.contentEditable;\n\n    const getNodeName = elm => elm.nodeName.toLowerCase();\n    const getBody = editor => SugarElement.fromDom(editor.getBody());\n    const getIsRoot = editor => element => eq(element, getBody(editor));\n    const removePxSuffix = size => size ? size.replace(/px$/, '') : '';\n    const addPxSuffix = size => /^\\d+(\\.\\d+)?$/.test(size) ? size + 'px' : size;\n    const getSelectionStart = editor => SugarElement.fromDom(editor.selection.getStart());\n    const getSelectionEnd = editor => SugarElement.fromDom(editor.selection.getEnd());\n    const isInEditableContext = cell => closest$2(cell, isTag('table')).forall(isEditable);\n\n    const children$2 = (scope, predicate) => filter(children$3(scope), predicate);\n    const descendants$1 = (scope, predicate) => {\n      let result = [];\n      each(children$3(scope), x => {\n        if (predicate(x)) {\n          result = result.concat([x]);\n        }\n        result = result.concat(descendants$1(x, predicate));\n      });\n      return result;\n    };\n\n    const children$1 = (scope, selector) => children$2(scope, e => is$2(e, selector));\n    const descendants = (scope, selector) => all$1(selector, scope);\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const set$2 = (element, key, value) => {\n      rawSet(element.dom, key, value);\n    };\n    const setAll = (element, attrs) => {\n      const dom = element.dom;\n      each$1(attrs, (v, k) => {\n        rawSet(dom, k, v);\n      });\n    };\n    const get$2 = (element, key) => {\n      const v = element.dom.getAttribute(key);\n      return v === null ? undefined : v;\n    };\n    const getOpt = (element, key) => Optional.from(get$2(element, key));\n    const remove$2 = (element, key) => {\n      element.dom.removeAttribute(key);\n    };\n    const clone = element => foldl(element.dom.attributes, (acc, attr) => {\n      acc[attr.name] = attr.value;\n      return acc;\n    }, {});\n\n    const is = (lhs, rhs, comparator = tripleEquals) => lhs.exists(left => comparator(left, rhs));\n    const cat = arr => {\n      const r = [];\n      const push = x => {\n        r.push(x);\n      };\n      for (let i = 0; i < arr.length; i++) {\n        arr[i].each(push);\n      }\n      return r;\n    };\n    const lift2 = (oa, ob, f) => oa.isSome() && ob.isSome() ? Optional.some(f(oa.getOrDie(), ob.getOrDie())) : Optional.none();\n    const flatten = oot => oot.bind(identity);\n    const someIf = (b, a) => b ? Optional.some(a) : Optional.none();\n\n    const removeFromStart = (str, numChars) => {\n      return str.substring(numChars);\n    };\n\n    const checkRange = (str, substr, start) => substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    const removeLeading = (str, prefix) => {\n      return startsWith(str, prefix) ? removeFromStart(str, prefix.length) : str;\n    };\n    const startsWith = (str, prefix) => {\n      return checkRange(str, prefix, 0);\n    };\n    const blank = r => s => s.replace(r, '');\n    const trim = blank(/^\\s+|\\s+$/g);\n    const isNotEmpty = s => s.length > 0;\n    const isEmpty = s => !isNotEmpty(s);\n    const toInt = (value, radix = 10) => {\n      const num = parseInt(value, radix);\n      return isNaN(num) ? Optional.none() : Optional.some(num);\n    };\n    const toFloat = value => {\n      const num = parseFloat(value);\n      return isNaN(num) ? Optional.none() : Optional.some(num);\n    };\n\n    const isSupported = dom => dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    const internalSet = (dom, property, value) => {\n      if (!isString(value)) {\n        console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n        throw new Error('CSS value must be a string: ' + value);\n      }\n      if (isSupported(dom)) {\n        dom.style.setProperty(property, value);\n      }\n    };\n    const internalRemove = (dom, property) => {\n      if (isSupported(dom)) {\n        dom.style.removeProperty(property);\n      }\n    };\n    const set$1 = (element, property, value) => {\n      const dom = element.dom;\n      internalSet(dom, property, value);\n    };\n    const get$1 = (element, property) => {\n      const dom = element.dom;\n      const styles = window.getComputedStyle(dom);\n      const r = styles.getPropertyValue(property);\n      return r === '' && !inBody(element) ? getUnsafeProperty(dom, property) : r;\n    };\n    const getUnsafeProperty = (dom, property) => isSupported(dom) ? dom.style.getPropertyValue(property) : '';\n    const getRaw = (element, property) => {\n      const dom = element.dom;\n      const raw = getUnsafeProperty(dom, property);\n      return Optional.from(raw).filter(r => r.length > 0);\n    };\n    const remove$1 = (element, property) => {\n      const dom = element.dom;\n      internalRemove(dom, property);\n      if (is(getOpt(element, 'style').map(trim), '')) {\n        remove$2(element, 'style');\n      }\n    };\n\n    const getAttrValue = (cell, name, fallback = 0) => getOpt(cell, name).map(value => parseInt(value, 10)).getOr(fallback);\n\n    const firstLayer = (scope, selector) => {\n      return filterFirstLayer(scope, selector, always);\n    };\n    const filterFirstLayer = (scope, selector, predicate) => {\n      return bind(children$3(scope), x => {\n        if (is$2(x, selector)) {\n          return predicate(x) ? [x] : [];\n        } else {\n          return filterFirstLayer(x, selector, predicate);\n        }\n      });\n    };\n\n    const validSectionList = [\n      'tfoot',\n      'thead',\n      'tbody',\n      'colgroup'\n    ];\n    const isValidSection = parentName => contains(validSectionList, parentName);\n    const grid = (rows, columns) => ({\n      rows,\n      columns\n    });\n    const detail = (element, rowspan, colspan) => ({\n      element,\n      rowspan,\n      colspan\n    });\n    const extended = (element, rowspan, colspan, row, column, isLocked) => ({\n      element,\n      rowspan,\n      colspan,\n      row,\n      column,\n      isLocked\n    });\n    const rowdetail = (element, cells, section) => ({\n      element,\n      cells,\n      section\n    });\n    const bounds = (startRow, startCol, finishRow, finishCol) => ({\n      startRow,\n      startCol,\n      finishRow,\n      finishCol\n    });\n    const columnext = (element, colspan, column) => ({\n      element,\n      colspan,\n      column\n    });\n    const colgroup = (element, columns) => ({\n      element,\n      columns\n    });\n\n    const lookup = (tags, element, isRoot = never) => {\n      if (isRoot(element)) {\n        return Optional.none();\n      }\n      if (contains(tags, name(element))) {\n        return Optional.some(element);\n      }\n      const isRootOrUpperTable = elm => is$2(elm, 'table') || isRoot(elm);\n      return ancestor(element, tags.join(','), isRootOrUpperTable);\n    };\n    const cell = (element, isRoot) => lookup([\n      'td',\n      'th'\n    ], element, isRoot);\n    const cells = ancestor => firstLayer(ancestor, 'th,td');\n    const columns = ancestor => {\n      if (is$2(ancestor, 'colgroup')) {\n        return children$1(ancestor, 'col');\n      } else {\n        return bind(columnGroups(ancestor), columnGroup => children$1(columnGroup, 'col'));\n      }\n    };\n    const table = (element, isRoot) => closest$1(element, 'table', isRoot);\n    const rows = ancestor => firstLayer(ancestor, 'tr');\n    const columnGroups = ancestor => table(ancestor).fold(constant([]), table => children$1(table, 'colgroup'));\n\n    const fromRowsOrColGroups = (elems, getSection) => map(elems, row => {\n      if (name(row) === 'colgroup') {\n        const cells = map(columns(row), column => {\n          const colspan = getAttrValue(column, 'span', 1);\n          return detail(column, 1, colspan);\n        });\n        return rowdetail(row, cells, 'colgroup');\n      } else {\n        const cells$1 = map(cells(row), cell => {\n          const rowspan = getAttrValue(cell, 'rowspan', 1);\n          const colspan = getAttrValue(cell, 'colspan', 1);\n          return detail(cell, rowspan, colspan);\n        });\n        return rowdetail(row, cells$1, getSection(row));\n      }\n    });\n    const getParentSection = group => parent(group).map(parent => {\n      const parentName = name(parent);\n      return isValidSection(parentName) ? parentName : 'tbody';\n    }).getOr('tbody');\n    const fromTable$1 = table => {\n      const rows$1 = rows(table);\n      const columnGroups$1 = columnGroups(table);\n      const elems = [\n        ...columnGroups$1,\n        ...rows$1\n      ];\n      return fromRowsOrColGroups(elems, getParentSection);\n    };\n\n    const LOCKED_COL_ATTR = 'data-snooker-locked-cols';\n    const getLockedColumnsFromTable = table => getOpt(table, LOCKED_COL_ATTR).bind(lockedColStr => Optional.from(lockedColStr.match(/\\d+/g))).map(lockedCols => mapToObject(lockedCols, always));\n\n    const key = (row, column) => {\n      return row + ',' + column;\n    };\n    const getAt = (warehouse, row, column) => Optional.from(warehouse.access[key(row, column)]);\n    const findItem = (warehouse, item, comparator) => {\n      const filtered = filterItems(warehouse, detail => {\n        return comparator(item, detail.element);\n      });\n      return filtered.length > 0 ? Optional.some(filtered[0]) : Optional.none();\n    };\n    const filterItems = (warehouse, predicate) => {\n      const all = bind(warehouse.all, r => {\n        return r.cells;\n      });\n      return filter(all, predicate);\n    };\n    const generateColumns = rowData => {\n      const columnsGroup = {};\n      let index = 0;\n      each(rowData.cells, column => {\n        const colspan = column.colspan;\n        range(colspan, columnIndex => {\n          const colIndex = index + columnIndex;\n          columnsGroup[colIndex] = columnext(column.element, colspan, colIndex);\n        });\n        index += colspan;\n      });\n      return columnsGroup;\n    };\n    const generate$1 = list => {\n      const access = {};\n      const cells = [];\n      const tableOpt = head(list).map(rowData => rowData.element).bind(table);\n      const lockedColumns = tableOpt.bind(getLockedColumnsFromTable).getOr({});\n      let maxRows = 0;\n      let maxColumns = 0;\n      let rowCount = 0;\n      const {\n        pass: colgroupRows,\n        fail: rows\n      } = partition(list, rowData => rowData.section === 'colgroup');\n      each(rows, rowData => {\n        const currentRow = [];\n        each(rowData.cells, rowCell => {\n          let start = 0;\n          while (access[key(rowCount, start)] !== undefined) {\n            start++;\n          }\n          const isLocked = hasNonNullableKey(lockedColumns, start.toString());\n          const current = extended(rowCell.element, rowCell.rowspan, rowCell.colspan, rowCount, start, isLocked);\n          for (let occupiedColumnPosition = 0; occupiedColumnPosition < rowCell.colspan; occupiedColumnPosition++) {\n            for (let occupiedRowPosition = 0; occupiedRowPosition < rowCell.rowspan; occupiedRowPosition++) {\n              const rowPosition = rowCount + occupiedRowPosition;\n              const columnPosition = start + occupiedColumnPosition;\n              const newpos = key(rowPosition, columnPosition);\n              access[newpos] = current;\n              maxColumns = Math.max(maxColumns, columnPosition + 1);\n            }\n          }\n          currentRow.push(current);\n        });\n        maxRows++;\n        cells.push(rowdetail(rowData.element, currentRow, rowData.section));\n        rowCount++;\n      });\n      const {columns, colgroups} = last(colgroupRows).map(rowData => {\n        const columns = generateColumns(rowData);\n        const colgroup$1 = colgroup(rowData.element, values(columns));\n        return {\n          colgroups: [colgroup$1],\n          columns\n        };\n      }).getOrThunk(() => ({\n        colgroups: [],\n        columns: {}\n      }));\n      const grid$1 = grid(maxRows, maxColumns);\n      return {\n        grid: grid$1,\n        access,\n        all: cells,\n        columns,\n        colgroups\n      };\n    };\n    const fromTable = table => {\n      const list = fromTable$1(table);\n      return generate$1(list);\n    };\n    const justCells = warehouse => bind(warehouse.all, w => w.cells);\n    const justColumns = warehouse => values(warehouse.columns);\n    const hasColumns = warehouse => keys(warehouse.columns).length > 0;\n    const getColumnAt = (warehouse, columnIndex) => Optional.from(warehouse.columns[columnIndex]);\n    const Warehouse = {\n      fromTable,\n      generate: generate$1,\n      getAt,\n      findItem,\n      filterItems,\n      justCells,\n      justColumns,\n      hasColumns,\n      getColumnAt\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const getTDTHOverallStyle = (dom, elm, name) => {\n      const cells = dom.select('td,th', elm);\n      let firstChildStyle;\n      for (let i = 0; i < cells.length; i++) {\n        const currentStyle = dom.getStyle(cells[i], name);\n        if (isUndefined(firstChildStyle)) {\n          firstChildStyle = currentStyle;\n        }\n        if (firstChildStyle !== currentStyle) {\n          return '';\n        }\n      }\n      return firstChildStyle;\n    };\n    const setAlign = (editor, elm, name) => {\n      global$2.each('left center right'.split(' '), align => {\n        if (align !== name) {\n          editor.formatter.remove('align' + align, {}, elm);\n        }\n      });\n      if (name) {\n        editor.formatter.apply('align' + name, {}, elm);\n      }\n    };\n    const setVAlign = (editor, elm, name) => {\n      global$2.each('top middle bottom'.split(' '), align => {\n        if (align !== name) {\n          editor.formatter.remove('valign' + align, {}, elm);\n        }\n      });\n      if (name) {\n        editor.formatter.apply('valign' + name, {}, elm);\n      }\n    };\n\n    const fireTableModified = (editor, table, data) => {\n      editor.dispatch('TableModified', {\n        ...data,\n        table\n      });\n    };\n\n    const toNumber = (px, fallback) => toFloat(px).getOr(fallback);\n    const getProp = (element, name, fallback) => toNumber(get$1(element, name), fallback);\n    const calcContentBoxSize = (element, size, upper, lower) => {\n      const paddingUpper = getProp(element, `padding-${ upper }`, 0);\n      const paddingLower = getProp(element, `padding-${ lower }`, 0);\n      const borderUpper = getProp(element, `border-${ upper }-width`, 0);\n      const borderLower = getProp(element, `border-${ lower }-width`, 0);\n      return size - paddingUpper - paddingLower - borderUpper - borderLower;\n    };\n    const getCalculatedWidth = (element, boxSizing) => {\n      const dom = element.dom;\n      const width = dom.getBoundingClientRect().width || dom.offsetWidth;\n      return boxSizing === 'border-box' ? width : calcContentBoxSize(element, width, 'left', 'right');\n    };\n    const getInnerWidth = element => getCalculatedWidth(element, 'content-box');\n\n    const getInner = getInnerWidth;\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const defaultTableToolbar = 'tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol';\n    const defaultCellBorderWidths = range(5, i => {\n      const size = `${ i + 1 }px`;\n      return {\n        title: size,\n        value: size\n      };\n    });\n    const defaultCellBorderStyles = map([\n      'Solid',\n      'Dotted',\n      'Dashed',\n      'Double',\n      'Groove',\n      'Ridge',\n      'Inset',\n      'Outset',\n      'None',\n      'Hidden'\n    ], type => {\n      return {\n        title: type,\n        value: type.toLowerCase()\n      };\n    });\n    const defaultWidth = '100%';\n    const getPixelForcedWidth = editor => {\n      var _a;\n      const dom = editor.dom;\n      const parentBlock = (_a = dom.getParent(editor.selection.getStart(), dom.isBlock)) !== null && _a !== void 0 ? _a : editor.getBody();\n      return getInner(SugarElement.fromDom(parentBlock)) + 'px';\n    };\n    const determineDefaultStyles = (editor, defaultStyles) => {\n      if (isResponsiveForced(editor) || !shouldStyleWithCss(editor)) {\n        return defaultStyles;\n      } else if (isPixelsForced(editor)) {\n        return {\n          ...defaultStyles,\n          width: getPixelForcedWidth(editor)\n        };\n      } else {\n        return {\n          ...defaultStyles,\n          width: defaultWidth\n        };\n      }\n    };\n    const determineDefaultAttributes = (editor, defaultAttributes) => {\n      if (isResponsiveForced(editor) || shouldStyleWithCss(editor)) {\n        return defaultAttributes;\n      } else if (isPixelsForced(editor)) {\n        return {\n          ...defaultAttributes,\n          width: getPixelForcedWidth(editor)\n        };\n      } else {\n        return {\n          ...defaultAttributes,\n          width: defaultWidth\n        };\n      }\n    };\n    const option = name => editor => editor.options.get(name);\n    const register = editor => {\n      const registerOption = editor.options.register;\n      registerOption('table_border_widths', {\n        processor: 'object[]',\n        default: defaultCellBorderWidths\n      });\n      registerOption('table_border_styles', {\n        processor: 'object[]',\n        default: defaultCellBorderStyles\n      });\n      registerOption('table_cell_advtab', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_row_advtab', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_advtab', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_appearance_options', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('table_grid', {\n        processor: 'boolean',\n        default: !global$1.deviceType.isTouch()\n      });\n      registerOption('table_cell_class_list', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('table_row_class_list', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('table_class_list', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('table_toolbar', {\n        processor: 'string',\n        default: defaultTableToolbar\n      });\n      registerOption('table_background_color_map', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('table_border_color_map', {\n        processor: 'object[]',\n        default: []\n      });\n    };\n    const getTableSizingMode = option('table_sizing_mode');\n    const getTableBorderWidths = option('table_border_widths');\n    const getTableBorderStyles = option('table_border_styles');\n    const hasAdvancedCellTab = option('table_cell_advtab');\n    const hasAdvancedRowTab = option('table_row_advtab');\n    const hasAdvancedTableTab = option('table_advtab');\n    const hasAppearanceOptions = option('table_appearance_options');\n    const hasTableGrid = option('table_grid');\n    const shouldStyleWithCss = option('table_style_by_css');\n    const getCellClassList = option('table_cell_class_list');\n    const getRowClassList = option('table_row_class_list');\n    const getTableClassList = option('table_class_list');\n    const getToolbar = option('table_toolbar');\n    const getTableBackgroundColorMap = option('table_background_color_map');\n    const getTableBorderColorMap = option('table_border_color_map');\n    const isPixelsForced = editor => getTableSizingMode(editor) === 'fixed';\n    const isResponsiveForced = editor => getTableSizingMode(editor) === 'responsive';\n    const getDefaultStyles = editor => {\n      const options = editor.options;\n      const defaultStyles = options.get('table_default_styles');\n      return options.isSet('table_default_styles') ? defaultStyles : determineDefaultStyles(editor, defaultStyles);\n    };\n    const getDefaultAttributes = editor => {\n      const options = editor.options;\n      const defaultAttributes = options.get('table_default_attributes');\n      return options.isSet('table_default_attributes') ? defaultAttributes : determineDefaultAttributes(editor, defaultAttributes);\n    };\n\n    const isWithin = (bounds, detail) => {\n      return detail.column >= bounds.startCol && detail.column + detail.colspan - 1 <= bounds.finishCol && detail.row >= bounds.startRow && detail.row + detail.rowspan - 1 <= bounds.finishRow;\n    };\n    const isRectangular = (warehouse, bounds) => {\n      let isRect = true;\n      const detailIsWithin = curry(isWithin, bounds);\n      for (let i = bounds.startRow; i <= bounds.finishRow; i++) {\n        for (let j = bounds.startCol; j <= bounds.finishCol; j++) {\n          isRect = isRect && Warehouse.getAt(warehouse, i, j).exists(detailIsWithin);\n        }\n      }\n      return isRect ? Optional.some(bounds) : Optional.none();\n    };\n\n    const getBounds = (detailA, detailB) => {\n      return bounds(Math.min(detailA.row, detailB.row), Math.min(detailA.column, detailB.column), Math.max(detailA.row + detailA.rowspan - 1, detailB.row + detailB.rowspan - 1), Math.max(detailA.column + detailA.colspan - 1, detailB.column + detailB.colspan - 1));\n    };\n    const getAnyBox = (warehouse, startCell, finishCell) => {\n      const startCoords = Warehouse.findItem(warehouse, startCell, eq);\n      const finishCoords = Warehouse.findItem(warehouse, finishCell, eq);\n      return startCoords.bind(sc => {\n        return finishCoords.map(fc => {\n          return getBounds(sc, fc);\n        });\n      });\n    };\n    const getBox$1 = (warehouse, startCell, finishCell) => {\n      return getAnyBox(warehouse, startCell, finishCell).bind(bounds => {\n        return isRectangular(warehouse, bounds);\n      });\n    };\n\n    const getBox = (table, first, last) => {\n      const warehouse = getWarehouse(table);\n      return getBox$1(warehouse, first, last);\n    };\n    const getWarehouse = Warehouse.fromTable;\n\n    const before = (marker, element) => {\n      const parent$1 = parent(marker);\n      parent$1.each(v => {\n        v.dom.insertBefore(element.dom, marker.dom);\n      });\n    };\n    const after$1 = (marker, element) => {\n      const sibling = nextSibling(marker);\n      sibling.fold(() => {\n        const parent$1 = parent(marker);\n        parent$1.each(v => {\n          append$1(v, element);\n        });\n      }, v => {\n        before(v, element);\n      });\n    };\n    const prepend = (parent, element) => {\n      const firstChild$1 = firstChild(parent);\n      firstChild$1.fold(() => {\n        append$1(parent, element);\n      }, v => {\n        parent.dom.insertBefore(element.dom, v.dom);\n      });\n    };\n    const append$1 = (parent, element) => {\n      parent.dom.appendChild(element.dom);\n    };\n    const wrap = (element, wrapper) => {\n      before(element, wrapper);\n      append$1(wrapper, element);\n    };\n\n    const after = (marker, elements) => {\n      each(elements, (x, i) => {\n        const e = i === 0 ? marker : elements[i - 1];\n        after$1(e, x);\n      });\n    };\n    const append = (parent, elements) => {\n      each(elements, x => {\n        append$1(parent, x);\n      });\n    };\n\n    const remove = element => {\n      const dom = element.dom;\n      if (dom.parentNode !== null) {\n        dom.parentNode.removeChild(dom);\n      }\n    };\n    const unwrap = wrapper => {\n      const children = children$3(wrapper);\n      if (children.length > 0) {\n        after(wrapper, children);\n      }\n      remove(wrapper);\n    };\n\n    const NodeValue = (is, name) => {\n      const get = element => {\n        if (!is(element)) {\n          throw new Error('Can only get ' + name + ' value of a ' + name + ' node');\n        }\n        return getOption(element).getOr('');\n      };\n      const getOption = element => is(element) ? Optional.from(element.dom.nodeValue) : Optional.none();\n      const set = (element, value) => {\n        if (!is(element)) {\n          throw new Error('Can only set raw ' + name + ' value of a ' + name + ' node');\n        }\n        element.dom.nodeValue = value;\n      };\n      return {\n        get,\n        getOption,\n        set\n      };\n    };\n\n    const api = NodeValue(isText, 'text');\n    const get = element => api.get(element);\n    const set = (element, value) => api.set(element, value);\n\n    var TagBoundaries = [\n      'body',\n      'p',\n      'div',\n      'article',\n      'aside',\n      'figcaption',\n      'figure',\n      'footer',\n      'header',\n      'nav',\n      'section',\n      'ol',\n      'ul',\n      'li',\n      'table',\n      'thead',\n      'tbody',\n      'tfoot',\n      'caption',\n      'tr',\n      'td',\n      'th',\n      'h1',\n      'h2',\n      'h3',\n      'h4',\n      'h5',\n      'h6',\n      'blockquote',\n      'pre',\n      'address'\n    ];\n\n    var DomUniverse = () => {\n      const clone$1 = element => {\n        return SugarElement.fromDom(element.dom.cloneNode(false));\n      };\n      const document = element => documentOrOwner(element).dom;\n      const isBoundary = element => {\n        if (!isElement(element)) {\n          return false;\n        }\n        if (name(element) === 'body') {\n          return true;\n        }\n        return contains(TagBoundaries, name(element));\n      };\n      const isEmptyTag = element => {\n        if (!isElement(element)) {\n          return false;\n        }\n        return contains([\n          'br',\n          'img',\n          'hr',\n          'input'\n        ], name(element));\n      };\n      const isNonEditable = element => isElement(element) && get$2(element, 'contenteditable') === 'false';\n      const comparePosition = (element, other) => {\n        return element.dom.compareDocumentPosition(other.dom);\n      };\n      const copyAttributesTo = (source, destination) => {\n        const as = clone(source);\n        setAll(destination, as);\n      };\n      const isSpecial = element => {\n        const tag = name(element);\n        return contains([\n          'script',\n          'noscript',\n          'iframe',\n          'noframes',\n          'noembed',\n          'title',\n          'style',\n          'textarea',\n          'xmp'\n        ], tag);\n      };\n      const getLanguage = element => isElement(element) ? getOpt(element, 'lang') : Optional.none();\n      return {\n        up: constant({\n          selector: ancestor,\n          closest: closest$1,\n          predicate: ancestor$1,\n          all: parents\n        }),\n        down: constant({\n          selector: descendants,\n          predicate: descendants$1\n        }),\n        styles: constant({\n          get: get$1,\n          getRaw: getRaw,\n          set: set$1,\n          remove: remove$1\n        }),\n        attrs: constant({\n          get: get$2,\n          set: set$2,\n          remove: remove$2,\n          copyTo: copyAttributesTo\n        }),\n        insert: constant({\n          before: before,\n          after: after$1,\n          afterAll: after,\n          append: append$1,\n          appendAll: append,\n          prepend: prepend,\n          wrap: wrap\n        }),\n        remove: constant({\n          unwrap: unwrap,\n          remove: remove\n        }),\n        create: constant({\n          nu: SugarElement.fromTag,\n          clone: clone$1,\n          text: SugarElement.fromText\n        }),\n        query: constant({\n          comparePosition,\n          prevSibling: prevSibling,\n          nextSibling: nextSibling\n        }),\n        property: constant({\n          children: children$3,\n          name: name,\n          parent: parent,\n          document,\n          isText: isText,\n          isComment: isComment,\n          isElement: isElement,\n          isSpecial,\n          getLanguage,\n          getText: get,\n          setText: set,\n          isBoundary,\n          isEmptyTag,\n          isNonEditable\n        }),\n        eq: eq,\n        is: is$1\n      };\n    };\n\n    const all = (universe, look, elements, f) => {\n      const head = elements[0];\n      const tail = elements.slice(1);\n      return f(universe, look, head, tail);\n    };\n    const oneAll = (universe, look, elements) => {\n      return elements.length > 0 ? all(universe, look, elements, unsafeOne) : Optional.none();\n    };\n    const unsafeOne = (universe, look, head, tail) => {\n      const start = look(universe, head);\n      return foldr(tail, (b, a) => {\n        const current = look(universe, a);\n        return commonElement(universe, b, current);\n      }, start);\n    };\n    const commonElement = (universe, start, end) => {\n      return start.bind(s => {\n        return end.filter(curry(universe.eq, s));\n      });\n    };\n\n    const sharedOne$1 = oneAll;\n\n    const universe = DomUniverse();\n    const sharedOne = (look, elements) => {\n      return sharedOne$1(universe, (_universe, element) => {\n        return look(element);\n      }, elements);\n    };\n\n    const lookupTable = container => {\n      return ancestor(container, 'table');\n    };\n    const retrieve$1 = (container, selector) => {\n      const sels = descendants(container, selector);\n      return sels.length > 0 ? Optional.some(sels) : Optional.none();\n    };\n    const getEdges = (container, firstSelectedSelector, lastSelectedSelector) => {\n      return descendant(container, firstSelectedSelector).bind(first => {\n        return descendant(container, lastSelectedSelector).bind(last => {\n          return sharedOne(lookupTable, [\n            first,\n            last\n          ]).map(table => {\n            return {\n              first,\n              last,\n              table\n            };\n          });\n        });\n      });\n    };\n\n    const retrieve = (container, selector) => {\n      return retrieve$1(container, selector);\n    };\n    const retrieveBox = (container, firstSelectedSelector, lastSelectedSelector) => {\n      return getEdges(container, firstSelectedSelector, lastSelectedSelector).bind(edges => {\n        const isRoot = ancestor => {\n          return eq(container, ancestor);\n        };\n        const sectionSelector = 'thead,tfoot,tbody,table';\n        const firstAncestor = ancestor(edges.first, sectionSelector, isRoot);\n        const lastAncestor = ancestor(edges.last, sectionSelector, isRoot);\n        return firstAncestor.bind(fA => {\n          return lastAncestor.bind(lA => {\n            return eq(fA, lA) ? getBox(edges.table, edges.first, edges.last) : Optional.none();\n          });\n        });\n      });\n    };\n\n    const fromDom = nodes => map(nodes, SugarElement.fromDom);\n\n    const strSelected = 'data-mce-selected';\n    const strSelectedSelector = 'td[' + strSelected + '],th[' + strSelected + ']';\n    const strFirstSelected = 'data-mce-first-selected';\n    const strFirstSelectedSelector = 'td[' + strFirstSelected + '],th[' + strFirstSelected + ']';\n    const strLastSelected = 'data-mce-last-selected';\n    const strLastSelectedSelector = 'td[' + strLastSelected + '],th[' + strLastSelected + ']';\n    const ephemera = {\n      selected: strSelected,\n      selectedSelector: strSelectedSelector,\n      firstSelected: strFirstSelected,\n      firstSelectedSelector: strFirstSelectedSelector,\n      lastSelected: strLastSelected,\n      lastSelectedSelector: strLastSelectedSelector\n    };\n\n    const getSelectionCellFallback = element => table(element).bind(table => retrieve(table, ephemera.firstSelectedSelector)).fold(constant(element), cells => cells[0]);\n    const getSelectionFromSelector = selector => (initCell, isRoot) => {\n      const cellName = name(initCell);\n      const cell = cellName === 'col' || cellName === 'colgroup' ? getSelectionCellFallback(initCell) : initCell;\n      return closest$1(cell, selector, isRoot);\n    };\n    const getSelectionCellOrCaption = getSelectionFromSelector('th,td,caption');\n    const getSelectionCell = getSelectionFromSelector('th,td');\n    const getCellsFromSelection = editor => fromDom(editor.model.table.getSelectedCells());\n    const getRowsFromSelection = (selected, selector) => {\n      const cellOpt = getSelectionCell(selected);\n      const rowsOpt = cellOpt.bind(cell => table(cell)).map(table => rows(table));\n      return lift2(cellOpt, rowsOpt, (cell, rows) => filter(rows, row => exists(fromDom(row.dom.cells), rowCell => get$2(rowCell, selector) === '1' || eq(rowCell, cell)))).getOr([]);\n    };\n\n    const verticalAlignValues = [\n      {\n        text: 'None',\n        value: ''\n      },\n      {\n        text: 'Top',\n        value: 'top'\n      },\n      {\n        text: 'Middle',\n        value: 'middle'\n      },\n      {\n        text: 'Bottom',\n        value: 'bottom'\n      }\n    ];\n\n    const hexColour = value => ({ value: normalizeHex(value) });\n    const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\n    const longformRegex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i;\n    const isHexString = hex => shorthandRegex.test(hex) || longformRegex.test(hex);\n    const normalizeHex = hex => removeLeading(hex, '#').toUpperCase();\n    const fromString$1 = hex => isHexString(hex) ? Optional.some({ value: normalizeHex(hex) }) : Optional.none();\n    const toHex = component => {\n      const hex = component.toString(16);\n      return (hex.length === 1 ? '0' + hex : hex).toUpperCase();\n    };\n    const fromRgba = rgbaColour => {\n      const value = toHex(rgbaColour.red) + toHex(rgbaColour.green) + toHex(rgbaColour.blue);\n      return hexColour(value);\n    };\n\n    const rgbRegex = /^\\s*rgb\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)\\s*$/i;\n    const rgbaRegex = /^\\s*rgba\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d?(?:\\.\\d+)?)\\s*\\)\\s*$/i;\n    const rgbaColour = (red, green, blue, alpha) => ({\n      red,\n      green,\n      blue,\n      alpha\n    });\n    const fromStringValues = (red, green, blue, alpha) => {\n      const r = parseInt(red, 10);\n      const g = parseInt(green, 10);\n      const b = parseInt(blue, 10);\n      const a = parseFloat(alpha);\n      return rgbaColour(r, g, b, a);\n    };\n    const fromString = rgbaString => {\n      if (rgbaString === 'transparent') {\n        return Optional.some(rgbaColour(0, 0, 0, 0));\n      }\n      const rgbMatch = rgbRegex.exec(rgbaString);\n      if (rgbMatch !== null) {\n        return Optional.some(fromStringValues(rgbMatch[1], rgbMatch[2], rgbMatch[3], '1'));\n      }\n      const rgbaMatch = rgbaRegex.exec(rgbaString);\n      if (rgbaMatch !== null) {\n        return Optional.some(fromStringValues(rgbaMatch[1], rgbaMatch[2], rgbaMatch[3], rgbaMatch[4]));\n      }\n      return Optional.none();\n    };\n\n    const anyToHex = color => fromString$1(color).orThunk(() => fromString(color).map(fromRgba)).getOrThunk(() => {\n      const canvas = document.createElement('canvas');\n      canvas.height = 1;\n      canvas.width = 1;\n      const canvasContext = canvas.getContext('2d');\n      canvasContext.clearRect(0, 0, canvas.width, canvas.height);\n      canvasContext.fillStyle = '#FFFFFF';\n      canvasContext.fillStyle = color;\n      canvasContext.fillRect(0, 0, 1, 1);\n      const rgba = canvasContext.getImageData(0, 0, 1, 1).data;\n      const r = rgba[0];\n      const g = rgba[1];\n      const b = rgba[2];\n      const a = rgba[3];\n      return fromRgba(rgbaColour(r, g, b, a));\n    });\n    const rgbaToHexString = color => fromString(color).map(fromRgba).map(h => '#' + h.value).getOr(color);\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    const singleton = doRevoke => {\n      const subject = Cell(Optional.none());\n      const revoke = () => subject.get().each(doRevoke);\n      const clear = () => {\n        revoke();\n        subject.set(Optional.none());\n      };\n      const isSet = () => subject.get().isSome();\n      const get = () => subject.get();\n      const set = s => {\n        revoke();\n        subject.set(Optional.some(s));\n      };\n      return {\n        clear,\n        isSet,\n        get,\n        set\n      };\n    };\n    const unbindable = () => singleton(s => s.unbind());\n\n    const onSetupToggle = (editor, formatName, formatValue) => {\n      return api => {\n        const boundCallback = unbindable();\n        const isNone = isEmpty(formatValue);\n        const init = () => {\n          const selectedCells = getCellsFromSelection(editor);\n          const checkNode = cell => editor.formatter.match(formatName, { value: formatValue }, cell.dom, isNone);\n          if (isNone) {\n            api.setActive(!exists(selectedCells, checkNode));\n            boundCallback.set(editor.formatter.formatChanged(formatName, match => api.setActive(!match), true));\n          } else {\n            api.setActive(forall(selectedCells, checkNode));\n            boundCallback.set(editor.formatter.formatChanged(formatName, api.setActive, false, { value: formatValue }));\n          }\n        };\n        editor.initialized ? init() : editor.on('init', init);\n        return boundCallback.clear;\n      };\n    };\n    const isListGroup = item => hasNonNullableKey(item, 'menu');\n    const buildListItems = items => map(items, item => {\n      const text = item.text || item.title || '';\n      if (isListGroup(item)) {\n        return {\n          text,\n          items: buildListItems(item.menu)\n        };\n      } else {\n        return {\n          text,\n          value: item.value\n        };\n      }\n    });\n    const buildClassList = classList => {\n      if (!classList.length) {\n        return Optional.none();\n      }\n      return Optional.some(buildListItems([\n        {\n          text: 'Select...',\n          value: 'mce-no-match'\n        },\n        ...classList\n      ]));\n    };\n    const buildMenuItems = (editor, items, format, onAction) => map(items, item => {\n      const text = item.text || item.title;\n      if (isListGroup(item)) {\n        return {\n          type: 'nestedmenuitem',\n          text,\n          getSubmenuItems: () => buildMenuItems(editor, item.menu, format, onAction)\n        };\n      } else {\n        return {\n          text,\n          type: 'togglemenuitem',\n          onAction: () => onAction(item.value),\n          onSetup: onSetupToggle(editor, format, item.value)\n        };\n      }\n    });\n    const applyTableCellStyle = (editor, style) => value => {\n      editor.execCommand('mceTableApplyCellStyle', false, { [style]: value });\n    };\n    const filterNoneItem = list => bind(list, item => {\n      if (isListGroup(item)) {\n        return [{\n            ...item,\n            menu: filterNoneItem(item.menu)\n          }];\n      } else {\n        return isNotEmpty(item.value) ? [item] : [];\n      }\n    });\n    const generateMenuItemsCallback = (editor, items, format, onAction) => callback => callback(buildMenuItems(editor, items, format, onAction));\n    const buildColorMenu = (editor, colorList, style) => {\n      const colorMap = map(colorList, entry => ({\n        text: entry.title,\n        value: '#' + anyToHex(entry.value).value,\n        type: 'choiceitem'\n      }));\n      return [{\n          type: 'fancymenuitem',\n          fancytype: 'colorswatch',\n          initData: {\n            colors: colorMap.length > 0 ? colorMap : undefined,\n            allowCustomColors: false\n          },\n          onAction: data => {\n            const value = data.value === 'remove' ? '' : data.value;\n            editor.execCommand('mceTableApplyCellStyle', false, { [style]: value });\n          }\n        }];\n    };\n    const changeRowHeader = editor => () => {\n      const currentType = editor.queryCommandValue('mceTableRowType');\n      const newType = currentType === 'header' ? 'body' : 'header';\n      editor.execCommand('mceTableRowType', false, { type: newType });\n    };\n    const changeColumnHeader = editor => () => {\n      const currentType = editor.queryCommandValue('mceTableColType');\n      const newType = currentType === 'th' ? 'td' : 'th';\n      editor.execCommand('mceTableColType', false, { type: newType });\n    };\n\n    const getClassList$1 = editor => buildClassList(getCellClassList(editor)).map(items => ({\n      name: 'class',\n      type: 'listbox',\n      label: 'Class',\n      items\n    }));\n    const children = [\n      {\n        name: 'width',\n        type: 'input',\n        label: 'Width'\n      },\n      {\n        name: 'celltype',\n        type: 'listbox',\n        label: 'Cell type',\n        items: [\n          {\n            text: 'Cell',\n            value: 'td'\n          },\n          {\n            text: 'Header cell',\n            value: 'th'\n          }\n        ]\n      },\n      {\n        name: 'scope',\n        type: 'listbox',\n        label: 'Scope',\n        items: [\n          {\n            text: 'None',\n            value: ''\n          },\n          {\n            text: 'Row',\n            value: 'row'\n          },\n          {\n            text: 'Column',\n            value: 'col'\n          },\n          {\n            text: 'Row group',\n            value: 'rowgroup'\n          },\n          {\n            text: 'Column group',\n            value: 'colgroup'\n          }\n        ]\n      },\n      {\n        name: 'halign',\n        type: 'listbox',\n        label: 'Horizontal align',\n        items: [\n          {\n            text: 'None',\n            value: ''\n          },\n          {\n            text: 'Left',\n            value: 'left'\n          },\n          {\n            text: 'Center',\n            value: 'center'\n          },\n          {\n            text: 'Right',\n            value: 'right'\n          }\n        ]\n      },\n      {\n        name: 'valign',\n        type: 'listbox',\n        label: 'Vertical align',\n        items: verticalAlignValues\n      }\n    ];\n    const getItems$2 = editor => children.concat(getClassList$1(editor).toArray());\n\n    const getAdvancedTab = (editor, dialogName) => {\n      const emptyBorderStyle = [{\n          text: 'Select...',\n          value: ''\n        }];\n      const advTabItems = [\n        {\n          name: 'borderstyle',\n          type: 'listbox',\n          label: 'Border style',\n          items: emptyBorderStyle.concat(buildListItems(getTableBorderStyles(editor)))\n        },\n        {\n          name: 'bordercolor',\n          type: 'colorinput',\n          label: 'Border color'\n        },\n        {\n          name: 'backgroundcolor',\n          type: 'colorinput',\n          label: 'Background color'\n        }\n      ];\n      const borderWidth = {\n        name: 'borderwidth',\n        type: 'input',\n        label: 'Border width'\n      };\n      const items = dialogName === 'cell' ? [borderWidth].concat(advTabItems) : advTabItems;\n      return {\n        title: 'Advanced',\n        name: 'advanced',\n        items\n      };\n    };\n\n    const normal = (editor, element) => {\n      const dom = editor.dom;\n      const setAttrib = (attr, value) => {\n        dom.setAttrib(element, attr, value);\n      };\n      const setStyle = (prop, value) => {\n        dom.setStyle(element, prop, value);\n      };\n      const setFormat = (formatName, value) => {\n        if (value === '') {\n          editor.formatter.remove(formatName, { value: null }, element, true);\n        } else {\n          editor.formatter.apply(formatName, { value }, element);\n        }\n      };\n      return {\n        setAttrib,\n        setStyle,\n        setFormat\n      };\n    };\n    const DomModifier = { normal };\n\n    const isHeaderCell = isTag('th');\n    const getRowHeaderType = (isHeaderRow, isHeaderCells) => {\n      if (isHeaderRow && isHeaderCells) {\n        return 'sectionCells';\n      } else if (isHeaderRow) {\n        return 'section';\n      } else {\n        return 'cells';\n      }\n    };\n    const getRowType$1 = row => {\n      const isHeaderRow = row.section === 'thead';\n      const isHeaderCells = is(findCommonCellType(row.cells), 'th');\n      if (row.section === 'tfoot') {\n        return { type: 'footer' };\n      } else if (isHeaderRow || isHeaderCells) {\n        return {\n          type: 'header',\n          subType: getRowHeaderType(isHeaderRow, isHeaderCells)\n        };\n      } else {\n        return { type: 'body' };\n      }\n    };\n    const findCommonCellType = cells => {\n      const headerCells = filter(cells, cell => isHeaderCell(cell.element));\n      if (headerCells.length === 0) {\n        return Optional.some('td');\n      } else if (headerCells.length === cells.length) {\n        return Optional.some('th');\n      } else {\n        return Optional.none();\n      }\n    };\n    const findCommonRowType = rows => {\n      const rowTypes = map(rows, row => getRowType$1(row).type);\n      const hasHeader = contains(rowTypes, 'header');\n      const hasFooter = contains(rowTypes, 'footer');\n      if (!hasHeader && !hasFooter) {\n        return Optional.some('body');\n      } else {\n        const hasBody = contains(rowTypes, 'body');\n        if (hasHeader && !hasBody && !hasFooter) {\n          return Optional.some('header');\n        } else if (!hasHeader && !hasBody && hasFooter) {\n          return Optional.some('footer');\n        } else {\n          return Optional.none();\n        }\n      }\n    };\n\n    const cached = f => {\n      let called = false;\n      let r;\n      return (...args) => {\n        if (!called) {\n          called = true;\n          r = f.apply(null, args);\n        }\n        return r;\n      };\n    };\n\n    const findInWarehouse = (warehouse, element) => findMap(warehouse.all, r => find(r.cells, e => eq(element, e.element)));\n    const extractCells = (warehouse, target, predicate) => {\n      const details = map(target.selection, cell$1 => {\n        return cell(cell$1).bind(lc => findInWarehouse(warehouse, lc)).filter(predicate);\n      });\n      const cells = cat(details);\n      return someIf(cells.length > 0, cells);\n    };\n    const onMergable = (_warehouse, target) => target.mergable;\n    const onUnmergable = (_warehouse, target) => target.unmergable;\n    const onCells = (warehouse, target) => extractCells(warehouse, target, always);\n    const isUnlockedTableCell = (warehouse, cell) => findInWarehouse(warehouse, cell).exists(detail => !detail.isLocked);\n    const allUnlocked = (warehouse, cells) => forall(cells, cell => isUnlockedTableCell(warehouse, cell));\n    const onUnlockedMergable = (warehouse, target) => onMergable(warehouse, target).filter(mergeable => allUnlocked(warehouse, mergeable.cells));\n    const onUnlockedUnmergable = (warehouse, target) => onUnmergable(warehouse, target).filter(cells => allUnlocked(warehouse, cells));\n\n    const generate = cases => {\n      if (!isArray(cases)) {\n        throw new Error('cases must be an array');\n      }\n      if (cases.length === 0) {\n        throw new Error('there must be at least one case');\n      }\n      const constructors = [];\n      const adt = {};\n      each(cases, (acase, count) => {\n        const keys$1 = keys(acase);\n        if (keys$1.length !== 1) {\n          throw new Error('one and only one name per case');\n        }\n        const key = keys$1[0];\n        const value = acase[key];\n        if (adt[key] !== undefined) {\n          throw new Error('duplicate key detected:' + key);\n        } else if (key === 'cata') {\n          throw new Error('cannot have a case named cata (sorry)');\n        } else if (!isArray(value)) {\n          throw new Error('case arguments must be an array');\n        }\n        constructors.push(key);\n        adt[key] = (...args) => {\n          const argLength = args.length;\n          if (argLength !== value.length) {\n            throw new Error('Wrong number of arguments to case ' + key + '. Expected ' + value.length + ' (' + value + '), got ' + argLength);\n          }\n          const match = branches => {\n            const branchKeys = keys(branches);\n            if (constructors.length !== branchKeys.length) {\n              throw new Error('Wrong number of arguments to match. Expected: ' + constructors.join(',') + '\\nActual: ' + branchKeys.join(','));\n            }\n            const allReqd = forall(constructors, reqKey => {\n              return contains(branchKeys, reqKey);\n            });\n            if (!allReqd) {\n              throw new Error('Not all branches were specified when using match. Specified: ' + branchKeys.join(', ') + '\\nRequired: ' + constructors.join(', '));\n            }\n            return branches[key].apply(null, args);\n          };\n          return {\n            fold: (...foldArgs) => {\n              if (foldArgs.length !== cases.length) {\n                throw new Error('Wrong number of arguments to fold. Expected ' + cases.length + ', got ' + foldArgs.length);\n              }\n              const target = foldArgs[count];\n              return target.apply(null, args);\n            },\n            match,\n            log: label => {\n              console.log(label, {\n                constructors,\n                constructor: key,\n                params: args\n              });\n            }\n          };\n        };\n      });\n      return adt;\n    };\n    const Adt = { generate };\n\n    const adt = Adt.generate([\n      { none: [] },\n      { only: ['index'] },\n      {\n        left: [\n          'index',\n          'next'\n        ]\n      },\n      {\n        middle: [\n          'prev',\n          'index',\n          'next'\n        ]\n      },\n      {\n        right: [\n          'prev',\n          'index'\n        ]\n      }\n    ]);\n    ({ ...adt });\n\n    const opGetRowsType = (table, target) => {\n      const house = Warehouse.fromTable(table);\n      const details = onCells(house, target);\n      return details.bind(selectedCells => {\n        const lastSelectedCell = selectedCells[selectedCells.length - 1];\n        const minRowRange = selectedCells[0].row;\n        const maxRowRange = lastSelectedCell.row + lastSelectedCell.rowspan;\n        const selectedRows = house.all.slice(minRowRange, maxRowRange);\n        return findCommonRowType(selectedRows);\n      }).getOr('');\n    };\n    const getRowsType = opGetRowsType;\n\n    const rgbToHex = value => startsWith(value, 'rgb') ? rgbaToHexString(value) : value;\n    const extractAdvancedStyles = elm => {\n      const element = SugarElement.fromDom(elm);\n      return {\n        borderwidth: getRaw(element, 'border-width').getOr(''),\n        borderstyle: getRaw(element, 'border-style').getOr(''),\n        bordercolor: getRaw(element, 'border-color').map(rgbToHex).getOr(''),\n        backgroundcolor: getRaw(element, 'background-color').map(rgbToHex).getOr('')\n      };\n    };\n    const getSharedValues = data => {\n      const baseData = data[0];\n      const comparisonData = data.slice(1);\n      each(comparisonData, items => {\n        each(keys(baseData), key => {\n          each$1(items, (itemValue, itemKey) => {\n            const comparisonValue = baseData[key];\n            if (comparisonValue !== '' && key === itemKey) {\n              if (comparisonValue !== itemValue) {\n                baseData[key] = key === 'class' ? 'mce-no-match' : '';\n              }\n            }\n          });\n        });\n      });\n      return baseData;\n    };\n    const getAlignment = (formats, formatName, editor, elm) => find(formats, name => !isUndefined(editor.formatter.matchNode(elm, formatName + name))).getOr('');\n    const getHAlignment = curry(getAlignment, [\n      'left',\n      'center',\n      'right'\n    ], 'align');\n    const getVAlignment = curry(getAlignment, [\n      'top',\n      'middle',\n      'bottom'\n    ], 'valign');\n    const extractDataFromSettings = (editor, hasAdvTableTab) => {\n      const style = getDefaultStyles(editor);\n      const attrs = getDefaultAttributes(editor);\n      const extractAdvancedStyleData = () => ({\n        borderstyle: get$4(style, 'border-style').getOr(''),\n        bordercolor: rgbToHex(get$4(style, 'border-color').getOr('')),\n        backgroundcolor: rgbToHex(get$4(style, 'background-color').getOr(''))\n      });\n      const defaultData = {\n        height: '',\n        width: '100%',\n        cellspacing: '',\n        cellpadding: '',\n        caption: false,\n        class: '',\n        align: '',\n        border: ''\n      };\n      const getBorder = () => {\n        const borderWidth = style['border-width'];\n        if (shouldStyleWithCss(editor) && borderWidth) {\n          return { border: borderWidth };\n        }\n        return get$4(attrs, 'border').fold(() => ({}), border => ({ border }));\n      };\n      const advStyle = hasAdvTableTab ? extractAdvancedStyleData() : {};\n      const getCellPaddingCellSpacing = () => {\n        const spacing = get$4(style, 'border-spacing').or(get$4(attrs, 'cellspacing')).fold(() => ({}), cellspacing => ({ cellspacing }));\n        const padding = get$4(style, 'border-padding').or(get$4(attrs, 'cellpadding')).fold(() => ({}), cellpadding => ({ cellpadding }));\n        return {\n          ...spacing,\n          ...padding\n        };\n      };\n      const data = {\n        ...defaultData,\n        ...style,\n        ...attrs,\n        ...advStyle,\n        ...getBorder(),\n        ...getCellPaddingCellSpacing()\n      };\n      return data;\n    };\n    const getRowType = elm => table(SugarElement.fromDom(elm)).map(table => {\n      const target = { selection: fromDom(elm.cells) };\n      return getRowsType(table, target);\n    }).getOr('');\n    const extractDataFromTableElement = (editor, elm, hasAdvTableTab) => {\n      const getBorder = (dom, elm) => {\n        const optBorderWidth = getRaw(SugarElement.fromDom(elm), 'border-width');\n        if (shouldStyleWithCss(editor) && optBorderWidth.isSome()) {\n          return optBorderWidth.getOr('');\n        }\n        return dom.getAttrib(elm, 'border') || getTDTHOverallStyle(editor.dom, elm, 'border-width') || getTDTHOverallStyle(editor.dom, elm, 'border') || '';\n      };\n      const dom = editor.dom;\n      const cellspacing = shouldStyleWithCss(editor) ? dom.getStyle(elm, 'border-spacing') || dom.getAttrib(elm, 'cellspacing') : dom.getAttrib(elm, 'cellspacing') || dom.getStyle(elm, 'border-spacing');\n      const cellpadding = shouldStyleWithCss(editor) ? getTDTHOverallStyle(dom, elm, 'padding') || dom.getAttrib(elm, 'cellpadding') : dom.getAttrib(elm, 'cellpadding') || getTDTHOverallStyle(dom, elm, 'padding');\n      return {\n        width: dom.getStyle(elm, 'width') || dom.getAttrib(elm, 'width'),\n        height: dom.getStyle(elm, 'height') || dom.getAttrib(elm, 'height'),\n        cellspacing: cellspacing !== null && cellspacing !== void 0 ? cellspacing : '',\n        cellpadding: cellpadding !== null && cellpadding !== void 0 ? cellpadding : '',\n        border: getBorder(dom, elm),\n        caption: !!dom.select('caption', elm)[0],\n        class: dom.getAttrib(elm, 'class', ''),\n        align: getHAlignment(editor, elm),\n        ...hasAdvTableTab ? extractAdvancedStyles(elm) : {}\n      };\n    };\n    const extractDataFromRowElement = (editor, elm, hasAdvancedRowTab) => {\n      const dom = editor.dom;\n      return {\n        height: dom.getStyle(elm, 'height') || dom.getAttrib(elm, 'height'),\n        class: dom.getAttrib(elm, 'class', ''),\n        type: getRowType(elm),\n        align: getHAlignment(editor, elm),\n        ...hasAdvancedRowTab ? extractAdvancedStyles(elm) : {}\n      };\n    };\n    const extractDataFromCellElement = (editor, cell, hasAdvancedCellTab, column) => {\n      const dom = editor.dom;\n      const colElm = column.getOr(cell);\n      const getStyle = (element, style) => dom.getStyle(element, style) || dom.getAttrib(element, style);\n      return {\n        width: getStyle(colElm, 'width'),\n        scope: dom.getAttrib(cell, 'scope'),\n        celltype: getNodeName(cell),\n        class: dom.getAttrib(cell, 'class', ''),\n        halign: getHAlignment(editor, cell),\n        valign: getVAlignment(editor, cell),\n        ...hasAdvancedCellTab ? extractAdvancedStyles(cell) : {}\n      };\n    };\n\n    const getSelectedCells = (table, cells) => {\n      const warehouse = Warehouse.fromTable(table);\n      const allCells = Warehouse.justCells(warehouse);\n      const filtered = filter(allCells, cellA => exists(cells, cellB => eq(cellA.element, cellB)));\n      return map(filtered, cell => ({\n        element: cell.element.dom,\n        column: Warehouse.getColumnAt(warehouse, cell.column).map(col => col.element.dom)\n      }));\n    };\n    const updateSimpleProps$1 = (modifier, colModifier, data, shouldUpdate) => {\n      if (shouldUpdate('scope')) {\n        modifier.setAttrib('scope', data.scope);\n      }\n      if (shouldUpdate('class') && data.class !== 'mce-no-match') {\n        modifier.setAttrib('class', data.class);\n      }\n      if (shouldUpdate('width')) {\n        colModifier.setStyle('width', addPxSuffix(data.width));\n      }\n    };\n    const updateAdvancedProps$1 = (modifier, data, shouldUpdate) => {\n      if (shouldUpdate('backgroundcolor')) {\n        modifier.setFormat('tablecellbackgroundcolor', data.backgroundcolor);\n      }\n      if (shouldUpdate('bordercolor')) {\n        modifier.setFormat('tablecellbordercolor', data.bordercolor);\n      }\n      if (shouldUpdate('borderstyle')) {\n        modifier.setFormat('tablecellborderstyle', data.borderstyle);\n      }\n      if (shouldUpdate('borderwidth')) {\n        modifier.setFormat('tablecellborderwidth', addPxSuffix(data.borderwidth));\n      }\n    };\n    const applyStyleData$1 = (editor, cells, data, wasChanged) => {\n      const isSingleCell = cells.length === 1;\n      each(cells, item => {\n        const cellElm = item.element;\n        const shouldOverrideCurrentValue = isSingleCell ? always : wasChanged;\n        const modifier = DomModifier.normal(editor, cellElm);\n        const colModifier = item.column.map(col => DomModifier.normal(editor, col)).getOr(modifier);\n        updateSimpleProps$1(modifier, colModifier, data, shouldOverrideCurrentValue);\n        if (hasAdvancedCellTab(editor)) {\n          updateAdvancedProps$1(modifier, data, shouldOverrideCurrentValue);\n        }\n        if (wasChanged('halign')) {\n          setAlign(editor, cellElm, data.halign);\n        }\n        if (wasChanged('valign')) {\n          setVAlign(editor, cellElm, data.valign);\n        }\n      });\n    };\n    const applyStructureData$1 = (editor, data) => {\n      editor.execCommand('mceTableCellType', false, {\n        type: data.celltype,\n        no_events: true\n      });\n    };\n    const applyCellData = (editor, cells, oldData, data) => {\n      const modifiedData = filter$1(data, (value, key) => oldData[key] !== value);\n      if (size(modifiedData) > 0 && cells.length >= 1) {\n        table(cells[0]).each(table => {\n          const selectedCells = getSelectedCells(table, cells);\n          const styleModified = size(filter$1(modifiedData, (_value, key) => key !== 'scope' && key !== 'celltype')) > 0;\n          const structureModified = has(modifiedData, 'celltype');\n          if (styleModified || has(modifiedData, 'scope')) {\n            applyStyleData$1(editor, selectedCells, data, curry(has, modifiedData));\n          }\n          if (structureModified) {\n            applyStructureData$1(editor, data);\n          }\n          fireTableModified(editor, table.dom, {\n            structure: structureModified,\n            style: styleModified\n          });\n        });\n      }\n    };\n    const onSubmitCellForm = (editor, cells, oldData, api) => {\n      const data = api.getData();\n      api.close();\n      editor.undoManager.transact(() => {\n        applyCellData(editor, cells, oldData, data);\n        editor.focus();\n      });\n    };\n    const getData$1 = (editor, cells) => {\n      const cellsData = table(cells[0]).map(table => map(getSelectedCells(table, cells), item => extractDataFromCellElement(editor, item.element, hasAdvancedCellTab(editor), item.column)));\n      return getSharedValues(cellsData.getOrDie());\n    };\n    const open$2 = editor => {\n      const cells = getCellsFromSelection(editor);\n      if (cells.length === 0) {\n        return;\n      }\n      const data = getData$1(editor, cells);\n      const dialogTabPanel = {\n        type: 'tabpanel',\n        tabs: [\n          {\n            title: 'General',\n            name: 'general',\n            items: getItems$2(editor)\n          },\n          getAdvancedTab(editor, 'cell')\n        ]\n      };\n      const dialogPanel = {\n        type: 'panel',\n        items: [{\n            type: 'grid',\n            columns: 2,\n            items: getItems$2(editor)\n          }]\n      };\n      editor.windowManager.open({\n        title: 'Cell Properties',\n        size: 'normal',\n        body: hasAdvancedCellTab(editor) ? dialogTabPanel : dialogPanel,\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: data,\n        onSubmit: curry(onSubmitCellForm, editor, cells, data)\n      });\n    };\n\n    const getClassList = editor => buildClassList(getRowClassList(editor)).map(items => ({\n      name: 'class',\n      type: 'listbox',\n      label: 'Class',\n      items\n    }));\n    const formChildren = [\n      {\n        type: 'listbox',\n        name: 'type',\n        label: 'Row type',\n        items: [\n          {\n            text: 'Header',\n            value: 'header'\n          },\n          {\n            text: 'Body',\n            value: 'body'\n          },\n          {\n            text: 'Footer',\n            value: 'footer'\n          }\n        ]\n      },\n      {\n        type: 'listbox',\n        name: 'align',\n        label: 'Alignment',\n        items: [\n          {\n            text: 'None',\n            value: ''\n          },\n          {\n            text: 'Left',\n            value: 'left'\n          },\n          {\n            text: 'Center',\n            value: 'center'\n          },\n          {\n            text: 'Right',\n            value: 'right'\n          }\n        ]\n      },\n      {\n        label: 'Height',\n        name: 'height',\n        type: 'input'\n      }\n    ];\n    const getItems$1 = editor => formChildren.concat(getClassList(editor).toArray());\n\n    const updateSimpleProps = (modifier, data, shouldUpdate) => {\n      if (shouldUpdate('class') && data.class !== 'mce-no-match') {\n        modifier.setAttrib('class', data.class);\n      }\n      if (shouldUpdate('height')) {\n        modifier.setStyle('height', addPxSuffix(data.height));\n      }\n    };\n    const updateAdvancedProps = (modifier, data, shouldUpdate) => {\n      if (shouldUpdate('backgroundcolor')) {\n        modifier.setStyle('background-color', data.backgroundcolor);\n      }\n      if (shouldUpdate('bordercolor')) {\n        modifier.setStyle('border-color', data.bordercolor);\n      }\n      if (shouldUpdate('borderstyle')) {\n        modifier.setStyle('border-style', data.borderstyle);\n      }\n    };\n    const applyStyleData = (editor, rows, data, wasChanged) => {\n      const isSingleRow = rows.length === 1;\n      const shouldOverrideCurrentValue = isSingleRow ? always : wasChanged;\n      each(rows, rowElm => {\n        const rowCells = children$1(SugarElement.fromDom(rowElm), 'td,th');\n        const modifier = DomModifier.normal(editor, rowElm);\n        updateSimpleProps(modifier, data, shouldOverrideCurrentValue);\n        if (hasAdvancedRowTab(editor)) {\n          updateAdvancedProps(modifier, data, shouldOverrideCurrentValue);\n        }\n        if (wasChanged('height')) {\n          each(rowCells, cell => {\n            editor.dom.setStyle(cell.dom, 'height', null);\n          });\n        }\n        if (wasChanged('align')) {\n          setAlign(editor, rowElm, data.align);\n        }\n      });\n    };\n    const applyStructureData = (editor, data) => {\n      editor.execCommand('mceTableRowType', false, {\n        type: data.type,\n        no_events: true\n      });\n    };\n    const applyRowData = (editor, rows, oldData, data) => {\n      const modifiedData = filter$1(data, (value, key) => oldData[key] !== value);\n      if (size(modifiedData) > 0) {\n        const typeModified = has(modifiedData, 'type');\n        const styleModified = typeModified ? size(modifiedData) > 1 : true;\n        if (styleModified) {\n          applyStyleData(editor, rows, data, curry(has, modifiedData));\n        }\n        if (typeModified) {\n          applyStructureData(editor, data);\n        }\n        table(SugarElement.fromDom(rows[0])).each(table => fireTableModified(editor, table.dom, {\n          structure: typeModified,\n          style: styleModified\n        }));\n      }\n    };\n    const onSubmitRowForm = (editor, rows, oldData, api) => {\n      const data = api.getData();\n      api.close();\n      editor.undoManager.transact(() => {\n        applyRowData(editor, rows, oldData, data);\n        editor.focus();\n      });\n    };\n    const open$1 = editor => {\n      const rows = getRowsFromSelection(getSelectionStart(editor), ephemera.selected);\n      if (rows.length === 0) {\n        return;\n      }\n      const rowsData = map(rows, rowElm => extractDataFromRowElement(editor, rowElm.dom, hasAdvancedRowTab(editor)));\n      const data = getSharedValues(rowsData);\n      const dialogTabPanel = {\n        type: 'tabpanel',\n        tabs: [\n          {\n            title: 'General',\n            name: 'general',\n            items: getItems$1(editor)\n          },\n          getAdvancedTab(editor, 'row')\n        ]\n      };\n      const dialogPanel = {\n        type: 'panel',\n        items: [{\n            type: 'grid',\n            columns: 2,\n            items: getItems$1(editor)\n          }]\n      };\n      editor.windowManager.open({\n        title: 'Row Properties',\n        size: 'normal',\n        body: hasAdvancedRowTab(editor) ? dialogTabPanel : dialogPanel,\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: data,\n        onSubmit: curry(onSubmitRowForm, editor, map(rows, r => r.dom), data)\n      });\n    };\n\n    const getItems = (editor, classes, insertNewTable) => {\n      const rowColCountItems = !insertNewTable ? [] : [\n        {\n          type: 'input',\n          name: 'cols',\n          label: 'Cols',\n          inputMode: 'numeric'\n        },\n        {\n          type: 'input',\n          name: 'rows',\n          label: 'Rows',\n          inputMode: 'numeric'\n        }\n      ];\n      const alwaysItems = [\n        {\n          type: 'input',\n          name: 'width',\n          label: 'Width'\n        },\n        {\n          type: 'input',\n          name: 'height',\n          label: 'Height'\n        }\n      ];\n      const appearanceItems = hasAppearanceOptions(editor) ? [\n        {\n          type: 'input',\n          name: 'cellspacing',\n          label: 'Cell spacing',\n          inputMode: 'numeric'\n        },\n        {\n          type: 'input',\n          name: 'cellpadding',\n          label: 'Cell padding',\n          inputMode: 'numeric'\n        },\n        {\n          type: 'input',\n          name: 'border',\n          label: 'Border width'\n        },\n        {\n          type: 'label',\n          label: 'Caption',\n          items: [{\n              type: 'checkbox',\n              name: 'caption',\n              label: 'Show caption'\n            }]\n        }\n      ] : [];\n      const alignmentItem = [{\n          type: 'listbox',\n          name: 'align',\n          label: 'Alignment',\n          items: [\n            {\n              text: 'None',\n              value: ''\n            },\n            {\n              text: 'Left',\n              value: 'left'\n            },\n            {\n              text: 'Center',\n              value: 'center'\n            },\n            {\n              text: 'Right',\n              value: 'right'\n            }\n          ]\n        }];\n      const classListItem = classes.length > 0 ? [{\n          name: 'class',\n          type: 'listbox',\n          label: 'Class',\n          items: classes\n        }] : [];\n      return rowColCountItems.concat(alwaysItems).concat(appearanceItems).concat(alignmentItem).concat(classListItem);\n    };\n\n    const styleTDTH = (dom, elm, name, value) => {\n      if (elm.tagName === 'TD' || elm.tagName === 'TH') {\n        if (isString(name) && isNonNullable(value)) {\n          dom.setStyle(elm, name, value);\n        } else {\n          dom.setStyles(elm, name);\n        }\n      } else {\n        if (elm.children) {\n          for (let i = 0; i < elm.children.length; i++) {\n            styleTDTH(dom, elm.children[i], name, value);\n          }\n        }\n      }\n    };\n    const applyDataToElement = (editor, tableElm, data, shouldApplyOnCell) => {\n      const dom = editor.dom;\n      const attrs = {};\n      const styles = {};\n      const shouldStyleWithCss$1 = shouldStyleWithCss(editor);\n      const hasAdvancedTableTab$1 = hasAdvancedTableTab(editor);\n      const borderIsZero = parseFloat(data.border) === 0;\n      if (!isUndefined(data.class) && data.class !== 'mce-no-match') {\n        attrs.class = data.class;\n      }\n      styles.height = addPxSuffix(data.height);\n      if (shouldStyleWithCss$1) {\n        styles.width = addPxSuffix(data.width);\n      } else if (dom.getAttrib(tableElm, 'width')) {\n        attrs.width = removePxSuffix(data.width);\n      }\n      if (shouldStyleWithCss$1) {\n        if (borderIsZero) {\n          attrs.border = 0;\n          styles['border-width'] = '';\n        } else {\n          styles['border-width'] = addPxSuffix(data.border);\n          attrs.border = 1;\n        }\n        styles['border-spacing'] = addPxSuffix(data.cellspacing);\n      } else {\n        attrs.border = borderIsZero ? 0 : data.border;\n        attrs.cellpadding = data.cellpadding;\n        attrs.cellspacing = data.cellspacing;\n      }\n      if (shouldStyleWithCss$1 && tableElm.children) {\n        const cellStyles = {};\n        if (borderIsZero) {\n          cellStyles['border-width'] = '';\n        } else if (shouldApplyOnCell.border) {\n          cellStyles['border-width'] = addPxSuffix(data.border);\n        }\n        if (shouldApplyOnCell.cellpadding) {\n          cellStyles.padding = addPxSuffix(data.cellpadding);\n        }\n        if (hasAdvancedTableTab$1 && shouldApplyOnCell.bordercolor) {\n          cellStyles['border-color'] = data.bordercolor;\n        }\n        if (!isEmpty$1(cellStyles)) {\n          for (let i = 0; i < tableElm.children.length; i++) {\n            styleTDTH(dom, tableElm.children[i], cellStyles);\n          }\n        }\n      }\n      if (hasAdvancedTableTab$1) {\n        const advData = data;\n        styles['background-color'] = advData.backgroundcolor;\n        styles['border-color'] = advData.bordercolor;\n        styles['border-style'] = advData.borderstyle;\n      }\n      dom.setStyles(tableElm, {\n        ...getDefaultStyles(editor),\n        ...styles\n      });\n      dom.setAttribs(tableElm, {\n        ...getDefaultAttributes(editor),\n        ...attrs\n      });\n    };\n    const onSubmitTableForm = (editor, tableElm, oldData, api) => {\n      const dom = editor.dom;\n      const data = api.getData();\n      const modifiedData = filter$1(data, (value, key) => oldData[key] !== value);\n      api.close();\n      editor.undoManager.transact(() => {\n        if (!tableElm) {\n          const cols = toInt(data.cols).getOr(1);\n          const rows = toInt(data.rows).getOr(1);\n          editor.execCommand('mceInsertTable', false, {\n            rows,\n            columns: cols\n          });\n          tableElm = getSelectionCell(getSelectionStart(editor), getIsRoot(editor)).bind(cell => table(cell, getIsRoot(editor))).map(table => table.dom).getOrDie();\n        }\n        if (size(modifiedData) > 0) {\n          const applicableCellProperties = {\n            border: has(modifiedData, 'border'),\n            bordercolor: has(modifiedData, 'bordercolor'),\n            cellpadding: has(modifiedData, 'cellpadding')\n          };\n          applyDataToElement(editor, tableElm, data, applicableCellProperties);\n          const captionElm = dom.select('caption', tableElm)[0];\n          if (captionElm && !data.caption || !captionElm && data.caption) {\n            editor.execCommand('mceTableToggleCaption');\n          }\n          setAlign(editor, tableElm, data.align);\n        }\n        editor.focus();\n        editor.addVisual();\n        if (size(modifiedData) > 0) {\n          const captionModified = has(modifiedData, 'caption');\n          const styleModified = captionModified ? size(modifiedData) > 1 : true;\n          fireTableModified(editor, tableElm, {\n            structure: captionModified,\n            style: styleModified\n          });\n        }\n      });\n    };\n    const open = (editor, insertNewTable) => {\n      const dom = editor.dom;\n      let tableElm;\n      let data = extractDataFromSettings(editor, hasAdvancedTableTab(editor));\n      if (insertNewTable) {\n        data.cols = '1';\n        data.rows = '1';\n        if (hasAdvancedTableTab(editor)) {\n          data.borderstyle = '';\n          data.bordercolor = '';\n          data.backgroundcolor = '';\n        }\n      } else {\n        tableElm = dom.getParent(editor.selection.getStart(), 'table', editor.getBody());\n        if (tableElm) {\n          data = extractDataFromTableElement(editor, tableElm, hasAdvancedTableTab(editor));\n        } else {\n          if (hasAdvancedTableTab(editor)) {\n            data.borderstyle = '';\n            data.bordercolor = '';\n            data.backgroundcolor = '';\n          }\n        }\n      }\n      const classes = buildClassList(getTableClassList(editor));\n      if (classes.isSome()) {\n        if (data.class) {\n          data.class = data.class.replace(/\\s*mce\\-item\\-table\\s*/g, '');\n        }\n      }\n      const generalPanel = {\n        type: 'grid',\n        columns: 2,\n        items: getItems(editor, classes.getOr([]), insertNewTable)\n      };\n      const nonAdvancedForm = () => ({\n        type: 'panel',\n        items: [generalPanel]\n      });\n      const advancedForm = () => ({\n        type: 'tabpanel',\n        tabs: [\n          {\n            title: 'General',\n            name: 'general',\n            items: [generalPanel]\n          },\n          getAdvancedTab(editor, 'table')\n        ]\n      });\n      const dialogBody = hasAdvancedTableTab(editor) ? advancedForm() : nonAdvancedForm();\n      editor.windowManager.open({\n        title: 'Table Properties',\n        size: 'normal',\n        body: dialogBody,\n        onSubmit: curry(onSubmitTableForm, editor, tableElm, data),\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: data\n      });\n    };\n\n    const registerCommands = editor => {\n      const runAction = f => {\n        if (isInEditableContext(getSelectionStart(editor))) {\n          f();\n        }\n      };\n      each$1({\n        mceTableProps: curry(open, editor, false),\n        mceTableRowProps: curry(open$1, editor),\n        mceTableCellProps: curry(open$2, editor),\n        mceInsertTableDialog: curry(open, editor, true)\n      }, (func, name) => editor.addCommand(name, () => runAction(func)));\n    };\n\n    const child = (scope, selector) => child$1(scope, selector).isSome();\n\n    const selection = identity;\n    const unmergable = selectedCells => {\n      const hasSpan = (elem, type) => getOpt(elem, type).exists(span => parseInt(span, 10) > 1);\n      const hasRowOrColSpan = elem => hasSpan(elem, 'rowspan') || hasSpan(elem, 'colspan');\n      return selectedCells.length > 0 && forall(selectedCells, hasRowOrColSpan) ? Optional.some(selectedCells) : Optional.none();\n    };\n    const mergable = (table, selectedCells, ephemera) => {\n      if (selectedCells.length <= 1) {\n        return Optional.none();\n      } else {\n        return retrieveBox(table, ephemera.firstSelectedSelector, ephemera.lastSelectedSelector).map(bounds => ({\n          bounds,\n          cells: selectedCells\n        }));\n      }\n    };\n\n    const noMenu = cell => ({\n      element: cell,\n      mergable: Optional.none(),\n      unmergable: Optional.none(),\n      selection: [cell]\n    });\n    const forMenu = (selectedCells, table, cell) => ({\n      element: cell,\n      mergable: mergable(table, selectedCells, ephemera),\n      unmergable: unmergable(selectedCells),\n      selection: selection(selectedCells)\n    });\n\n    const getSelectionTargets = editor => {\n      const targets = Cell(Optional.none());\n      const changeHandlers = Cell([]);\n      let selectionDetails = Optional.none();\n      const isCaption = isTag('caption');\n      const isDisabledForSelection = key => selectionDetails.forall(details => !details[key]);\n      const getStart = () => getSelectionCellOrCaption(getSelectionStart(editor), getIsRoot(editor));\n      const getEnd = () => getSelectionCellOrCaption(getSelectionEnd(editor), getIsRoot(editor));\n      const findTargets = () => getStart().bind(startCellOrCaption => flatten(lift2(table(startCellOrCaption), getEnd().bind(table), (startTable, endTable) => {\n        if (eq(startTable, endTable)) {\n          if (isCaption(startCellOrCaption)) {\n            return Optional.some(noMenu(startCellOrCaption));\n          } else {\n            return Optional.some(forMenu(getCellsFromSelection(editor), startTable, startCellOrCaption));\n          }\n        }\n        return Optional.none();\n      })));\n      const getExtractedDetails = targets => {\n        const tableOpt = table(targets.element);\n        return tableOpt.map(table => {\n          const warehouse = Warehouse.fromTable(table);\n          const selectedCells = onCells(warehouse, targets).getOr([]);\n          const locked = foldl(selectedCells, (acc, cell) => {\n            if (cell.isLocked) {\n              acc.onAny = true;\n              if (cell.column === 0) {\n                acc.onFirst = true;\n              } else if (cell.column + cell.colspan >= warehouse.grid.columns) {\n                acc.onLast = true;\n              }\n            }\n            return acc;\n          }, {\n            onAny: false,\n            onFirst: false,\n            onLast: false\n          });\n          return {\n            mergeable: onUnlockedMergable(warehouse, targets).isSome(),\n            unmergeable: onUnlockedUnmergable(warehouse, targets).isSome(),\n            locked\n          };\n        });\n      };\n      const resetTargets = () => {\n        targets.set(cached(findTargets)());\n        selectionDetails = targets.get().bind(getExtractedDetails);\n        each(changeHandlers.get(), call);\n      };\n      const setupHandler = handler => {\n        handler();\n        changeHandlers.set(changeHandlers.get().concat([handler]));\n        return () => {\n          changeHandlers.set(filter(changeHandlers.get(), h => h !== handler));\n        };\n      };\n      const onSetup = (api, isDisabled) => setupHandler(() => targets.get().fold(() => {\n        api.setEnabled(false);\n      }, targets => {\n        api.setEnabled(!isDisabled(targets) && editor.selection.isEditable());\n      }));\n      const onSetupWithToggle = (api, isDisabled, isActive) => setupHandler(() => targets.get().fold(() => {\n        api.setEnabled(false);\n        api.setActive(false);\n      }, targets => {\n        api.setEnabled(!isDisabled(targets) && editor.selection.isEditable());\n        api.setActive(isActive(targets));\n      }));\n      const isDisabledFromLocked = lockedDisable => selectionDetails.exists(details => details.locked[lockedDisable]);\n      const onSetupTable = api => onSetup(api, _ => false);\n      const onSetupCellOrRow = api => onSetup(api, targets => isCaption(targets.element));\n      const onSetupColumn = lockedDisable => api => onSetup(api, targets => isCaption(targets.element) || isDisabledFromLocked(lockedDisable));\n      const onSetupPasteable = getClipboardData => api => onSetup(api, targets => isCaption(targets.element) || getClipboardData().isNone());\n      const onSetupPasteableColumn = (getClipboardData, lockedDisable) => api => onSetup(api, targets => isCaption(targets.element) || getClipboardData().isNone() || isDisabledFromLocked(lockedDisable));\n      const onSetupMergeable = api => onSetup(api, _targets => isDisabledForSelection('mergeable'));\n      const onSetupUnmergeable = api => onSetup(api, _targets => isDisabledForSelection('unmergeable'));\n      const onSetupTableWithCaption = api => {\n        return onSetupWithToggle(api, never, targets => {\n          const tableOpt = table(targets.element, getIsRoot(editor));\n          return tableOpt.exists(table => child(table, 'caption'));\n        });\n      };\n      const onSetupTableHeaders = (command, headerType) => api => {\n        return onSetupWithToggle(api, targets => isCaption(targets.element), () => editor.queryCommandValue(command) === headerType);\n      };\n      const onSetupTableRowHeaders = onSetupTableHeaders('mceTableRowType', 'header');\n      const onSetupTableColumnHeaders = onSetupTableHeaders('mceTableColType', 'th');\n      editor.on('NodeChange ExecCommand TableSelectorChange', resetTargets);\n      return {\n        onSetupTable,\n        onSetupCellOrRow,\n        onSetupColumn,\n        onSetupPasteable,\n        onSetupPasteableColumn,\n        onSetupMergeable,\n        onSetupUnmergeable,\n        resetTargets,\n        onSetupTableWithCaption,\n        onSetupTableRowHeaders,\n        onSetupTableColumnHeaders,\n        targets: targets.get\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.FakeClipboard');\n\n    const tableTypeBase = 'x-tinymce/dom-table-';\n    const tableTypeRow = tableTypeBase + 'rows';\n    const tableTypeColumn = tableTypeBase + 'columns';\n    const getData = type => {\n      var _a;\n      const items = (_a = global.read()) !== null && _a !== void 0 ? _a : [];\n      return findMap(items, item => Optional.from(item.getType(type)));\n    };\n    const getRows = () => getData(tableTypeRow);\n    const getColumns = () => getData(tableTypeColumn);\n\n    const onSetupEditable$1 = editor => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const addButtons = (editor, selectionTargets) => {\n      editor.ui.registry.addMenuButton('table', {\n        tooltip: 'Table',\n        icon: 'table',\n        onSetup: onSetupEditable$1(editor),\n        fetch: callback => callback('inserttable | cell row column | advtablesort | tableprops deletetable')\n      });\n      const cmd = command => () => editor.execCommand(command);\n      const addButtonIfRegistered = (name, spec) => {\n        if (editor.queryCommandSupported(spec.command)) {\n          editor.ui.registry.addButton(name, {\n            ...spec,\n            onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n          });\n        }\n      };\n      const addToggleButtonIfRegistered = (name, spec) => {\n        if (editor.queryCommandSupported(spec.command)) {\n          editor.ui.registry.addToggleButton(name, {\n            ...spec,\n            onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n          });\n        }\n      };\n      addButtonIfRegistered('tableprops', {\n        tooltip: 'Table properties',\n        command: 'mceTableProps',\n        icon: 'table',\n        onSetup: selectionTargets.onSetupTable\n      });\n      addButtonIfRegistered('tabledelete', {\n        tooltip: 'Delete table',\n        command: 'mceTableDelete',\n        icon: 'table-delete-table',\n        onSetup: selectionTargets.onSetupTable\n      });\n      addButtonIfRegistered('tablecellprops', {\n        tooltip: 'Cell properties',\n        command: 'mceTableCellProps',\n        icon: 'table-cell-properties',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tablemergecells', {\n        tooltip: 'Merge cells',\n        command: 'mceTableMergeCells',\n        icon: 'table-merge-cells',\n        onSetup: selectionTargets.onSetupMergeable\n      });\n      addButtonIfRegistered('tablesplitcells', {\n        tooltip: 'Split cell',\n        command: 'mceTableSplitCells',\n        icon: 'table-split-cells',\n        onSetup: selectionTargets.onSetupUnmergeable\n      });\n      addButtonIfRegistered('tableinsertrowbefore', {\n        tooltip: 'Insert row before',\n        command: 'mceTableInsertRowBefore',\n        icon: 'table-insert-row-above',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tableinsertrowafter', {\n        tooltip: 'Insert row after',\n        command: 'mceTableInsertRowAfter',\n        icon: 'table-insert-row-after',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tabledeleterow', {\n        tooltip: 'Delete row',\n        command: 'mceTableDeleteRow',\n        icon: 'table-delete-row',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tablerowprops', {\n        tooltip: 'Row properties',\n        command: 'mceTableRowProps',\n        icon: 'table-row-properties',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tableinsertcolbefore', {\n        tooltip: 'Insert column before',\n        command: 'mceTableInsertColBefore',\n        icon: 'table-insert-column-before',\n        onSetup: selectionTargets.onSetupColumn('onFirst')\n      });\n      addButtonIfRegistered('tableinsertcolafter', {\n        tooltip: 'Insert column after',\n        command: 'mceTableInsertColAfter',\n        icon: 'table-insert-column-after',\n        onSetup: selectionTargets.onSetupColumn('onLast')\n      });\n      addButtonIfRegistered('tabledeletecol', {\n        tooltip: 'Delete column',\n        command: 'mceTableDeleteCol',\n        icon: 'table-delete-column',\n        onSetup: selectionTargets.onSetupColumn('onAny')\n      });\n      addButtonIfRegistered('tablecutrow', {\n        tooltip: 'Cut row',\n        command: 'mceTableCutRow',\n        icon: 'cut-row',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tablecopyrow', {\n        tooltip: 'Copy row',\n        command: 'mceTableCopyRow',\n        icon: 'duplicate-row',\n        onSetup: selectionTargets.onSetupCellOrRow\n      });\n      addButtonIfRegistered('tablepasterowbefore', {\n        tooltip: 'Paste row before',\n        command: 'mceTablePasteRowBefore',\n        icon: 'paste-row-before',\n        onSetup: selectionTargets.onSetupPasteable(getRows)\n      });\n      addButtonIfRegistered('tablepasterowafter', {\n        tooltip: 'Paste row after',\n        command: 'mceTablePasteRowAfter',\n        icon: 'paste-row-after',\n        onSetup: selectionTargets.onSetupPasteable(getRows)\n      });\n      addButtonIfRegistered('tablecutcol', {\n        tooltip: 'Cut column',\n        command: 'mceTableCutCol',\n        icon: 'cut-column',\n        onSetup: selectionTargets.onSetupColumn('onAny')\n      });\n      addButtonIfRegistered('tablecopycol', {\n        tooltip: 'Copy column',\n        command: 'mceTableCopyCol',\n        icon: 'duplicate-column',\n        onSetup: selectionTargets.onSetupColumn('onAny')\n      });\n      addButtonIfRegistered('tablepastecolbefore', {\n        tooltip: 'Paste column before',\n        command: 'mceTablePasteColBefore',\n        icon: 'paste-column-before',\n        onSetup: selectionTargets.onSetupPasteableColumn(getColumns, 'onFirst')\n      });\n      addButtonIfRegistered('tablepastecolafter', {\n        tooltip: 'Paste column after',\n        command: 'mceTablePasteColAfter',\n        icon: 'paste-column-after',\n        onSetup: selectionTargets.onSetupPasteableColumn(getColumns, 'onLast')\n      });\n      addButtonIfRegistered('tableinsertdialog', {\n        tooltip: 'Insert table',\n        command: 'mceInsertTableDialog',\n        icon: 'table',\n        onSetup: onSetupEditable$1(editor)\n      });\n      const tableClassList = filterNoneItem(getTableClassList(editor));\n      if (tableClassList.length !== 0 && editor.queryCommandSupported('mceTableToggleClass')) {\n        editor.ui.registry.addMenuButton('tableclass', {\n          icon: 'table-classes',\n          tooltip: 'Table styles',\n          fetch: generateMenuItemsCallback(editor, tableClassList, 'tableclass', value => editor.execCommand('mceTableToggleClass', false, value)),\n          onSetup: selectionTargets.onSetupTable\n        });\n      }\n      const tableCellClassList = filterNoneItem(getCellClassList(editor));\n      if (tableCellClassList.length !== 0 && editor.queryCommandSupported('mceTableCellToggleClass')) {\n        editor.ui.registry.addMenuButton('tablecellclass', {\n          icon: 'table-cell-classes',\n          tooltip: 'Cell styles',\n          fetch: generateMenuItemsCallback(editor, tableCellClassList, 'tablecellclass', value => editor.execCommand('mceTableCellToggleClass', false, value)),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n      }\n      if (editor.queryCommandSupported('mceTableApplyCellStyle')) {\n        editor.ui.registry.addMenuButton('tablecellvalign', {\n          icon: 'vertical-align',\n          tooltip: 'Vertical align',\n          fetch: generateMenuItemsCallback(editor, verticalAlignValues, 'tablecellverticalalign', applyTableCellStyle(editor, 'vertical-align')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addMenuButton('tablecellborderwidth', {\n          icon: 'border-width',\n          tooltip: 'Border width',\n          fetch: generateMenuItemsCallback(editor, getTableBorderWidths(editor), 'tablecellborderwidth', applyTableCellStyle(editor, 'border-width')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addMenuButton('tablecellborderstyle', {\n          icon: 'border-style',\n          tooltip: 'Border style',\n          fetch: generateMenuItemsCallback(editor, getTableBorderStyles(editor), 'tablecellborderstyle', applyTableCellStyle(editor, 'border-style')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addMenuButton('tablecellbackgroundcolor', {\n          icon: 'cell-background-color',\n          tooltip: 'Background color',\n          fetch: callback => callback(buildColorMenu(editor, getTableBackgroundColorMap(editor), 'background-color')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addMenuButton('tablecellbordercolor', {\n          icon: 'cell-border-color',\n          tooltip: 'Border color',\n          fetch: callback => callback(buildColorMenu(editor, getTableBorderColorMap(editor), 'border-color')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n      }\n      addToggleButtonIfRegistered('tablecaption', {\n        tooltip: 'Table caption',\n        icon: 'table-caption',\n        command: 'mceTableToggleCaption',\n        onSetup: selectionTargets.onSetupTableWithCaption\n      });\n      addToggleButtonIfRegistered('tablerowheader', {\n        tooltip: 'Row header',\n        icon: 'table-top-header',\n        command: 'mceTableRowType',\n        onAction: changeRowHeader(editor),\n        onSetup: selectionTargets.onSetupTableRowHeaders\n      });\n      addToggleButtonIfRegistered('tablecolheader', {\n        tooltip: 'Column header',\n        icon: 'table-left-header',\n        command: 'mceTableColType',\n        onAction: changeColumnHeader(editor),\n        onSetup: selectionTargets.onSetupTableColumnHeaders\n      });\n    };\n    const addToolbars = editor => {\n      const isEditableTable = table => editor.dom.is(table, 'table') && editor.getBody().contains(table) && editor.dom.isEditable(table.parentNode);\n      const toolbar = getToolbar(editor);\n      if (toolbar.length > 0) {\n        editor.ui.registry.addContextToolbar('table', {\n          predicate: isEditableTable,\n          items: toolbar,\n          scope: 'node',\n          position: 'node'\n        });\n      }\n    };\n\n    const onSetupEditable = editor => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const addMenuItems = (editor, selectionTargets) => {\n      const cmd = command => () => editor.execCommand(command);\n      const addMenuIfRegistered = (name, spec) => {\n        if (editor.queryCommandSupported(spec.command)) {\n          editor.ui.registry.addMenuItem(name, {\n            ...spec,\n            onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n          });\n          return true;\n        } else {\n          return false;\n        }\n      };\n      const addToggleMenuIfRegistered = (name, spec) => {\n        if (editor.queryCommandSupported(spec.command)) {\n          editor.ui.registry.addToggleMenuItem(name, {\n            ...spec,\n            onAction: isFunction(spec.onAction) ? spec.onAction : cmd(spec.command)\n          });\n        }\n      };\n      const insertTableAction = data => {\n        editor.execCommand('mceInsertTable', false, {\n          rows: data.numRows,\n          columns: data.numColumns\n        });\n      };\n      const hasRowMenuItems = [\n        addMenuIfRegistered('tableinsertrowbefore', {\n          text: 'Insert row before',\n          icon: 'table-insert-row-above',\n          command: 'mceTableInsertRowBefore',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tableinsertrowafter', {\n          text: 'Insert row after',\n          icon: 'table-insert-row-after',\n          command: 'mceTableInsertRowAfter',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tabledeleterow', {\n          text: 'Delete row',\n          icon: 'table-delete-row',\n          command: 'mceTableDeleteRow',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tablerowprops', {\n          text: 'Row properties',\n          icon: 'table-row-properties',\n          command: 'mceTableRowProps',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tablecutrow', {\n          text: 'Cut row',\n          icon: 'cut-row',\n          command: 'mceTableCutRow',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tablecopyrow', {\n          text: 'Copy row',\n          icon: 'duplicate-row',\n          command: 'mceTableCopyRow',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tablepasterowbefore', {\n          text: 'Paste row before',\n          icon: 'paste-row-before',\n          command: 'mceTablePasteRowBefore',\n          onSetup: selectionTargets.onSetupPasteable(getRows)\n        }),\n        addMenuIfRegistered('tablepasterowafter', {\n          text: 'Paste row after',\n          icon: 'paste-row-after',\n          command: 'mceTablePasteRowAfter',\n          onSetup: selectionTargets.onSetupPasteable(getRows)\n        })\n      ];\n      const hasColumnMenuItems = [\n        addMenuIfRegistered('tableinsertcolumnbefore', {\n          text: 'Insert column before',\n          icon: 'table-insert-column-before',\n          command: 'mceTableInsertColBefore',\n          onSetup: selectionTargets.onSetupColumn('onFirst')\n        }),\n        addMenuIfRegistered('tableinsertcolumnafter', {\n          text: 'Insert column after',\n          icon: 'table-insert-column-after',\n          command: 'mceTableInsertColAfter',\n          onSetup: selectionTargets.onSetupColumn('onLast')\n        }),\n        addMenuIfRegistered('tabledeletecolumn', {\n          text: 'Delete column',\n          icon: 'table-delete-column',\n          command: 'mceTableDeleteCol',\n          onSetup: selectionTargets.onSetupColumn('onAny')\n        }),\n        addMenuIfRegistered('tablecutcolumn', {\n          text: 'Cut column',\n          icon: 'cut-column',\n          command: 'mceTableCutCol',\n          onSetup: selectionTargets.onSetupColumn('onAny')\n        }),\n        addMenuIfRegistered('tablecopycolumn', {\n          text: 'Copy column',\n          icon: 'duplicate-column',\n          command: 'mceTableCopyCol',\n          onSetup: selectionTargets.onSetupColumn('onAny')\n        }),\n        addMenuIfRegistered('tablepastecolumnbefore', {\n          text: 'Paste column before',\n          icon: 'paste-column-before',\n          command: 'mceTablePasteColBefore',\n          onSetup: selectionTargets.onSetupPasteableColumn(getColumns, 'onFirst')\n        }),\n        addMenuIfRegistered('tablepastecolumnafter', {\n          text: 'Paste column after',\n          icon: 'paste-column-after',\n          command: 'mceTablePasteColAfter',\n          onSetup: selectionTargets.onSetupPasteableColumn(getColumns, 'onLast')\n        })\n      ];\n      const hasCellMenuItems = [\n        addMenuIfRegistered('tablecellprops', {\n          text: 'Cell properties',\n          icon: 'table-cell-properties',\n          command: 'mceTableCellProps',\n          onSetup: selectionTargets.onSetupCellOrRow\n        }),\n        addMenuIfRegistered('tablemergecells', {\n          text: 'Merge cells',\n          icon: 'table-merge-cells',\n          command: 'mceTableMergeCells',\n          onSetup: selectionTargets.onSetupMergeable\n        }),\n        addMenuIfRegistered('tablesplitcells', {\n          text: 'Split cell',\n          icon: 'table-split-cells',\n          command: 'mceTableSplitCells',\n          onSetup: selectionTargets.onSetupUnmergeable\n        })\n      ];\n      if (!hasTableGrid(editor)) {\n        editor.ui.registry.addMenuItem('inserttable', {\n          text: 'Table',\n          icon: 'table',\n          onAction: cmd('mceInsertTableDialog'),\n          onSetup: onSetupEditable(editor)\n        });\n      } else {\n        editor.ui.registry.addNestedMenuItem('inserttable', {\n          text: 'Table',\n          icon: 'table',\n          getSubmenuItems: () => [{\n              type: 'fancymenuitem',\n              fancytype: 'inserttable',\n              onAction: insertTableAction\n            }],\n          onSetup: onSetupEditable(editor)\n        });\n      }\n      editor.ui.registry.addMenuItem('inserttabledialog', {\n        text: 'Insert table',\n        icon: 'table',\n        onAction: cmd('mceInsertTableDialog'),\n        onSetup: onSetupEditable(editor)\n      });\n      addMenuIfRegistered('tableprops', {\n        text: 'Table properties',\n        onSetup: selectionTargets.onSetupTable,\n        command: 'mceTableProps'\n      });\n      addMenuIfRegistered('deletetable', {\n        text: 'Delete table',\n        icon: 'table-delete-table',\n        onSetup: selectionTargets.onSetupTable,\n        command: 'mceTableDelete'\n      });\n      if (contains(hasRowMenuItems, true)) {\n        editor.ui.registry.addNestedMenuItem('row', {\n          type: 'nestedmenuitem',\n          text: 'Row',\n          getSubmenuItems: constant('tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter')\n        });\n      }\n      if (contains(hasColumnMenuItems, true)) {\n        editor.ui.registry.addNestedMenuItem('column', {\n          type: 'nestedmenuitem',\n          text: 'Column',\n          getSubmenuItems: constant('tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter')\n        });\n      }\n      if (contains(hasCellMenuItems, true)) {\n        editor.ui.registry.addNestedMenuItem('cell', {\n          type: 'nestedmenuitem',\n          text: 'Cell',\n          getSubmenuItems: constant('tablecellprops tablemergecells tablesplitcells')\n        });\n      }\n      editor.ui.registry.addContextMenu('table', {\n        update: () => {\n          selectionTargets.resetTargets();\n          return selectionTargets.targets().fold(constant(''), targets => {\n            if (name(targets.element) === 'caption') {\n              return 'tableprops deletetable';\n            } else {\n              return 'cell row column | advtablesort | tableprops deletetable';\n            }\n          });\n        }\n      });\n      const tableClassList = filterNoneItem(getTableClassList(editor));\n      if (tableClassList.length !== 0 && editor.queryCommandSupported('mceTableToggleClass')) {\n        editor.ui.registry.addNestedMenuItem('tableclass', {\n          icon: 'table-classes',\n          text: 'Table styles',\n          getSubmenuItems: () => buildMenuItems(editor, tableClassList, 'tableclass', value => editor.execCommand('mceTableToggleClass', false, value)),\n          onSetup: selectionTargets.onSetupTable\n        });\n      }\n      const tableCellClassList = filterNoneItem(getCellClassList(editor));\n      if (tableCellClassList.length !== 0 && editor.queryCommandSupported('mceTableCellToggleClass')) {\n        editor.ui.registry.addNestedMenuItem('tablecellclass', {\n          icon: 'table-cell-classes',\n          text: 'Cell styles',\n          getSubmenuItems: () => buildMenuItems(editor, tableCellClassList, 'tablecellclass', value => editor.execCommand('mceTableCellToggleClass', false, value)),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n      }\n      if (editor.queryCommandSupported('mceTableApplyCellStyle')) {\n        editor.ui.registry.addNestedMenuItem('tablecellvalign', {\n          icon: 'vertical-align',\n          text: 'Vertical align',\n          getSubmenuItems: () => buildMenuItems(editor, verticalAlignValues, 'tablecellverticalalign', applyTableCellStyle(editor, 'vertical-align')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addNestedMenuItem('tablecellborderwidth', {\n          icon: 'border-width',\n          text: 'Border width',\n          getSubmenuItems: () => buildMenuItems(editor, getTableBorderWidths(editor), 'tablecellborderwidth', applyTableCellStyle(editor, 'border-width')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addNestedMenuItem('tablecellborderstyle', {\n          icon: 'border-style',\n          text: 'Border style',\n          getSubmenuItems: () => buildMenuItems(editor, getTableBorderStyles(editor), 'tablecellborderstyle', applyTableCellStyle(editor, 'border-style')),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addNestedMenuItem('tablecellbackgroundcolor', {\n          icon: 'cell-background-color',\n          text: 'Background color',\n          getSubmenuItems: () => buildColorMenu(editor, getTableBackgroundColorMap(editor), 'background-color'),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n        editor.ui.registry.addNestedMenuItem('tablecellbordercolor', {\n          icon: 'cell-border-color',\n          text: 'Border color',\n          getSubmenuItems: () => buildColorMenu(editor, getTableBorderColorMap(editor), 'border-color'),\n          onSetup: selectionTargets.onSetupCellOrRow\n        });\n      }\n      addToggleMenuIfRegistered('tablecaption', {\n        icon: 'table-caption',\n        text: 'Table caption',\n        command: 'mceTableToggleCaption',\n        onSetup: selectionTargets.onSetupTableWithCaption\n      });\n      addToggleMenuIfRegistered('tablerowheader', {\n        text: 'Row header',\n        icon: 'table-top-header',\n        command: 'mceTableRowType',\n        onAction: changeRowHeader(editor),\n        onSetup: selectionTargets.onSetupTableRowHeaders\n      });\n      addToggleMenuIfRegistered('tablecolheader', {\n        text: 'Column header',\n        icon: 'table-left-header',\n        command: 'mceTableColType',\n        onAction: changeColumnHeader(editor),\n        onSetup: selectionTargets.onSetupTableRowHeaders\n      });\n    };\n\n    const Plugin = editor => {\n      const selectionTargets = getSelectionTargets(editor);\n      register(editor);\n      registerCommands(editor);\n      addMenuItems(editor, selectionTargets);\n      addButtons(editor, selectionTargets);\n      addToolbars(editor);\n    };\n    var Plugin$1 = () => {\n      global$3.add('table', Plugin);\n    };\n\n    Plugin$1();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,QAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,QAAI;AACJ,QAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,aAAO;AAAA,IACT,OAAO;AACL,eAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,IAC7F;AAAA,EACF;AACA,QAAM,SAAS,OAAK;AAClB,UAAM,IAAI,OAAO;AACjB,QAAI,MAAM,MAAM;AACd,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,WAAW,CAAAA,UAAQ,WAAS,OAAO,KAAK,MAAMA;AACpD,QAAM,eAAe,CAAAA,UAAQ,WAAS,OAAO,UAAUA;AACvD,QAAM,OAAO,OAAK,OAAK,MAAM;AAC7B,QAAM,WAAW,SAAS,QAAQ;AAClC,QAAM,UAAU,SAAS,OAAO;AAChC,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,cAAc,KAAK,MAAS;AAClC,QAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,QAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,QAAM,aAAa,aAAa,UAAU;AAC1C,QAAM,WAAW,aAAa,QAAQ;AAEtC,QAAM,OAAO,MAAM;AAAA,EACnB;AACA,QAAM,WAAW,CAAC,KAAK,QAAQ,OAAK,IAAI,IAAI,CAAC,CAAC;AAC9C,QAAM,WAAW,WAAS;AACxB,WAAO,MAAM;AACX,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,WAAW,OAAK;AACpB,WAAO;AAAA,EACT;AACA,QAAM,eAAe,CAAC,GAAG,MAAM;AAC7B,WAAO,MAAM;AAAA,EACf;AACA,WAAS,MAAM,OAAO,aAAa;AACjC,WAAO,IAAI,aAAa;AACtB,YAAMC,OAAM,YAAY,OAAO,QAAQ;AACvC,aAAO,GAAG,MAAM,MAAMA,IAAG;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,OAAO,OAAK;AAChB,MAAE;AAAA,EACJ;AACA,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,SAAS,SAAS,IAAI;AAAA,EAE5B,MAAM,SAAS;AAAA,IACb,YAAY,KAAK,OAAO;AACtB,WAAK,MAAM;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,IACA,OAAO,KAAK,OAAO;AACjB,aAAO,IAAI,SAAS,MAAM,KAAK;AAAA,IACjC;AAAA,IACA,OAAO,OAAO;AACZ,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,KAAK,QAAQ,QAAQ;AACnB,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AACP,aAAO,KAAK;AAAA,IACd;AAAA,IACA,SAAS;AACP,aAAO,CAAC,KAAK;AAAA,IACf;AAAA,IACA,IAAI,QAAQ;AACV,UAAI,KAAK,KAAK;AACZ,eAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,MACzC,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IACzC;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IAC1C;AAAA,IACA,OAAO,WAAW;AAChB,UAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,MAAM,aAAa;AACjB,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,GAAG,aAAa;AACd,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B;AAAA,IACA,WAAW,OAAO;AAChB,aAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,IACvC;AAAA,IACA,QAAQ,OAAO;AACb,aAAO,KAAK,MAAM,OAAO,MAAM;AAAA,IACjC;AAAA,IACA,SAAS,SAAS;AAChB,UAAI,CAAC,KAAK,KAAK;AACb,cAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,MAC9F,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,IACA,OAAO,KAAK,OAAO;AACjB,aAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,IACrE;AAAA,IACA,YAAY;AACV,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,iBAAiB;AACf,aAAO,KAAK;AAAA,IACd;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,IACA,UAAU;AACR,aAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,IACpC;AAAA,IACA,WAAW;AACT,aAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,IAC9C;AAAA,EACF;AACA,WAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,QAAM,OAAO,OAAO;AACpB,QAAM,iBAAiB,OAAO;AAC9B,QAAM,SAAS,CAAC,KAAK,MAAM;AACzB,UAAM,QAAQ,KAAK,GAAG;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,IAAI,IAAI,CAAC;AACf,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,SAAS,OAAK,CAAC,GAAG,MAAM;AAC5B,MAAE,CAAC,IAAI;AAAA,EACT;AACA,QAAM,iBAAiB,CAAC,KAAK,MAAM,QAAQ,YAAY;AACrD,WAAO,KAAK,CAAC,GAAG,MAAM;AACpB,OAAC,KAAK,GAAG,CAAC,IAAI,SAAS,SAAS,GAAG,CAAC;AAAA,IACtC,CAAC;AAAA,EACH;AACA,QAAM,WAAW,CAAC,KAAK,SAAS;AAC9B,UAAM,IAAI,CAAC;AACX,mBAAe,KAAK,MAAM,OAAO,CAAC,GAAG,IAAI;AACzC,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC,KAAK,MAAM;AAC7B,UAAM,IAAI,CAAC;AACX,WAAO,KAAK,CAAC,OAAOC,UAAS;AAC3B,QAAE,KAAK,EAAE,OAAOA,KAAI,CAAC;AAAA,IACvB,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,SAAS,SAAO;AACpB,WAAO,WAAW,KAAK,QAAQ;AAAA,EACjC;AACA,QAAM,OAAO,SAAO;AAClB,WAAO,KAAK,GAAG,EAAE;AAAA,EACnB;AACA,QAAM,QAAQ,CAAC,KAAKC,SAAQ;AAC1B,WAAO,IAAI,KAAKA,IAAG,IAAI,SAAS,KAAK,IAAIA,IAAG,CAAC,IAAI,SAAS,KAAK;AAAA,EACjE;AACA,QAAM,MAAM,CAAC,KAAKA,SAAQ,eAAe,KAAK,KAAKA,IAAG;AACtD,QAAM,oBAAoB,CAAC,KAAKA,SAAQ,IAAI,KAAKA,IAAG,KAAK,IAAIA,IAAG,MAAM,UAAa,IAAIA,IAAG,MAAM;AAChG,QAAM,YAAY,OAAK;AACrB,eAAW,KAAK,GAAG;AACjB,UAAI,eAAe,KAAK,GAAG,CAAC,GAAG;AAC7B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,QAAM,gBAAgB,MAAM,UAAU;AACtC,QAAM,aAAa,MAAM,UAAU;AACnC,QAAM,aAAa,CAAC,IAAI,MAAM,cAAc,KAAK,IAAI,CAAC;AACtD,QAAM,WAAW,CAAC,IAAI,MAAM,WAAW,IAAI,CAAC,IAAI;AAChD,QAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,KAAK,GAAG,CAAC,GAAG;AACd,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,CAAC,KAAK,MAAM;AACxB,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAE,KAAK,EAAE,CAAC,CAAC;AAAA,IACb;AACA,WAAO;AAAA,EACT;AACA,QAAM,MAAM,CAAC,IAAI,MAAM;AACrB,UAAM,MAAM,GAAG;AACf,UAAM,IAAI,IAAI,MAAM,GAAG;AACvB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,IACf;AACA,WAAO;AAAA,EACT;AACA,QAAM,OAAO,CAAC,IAAI,MAAM;AACtB,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,IAAI,MAAM;AACvB,aAAS,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,YAAY,CAAC,IAAI,SAAS;AAC9B,UAAM,OAAO,CAAC;AACd,UAAM,OAAO,CAAC;AACd,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,YAAM,MAAM,KAAK,GAAG,CAAC,IAAI,OAAO;AAChC,UAAI,KAAK,CAAC;AAAA,IACZ;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,KAAK,GAAG,CAAC,GAAG;AACd,UAAE,KAAK,CAAC;AAAA,MACV;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,UAAM,IAAI,CAAC,GAAG,MAAM;AAClB,YAAM,EAAE,KAAK,GAAG,CAAC;AAAA,IACnB,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,SAAK,IAAI,CAAC,GAAG,MAAM;AACjB,YAAM,EAAE,KAAK,GAAG,CAAC;AAAA,IACnB,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,YAAY,CAAC,IAAI,MAAM,UAAU;AACrC,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,KAAK,GAAG,CAAC,GAAG;AACd,eAAO,SAAS,KAAK,CAAC;AAAA,MACxB,WAAW,MAAM,GAAG,CAAC,GAAG;AACtB;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,QAAM,OAAO,CAAC,IAAI,SAAS;AACzB,WAAO,UAAU,IAAI,MAAM,KAAK;AAAA,EAClC;AACA,QAAM,YAAY,QAAM;AACtB,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,UAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG;AACnB,cAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,MAC7E;AACA,iBAAW,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AACA,QAAM,OAAO,CAAC,IAAI,MAAM,UAAU,IAAI,IAAI,CAAC,CAAC;AAC5C,QAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,KAAK,GAAG,CAAC,MAAM,MAAM;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAc,CAAC,IAAI,MAAM;AAC7B,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,CAAC,CAAC,IAAI,SAAS,KAAK;AACxF,QAAM,OAAO,QAAM,MAAM,IAAI,CAAC;AAC9B,QAAM,OAAO,QAAM,MAAM,IAAI,GAAG,SAAS,CAAC;AAC1C,QAAM,UAAU,CAAC,KAAK,MAAM;AAC1B,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAM,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;AACrB,UAAI,EAAE,OAAO,GAAG;AACd,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AAEA,QAAM,UAAU;AAChB,QAAM,WAAW;AACjB,QAAM,oBAAoB;AAC1B,QAAM,UAAU;AAChB,QAAM,OAAO;AAEb,QAAM,WAAW,CAAC,MAAM,UAAU;AAChC,UAAM,MAAM,SAAS;AACrB,UAAM,MAAM,IAAI,cAAc,KAAK;AACnC,QAAI,YAAY;AAChB,QAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,YAAM,UAAU;AAChB,cAAQ,MAAM,SAAS,IAAI;AAC3B,YAAM,IAAI,MAAM,OAAO;AAAA,IACzB;AACA,WAAO,UAAU,IAAI,WAAW,CAAC,CAAC;AAAA,EACpC;AACA,QAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,UAAM,MAAM,SAAS;AACrB,UAAM,OAAO,IAAI,cAAc,GAAG;AAClC,WAAO,UAAU,IAAI;AAAA,EACvB;AACA,QAAM,WAAW,CAAC,MAAM,UAAU;AAChC,UAAM,MAAM,SAAS;AACrB,UAAM,OAAO,IAAI,eAAe,IAAI;AACpC,WAAO,UAAU,IAAI;AAAA,EACvB;AACA,QAAM,YAAY,UAAQ;AACxB,QAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,WAAO,EAAE,KAAK,KAAK;AAAA,EACrB;AACA,QAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,SAAS;AAClG,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,EACF;AAEA,QAAM,OAAO,CAAC,SAAS,aAAa;AAClC,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI,aAAa,SAAS;AAC5B,aAAO;AAAA,IACT,OAAO;AACL,YAAM,OAAO;AACb,UAAI,KAAK,YAAY,QAAW;AAC9B,eAAO,KAAK,QAAQ,QAAQ;AAAA,MAC9B,WAAW,KAAK,sBAAsB,QAAW;AAC/C,eAAO,KAAK,kBAAkB,QAAQ;AAAA,MACxC,WAAW,KAAK,0BAA0B,QAAW;AACnD,eAAO,KAAK,sBAAsB,QAAQ;AAAA,MAC5C,WAAW,KAAK,uBAAuB,QAAW;AAChD,eAAO,KAAK,mBAAmB,QAAQ;AAAA,MACzC,OAAO;AACL,cAAM,IAAI,MAAM,gCAAgC;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,SAAO,IAAI,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,qBAAqB,IAAI,sBAAsB;AACvJ,QAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,UAAM,OAAO,UAAU,SAAY,WAAW,MAAM;AACpD,WAAO,eAAe,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,iBAAiB,QAAQ,GAAG,aAAa,OAAO;AAAA,EAC9F;AACA,QAAM,MAAM,CAAC,UAAU,UAAU;AAC/B,UAAM,OAAO,UAAU,SAAY,WAAW,MAAM;AACpD,WAAO,eAAe,IAAI,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,KAAK,cAAc,QAAQ,CAAC,EAAE,IAAI,aAAa,OAAO;AAAA,EACtH;AAEA,QAAM,KAAK,CAAC,IAAI,OAAO,GAAG,QAAQ,GAAG;AACrC,QAAM,OAAO;AAEb,SAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAElE,QAAM,OAAO,aAAW;AACtB,UAAM,IAAI,QAAQ,IAAI;AACtB,WAAO,EAAE,YAAY;AAAA,EACvB;AACA,QAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,QAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,QAAM,YAAY,aAAW,KAAK,OAAO,MAAM,WAAW,KAAK,OAAO,MAAM;AAC5E,QAAM,YAAY,OAAO,OAAO;AAChC,QAAM,SAAS,OAAO,IAAI;AAC1B,QAAM,aAAa,OAAO,QAAQ;AAClC,QAAM,qBAAqB,OAAO,iBAAiB;AACnD,QAAM,QAAQ,SAAO,OAAK,UAAU,CAAC,KAAK,KAAK,CAAC,MAAM;AAEtD,QAAM,QAAQ,aAAW,aAAa,QAAQ,QAAQ,IAAI,aAAa;AACvE,QAAM,kBAAkB,SAAO,WAAW,GAAG,IAAI,MAAM,MAAM,GAAG;AAChE,QAAM,SAAS,aAAW,SAAS,KAAK,QAAQ,IAAI,UAAU,EAAE,IAAI,aAAa,OAAO;AACxF,QAAM,UAAU,CAAC,SAAS,WAAW;AACnC,UAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,QAAI,MAAM,QAAQ;AAClB,UAAM,MAAM,CAAC;AACb,WAAO,IAAI,eAAe,QAAQ,IAAI,eAAe,QAAW;AAC9D,YAAM,YAAY,IAAI;AACtB,YAAM,IAAI,aAAa,QAAQ,SAAS;AACxC,UAAI,KAAK,CAAC;AACV,UAAI,KAAK,CAAC,MAAM,MAAM;AACpB;AAAA,MACF,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAc,aAAW,SAAS,KAAK,QAAQ,IAAI,eAAe,EAAE,IAAI,aAAa,OAAO;AAClG,QAAM,cAAc,aAAW,SAAS,KAAK,QAAQ,IAAI,WAAW,EAAE,IAAI,aAAa,OAAO;AAC9F,QAAM,aAAa,aAAW,IAAI,QAAQ,IAAI,YAAY,aAAa,OAAO;AAC9E,QAAM,UAAU,CAAC,SAAS,UAAU;AAClC,UAAM,KAAK,QAAQ,IAAI;AACvB,WAAO,SAAS,KAAK,GAAG,KAAK,CAAC,EAAE,IAAI,aAAa,OAAO;AAAA,EAC1D;AACA,QAAM,aAAa,aAAW,QAAQ,SAAS,CAAC;AAEhD,QAAM,eAAe,SAAO,mBAAmB,GAAG,KAAK,cAAc,IAAI,IAAI,IAAI;AACjF,QAAM,YAAY,WAAW,QAAQ,UAAU,YAAY,KAAK,WAAW,KAAK,UAAU,WAAW;AACrG,QAAM,cAAc,YAAY,OAAK,aAAa,QAAQ,EAAE,IAAI,YAAY,CAAC,IAAI;AACjF,QAAM,gBAAgB,OAAK;AACzB,UAAM,IAAI,YAAY,CAAC;AACvB,WAAO,aAAa,CAAC,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAAA,EAC5D;AACA,QAAM,gBAAgB,OAAK,aAAa,QAAQ,EAAE,IAAI,IAAI;AAE1D,QAAM,SAAS,aAAW;AACxB,UAAM,MAAM,OAAO,OAAO,IAAI,QAAQ,IAAI,aAAa,QAAQ;AAC/D,QAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,aAAO;AAAA,IACT;AACA,UAAM,MAAM,IAAI;AAChB,WAAO,cAAc,aAAa,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG,SAAS,QAAQ,aAAa,CAAC;AAAA,EACpH;AAEA,MAAI,oBAAoB,CAACC,KAAIC,WAAU,OAAO,GAAG,WAAW;AAC1D,QAAID,IAAG,OAAO,CAAC,GAAG;AAChB,aAAO,SAAS,KAAK,KAAK;AAAA,IAC5B,WAAW,WAAW,MAAM,KAAK,OAAO,KAAK,GAAG;AAC9C,aAAO,SAAS,KAAK;AAAA,IACvB,OAAO;AACL,aAAOC,UAAS,OAAO,GAAG,MAAM;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,aAAa,CAAC,OAAO,WAAW,WAAW;AAC/C,QAAI,UAAU,MAAM;AACpB,UAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,WAAO,QAAQ,YAAY;AACzB,gBAAU,QAAQ;AAClB,YAAM,KAAK,aAAa,QAAQ,OAAO;AACvC,UAAI,UAAU,EAAE,GAAG;AACjB,eAAO,SAAS,KAAK,EAAE;AAAA,MACzB,WAAW,KAAK,EAAE,GAAG;AACnB;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,QAAM,YAAY,CAAC,OAAO,WAAW,WAAW;AAC9C,UAAMD,MAAK,CAAC,GAAG,SAAS,KAAK,CAAC;AAC9B,WAAO,kBAAkBA,KAAI,YAAY,OAAO,WAAW,MAAM;AAAA,EACnE;AACA,QAAM,UAAU,CAAC,OAAO,cAAc;AACpC,UAAM,OAAO,UAAQ,UAAU,aAAa,QAAQ,IAAI,CAAC;AACzD,UAAM,SAAS,KAAK,MAAM,IAAI,YAAY,IAAI;AAC9C,WAAO,OAAO,IAAI,aAAa,OAAO;AAAA,EACxC;AAEA,QAAM,WAAW,CAAC,OAAO,UAAU,WAAW,WAAW,OAAO,OAAK,KAAK,GAAG,QAAQ,GAAG,MAAM;AAC9F,QAAM,UAAU,CAAC,OAAO,aAAa,QAAQ,OAAO,OAAK,KAAK,GAAG,QAAQ,CAAC;AAC1E,QAAM,aAAa,CAAC,OAAO,aAAa,IAAI,UAAU,KAAK;AAC3D,QAAM,YAAY,CAAC,OAAO,UAAU,WAAW;AAC7C,UAAMA,MAAK,CAAC,SAASE,cAAa,KAAK,SAASA,SAAQ;AACxD,WAAO,kBAAkBF,KAAI,UAAU,OAAO,UAAU,MAAM;AAAA,EAChE;AAEA,QAAM,UAAU,YAAU,UAAU,QAAQ,mBAAmB;AAC/D,QAAM,aAAa,CAAC,SAAS,iBAAiB,UAAU;AACtD,QAAI,OAAO,OAAO,GAAG;AACnB,aAAO,QAAQ,IAAI;AAAA,IACrB,OAAO;AACL,aAAO,QAAQ,OAAO,EAAE,KAAK,SAAS,cAAc,GAAG,cAAY,SAAS,QAAQ,MAAM,MAAM;AAAA,IAClG;AAAA,EACF;AACA,QAAM,WAAW,aAAW,QAAQ,IAAI;AAExC,QAAM,cAAc,SAAO,IAAI,SAAS,YAAY;AACpD,QAAM,UAAU,YAAU,aAAa,QAAQ,OAAO,QAAQ,CAAC;AAC/D,QAAM,YAAY,YAAU,aAAW,GAAG,SAAS,QAAQ,MAAM,CAAC;AAClE,QAAM,iBAAiB,CAAAG,UAAQA,QAAOA,MAAK,QAAQ,OAAO,EAAE,IAAI;AAChE,QAAM,cAAc,CAAAA,UAAQ,gBAAgB,KAAKA,KAAI,IAAIA,QAAO,OAAOA;AACvE,QAAM,oBAAoB,YAAU,aAAa,QAAQ,OAAO,UAAU,SAAS,CAAC;AACpF,QAAM,kBAAkB,YAAU,aAAa,QAAQ,OAAO,UAAU,OAAO,CAAC;AAChF,QAAM,sBAAsB,CAAAC,UAAQ,UAAUA,OAAM,MAAM,OAAO,CAAC,EAAE,OAAO,UAAU;AAErF,QAAM,aAAa,CAAC,OAAO,cAAc,OAAO,WAAW,KAAK,GAAG,SAAS;AAC5E,QAAM,gBAAgB,CAAC,OAAO,cAAc;AAC1C,QAAI,SAAS,CAAC;AACd,SAAK,WAAW,KAAK,GAAG,OAAK;AAC3B,UAAI,UAAU,CAAC,GAAG;AAChB,iBAAS,OAAO,OAAO,CAAC,CAAC,CAAC;AAAA,MAC5B;AACA,eAAS,OAAO,OAAO,cAAc,GAAG,SAAS,CAAC;AAAA,IACpD,CAAC;AACD,WAAO;AAAA,EACT;AAEA,QAAM,aAAa,CAAC,OAAO,aAAa,WAAW,OAAO,OAAK,KAAK,GAAG,QAAQ,CAAC;AAChF,QAAM,cAAc,CAAC,OAAO,aAAa,MAAM,UAAU,KAAK;AAE9D,QAAM,SAAS,CAAC,KAAKL,MAAK,UAAU;AAClC,QAAI,SAAS,KAAK,KAAK,UAAU,KAAK,KAAK,SAAS,KAAK,GAAG;AAC1D,UAAI,aAAaA,MAAK,QAAQ,EAAE;AAAA,IAClC,OAAO;AACL,cAAQ,MAAM,uCAAuCA,MAAK,aAAa,OAAO,eAAe,GAAG;AAChG,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,SAASA,MAAK,UAAU;AACrC,WAAO,QAAQ,KAAKA,MAAK,KAAK;AAAA,EAChC;AACA,QAAM,SAAS,CAAC,SAAS,UAAU;AACjC,UAAM,MAAM,QAAQ;AACpB,WAAO,OAAO,CAAC,GAAG,MAAM;AACtB,aAAO,KAAK,GAAG,CAAC;AAAA,IAClB,CAAC;AAAA,EACH;AACA,QAAM,QAAQ,CAAC,SAASA,SAAQ;AAC9B,UAAM,IAAI,QAAQ,IAAI,aAAaA,IAAG;AACtC,WAAO,MAAM,OAAO,SAAY;AAAA,EAClC;AACA,QAAM,SAAS,CAAC,SAASA,SAAQ,SAAS,KAAK,MAAM,SAASA,IAAG,CAAC;AAClE,QAAM,WAAW,CAAC,SAASA,SAAQ;AACjC,YAAQ,IAAI,gBAAgBA,IAAG;AAAA,EACjC;AACA,QAAM,QAAQ,aAAW,MAAM,QAAQ,IAAI,YAAY,CAAC,KAAK,SAAS;AACpE,QAAI,KAAK,IAAI,IAAI,KAAK;AACtB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,QAAM,KAAK,CAAC,KAAK,KAAK,aAAa,iBAAiB,IAAI,OAAO,UAAQ,WAAW,MAAM,GAAG,CAAC;AAC5F,QAAM,MAAM,SAAO;AACjB,UAAM,IAAI,CAAC;AACX,UAAM,OAAO,OAAK;AAChB,QAAE,KAAK,CAAC;AAAA,IACV;AACA,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAI,CAAC,EAAE,KAAK,IAAI;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI,SAAS,KAAK,EAAE,GAAG,SAAS,GAAG,GAAG,SAAS,CAAC,CAAC,IAAI,SAAS,KAAK;AACzH,QAAM,UAAU,SAAO,IAAI,KAAK,QAAQ;AACxC,QAAM,SAAS,CAAC,GAAG,MAAM,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAE9D,QAAM,kBAAkB,CAAC,KAAK,aAAa;AACzC,WAAO,IAAI,UAAU,QAAQ;AAAA,EAC/B;AAEA,QAAM,aAAa,CAAC,KAAK,QAAQ,UAAU,WAAW,MAAM,IAAI,UAAU,OAAO,UAAU,IAAI,OAAO,OAAO,QAAQ,OAAO,MAAM,MAAM;AACxI,QAAM,gBAAgB,CAAC,KAAK,WAAW;AACrC,WAAO,WAAW,KAAK,MAAM,IAAI,gBAAgB,KAAK,OAAO,MAAM,IAAI;AAAA,EACzE;AACA,QAAM,aAAa,CAAC,KAAK,WAAW;AAClC,WAAO,WAAW,KAAK,QAAQ,CAAC;AAAA,EAClC;AACA,QAAM,QAAQ,OAAK,OAAK,EAAE,QAAQ,GAAG,EAAE;AACvC,QAAM,OAAO,MAAM,YAAY;AAC/B,QAAM,aAAa,OAAK,EAAE,SAAS;AACnC,QAAM,UAAU,OAAK,CAAC,WAAW,CAAC;AAClC,QAAM,QAAQ,CAAC,OAAO,QAAQ,OAAO;AACnC,UAAM,MAAM,SAAS,OAAO,KAAK;AACjC,WAAO,MAAM,GAAG,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,GAAG;AAAA,EACzD;AACA,QAAM,UAAU,WAAS;AACvB,UAAM,MAAM,WAAW,KAAK;AAC5B,WAAO,MAAM,GAAG,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,GAAG;AAAA,EACzD;AAEA,QAAM,cAAc,SAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM,gBAAgB;AAE3F,QAAM,cAAc,CAAC,KAAK,UAAU,UAAU;AAC5C,QAAI,CAAC,SAAS,KAAK,GAAG;AACpB,cAAQ,MAAM,sCAAsC,UAAU,aAAa,OAAO,eAAe,GAAG;AACpG,YAAM,IAAI,MAAM,iCAAiC,KAAK;AAAA,IACxD;AACA,QAAI,YAAY,GAAG,GAAG;AACpB,UAAI,MAAM,YAAY,UAAU,KAAK;AAAA,IACvC;AAAA,EACF;AACA,QAAM,iBAAiB,CAAC,KAAK,aAAa;AACxC,QAAI,YAAY,GAAG,GAAG;AACpB,UAAI,MAAM,eAAe,QAAQ;AAAA,IACnC;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,SAAS,UAAU,UAAU;AAC1C,UAAM,MAAM,QAAQ;AACpB,gBAAY,KAAK,UAAU,KAAK;AAAA,EAClC;AACA,QAAM,QAAQ,CAAC,SAAS,aAAa;AACnC,UAAM,MAAM,QAAQ;AACpB,UAAM,SAAS,OAAO,iBAAiB,GAAG;AAC1C,UAAM,IAAI,OAAO,iBAAiB,QAAQ;AAC1C,WAAO,MAAM,MAAM,CAAC,OAAO,OAAO,IAAI,kBAAkB,KAAK,QAAQ,IAAI;AAAA,EAC3E;AACA,QAAM,oBAAoB,CAAC,KAAK,aAAa,YAAY,GAAG,IAAI,IAAI,MAAM,iBAAiB,QAAQ,IAAI;AACvG,QAAM,SAAS,CAAC,SAAS,aAAa;AACpC,UAAM,MAAM,QAAQ;AACpB,UAAM,MAAM,kBAAkB,KAAK,QAAQ;AAC3C,WAAO,SAAS,KAAK,GAAG,EAAE,OAAO,OAAK,EAAE,SAAS,CAAC;AAAA,EACpD;AACA,QAAM,WAAW,CAAC,SAAS,aAAa;AACtC,UAAM,MAAM,QAAQ;AACpB,mBAAe,KAAK,QAAQ;AAC5B,QAAI,GAAG,OAAO,SAAS,OAAO,EAAE,IAAI,IAAI,GAAG,EAAE,GAAG;AAC9C,eAAS,SAAS,OAAO;AAAA,IAC3B;AAAA,EACF;AAEA,QAAM,eAAe,CAACK,OAAMN,OAAM,WAAW,MAAM,OAAOM,OAAMN,KAAI,EAAE,IAAI,WAAS,SAAS,OAAO,EAAE,CAAC,EAAE,MAAM,QAAQ;AAEtH,QAAM,aAAa,CAAC,OAAO,aAAa;AACtC,WAAO,iBAAiB,OAAO,UAAU,MAAM;AAAA,EACjD;AACA,QAAM,mBAAmB,CAAC,OAAO,UAAU,cAAc;AACvD,WAAO,KAAK,WAAW,KAAK,GAAG,OAAK;AAClC,UAAI,KAAK,GAAG,QAAQ,GAAG;AACrB,eAAO,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAAA,MAC/B,OAAO;AACL,eAAO,iBAAiB,GAAG,UAAU,SAAS;AAAA,MAChD;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,mBAAmB;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,iBAAiB,gBAAc,SAAS,kBAAkB,UAAU;AAC1E,QAAM,OAAO,CAACO,OAAMC,cAAa;AAAA,IAC/B,MAAAD;AAAA,IACA,SAAAC;AAAA,EACF;AACA,QAAM,SAAS,CAAC,SAAS,SAAS,aAAa;AAAA,IAC7C;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW,CAAC,SAAS,SAAS,SAAS,KAAK,QAAQ,cAAc;AAAA,IACtE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAY,CAAC,SAASC,QAAO,aAAa;AAAA,IAC9C;AAAA,IACA,OAAAA;AAAA,IACA;AAAA,EACF;AACA,QAAM,SAAS,CAAC,UAAU,UAAU,WAAW,eAAe;AAAA,IAC5D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAY,CAAC,SAAS,SAAS,YAAY;AAAA,IAC/C;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW,CAAC,SAASD,cAAa;AAAA,IACtC;AAAA,IACA,SAAAA;AAAA,EACF;AAEA,QAAM,SAAS,CAAC,MAAM,SAAS,SAAS,UAAU;AAChD,QAAI,OAAO,OAAO,GAAG;AACnB,aAAO,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,SAAS,MAAM,KAAK,OAAO,CAAC,GAAG;AACjC,aAAO,SAAS,KAAK,OAAO;AAAA,IAC9B;AACA,UAAM,qBAAqB,SAAO,KAAK,KAAK,OAAO,KAAK,OAAO,GAAG;AAClE,WAAO,SAAS,SAAS,KAAK,KAAK,GAAG,GAAG,kBAAkB;AAAA,EAC7D;AACA,QAAM,OAAO,CAAC,SAAS,WAAW,OAAO;AAAA,IACvC;AAAA,IACA;AAAA,EACF,GAAG,SAAS,MAAM;AAClB,QAAM,QAAQ,CAAAL,cAAY,WAAWA,WAAU,OAAO;AACtD,QAAM,UAAU,CAAAA,cAAY;AAC1B,QAAI,KAAKA,WAAU,UAAU,GAAG;AAC9B,aAAO,WAAWA,WAAU,KAAK;AAAA,IACnC,OAAO;AACL,aAAO,KAAK,aAAaA,SAAQ,GAAG,iBAAe,WAAW,aAAa,KAAK,CAAC;AAAA,IACnF;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,SAAS,WAAW,UAAU,SAAS,SAAS,MAAM;AACrE,QAAM,OAAO,CAAAA,cAAY,WAAWA,WAAU,IAAI;AAClD,QAAM,eAAe,CAAAA,cAAY,MAAMA,SAAQ,EAAE,KAAK,SAAS,CAAC,CAAC,GAAG,CAAAO,WAAS,WAAWA,QAAO,UAAU,CAAC;AAE1G,QAAM,sBAAsB,CAAC,OAAO,eAAe,IAAI,OAAO,SAAO;AACnE,QAAI,KAAK,GAAG,MAAM,YAAY;AAC5B,YAAMD,SAAQ,IAAI,QAAQ,GAAG,GAAG,YAAU;AACxC,cAAM,UAAU,aAAa,QAAQ,QAAQ,CAAC;AAC9C,eAAO,OAAO,QAAQ,GAAG,OAAO;AAAA,MAClC,CAAC;AACD,aAAO,UAAU,KAAKA,QAAO,UAAU;AAAA,IACzC,OAAO;AACL,YAAM,UAAU,IAAI,MAAM,GAAG,GAAG,CAAAH,UAAQ;AACtC,cAAM,UAAU,aAAaA,OAAM,WAAW,CAAC;AAC/C,cAAM,UAAU,aAAaA,OAAM,WAAW,CAAC;AAC/C,eAAO,OAAOA,OAAM,SAAS,OAAO;AAAA,MACtC,CAAC;AACD,aAAO,UAAU,KAAK,SAAS,WAAW,GAAG,CAAC;AAAA,IAChD;AAAA,EACF,CAAC;AACD,QAAM,mBAAmB,WAAS,OAAO,KAAK,EAAE,IAAI,CAAAK,YAAU;AAC5D,UAAM,aAAa,KAAKA,OAAM;AAC9B,WAAO,eAAe,UAAU,IAAI,aAAa;AAAA,EACnD,CAAC,EAAE,MAAM,OAAO;AAChB,QAAM,cAAc,CAAAD,WAAS;AAC3B,UAAM,SAAS,KAAKA,MAAK;AACzB,UAAM,iBAAiB,aAAaA,MAAK;AACzC,UAAM,QAAQ;AAAA,MACZ,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,WAAO,oBAAoB,OAAO,gBAAgB;AAAA,EACpD;AAEA,QAAM,kBAAkB;AACxB,QAAM,4BAA4B,CAAAA,WAAS,OAAOA,QAAO,eAAe,EAAE,KAAK,kBAAgB,SAAS,KAAK,aAAa,MAAM,MAAM,CAAC,CAAC,EAAE,IAAI,gBAAc,YAAY,YAAY,MAAM,CAAC;AAE3L,QAAM,MAAM,CAAC,KAAK,WAAW;AAC3B,WAAO,MAAM,MAAM;AAAA,EACrB;AACA,QAAM,QAAQ,CAAC,WAAW,KAAK,WAAW,SAAS,KAAK,UAAU,OAAO,IAAI,KAAK,MAAM,CAAC,CAAC;AAC1F,QAAM,WAAW,CAAC,WAAW,MAAM,eAAe;AAChD,UAAM,WAAW,YAAY,WAAW,CAAAE,YAAU;AAChD,aAAO,WAAW,MAAMA,QAAO,OAAO;AAAA,IACxC,CAAC;AACD,WAAO,SAAS,SAAS,IAAI,SAAS,KAAK,SAAS,CAAC,CAAC,IAAI,SAAS,KAAK;AAAA,EAC1E;AACA,QAAM,cAAc,CAAC,WAAW,cAAc;AAC5C,UAAMb,OAAM,KAAK,UAAU,KAAK,OAAK;AACnC,aAAO,EAAE;AAAA,IACX,CAAC;AACD,WAAO,OAAOA,MAAK,SAAS;AAAA,EAC9B;AACA,QAAM,kBAAkB,aAAW;AACjC,UAAM,eAAe,CAAC;AACtB,QAAI,QAAQ;AACZ,SAAK,QAAQ,OAAO,YAAU;AAC5B,YAAM,UAAU,OAAO;AACvB,YAAM,SAAS,iBAAe;AAC5B,cAAM,WAAW,QAAQ;AACzB,qBAAa,QAAQ,IAAI,UAAU,OAAO,SAAS,SAAS,QAAQ;AAAA,MACtE,CAAC;AACD,eAAS;AAAA,IACX,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,aAAa,UAAQ;AACzB,UAAM,SAAS,CAAC;AAChB,UAAMU,SAAQ,CAAC;AACf,UAAM,WAAW,KAAK,IAAI,EAAE,IAAI,aAAW,QAAQ,OAAO,EAAE,KAAK,KAAK;AACtE,UAAM,gBAAgB,SAAS,KAAK,yBAAyB,EAAE,MAAM,CAAC,CAAC;AACvE,QAAI,UAAU;AACd,QAAI,aAAa;AACjB,QAAI,WAAW;AACf,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,MAAMF;AAAA,IACR,IAAI,UAAU,MAAM,aAAW,QAAQ,YAAY,UAAU;AAC7D,SAAKA,OAAM,aAAW;AACpB,YAAM,aAAa,CAAC;AACpB,WAAK,QAAQ,OAAO,aAAW;AAC7B,YAAI,QAAQ;AACZ,eAAO,OAAO,IAAI,UAAU,KAAK,CAAC,MAAM,QAAW;AACjD;AAAA,QACF;AACA,cAAM,WAAW,kBAAkB,eAAe,MAAM,SAAS,CAAC;AAClE,cAAM,UAAU,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,UAAU,OAAO,QAAQ;AACrG,iBAAS,yBAAyB,GAAG,yBAAyB,QAAQ,SAAS,0BAA0B;AACvG,mBAAS,sBAAsB,GAAG,sBAAsB,QAAQ,SAAS,uBAAuB;AAC9F,kBAAM,cAAc,WAAW;AAC/B,kBAAM,iBAAiB,QAAQ;AAC/B,kBAAM,SAAS,IAAI,aAAa,cAAc;AAC9C,mBAAO,MAAM,IAAI;AACjB,yBAAa,KAAK,IAAI,YAAY,iBAAiB,CAAC;AAAA,UACtD;AAAA,QACF;AACA,mBAAW,KAAK,OAAO;AAAA,MACzB,CAAC;AACD;AACA,MAAAE,OAAM,KAAK,UAAU,QAAQ,SAAS,YAAY,QAAQ,OAAO,CAAC;AAClE;AAAA,IACF,CAAC;AACD,UAAM,EAAC,SAAAD,UAAS,UAAS,IAAI,KAAK,YAAY,EAAE,IAAI,aAAW;AAC7D,YAAMA,WAAU,gBAAgB,OAAO;AACvC,YAAM,aAAa,SAAS,QAAQ,SAAS,OAAOA,QAAO,CAAC;AAC5D,aAAO;AAAA,QACL,WAAW,CAAC,UAAU;AAAA,QACtB,SAAAA;AAAA,MACF;AAAA,IACF,CAAC,EAAE,WAAW,OAAO;AAAA,MACnB,WAAW,CAAC;AAAA,MACZ,SAAS,CAAC;AAAA,IACZ,EAAE;AACF,UAAM,SAAS,KAAK,SAAS,UAAU;AACvC,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,KAAKC;AAAA,MACL,SAAAD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,CAAAE,WAAS;AACzB,UAAM,OAAO,YAAYA,MAAK;AAC9B,WAAO,WAAW,IAAI;AAAA,EACxB;AACA,QAAM,YAAY,eAAa,KAAK,UAAU,KAAK,OAAK,EAAE,KAAK;AAC/D,QAAM,cAAc,eAAa,OAAO,UAAU,OAAO;AACzD,QAAM,aAAa,eAAa,KAAK,UAAU,OAAO,EAAE,SAAS;AACjE,QAAM,cAAc,CAAC,WAAW,gBAAgB,SAAS,KAAK,UAAU,QAAQ,WAAW,CAAC;AAC5F,QAAM,YAAY;AAAA,IAChB;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE9D,QAAM,sBAAsB,CAAC,KAAK,KAAKV,UAAS;AAC9C,UAAMS,SAAQ,IAAI,OAAO,SAAS,GAAG;AACrC,QAAI;AACJ,aAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,YAAM,eAAe,IAAI,SAASA,OAAM,CAAC,GAAGT,KAAI;AAChD,UAAI,YAAY,eAAe,GAAG;AAChC,0BAAkB;AAAA,MACpB;AACA,UAAI,oBAAoB,cAAc;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,WAAW,CAAC,QAAQ,KAAKA,UAAS;AACtC,aAAS,KAAK,oBAAoB,MAAM,GAAG,GAAG,WAAS;AACrD,UAAI,UAAUA,OAAM;AAClB,eAAO,UAAU,OAAO,UAAU,OAAO,CAAC,GAAG,GAAG;AAAA,MAClD;AAAA,IACF,CAAC;AACD,QAAIA,OAAM;AACR,aAAO,UAAU,MAAM,UAAUA,OAAM,CAAC,GAAG,GAAG;AAAA,IAChD;AAAA,EACF;AACA,QAAM,YAAY,CAAC,QAAQ,KAAKA,UAAS;AACvC,aAAS,KAAK,oBAAoB,MAAM,GAAG,GAAG,WAAS;AACrD,UAAI,UAAUA,OAAM;AAClB,eAAO,UAAU,OAAO,WAAW,OAAO,CAAC,GAAG,GAAG;AAAA,MACnD;AAAA,IACF,CAAC;AACD,QAAIA,OAAM;AACR,aAAO,UAAU,MAAM,WAAWA,OAAM,CAAC,GAAG,GAAG;AAAA,IACjD;AAAA,EACF;AAEA,QAAM,oBAAoB,CAAC,QAAQU,QAAO,SAAS;AACjD,WAAO,SAAS,iBAAiB;AAAA,MAC/B,GAAG;AAAA,MACH,OAAAA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,CAAC,IAAI,aAAa,QAAQ,EAAE,EAAE,MAAM,QAAQ;AAC7D,QAAM,UAAU,CAAC,SAASV,OAAM,aAAa,SAAS,MAAM,SAASA,KAAI,GAAG,QAAQ;AACpF,QAAM,qBAAqB,CAAC,SAASK,OAAM,OAAO,UAAU;AAC1D,UAAM,eAAe,QAAQ,SAAS,WAAY,KAAM,IAAI,CAAC;AAC7D,UAAM,eAAe,QAAQ,SAAS,WAAY,KAAM,IAAI,CAAC;AAC7D,UAAM,cAAc,QAAQ,SAAS,UAAW,KAAM,UAAU,CAAC;AACjE,UAAM,cAAc,QAAQ,SAAS,UAAW,KAAM,UAAU,CAAC;AACjE,WAAOA,QAAO,eAAe,eAAe,cAAc;AAAA,EAC5D;AACA,QAAM,qBAAqB,CAAC,SAAS,cAAc;AACjD,UAAM,MAAM,QAAQ;AACpB,UAAM,QAAQ,IAAI,sBAAsB,EAAE,SAAS,IAAI;AACvD,WAAO,cAAc,eAAe,QAAQ,mBAAmB,SAAS,OAAO,QAAQ,OAAO;AAAA,EAChG;AACA,QAAM,gBAAgB,aAAW,mBAAmB,SAAS,aAAa;AAE1E,QAAM,WAAW;AAEjB,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAEvD,QAAM,sBAAsB;AAC5B,QAAM,0BAA0B,MAAM,GAAG,OAAK;AAC5C,UAAMA,QAAO,GAAI,IAAI,CAAE;AACvB,WAAO;AAAA,MACL,OAAOA;AAAA,MACP,OAAOA;AAAA,IACT;AAAA,EACF,CAAC;AACD,QAAM,0BAA0B,IAAI;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,CAAAP,UAAQ;AACT,WAAO;AAAA,MACL,OAAOA;AAAA,MACP,OAAOA,MAAK,YAAY;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,QAAM,eAAe;AACrB,QAAM,sBAAsB,YAAU;AACpC,QAAI;AACJ,UAAM,MAAM,OAAO;AACnB,UAAM,eAAe,KAAK,IAAI,UAAU,OAAO,UAAU,SAAS,GAAG,IAAI,OAAO,OAAO,QAAQ,OAAO,SAAS,KAAK,OAAO,QAAQ;AACnI,WAAO,SAAS,aAAa,QAAQ,WAAW,CAAC,IAAI;AAAA,EACvD;AACA,QAAM,yBAAyB,CAAC,QAAQ,kBAAkB;AACxD,QAAI,mBAAmB,MAAM,KAAK,CAAC,mBAAmB,MAAM,GAAG;AAC7D,aAAO;AAAA,IACT,WAAW,eAAe,MAAM,GAAG;AACjC,aAAO;AAAA,QACL,GAAG;AAAA,QACH,OAAO,oBAAoB,MAAM;AAAA,MACnC;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,GAAG;AAAA,QACH,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,6BAA6B,CAAC,QAAQ,sBAAsB;AAChE,QAAI,mBAAmB,MAAM,KAAK,mBAAmB,MAAM,GAAG;AAC5D,aAAO;AAAA,IACT,WAAW,eAAe,MAAM,GAAG;AACjC,aAAO;AAAA,QACL,GAAG;AAAA,QACH,OAAO,oBAAoB,MAAM;AAAA,MACnC;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,GAAG;AAAA,QACH,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,SAAS,CAAAE,UAAQ,YAAU,OAAO,QAAQ,IAAIA,KAAI;AACxD,QAAM,WAAW,YAAU;AACzB,UAAM,iBAAiB,OAAO,QAAQ;AACtC,mBAAe,uBAAuB;AAAA,MACpC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,uBAAuB;AAAA,MACpC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,qBAAqB;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,oBAAoB;AAAA,MACjC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,gBAAgB;AAAA,MAC7B,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,4BAA4B;AAAA,MACzC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,cAAc;AAAA,MAC3B,WAAW;AAAA,MACX,SAAS,CAAC,SAAS,WAAW,QAAQ;AAAA,IACxC,CAAC;AACD,mBAAe,yBAAyB;AAAA,MACtC,WAAW;AAAA,MACX,SAAS,CAAC;AAAA,IACZ,CAAC;AACD,mBAAe,wBAAwB;AAAA,MACrC,WAAW;AAAA,MACX,SAAS,CAAC;AAAA,IACZ,CAAC;AACD,mBAAe,oBAAoB;AAAA,MACjC,WAAW;AAAA,MACX,SAAS,CAAC;AAAA,IACZ,CAAC;AACD,mBAAe,iBAAiB;AAAA,MAC9B,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,8BAA8B;AAAA,MAC3C,WAAW;AAAA,MACX,SAAS,CAAC;AAAA,IACZ,CAAC;AACD,mBAAe,0BAA0B;AAAA,MACvC,WAAW;AAAA,MACX,SAAS,CAAC;AAAA,IACZ,CAAC;AAAA,EACH;AACA,QAAM,qBAAqB,OAAO,mBAAmB;AACrD,QAAM,uBAAuB,OAAO,qBAAqB;AACzD,QAAM,uBAAuB,OAAO,qBAAqB;AACzD,QAAM,qBAAqB,OAAO,mBAAmB;AACrD,QAAM,oBAAoB,OAAO,kBAAkB;AACnD,QAAM,sBAAsB,OAAO,cAAc;AACjD,QAAM,uBAAuB,OAAO,0BAA0B;AAC9D,QAAM,eAAe,OAAO,YAAY;AACxC,QAAM,qBAAqB,OAAO,oBAAoB;AACtD,QAAM,mBAAmB,OAAO,uBAAuB;AACvD,QAAM,kBAAkB,OAAO,sBAAsB;AACrD,QAAM,oBAAoB,OAAO,kBAAkB;AACnD,QAAM,aAAa,OAAO,eAAe;AACzC,QAAM,6BAA6B,OAAO,4BAA4B;AACtE,QAAM,yBAAyB,OAAO,wBAAwB;AAC9D,QAAM,iBAAiB,YAAU,mBAAmB,MAAM,MAAM;AAChE,QAAM,qBAAqB,YAAU,mBAAmB,MAAM,MAAM;AACpE,QAAM,mBAAmB,YAAU;AACjC,UAAM,UAAU,OAAO;AACvB,UAAM,gBAAgB,QAAQ,IAAI,sBAAsB;AACxD,WAAO,QAAQ,MAAM,sBAAsB,IAAI,gBAAgB,uBAAuB,QAAQ,aAAa;AAAA,EAC7G;AACA,QAAM,uBAAuB,YAAU;AACrC,UAAM,UAAU,OAAO;AACvB,UAAM,oBAAoB,QAAQ,IAAI,0BAA0B;AAChE,WAAO,QAAQ,MAAM,0BAA0B,IAAI,oBAAoB,2BAA2B,QAAQ,iBAAiB;AAAA,EAC7H;AAEA,QAAM,WAAW,CAACa,SAAQD,YAAW;AACnC,WAAOA,QAAO,UAAUC,QAAO,YAAYD,QAAO,SAASA,QAAO,UAAU,KAAKC,QAAO,aAAaD,QAAO,OAAOC,QAAO,YAAYD,QAAO,MAAMA,QAAO,UAAU,KAAKC,QAAO;AAAA,EAClL;AACA,QAAM,gBAAgB,CAAC,WAAWA,YAAW;AAC3C,QAAI,SAAS;AACb,UAAM,iBAAiB,MAAM,UAAUA,OAAM;AAC7C,aAAS,IAAIA,QAAO,UAAU,KAAKA,QAAO,WAAW,KAAK;AACxD,eAAS,IAAIA,QAAO,UAAU,KAAKA,QAAO,WAAW,KAAK;AACxD,iBAAS,UAAU,UAAU,MAAM,WAAW,GAAG,CAAC,EAAE,OAAO,cAAc;AAAA,MAC3E;AAAA,IACF;AACA,WAAO,SAAS,SAAS,KAAKA,OAAM,IAAI,SAAS,KAAK;AAAA,EACxD;AAEA,QAAM,YAAY,CAAC,SAAS,YAAY;AACtC,WAAO,OAAO,KAAK,IAAI,QAAQ,KAAK,QAAQ,GAAG,GAAG,KAAK,IAAI,QAAQ,QAAQ,QAAQ,MAAM,GAAG,KAAK,IAAI,QAAQ,MAAM,QAAQ,UAAU,GAAG,QAAQ,MAAM,QAAQ,UAAU,CAAC,GAAG,KAAK,IAAI,QAAQ,SAAS,QAAQ,UAAU,GAAG,QAAQ,SAAS,QAAQ,UAAU,CAAC,CAAC;AAAA,EAClQ;AACA,QAAM,YAAY,CAAC,WAAW,WAAW,eAAe;AACtD,UAAM,cAAc,UAAU,SAAS,WAAW,WAAW,EAAE;AAC/D,UAAM,eAAe,UAAU,SAAS,WAAW,YAAY,EAAE;AACjE,WAAO,YAAY,KAAK,QAAM;AAC5B,aAAO,aAAa,IAAI,QAAM;AAC5B,eAAO,UAAU,IAAI,EAAE;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,WAAW,CAAC,WAAW,WAAW,eAAe;AACrD,WAAO,UAAU,WAAW,WAAW,UAAU,EAAE,KAAK,CAAAA,YAAU;AAChE,aAAO,cAAc,WAAWA,OAAM;AAAA,IACxC,CAAC;AAAA,EACH;AAEA,QAAM,SAAS,CAACH,QAAO,OAAOI,UAAS;AACrC,UAAM,YAAY,aAAaJ,MAAK;AACpC,WAAO,SAAS,WAAW,OAAOI,KAAI;AAAA,EACxC;AACA,QAAM,eAAe,UAAU;AAE/B,QAAM,SAAS,CAAC,QAAQ,YAAY;AAClC,UAAM,WAAW,OAAO,MAAM;AAC9B,aAAS,KAAK,OAAK;AACjB,QAAE,IAAI,aAAa,QAAQ,KAAK,OAAO,GAAG;AAAA,IAC5C,CAAC;AAAA,EACH;AACA,QAAM,UAAU,CAAC,QAAQ,YAAY;AACnC,UAAM,UAAU,YAAY,MAAM;AAClC,YAAQ,KAAK,MAAM;AACjB,YAAM,WAAW,OAAO,MAAM;AAC9B,eAAS,KAAK,OAAK;AACjB,iBAAS,GAAG,OAAO;AAAA,MACrB,CAAC;AAAA,IACH,GAAG,OAAK;AACN,aAAO,GAAG,OAAO;AAAA,IACnB,CAAC;AAAA,EACH;AACA,QAAM,UAAU,CAACH,SAAQ,YAAY;AACnC,UAAM,eAAe,WAAWA,OAAM;AACtC,iBAAa,KAAK,MAAM;AACtB,eAASA,SAAQ,OAAO;AAAA,IAC1B,GAAG,OAAK;AACN,MAAAA,QAAO,IAAI,aAAa,QAAQ,KAAK,EAAE,GAAG;AAAA,IAC5C,CAAC;AAAA,EACH;AACA,QAAM,WAAW,CAACA,SAAQ,YAAY;AACpC,IAAAA,QAAO,IAAI,YAAY,QAAQ,GAAG;AAAA,EACpC;AACA,QAAM,OAAO,CAAC,SAAS,YAAY;AACjC,WAAO,SAAS,OAAO;AACvB,aAAS,SAAS,OAAO;AAAA,EAC3B;AAEA,QAAM,QAAQ,CAAC,QAAQ,aAAa;AAClC,SAAK,UAAU,CAAC,GAAG,MAAM;AACvB,YAAM,IAAI,MAAM,IAAI,SAAS,SAAS,IAAI,CAAC;AAC3C,cAAQ,GAAG,CAAC;AAAA,IACd,CAAC;AAAA,EACH;AACA,QAAM,SAAS,CAACA,SAAQ,aAAa;AACnC,SAAK,UAAU,OAAK;AAClB,eAASA,SAAQ,CAAC;AAAA,IACpB,CAAC;AAAA,EACH;AAEA,QAAM,SAAS,aAAW;AACxB,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI,eAAe,MAAM;AAC3B,UAAI,WAAW,YAAY,GAAG;AAAA,IAChC;AAAA,EACF;AACA,QAAM,SAAS,aAAW;AACxB,UAAMI,YAAW,WAAW,OAAO;AACnC,QAAIA,UAAS,SAAS,GAAG;AACvB,YAAM,SAASA,SAAQ;AAAA,IACzB;AACA,WAAO,OAAO;AAAA,EAChB;AAEA,QAAM,YAAY,CAACb,KAAIF,UAAS;AAC9B,UAAMgB,OAAM,aAAW;AACrB,UAAI,CAACd,IAAG,OAAO,GAAG;AAChB,cAAM,IAAI,MAAM,kBAAkBF,QAAO,iBAAiBA,QAAO,OAAO;AAAA,MAC1E;AACA,aAAO,UAAU,OAAO,EAAE,MAAM,EAAE;AAAA,IACpC;AACA,UAAM,YAAY,aAAWE,IAAG,OAAO,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,IAAI,SAAS,KAAK;AAChG,UAAMe,OAAM,CAAC,SAAS,UAAU;AAC9B,UAAI,CAACf,IAAG,OAAO,GAAG;AAChB,cAAM,IAAI,MAAM,sBAAsBF,QAAO,iBAAiBA,QAAO,OAAO;AAAA,MAC9E;AACA,cAAQ,IAAI,YAAY;AAAA,IAC1B;AACA,WAAO;AAAA,MACL,KAAAgB;AAAA,MACA;AAAA,MACA,KAAAC;AAAA,IACF;AAAA,EACF;AAEA,QAAM,MAAM,UAAU,QAAQ,MAAM;AACpC,QAAM,MAAM,aAAW,IAAI,IAAI,OAAO;AACtC,QAAM,MAAM,CAAC,SAAS,UAAU,IAAI,IAAI,SAAS,KAAK;AAEtD,MAAI,gBAAgB;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,MAAI,cAAc,MAAM;AACtB,UAAM,UAAU,aAAW;AACzB,aAAO,aAAa,QAAQ,QAAQ,IAAI,UAAU,KAAK,CAAC;AAAA,IAC1D;AACA,UAAMC,YAAW,aAAW,gBAAgB,OAAO,EAAE;AACrD,UAAM,aAAa,aAAW;AAC5B,UAAI,CAAC,UAAU,OAAO,GAAG;AACvB,eAAO;AAAA,MACT;AACA,UAAI,KAAK,OAAO,MAAM,QAAQ;AAC5B,eAAO;AAAA,MACT;AACA,aAAO,SAAS,eAAe,KAAK,OAAO,CAAC;AAAA,IAC9C;AACA,UAAM,aAAa,aAAW;AAC5B,UAAI,CAAC,UAAU,OAAO,GAAG;AACvB,eAAO;AAAA,MACT;AACA,aAAO,SAAS;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,KAAK,OAAO,CAAC;AAAA,IAClB;AACA,UAAM,gBAAgB,aAAW,UAAU,OAAO,KAAK,MAAM,SAAS,iBAAiB,MAAM;AAC7F,UAAM,kBAAkB,CAAC,SAAS,UAAU;AAC1C,aAAO,QAAQ,IAAI,wBAAwB,MAAM,GAAG;AAAA,IACtD;AACA,UAAM,mBAAmB,CAAC,QAAQ,gBAAgB;AAChD,YAAM,KAAK,MAAM,MAAM;AACvB,aAAO,aAAa,EAAE;AAAA,IACxB;AACA,UAAM,YAAY,aAAW;AAC3B,YAAM,MAAM,KAAK,OAAO;AACxB,aAAO,SAAS;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AACA,UAAM,cAAc,aAAW,UAAU,OAAO,IAAI,OAAO,SAAS,MAAM,IAAI,SAAS,KAAK;AAC5F,WAAO;AAAA,MACL,IAAI,SAAS;AAAA,QACX,UAAU;AAAA,QACV,SAAS;AAAA,QACT,WAAW;AAAA,QACX,KAAK;AAAA,MACP,CAAC;AAAA,MACD,MAAM,SAAS;AAAA,QACb,UAAU;AAAA,QACV,WAAW;AAAA,MACb,CAAC;AAAA,MACD,QAAQ,SAAS;AAAA,QACf,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,QACL,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,OAAO,SAAS;AAAA,QACd,KAAK;AAAA,QACL,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,QAAQ,SAAS;AAAA,QACf;AAAA,QACA,OAAO;AAAA,QACP,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,WAAW;AAAA,QACX;AAAA,QACA;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,SAAS;AAAA,QACf;AAAA,QACA;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,SAAS;AAAA,QACf,IAAI,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,MAAM,aAAa;AAAA,MACrB,CAAC;AAAA,MACD,OAAO,SAAS;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,MACD,UAAU,SAAS;AAAA,QACjB,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,UAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,MACD;AAAA,MACA,IAAI;AAAA,IACN;AAAA,EACF;AAEA,QAAM,MAAM,CAACC,WAAU,MAAM,UAAU,MAAM;AAC3C,UAAMC,QAAO,SAAS,CAAC;AACvB,UAAM,OAAO,SAAS,MAAM,CAAC;AAC7B,WAAO,EAAED,WAAU,MAAMC,OAAM,IAAI;AAAA,EACrC;AACA,QAAM,SAAS,CAACD,WAAU,MAAM,aAAa;AAC3C,WAAO,SAAS,SAAS,IAAI,IAAIA,WAAU,MAAM,UAAU,SAAS,IAAI,SAAS,KAAK;AAAA,EACxF;AACA,QAAM,YAAY,CAACA,WAAU,MAAMC,OAAM,SAAS;AAChD,UAAM,QAAQ,KAAKD,WAAUC,KAAI;AACjC,WAAO,MAAM,MAAM,CAAC,GAAG,MAAM;AAC3B,YAAM,UAAU,KAAKD,WAAU,CAAC;AAChC,aAAO,cAAcA,WAAU,GAAG,OAAO;AAAA,IAC3C,GAAG,KAAK;AAAA,EACV;AACA,QAAM,gBAAgB,CAACA,WAAU,OAAO,QAAQ;AAC9C,WAAO,MAAM,KAAK,OAAK;AACrB,aAAO,IAAI,OAAO,MAAMA,UAAS,IAAI,CAAC,CAAC;AAAA,IACzC,CAAC;AAAA,EACH;AAEA,QAAM,cAAc;AAEpB,QAAM,WAAW,YAAY;AAC7B,QAAM,YAAY,CAAC,MAAM,aAAa;AACpC,WAAO,YAAY,UAAU,CAAC,WAAW,YAAY;AACnD,aAAO,KAAK,OAAO;AAAA,IACrB,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,cAAc,eAAa;AAC/B,WAAO,SAAS,WAAW,OAAO;AAAA,EACpC;AACA,QAAM,aAAa,CAAC,WAAW,aAAa;AAC1C,UAAM,OAAO,YAAY,WAAW,QAAQ;AAC5C,WAAO,KAAK,SAAS,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK;AAAA,EAC/D;AACA,QAAM,WAAW,CAAC,WAAW,uBAAuB,yBAAyB;AAC3E,WAAO,WAAW,WAAW,qBAAqB,EAAE,KAAK,WAAS;AAChE,aAAO,WAAW,WAAW,oBAAoB,EAAE,KAAK,CAAAL,UAAQ;AAC9D,eAAO,UAAU,aAAa;AAAA,UAC5B;AAAA,UACAA;AAAA,QACF,CAAC,EAAE,IAAI,CAAAJ,WAAS;AACd,iBAAO;AAAA,YACL;AAAA,YACA,MAAAI;AAAA,YACA,OAAAJ;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,CAAC,WAAW,aAAa;AACxC,WAAO,WAAW,WAAW,QAAQ;AAAA,EACvC;AACA,QAAM,cAAc,CAAC,WAAW,uBAAuB,yBAAyB;AAC9E,WAAO,SAAS,WAAW,uBAAuB,oBAAoB,EAAE,KAAK,WAAS;AACpF,YAAM,SAAS,CAAAP,cAAY;AACzB,eAAO,GAAG,WAAWA,SAAQ;AAAA,MAC/B;AACA,YAAM,kBAAkB;AACxB,YAAM,gBAAgB,SAAS,MAAM,OAAO,iBAAiB,MAAM;AACnE,YAAM,eAAe,SAAS,MAAM,MAAM,iBAAiB,MAAM;AACjE,aAAO,cAAc,KAAK,QAAM;AAC9B,eAAO,aAAa,KAAK,QAAM;AAC7B,iBAAO,GAAG,IAAI,EAAE,IAAI,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,IAAI,IAAI,SAAS,KAAK;AAAA,QACnF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,QAAM,UAAU,WAAS,IAAI,OAAO,aAAa,OAAO;AAExD,QAAM,cAAc;AACpB,QAAM,sBAAsB,QAAQ,cAAc,UAAU,cAAc;AAC1E,QAAM,mBAAmB;AACzB,QAAM,2BAA2B,QAAQ,mBAAmB,UAAU,mBAAmB;AACzF,QAAM,kBAAkB;AACxB,QAAM,0BAA0B,QAAQ,kBAAkB,UAAU,kBAAkB;AACtF,QAAM,WAAW;AAAA,IACf,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,cAAc;AAAA,IACd,sBAAsB;AAAA,EACxB;AAEA,QAAM,2BAA2B,aAAW,MAAM,OAAO,EAAE,KAAK,CAAAO,WAAS,SAASA,QAAO,SAAS,qBAAqB,CAAC,EAAE,KAAK,SAAS,OAAO,GAAG,CAAAD,WAASA,OAAM,CAAC,CAAC;AACnK,QAAM,2BAA2B,cAAY,CAAC,UAAU,WAAW;AACjE,UAAM,WAAW,KAAK,QAAQ;AAC9B,UAAMH,QAAO,aAAa,SAAS,aAAa,aAAa,yBAAyB,QAAQ,IAAI;AAClG,WAAO,UAAUA,OAAM,UAAU,MAAM;AAAA,EACzC;AACA,QAAM,4BAA4B,yBAAyB,eAAe;AAC1E,QAAM,mBAAmB,yBAAyB,OAAO;AACzD,QAAM,wBAAwB,YAAU,QAAQ,OAAO,MAAM,MAAM,iBAAiB,CAAC;AACrF,QAAM,uBAAuB,CAAC,UAAU,aAAa;AACnD,UAAM,UAAU,iBAAiB,QAAQ;AACzC,UAAM,UAAU,QAAQ,KAAK,CAAAA,UAAQ,MAAMA,KAAI,CAAC,EAAE,IAAI,CAAAI,WAAS,KAAKA,MAAK,CAAC;AAC1E,WAAO,MAAM,SAAS,SAAS,CAACJ,OAAMC,UAAS,OAAOA,OAAM,SAAO,OAAO,QAAQ,IAAI,IAAI,KAAK,GAAG,aAAW,MAAM,SAAS,QAAQ,MAAM,OAAO,GAAG,SAASD,KAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAAA,EAChL;AAEA,QAAM,sBAAsB;AAAA,IAC1B;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,YAAY,YAAU,EAAE,OAAO,aAAa,KAAK,EAAE;AACzD,QAAM,iBAAiB;AACvB,QAAM,gBAAgB;AACtB,QAAM,cAAc,SAAO,eAAe,KAAK,GAAG,KAAK,cAAc,KAAK,GAAG;AAC7E,QAAM,eAAe,SAAO,cAAc,KAAK,GAAG,EAAE,YAAY;AAChE,QAAM,eAAe,SAAO,YAAY,GAAG,IAAI,SAAS,KAAK,EAAE,OAAO,aAAa,GAAG,EAAE,CAAC,IAAI,SAAS,KAAK;AAC3G,QAAM,QAAQ,eAAa;AACzB,UAAM,MAAM,UAAU,SAAS,EAAE;AACjC,YAAQ,IAAI,WAAW,IAAI,MAAM,MAAM,KAAK,YAAY;AAAA,EAC1D;AACA,QAAM,WAAW,CAAAe,gBAAc;AAC7B,UAAM,QAAQ,MAAMA,YAAW,GAAG,IAAI,MAAMA,YAAW,KAAK,IAAI,MAAMA,YAAW,IAAI;AACrF,WAAO,UAAU,KAAK;AAAA,EACxB;AAEA,QAAM,WAAW;AACjB,QAAM,YAAY;AAClB,QAAM,aAAa,CAAC,KAAK,OAAO,MAAM,WAAW;AAAA,IAC/C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,mBAAmB,CAAC,KAAK,OAAO,MAAM,UAAU;AACpD,UAAM,IAAI,SAAS,KAAK,EAAE;AAC1B,UAAM,IAAI,SAAS,OAAO,EAAE;AAC5B,UAAM,IAAI,SAAS,MAAM,EAAE;AAC3B,UAAM,IAAI,WAAW,KAAK;AAC1B,WAAO,WAAW,GAAG,GAAG,GAAG,CAAC;AAAA,EAC9B;AACA,QAAM,aAAa,gBAAc;AAC/B,QAAI,eAAe,eAAe;AAChC,aAAO,SAAS,KAAK,WAAW,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,IAC7C;AACA,UAAM,WAAW,SAAS,KAAK,UAAU;AACzC,QAAI,aAAa,MAAM;AACrB,aAAO,SAAS,KAAK,iBAAiB,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC;AAAA,IACnF;AACA,UAAM,YAAY,UAAU,KAAK,UAAU;AAC3C,QAAI,cAAc,MAAM;AACtB,aAAO,SAAS,KAAK,iBAAiB,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;AAAA,IAC/F;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AAEA,QAAM,WAAW,WAAS,aAAa,KAAK,EAAE,QAAQ,MAAM,WAAW,KAAK,EAAE,IAAI,QAAQ,CAAC,EAAE,WAAW,MAAM;AAC5G,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,SAAS;AAChB,WAAO,QAAQ;AACf,UAAM,gBAAgB,OAAO,WAAW,IAAI;AAC5C,kBAAc,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AACzD,kBAAc,YAAY;AAC1B,kBAAc,YAAY;AAC1B,kBAAc,SAAS,GAAG,GAAG,GAAG,CAAC;AACjC,UAAM,OAAO,cAAc,aAAa,GAAG,GAAG,GAAG,CAAC,EAAE;AACpD,UAAM,IAAI,KAAK,CAAC;AAChB,UAAM,IAAI,KAAK,CAAC;AAChB,UAAM,IAAI,KAAK,CAAC;AAChB,UAAM,IAAI,KAAK,CAAC;AAChB,WAAO,SAAS,WAAW,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,EACxC,CAAC;AACD,QAAM,kBAAkB,WAAS,WAAW,KAAK,EAAE,IAAI,QAAQ,EAAE,IAAI,OAAK,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK;AAEpG,QAAM,OAAO,aAAW;AACtB,QAAI,QAAQ;AACZ,UAAML,OAAM,MAAM;AAChB,aAAO;AAAA,IACT;AACA,UAAMC,OAAM,OAAK;AACf,cAAQ;AAAA,IACV;AACA,WAAO;AAAA,MACL,KAAAD;AAAA,MACA,KAAAC;AAAA,IACF;AAAA,EACF;AAEA,QAAM,YAAY,cAAY;AAC5B,UAAM,UAAU,KAAK,SAAS,KAAK,CAAC;AACpC,UAAM,SAAS,MAAM,QAAQ,IAAI,EAAE,KAAK,QAAQ;AAChD,UAAM,QAAQ,MAAM;AAClB,aAAO;AACP,cAAQ,IAAI,SAAS,KAAK,CAAC;AAAA,IAC7B;AACA,UAAM,QAAQ,MAAM,QAAQ,IAAI,EAAE,OAAO;AACzC,UAAMD,OAAM,MAAM,QAAQ,IAAI;AAC9B,UAAMC,OAAM,OAAK;AACf,aAAO;AACP,cAAQ,IAAI,SAAS,KAAK,CAAC,CAAC;AAAA,IAC9B;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAAD;AAAA,MACA,KAAAC;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,MAAM,UAAU,OAAK,EAAE,OAAO,CAAC;AAElD,QAAM,gBAAgB,CAAC,QAAQ,YAAY,gBAAgB;AACzD,WAAO,CAAAK,SAAO;AACZ,YAAM,gBAAgB,WAAW;AACjC,YAAM,SAAS,QAAQ,WAAW;AAClC,YAAM,OAAO,MAAM;AACjB,cAAM,gBAAgB,sBAAsB,MAAM;AAClD,cAAM,YAAY,CAAAhB,UAAQ,OAAO,UAAU,MAAM,YAAY,EAAE,OAAO,YAAY,GAAGA,MAAK,KAAK,MAAM;AACrG,YAAI,QAAQ;AACV,UAAAgB,KAAI,UAAU,CAAC,OAAO,eAAe,SAAS,CAAC;AAC/C,wBAAc,IAAI,OAAO,UAAU,cAAc,YAAY,WAASA,KAAI,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC;AAAA,QACpG,OAAO;AACL,UAAAA,KAAI,UAAU,OAAO,eAAe,SAAS,CAAC;AAC9C,wBAAc,IAAI,OAAO,UAAU,cAAc,YAAYA,KAAI,WAAW,OAAO,EAAE,OAAO,YAAY,CAAC,CAAC;AAAA,QAC5G;AAAA,MACF;AACA,aAAO,cAAc,KAAK,IAAI,OAAO,GAAG,QAAQ,IAAI;AACpD,aAAO,cAAc;AAAA,IACvB;AAAA,EACF;AACA,QAAM,cAAc,UAAQ,kBAAkB,MAAM,MAAM;AAC1D,QAAM,iBAAiB,WAAS,IAAI,OAAO,UAAQ;AACjD,UAAM,OAAO,KAAK,QAAQ,KAAK,SAAS;AACxC,QAAI,YAAY,IAAI,GAAG;AACrB,aAAO;AAAA,QACL;AAAA,QACA,OAAO,eAAe,KAAK,IAAI;AAAA,MACjC;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL;AAAA,QACA,OAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,iBAAiB,eAAa;AAClC,QAAI,CAAC,UAAU,QAAQ;AACrB,aAAO,SAAS,KAAK;AAAA,IACvB;AACA,WAAO,SAAS,KAAK,eAAe;AAAA,MAClC;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,GAAG;AAAA,IACL,CAAC,CAAC;AAAA,EACJ;AACA,QAAM,iBAAiB,CAAC,QAAQ,OAAO,QAAQ,aAAa,IAAI,OAAO,UAAQ;AAC7E,UAAM,OAAO,KAAK,QAAQ,KAAK;AAC/B,QAAI,YAAY,IAAI,GAAG;AACrB,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,iBAAiB,MAAM,eAAe,QAAQ,KAAK,MAAM,QAAQ,QAAQ;AAAA,MAC3E;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL;AAAA,QACA,MAAM;AAAA,QACN,UAAU,MAAM,SAAS,KAAK,KAAK;AAAA,QACnC,SAAS,cAAc,QAAQ,QAAQ,KAAK,KAAK;AAAA,MACnD;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,sBAAsB,CAAC,QAAQ,UAAU,WAAS;AACtD,WAAO,YAAY,0BAA0B,OAAO,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EACxE;AACA,QAAM,iBAAiB,UAAQ,KAAK,MAAM,UAAQ;AAChD,QAAI,YAAY,IAAI,GAAG;AACrB,aAAO,CAAC;AAAA,QACJ,GAAG;AAAA,QACH,MAAM,eAAe,KAAK,IAAI;AAAA,MAChC,CAAC;AAAA,IACL,OAAO;AACL,aAAO,WAAW,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAC5C;AAAA,EACF,CAAC;AACD,QAAM,4BAA4B,CAAC,QAAQ,OAAO,QAAQ,aAAa,cAAY,SAAS,eAAe,QAAQ,OAAO,QAAQ,QAAQ,CAAC;AAC3I,QAAM,iBAAiB,CAAC,QAAQ,WAAW,UAAU;AACnD,UAAM,WAAW,IAAI,WAAW,YAAU;AAAA,MACxC,MAAM,MAAM;AAAA,MACZ,OAAO,MAAM,SAAS,MAAM,KAAK,EAAE;AAAA,MACnC,MAAM;AAAA,IACR,EAAE;AACF,WAAO,CAAC;AAAA,MACJ,MAAM;AAAA,MACN,WAAW;AAAA,MACX,UAAU;AAAA,QACR,QAAQ,SAAS,SAAS,IAAI,WAAW;AAAA,QACzC,mBAAmB;AAAA,MACrB;AAAA,MACA,UAAU,UAAQ;AAChB,cAAM,QAAQ,KAAK,UAAU,WAAW,KAAK,KAAK;AAClD,eAAO,YAAY,0BAA0B,OAAO,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,MACxE;AAAA,IACF,CAAC;AAAA,EACL;AACA,QAAM,kBAAkB,YAAU,MAAM;AACtC,UAAM,cAAc,OAAO,kBAAkB,iBAAiB;AAC9D,UAAM,UAAU,gBAAgB,WAAW,SAAS;AACpD,WAAO,YAAY,mBAAmB,OAAO,EAAE,MAAM,QAAQ,CAAC;AAAA,EAChE;AACA,QAAM,qBAAqB,YAAU,MAAM;AACzC,UAAM,cAAc,OAAO,kBAAkB,iBAAiB;AAC9D,UAAM,UAAU,gBAAgB,OAAO,OAAO;AAC9C,WAAO,YAAY,mBAAmB,OAAO,EAAE,MAAM,QAAQ,CAAC;AAAA,EAChE;AAEA,QAAM,iBAAiB,YAAU,eAAe,iBAAiB,MAAM,CAAC,EAAE,IAAI,YAAU;AAAA,IACtF,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP;AAAA,EACF,EAAE;AACF,QAAM,WAAW;AAAA,IACf;AAAA,MACE,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,aAAa,YAAU,SAAS,OAAO,eAAe,MAAM,EAAE,QAAQ,CAAC;AAE7E,QAAM,iBAAiB,CAAC,QAAQ,eAAe;AAC7C,UAAM,mBAAmB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC;AACH,UAAM,cAAc;AAAA,MAClB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO,iBAAiB,OAAO,eAAe,qBAAqB,MAAM,CAAC,CAAC;AAAA,MAC7E;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,cAAc;AAAA,MAClB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AACA,UAAM,QAAQ,eAAe,SAAS,CAAC,WAAW,EAAE,OAAO,WAAW,IAAI;AAC1E,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAEA,QAAM,SAAS,CAAC,QAAQ,YAAY;AAClC,UAAM,MAAM,OAAO;AACnB,UAAM,YAAY,CAAC,MAAM,UAAU;AACjC,UAAI,UAAU,SAAS,MAAM,KAAK;AAAA,IACpC;AACA,UAAM,WAAW,CAAC,MAAM,UAAU;AAChC,UAAI,SAAS,SAAS,MAAM,KAAK;AAAA,IACnC;AACA,UAAM,YAAY,CAAC,YAAY,UAAU;AACvC,UAAI,UAAU,IAAI;AAChB,eAAO,UAAU,OAAO,YAAY,EAAE,OAAO,KAAK,GAAG,SAAS,IAAI;AAAA,MACpE,OAAO;AACL,eAAO,UAAU,MAAM,YAAY,EAAE,MAAM,GAAG,OAAO;AAAA,MACvD;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,cAAc,EAAE,OAAO;AAE7B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,mBAAmB,CAAC,aAAa,kBAAkB;AACvD,QAAI,eAAe,eAAe;AAChC,aAAO;AAAA,IACT,WAAW,aAAa;AACtB,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,eAAe,SAAO;AAC1B,UAAM,cAAc,IAAI,YAAY;AACpC,UAAM,gBAAgB,GAAG,mBAAmB,IAAI,KAAK,GAAG,IAAI;AAC5D,QAAI,IAAI,YAAY,SAAS;AAC3B,aAAO,EAAE,MAAM,SAAS;AAAA,IAC1B,WAAW,eAAe,eAAe;AACvC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,iBAAiB,aAAa,aAAa;AAAA,MACtD;AAAA,IACF,OAAO;AACL,aAAO,EAAE,MAAM,OAAO;AAAA,IACxB;AAAA,EACF;AACA,QAAM,qBAAqB,CAAAb,WAAS;AAClC,UAAM,cAAc,OAAOA,QAAO,CAAAH,UAAQ,aAAaA,MAAK,OAAO,CAAC;AACpE,QAAI,YAAY,WAAW,GAAG;AAC5B,aAAO,SAAS,KAAK,IAAI;AAAA,IAC3B,WAAW,YAAY,WAAWG,OAAM,QAAQ;AAC9C,aAAO,SAAS,KAAK,IAAI;AAAA,IAC3B,OAAO;AACL,aAAO,SAAS,KAAK;AAAA,IACvB;AAAA,EACF;AACA,QAAM,oBAAoB,CAAAF,UAAQ;AAChC,UAAM,WAAW,IAAIA,OAAM,SAAO,aAAa,GAAG,EAAE,IAAI;AACxD,UAAM,YAAY,SAAS,UAAU,QAAQ;AAC7C,UAAM,YAAY,SAAS,UAAU,QAAQ;AAC7C,QAAI,CAAC,aAAa,CAAC,WAAW;AAC5B,aAAO,SAAS,KAAK,MAAM;AAAA,IAC7B,OAAO;AACL,YAAM,UAAU,SAAS,UAAU,MAAM;AACzC,UAAI,aAAa,CAAC,WAAW,CAAC,WAAW;AACvC,eAAO,SAAS,KAAK,QAAQ;AAAA,MAC/B,WAAW,CAAC,aAAa,CAAC,WAAW,WAAW;AAC9C,eAAO,SAAS,KAAK,QAAQ;AAAA,MAC/B,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAEA,QAAM,SAAS,OAAK;AAClB,QAAI,SAAS;AACb,QAAI;AACJ,WAAO,IAAI,SAAS;AAClB,UAAI,CAAC,QAAQ;AACX,iBAAS;AACT,YAAI,EAAE,MAAM,MAAM,IAAI;AAAA,MACxB;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,kBAAkB,CAAC,WAAW,YAAY,QAAQ,UAAU,KAAK,OAAK,KAAK,EAAE,OAAO,OAAK,GAAG,SAAS,EAAE,OAAO,CAAC,CAAC;AACtH,QAAM,eAAe,CAAC,WAAW,QAAQ,cAAc;AACrD,UAAM,UAAU,IAAI,OAAO,WAAW,YAAU;AAC9C,aAAO,KAAK,MAAM,EAAE,KAAK,QAAM,gBAAgB,WAAW,EAAE,CAAC,EAAE,OAAO,SAAS;AAAA,IACjF,CAAC;AACD,UAAME,SAAQ,IAAI,OAAO;AACzB,WAAO,OAAOA,OAAM,SAAS,GAAGA,MAAK;AAAA,EACvC;AACA,QAAM,aAAa,CAAC,YAAY,WAAW,OAAO;AAClD,QAAM,eAAe,CAAC,YAAY,WAAW,OAAO;AACpD,QAAM,UAAU,CAAC,WAAW,WAAW,aAAa,WAAW,QAAQ,MAAM;AAC7E,QAAM,sBAAsB,CAAC,WAAWH,UAAS,gBAAgB,WAAWA,KAAI,EAAE,OAAO,CAAAM,YAAU,CAACA,QAAO,QAAQ;AACnH,QAAM,cAAc,CAAC,WAAWH,WAAU,OAAOA,QAAO,CAAAH,UAAQ,oBAAoB,WAAWA,KAAI,CAAC;AACpG,QAAM,qBAAqB,CAAC,WAAW,WAAW,WAAW,WAAW,MAAM,EAAE,OAAO,eAAa,YAAY,WAAW,UAAU,KAAK,CAAC;AAC3I,QAAM,uBAAuB,CAAC,WAAW,WAAW,aAAa,WAAW,MAAM,EAAE,OAAO,CAAAG,WAAS,YAAY,WAAWA,MAAK,CAAC;AAEjI,QAAM,WAAW,WAAS;AACxB,QAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AACA,QAAI,MAAM,WAAW,GAAG;AACtB,YAAM,IAAI,MAAM,iCAAiC;AAAA,IACnD;AACA,UAAM,eAAe,CAAC;AACtB,UAAMc,OAAM,CAAC;AACb,SAAK,OAAO,CAAC,OAAO,UAAU;AAC5B,YAAM,SAAS,KAAK,KAAK;AACzB,UAAI,OAAO,WAAW,GAAG;AACvB,cAAM,IAAI,MAAM,gCAAgC;AAAA,MAClD;AACA,YAAMtB,OAAM,OAAO,CAAC;AACpB,YAAM,QAAQ,MAAMA,IAAG;AACvB,UAAIsB,KAAItB,IAAG,MAAM,QAAW;AAC1B,cAAM,IAAI,MAAM,4BAA4BA,IAAG;AAAA,MACjD,WAAWA,SAAQ,QAAQ;AACzB,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD,WAAW,CAAC,QAAQ,KAAK,GAAG;AAC1B,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AACA,mBAAa,KAAKA,IAAG;AACrB,MAAAsB,KAAItB,IAAG,IAAI,IAAI,SAAS;AACtB,cAAM,YAAY,KAAK;AACvB,YAAI,cAAc,MAAM,QAAQ;AAC9B,gBAAM,IAAI,MAAM,uCAAuCA,OAAM,gBAAgB,MAAM,SAAS,OAAO,QAAQ,YAAY,SAAS;AAAA,QAClI;AACA,cAAM,QAAQ,cAAY;AACxB,gBAAM,aAAa,KAAK,QAAQ;AAChC,cAAI,aAAa,WAAW,WAAW,QAAQ;AAC7C,kBAAM,IAAI,MAAM,mDAAmD,aAAa,KAAK,GAAG,IAAI,eAAe,WAAW,KAAK,GAAG,CAAC;AAAA,UACjI;AACA,gBAAM,UAAU,OAAO,cAAc,YAAU;AAC7C,mBAAO,SAAS,YAAY,MAAM;AAAA,UACpC,CAAC;AACD,cAAI,CAAC,SAAS;AACZ,kBAAM,IAAI,MAAM,kEAAkE,WAAW,KAAK,IAAI,IAAI,iBAAiB,aAAa,KAAK,IAAI,CAAC;AAAA,UACpJ;AACA,iBAAO,SAASA,IAAG,EAAE,MAAM,MAAM,IAAI;AAAA,QACvC;AACA,eAAO;AAAA,UACL,MAAM,IAAI,aAAa;AACrB,gBAAI,SAAS,WAAW,MAAM,QAAQ;AACpC,oBAAM,IAAI,MAAM,iDAAiD,MAAM,SAAS,WAAW,SAAS,MAAM;AAAA,YAC5G;AACA,kBAAM,SAAS,SAAS,KAAK;AAC7B,mBAAO,OAAO,MAAM,MAAM,IAAI;AAAA,UAChC;AAAA,UACA;AAAA,UACA,KAAK,WAAS;AACZ,oBAAQ,IAAI,OAAO;AAAA,cACjB;AAAA,cACA,aAAaA;AAAA,cACb,QAAQ;AAAA,YACV,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAOsB;AAAA,EACT;AACA,QAAM,MAAM,EAAE,SAAS;AAEvB,QAAM,MAAM,IAAI,SAAS;AAAA,IACvB,EAAE,MAAM,CAAC,EAAE;AAAA,IACX,EAAE,MAAM,CAAC,OAAO,EAAE;AAAA,IAClB;AAAA,MACE,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,GAAC,EAAE,GAAG,IAAI;AAEV,QAAM,gBAAgB,CAACb,QAAO,WAAW;AACvC,UAAM,QAAQ,UAAU,UAAUA,MAAK;AACvC,UAAM,UAAU,QAAQ,OAAO,MAAM;AACrC,WAAO,QAAQ,KAAK,mBAAiB;AACnC,YAAM,mBAAmB,cAAc,cAAc,SAAS,CAAC;AAC/D,YAAM,cAAc,cAAc,CAAC,EAAE;AACrC,YAAM,cAAc,iBAAiB,MAAM,iBAAiB;AAC5D,YAAM,eAAe,MAAM,IAAI,MAAM,aAAa,WAAW;AAC7D,aAAO,kBAAkB,YAAY;AAAA,IACvC,CAAC,EAAE,MAAM,EAAE;AAAA,EACb;AACA,QAAM,cAAc;AAEpB,QAAM,WAAW,WAAS,WAAW,OAAO,KAAK,IAAI,gBAAgB,KAAK,IAAI;AAC9E,QAAM,wBAAwB,SAAO;AACnC,UAAM,UAAU,aAAa,QAAQ,GAAG;AACxC,WAAO;AAAA,MACL,aAAa,OAAO,SAAS,cAAc,EAAE,MAAM,EAAE;AAAA,MACrD,aAAa,OAAO,SAAS,cAAc,EAAE,MAAM,EAAE;AAAA,MACrD,aAAa,OAAO,SAAS,cAAc,EAAE,IAAI,QAAQ,EAAE,MAAM,EAAE;AAAA,MACnE,iBAAiB,OAAO,SAAS,kBAAkB,EAAE,IAAI,QAAQ,EAAE,MAAM,EAAE;AAAA,IAC7E;AAAA,EACF;AACA,QAAM,kBAAkB,UAAQ;AAC9B,UAAM,WAAW,KAAK,CAAC;AACvB,UAAM,iBAAiB,KAAK,MAAM,CAAC;AACnC,SAAK,gBAAgB,WAAS;AAC5B,WAAK,KAAK,QAAQ,GAAG,CAAAT,SAAO;AAC1B,eAAO,OAAO,CAAC,WAAW,YAAY;AACpC,gBAAM,kBAAkB,SAASA,IAAG;AACpC,cAAI,oBAAoB,MAAMA,SAAQ,SAAS;AAC7C,gBAAI,oBAAoB,WAAW;AACjC,uBAASA,IAAG,IAAIA,SAAQ,UAAU,iBAAiB;AAAA,YACrD;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,eAAe,CAAC,SAAS,YAAY,QAAQ,QAAQ,KAAK,SAAS,CAAAD,UAAQ,CAAC,YAAY,OAAO,UAAU,UAAU,KAAK,aAAaA,KAAI,CAAC,CAAC,EAAE,MAAM,EAAE;AAC3J,QAAM,gBAAgB,MAAM,cAAc;AAAA,IACxC;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,OAAO;AACV,QAAM,gBAAgB,MAAM,cAAc;AAAA,IACxC;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,QAAQ;AACX,QAAM,0BAA0B,CAAC,QAAQ,mBAAmB;AAC1D,UAAM,QAAQ,iBAAiB,MAAM;AACrC,UAAM,QAAQ,qBAAqB,MAAM;AACzC,UAAM,2BAA2B,OAAO;AAAA,MACtC,aAAa,MAAM,OAAO,cAAc,EAAE,MAAM,EAAE;AAAA,MAClD,aAAa,SAAS,MAAM,OAAO,cAAc,EAAE,MAAM,EAAE,CAAC;AAAA,MAC5D,iBAAiB,SAAS,MAAM,OAAO,kBAAkB,EAAE,MAAM,EAAE,CAAC;AAAA,IACtE;AACA,UAAM,cAAc;AAAA,MAClB,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,aAAa;AAAA,MACb,aAAa;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,UAAM,YAAY,MAAM;AACtB,YAAM,cAAc,MAAM,cAAc;AACxC,UAAI,mBAAmB,MAAM,KAAK,aAAa;AAC7C,eAAO,EAAE,QAAQ,YAAY;AAAA,MAC/B;AACA,aAAO,MAAM,OAAO,QAAQ,EAAE,KAAK,OAAO,CAAC,IAAI,aAAW,EAAE,OAAO,EAAE;AAAA,IACvE;AACA,UAAM,WAAW,iBAAiB,yBAAyB,IAAI,CAAC;AAChE,UAAM,4BAA4B,MAAM;AACtC,YAAM,UAAU,MAAM,OAAO,gBAAgB,EAAE,GAAG,MAAM,OAAO,aAAa,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,kBAAgB,EAAE,YAAY,EAAE;AAChI,YAAM,UAAU,MAAM,OAAO,gBAAgB,EAAE,GAAG,MAAM,OAAO,aAAa,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,kBAAgB,EAAE,YAAY,EAAE;AAChI,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG,UAAU;AAAA,MACb,GAAG,0BAA0B;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAa,SAAO,MAAM,aAAa,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAAU,WAAS;AACtE,UAAM,SAAS,EAAE,WAAW,QAAQ,IAAI,KAAK,EAAE;AAC/C,WAAO,YAAYA,QAAO,MAAM;AAAA,EAClC,CAAC,EAAE,MAAM,EAAE;AACX,QAAM,8BAA8B,CAAC,QAAQ,KAAK,mBAAmB;AACnE,UAAM,YAAY,CAACc,MAAKC,SAAQ;AAC9B,YAAM,iBAAiB,OAAO,aAAa,QAAQA,IAAG,GAAG,cAAc;AACvE,UAAI,mBAAmB,MAAM,KAAK,eAAe,OAAO,GAAG;AACzD,eAAO,eAAe,MAAM,EAAE;AAAA,MAChC;AACA,aAAOD,KAAI,UAAUC,MAAK,QAAQ,KAAK,oBAAoB,OAAO,KAAKA,MAAK,cAAc,KAAK,oBAAoB,OAAO,KAAKA,MAAK,QAAQ,KAAK;AAAA,IACnJ;AACA,UAAM,MAAM,OAAO;AACnB,UAAM,cAAc,mBAAmB,MAAM,IAAI,IAAI,SAAS,KAAK,gBAAgB,KAAK,IAAI,UAAU,KAAK,aAAa,IAAI,IAAI,UAAU,KAAK,aAAa,KAAK,IAAI,SAAS,KAAK,gBAAgB;AACnM,UAAM,cAAc,mBAAmB,MAAM,IAAI,oBAAoB,KAAK,KAAK,SAAS,KAAK,IAAI,UAAU,KAAK,aAAa,IAAI,IAAI,UAAU,KAAK,aAAa,KAAK,oBAAoB,KAAK,KAAK,SAAS;AAC7M,WAAO;AAAA,MACL,OAAO,IAAI,SAAS,KAAK,OAAO,KAAK,IAAI,UAAU,KAAK,OAAO;AAAA,MAC/D,QAAQ,IAAI,SAAS,KAAK,QAAQ,KAAK,IAAI,UAAU,KAAK,QAAQ;AAAA,MAClE,aAAa,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc;AAAA,MAC5E,aAAa,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc;AAAA,MAC5E,QAAQ,UAAU,KAAK,GAAG;AAAA,MAC1B,SAAS,CAAC,CAAC,IAAI,OAAO,WAAW,GAAG,EAAE,CAAC;AAAA,MACvC,OAAO,IAAI,UAAU,KAAK,SAAS,EAAE;AAAA,MACrC,OAAO,cAAc,QAAQ,GAAG;AAAA,MAChC,GAAG,iBAAiB,sBAAsB,GAAG,IAAI,CAAC;AAAA,IACpD;AAAA,EACF;AACA,QAAM,4BAA4B,CAAC,QAAQ,KAAKC,uBAAsB;AACpE,UAAM,MAAM,OAAO;AACnB,WAAO;AAAA,MACL,QAAQ,IAAI,SAAS,KAAK,QAAQ,KAAK,IAAI,UAAU,KAAK,QAAQ;AAAA,MAClE,OAAO,IAAI,UAAU,KAAK,SAAS,EAAE;AAAA,MACrC,MAAM,WAAW,GAAG;AAAA,MACpB,OAAO,cAAc,QAAQ,GAAG;AAAA,MAChC,GAAGA,qBAAoB,sBAAsB,GAAG,IAAI,CAAC;AAAA,IACvD;AAAA,EACF;AACA,QAAM,6BAA6B,CAAC,QAAQpB,OAAMqB,qBAAoB,WAAW;AAC/E,UAAM,MAAM,OAAO;AACnB,UAAM,SAAS,OAAO,MAAMrB,KAAI;AAChC,UAAM,WAAW,CAAC,SAAS,UAAU,IAAI,SAAS,SAAS,KAAK,KAAK,IAAI,UAAU,SAAS,KAAK;AACjG,WAAO;AAAA,MACL,OAAO,SAAS,QAAQ,OAAO;AAAA,MAC/B,OAAO,IAAI,UAAUA,OAAM,OAAO;AAAA,MAClC,UAAU,YAAYA,KAAI;AAAA,MAC1B,OAAO,IAAI,UAAUA,OAAM,SAAS,EAAE;AAAA,MACtC,QAAQ,cAAc,QAAQA,KAAI;AAAA,MAClC,QAAQ,cAAc,QAAQA,KAAI;AAAA,MAClC,GAAGqB,sBAAqB,sBAAsBrB,KAAI,IAAI,CAAC;AAAA,IACzD;AAAA,EACF;AAEA,QAAM,mBAAmB,CAACI,QAAOD,WAAU;AACzC,UAAM,YAAY,UAAU,UAAUC,MAAK;AAC3C,UAAM,WAAW,UAAU,UAAU,SAAS;AAC9C,UAAM,WAAW,OAAO,UAAU,WAAS,OAAOD,QAAO,WAAS,GAAG,MAAM,SAAS,KAAK,CAAC,CAAC;AAC3F,WAAO,IAAI,UAAU,CAAAH,WAAS;AAAA,MAC5B,SAASA,MAAK,QAAQ;AAAA,MACtB,QAAQ,UAAU,YAAY,WAAWA,MAAK,MAAM,EAAE,IAAI,SAAO,IAAI,QAAQ,GAAG;AAAA,IAClF,EAAE;AAAA,EACJ;AACA,QAAM,sBAAsB,CAAC,UAAU,aAAa,MAAM,iBAAiB;AACzE,QAAI,aAAa,OAAO,GAAG;AACzB,eAAS,UAAU,SAAS,KAAK,KAAK;AAAA,IACxC;AACA,QAAI,aAAa,OAAO,KAAK,KAAK,UAAU,gBAAgB;AAC1D,eAAS,UAAU,SAAS,KAAK,KAAK;AAAA,IACxC;AACA,QAAI,aAAa,OAAO,GAAG;AACzB,kBAAY,SAAS,SAAS,YAAY,KAAK,KAAK,CAAC;AAAA,IACvD;AAAA,EACF;AACA,QAAM,wBAAwB,CAAC,UAAU,MAAM,iBAAiB;AAC9D,QAAI,aAAa,iBAAiB,GAAG;AACnC,eAAS,UAAU,4BAA4B,KAAK,eAAe;AAAA,IACrE;AACA,QAAI,aAAa,aAAa,GAAG;AAC/B,eAAS,UAAU,wBAAwB,KAAK,WAAW;AAAA,IAC7D;AACA,QAAI,aAAa,aAAa,GAAG;AAC/B,eAAS,UAAU,wBAAwB,KAAK,WAAW;AAAA,IAC7D;AACA,QAAI,aAAa,aAAa,GAAG;AAC/B,eAAS,UAAU,wBAAwB,YAAY,KAAK,WAAW,CAAC;AAAA,IAC1E;AAAA,EACF;AACA,QAAM,mBAAmB,CAAC,QAAQG,QAAO,MAAM,eAAe;AAC5D,UAAM,eAAeA,OAAM,WAAW;AACtC,SAAKA,QAAO,UAAQ;AAClB,YAAM,UAAU,KAAK;AACrB,YAAM,6BAA6B,eAAe,SAAS;AAC3D,YAAM,WAAW,YAAY,OAAO,QAAQ,OAAO;AACnD,YAAM,cAAc,KAAK,OAAO,IAAI,SAAO,YAAY,OAAO,QAAQ,GAAG,CAAC,EAAE,MAAM,QAAQ;AAC1F,0BAAoB,UAAU,aAAa,MAAM,0BAA0B;AAC3E,UAAI,mBAAmB,MAAM,GAAG;AAC9B,8BAAsB,UAAU,MAAM,0BAA0B;AAAA,MAClE;AACA,UAAI,WAAW,QAAQ,GAAG;AACxB,iBAAS,QAAQ,SAAS,KAAK,MAAM;AAAA,MACvC;AACA,UAAI,WAAW,QAAQ,GAAG;AACxB,kBAAU,QAAQ,SAAS,KAAK,MAAM;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,uBAAuB,CAAC,QAAQ,SAAS;AAC7C,WAAO,YAAY,oBAAoB,OAAO;AAAA,MAC5C,MAAM,KAAK;AAAA,MACX,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,CAAC,QAAQA,QAAO,SAAS,SAAS;AACtD,UAAM,eAAe,SAAS,MAAM,CAAC,OAAOR,SAAQ,QAAQA,IAAG,MAAM,KAAK;AAC1E,QAAI,KAAK,YAAY,IAAI,KAAKQ,OAAM,UAAU,GAAG;AAC/C,YAAMA,OAAM,CAAC,CAAC,EAAE,KAAK,CAAAC,WAAS;AAC5B,cAAM,gBAAgB,iBAAiBA,QAAOD,MAAK;AACnD,cAAM,gBAAgB,KAAK,SAAS,cAAc,CAAC,QAAQR,SAAQA,SAAQ,WAAWA,SAAQ,UAAU,CAAC,IAAI;AAC7G,cAAM,oBAAoB,IAAI,cAAc,UAAU;AACtD,YAAI,iBAAiB,IAAI,cAAc,OAAO,GAAG;AAC/C,2BAAiB,QAAQ,eAAe,MAAM,MAAM,KAAK,YAAY,CAAC;AAAA,QACxE;AACA,YAAI,mBAAmB;AACrB,+BAAqB,QAAQ,IAAI;AAAA,QACnC;AACA,0BAAkB,QAAQS,OAAM,KAAK;AAAA,UACnC,WAAW;AAAA,UACX,OAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,mBAAmB,CAAC,QAAQD,QAAO,SAASa,SAAQ;AACxD,UAAM,OAAOA,KAAI,QAAQ;AACzB,IAAAA,KAAI,MAAM;AACV,WAAO,YAAY,SAAS,MAAM;AAChC,oBAAc,QAAQb,QAAO,SAAS,IAAI;AAC1C,aAAO,MAAM;AAAA,IACf,CAAC;AAAA,EACH;AACA,QAAM,YAAY,CAAC,QAAQA,WAAU;AACnC,UAAM,YAAY,MAAMA,OAAM,CAAC,CAAC,EAAE,IAAI,CAAAC,WAAS,IAAI,iBAAiBA,QAAOD,MAAK,GAAG,UAAQ,2BAA2B,QAAQ,KAAK,SAAS,mBAAmB,MAAM,GAAG,KAAK,MAAM,CAAC,CAAC;AACrL,WAAO,gBAAgB,UAAU,SAAS,CAAC;AAAA,EAC7C;AACA,QAAM,SAAS,YAAU;AACvB,UAAMA,SAAQ,sBAAsB,MAAM;AAC1C,QAAIA,OAAM,WAAW,GAAG;AACtB;AAAA,IACF;AACA,UAAM,OAAO,UAAU,QAAQA,MAAK;AACpC,UAAM,iBAAiB;AAAA,MACrB,MAAM;AAAA,MACN,MAAM;AAAA,QACJ;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO,WAAW,MAAM;AAAA,QAC1B;AAAA,QACA,eAAe,QAAQ,MAAM;AAAA,MAC/B;AAAA,IACF;AACA,UAAM,cAAc;AAAA,MAClB,MAAM;AAAA,MACN,OAAO,CAAC;AAAA,QACJ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO,WAAW,MAAM;AAAA,MAC1B,CAAC;AAAA,IACL;AACA,WAAO,cAAc,KAAK;AAAA,MACxB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM,mBAAmB,MAAM,IAAI,iBAAiB;AAAA,MACpD,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,aAAa;AAAA,MACb,UAAU,MAAM,kBAAkB,QAAQA,QAAO,IAAI;AAAA,IACvD,CAAC;AAAA,EACH;AAEA,QAAM,eAAe,YAAU,eAAe,gBAAgB,MAAM,CAAC,EAAE,IAAI,YAAU;AAAA,IACnF,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP;AAAA,EACF,EAAE;AACF,QAAM,eAAe;AAAA,IACnB;AAAA,MACE,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,EACF;AACA,QAAM,aAAa,YAAU,aAAa,OAAO,aAAa,MAAM,EAAE,QAAQ,CAAC;AAE/E,QAAM,oBAAoB,CAAC,UAAU,MAAM,iBAAiB;AAC1D,QAAI,aAAa,OAAO,KAAK,KAAK,UAAU,gBAAgB;AAC1D,eAAS,UAAU,SAAS,KAAK,KAAK;AAAA,IACxC;AACA,QAAI,aAAa,QAAQ,GAAG;AAC1B,eAAS,SAAS,UAAU,YAAY,KAAK,MAAM,CAAC;AAAA,IACtD;AAAA,EACF;AACA,QAAM,sBAAsB,CAAC,UAAU,MAAM,iBAAiB;AAC5D,QAAI,aAAa,iBAAiB,GAAG;AACnC,eAAS,SAAS,oBAAoB,KAAK,eAAe;AAAA,IAC5D;AACA,QAAI,aAAa,aAAa,GAAG;AAC/B,eAAS,SAAS,gBAAgB,KAAK,WAAW;AAAA,IACpD;AACA,QAAI,aAAa,aAAa,GAAG;AAC/B,eAAS,SAAS,gBAAgB,KAAK,WAAW;AAAA,IACpD;AAAA,EACF;AACA,QAAM,iBAAiB,CAAC,QAAQF,OAAM,MAAM,eAAe;AACzD,UAAM,cAAcA,MAAK,WAAW;AACpC,UAAM,6BAA6B,cAAc,SAAS;AAC1D,SAAKA,OAAM,YAAU;AACnB,YAAM,WAAW,WAAW,aAAa,QAAQ,MAAM,GAAG,OAAO;AACjE,YAAM,WAAW,YAAY,OAAO,QAAQ,MAAM;AAClD,wBAAkB,UAAU,MAAM,0BAA0B;AAC5D,UAAI,kBAAkB,MAAM,GAAG;AAC7B,4BAAoB,UAAU,MAAM,0BAA0B;AAAA,MAChE;AACA,UAAI,WAAW,QAAQ,GAAG;AACxB,aAAK,UAAU,CAAAD,UAAQ;AACrB,iBAAO,IAAI,SAASA,MAAK,KAAK,UAAU,IAAI;AAAA,QAC9C,CAAC;AAAA,MACH;AACA,UAAI,WAAW,OAAO,GAAG;AACvB,iBAAS,QAAQ,QAAQ,KAAK,KAAK;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,qBAAqB,CAAC,QAAQ,SAAS;AAC3C,WAAO,YAAY,mBAAmB,OAAO;AAAA,MAC3C,MAAM,KAAK;AAAA,MACX,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,QAAM,eAAe,CAAC,QAAQC,OAAM,SAAS,SAAS;AACpD,UAAM,eAAe,SAAS,MAAM,CAAC,OAAON,SAAQ,QAAQA,IAAG,MAAM,KAAK;AAC1E,QAAI,KAAK,YAAY,IAAI,GAAG;AAC1B,YAAM,eAAe,IAAI,cAAc,MAAM;AAC7C,YAAM,gBAAgB,eAAe,KAAK,YAAY,IAAI,IAAI;AAC9D,UAAI,eAAe;AACjB,uBAAe,QAAQM,OAAM,MAAM,MAAM,KAAK,YAAY,CAAC;AAAA,MAC7D;AACA,UAAI,cAAc;AAChB,2BAAmB,QAAQ,IAAI;AAAA,MACjC;AACA,YAAM,aAAa,QAAQA,MAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAAG,WAAS,kBAAkB,QAAQA,OAAM,KAAK;AAAA,QACtF,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,QAAM,kBAAkB,CAAC,QAAQH,OAAM,SAASe,SAAQ;AACtD,UAAM,OAAOA,KAAI,QAAQ;AACzB,IAAAA,KAAI,MAAM;AACV,WAAO,YAAY,SAAS,MAAM;AAChC,mBAAa,QAAQf,OAAM,SAAS,IAAI;AACxC,aAAO,MAAM;AAAA,IACf,CAAC;AAAA,EACH;AACA,QAAM,SAAS,YAAU;AACvB,UAAMA,QAAO,qBAAqB,kBAAkB,MAAM,GAAG,SAAS,QAAQ;AAC9E,QAAIA,MAAK,WAAW,GAAG;AACrB;AAAA,IACF;AACA,UAAM,WAAW,IAAIA,OAAM,YAAU,0BAA0B,QAAQ,OAAO,KAAK,kBAAkB,MAAM,CAAC,CAAC;AAC7G,UAAM,OAAO,gBAAgB,QAAQ;AACrC,UAAM,iBAAiB;AAAA,MACrB,MAAM;AAAA,MACN,MAAM;AAAA,QACJ;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO,WAAW,MAAM;AAAA,QAC1B;AAAA,QACA,eAAe,QAAQ,KAAK;AAAA,MAC9B;AAAA,IACF;AACA,UAAM,cAAc;AAAA,MAClB,MAAM;AAAA,MACN,OAAO,CAAC;AAAA,QACJ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO,WAAW,MAAM;AAAA,MAC1B,CAAC;AAAA,IACL;AACA,WAAO,cAAc,KAAK;AAAA,MACxB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM,kBAAkB,MAAM,IAAI,iBAAiB;AAAA,MACnD,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,aAAa;AAAA,MACb,UAAU,MAAM,iBAAiB,QAAQ,IAAIA,OAAM,OAAK,EAAE,GAAG,GAAG,IAAI;AAAA,IACtE,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,CAAC,QAAQ,SAAS,mBAAmB;AACpD,UAAM,mBAAmB,CAAC,iBAAiB,CAAC,IAAI;AAAA,MAC9C;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AACA,UAAM,cAAc;AAAA,MAClB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,kBAAkB,qBAAqB,MAAM,IAAI;AAAA,MACrD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO,CAAC;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC;AAAA,MACL;AAAA,IACF,IAAI,CAAC;AACL,UAAM,gBAAgB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,CAAC;AACH,UAAM,gBAAgB,QAAQ,SAAS,IAAI,CAAC;AAAA,MACxC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,IACT,CAAC,IAAI,CAAC;AACR,WAAO,iBAAiB,OAAO,WAAW,EAAE,OAAO,eAAe,EAAE,OAAO,aAAa,EAAE,OAAO,aAAa;AAAA,EAChH;AAEA,QAAM,YAAY,CAAC,KAAK,KAAKP,OAAM,UAAU;AAC3C,QAAI,IAAI,YAAY,QAAQ,IAAI,YAAY,MAAM;AAChD,UAAI,SAASA,KAAI,KAAK,cAAc,KAAK,GAAG;AAC1C,YAAI,SAAS,KAAKA,OAAM,KAAK;AAAA,MAC/B,OAAO;AACL,YAAI,UAAU,KAAKA,KAAI;AAAA,MACzB;AAAA,IACF,OAAO;AACL,UAAI,IAAI,UAAU;AAChB,iBAAS,IAAI,GAAG,IAAI,IAAI,SAAS,QAAQ,KAAK;AAC5C,oBAAU,KAAK,IAAI,SAAS,CAAC,GAAGA,OAAM,KAAK;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,qBAAqB,CAAC,QAAQ,UAAU,MAAM,sBAAsB;AACxE,UAAM,MAAM,OAAO;AACnB,UAAM,QAAQ,CAAC;AACf,UAAM,SAAS,CAAC;AAChB,UAAM,uBAAuB,mBAAmB,MAAM;AACtD,UAAM,wBAAwB,oBAAoB,MAAM;AACxD,UAAM,eAAe,WAAW,KAAK,MAAM,MAAM;AACjD,QAAI,CAAC,YAAY,KAAK,KAAK,KAAK,KAAK,UAAU,gBAAgB;AAC7D,YAAM,QAAQ,KAAK;AAAA,IACrB;AACA,WAAO,SAAS,YAAY,KAAK,MAAM;AACvC,QAAI,sBAAsB;AACxB,aAAO,QAAQ,YAAY,KAAK,KAAK;AAAA,IACvC,WAAW,IAAI,UAAU,UAAU,OAAO,GAAG;AAC3C,YAAM,QAAQ,eAAe,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,sBAAsB;AACxB,UAAI,cAAc;AAChB,cAAM,SAAS;AACf,eAAO,cAAc,IAAI;AAAA,MAC3B,OAAO;AACL,eAAO,cAAc,IAAI,YAAY,KAAK,MAAM;AAChD,cAAM,SAAS;AAAA,MACjB;AACA,aAAO,gBAAgB,IAAI,YAAY,KAAK,WAAW;AAAA,IACzD,OAAO;AACL,YAAM,SAAS,eAAe,IAAI,KAAK;AACvC,YAAM,cAAc,KAAK;AACzB,YAAM,cAAc,KAAK;AAAA,IAC3B;AACA,QAAI,wBAAwB,SAAS,UAAU;AAC7C,YAAM,aAAa,CAAC;AACpB,UAAI,cAAc;AAChB,mBAAW,cAAc,IAAI;AAAA,MAC/B,WAAW,kBAAkB,QAAQ;AACnC,mBAAW,cAAc,IAAI,YAAY,KAAK,MAAM;AAAA,MACtD;AACA,UAAI,kBAAkB,aAAa;AACjC,mBAAW,UAAU,YAAY,KAAK,WAAW;AAAA,MACnD;AACA,UAAI,yBAAyB,kBAAkB,aAAa;AAC1D,mBAAW,cAAc,IAAI,KAAK;AAAA,MACpC;AACA,UAAI,CAAC,UAAU,UAAU,GAAG;AAC1B,iBAAS,IAAI,GAAG,IAAI,SAAS,SAAS,QAAQ,KAAK;AACjD,oBAAU,KAAK,SAAS,SAAS,CAAC,GAAG,UAAU;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AACA,QAAI,uBAAuB;AACzB,YAAM,UAAU;AAChB,aAAO,kBAAkB,IAAI,QAAQ;AACrC,aAAO,cAAc,IAAI,QAAQ;AACjC,aAAO,cAAc,IAAI,QAAQ;AAAA,IACnC;AACA,QAAI,UAAU,UAAU;AAAA,MACtB,GAAG,iBAAiB,MAAM;AAAA,MAC1B,GAAG;AAAA,IACL,CAAC;AACD,QAAI,WAAW,UAAU;AAAA,MACvB,GAAG,qBAAqB,MAAM;AAAA,MAC9B,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACA,QAAM,oBAAoB,CAAC,QAAQ,UAAU,SAASsB,SAAQ;AAC5D,UAAM,MAAM,OAAO;AACnB,UAAM,OAAOA,KAAI,QAAQ;AACzB,UAAM,eAAe,SAAS,MAAM,CAAC,OAAOrB,SAAQ,QAAQA,IAAG,MAAM,KAAK;AAC1E,IAAAqB,KAAI,MAAM;AACV,WAAO,YAAY,SAAS,MAAM;AAChC,UAAI,CAAC,UAAU;AACb,cAAM,OAAO,MAAM,KAAK,IAAI,EAAE,MAAM,CAAC;AACrC,cAAMf,QAAO,MAAM,KAAK,IAAI,EAAE,MAAM,CAAC;AACrC,eAAO,YAAY,kBAAkB,OAAO;AAAA,UAC1C,MAAAA;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AACD,mBAAW,iBAAiB,kBAAkB,MAAM,GAAG,UAAU,MAAM,CAAC,EAAE,KAAK,CAAAD,UAAQ,MAAMA,OAAM,UAAU,MAAM,CAAC,CAAC,EAAE,IAAI,CAAAI,WAASA,OAAM,GAAG,EAAE,SAAS;AAAA,MAC1J;AACA,UAAI,KAAK,YAAY,IAAI,GAAG;AAC1B,cAAM,2BAA2B;AAAA,UAC/B,QAAQ,IAAI,cAAc,QAAQ;AAAA,UAClC,aAAa,IAAI,cAAc,aAAa;AAAA,UAC5C,aAAa,IAAI,cAAc,aAAa;AAAA,QAC9C;AACA,2BAAmB,QAAQ,UAAU,MAAM,wBAAwB;AACnE,cAAM,aAAa,IAAI,OAAO,WAAW,QAAQ,EAAE,CAAC;AACpD,YAAI,cAAc,CAAC,KAAK,WAAW,CAAC,cAAc,KAAK,SAAS;AAC9D,iBAAO,YAAY,uBAAuB;AAAA,QAC5C;AACA,iBAAS,QAAQ,UAAU,KAAK,KAAK;AAAA,MACvC;AACA,aAAO,MAAM;AACb,aAAO,UAAU;AACjB,UAAI,KAAK,YAAY,IAAI,GAAG;AAC1B,cAAM,kBAAkB,IAAI,cAAc,SAAS;AACnD,cAAM,gBAAgB,kBAAkB,KAAK,YAAY,IAAI,IAAI;AACjE,0BAAkB,QAAQ,UAAU;AAAA,UAClC,WAAW;AAAA,UACX,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,OAAO,CAAC,QAAQ,mBAAmB;AACvC,UAAM,MAAM,OAAO;AACnB,QAAI;AACJ,QAAI,OAAO,wBAAwB,QAAQ,oBAAoB,MAAM,CAAC;AACtE,QAAI,gBAAgB;AAClB,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,UAAI,oBAAoB,MAAM,GAAG;AAC/B,aAAK,cAAc;AACnB,aAAK,cAAc;AACnB,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF,OAAO;AACL,iBAAW,IAAI,UAAU,OAAO,UAAU,SAAS,GAAG,SAAS,OAAO,QAAQ,CAAC;AAC/E,UAAI,UAAU;AACZ,eAAO,4BAA4B,QAAQ,UAAU,oBAAoB,MAAM,CAAC;AAAA,MAClF,OAAO;AACL,YAAI,oBAAoB,MAAM,GAAG;AAC/B,eAAK,cAAc;AACnB,eAAK,cAAc;AACnB,eAAK,kBAAkB;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,UAAM,UAAU,eAAe,kBAAkB,MAAM,CAAC;AACxD,QAAI,QAAQ,OAAO,GAAG;AACpB,UAAI,KAAK,OAAO;AACd,aAAK,QAAQ,KAAK,MAAM,QAAQ,2BAA2B,EAAE;AAAA,MAC/D;AAAA,IACF;AACA,UAAM,eAAe;AAAA,MACnB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO,SAAS,QAAQ,QAAQ,MAAM,CAAC,CAAC,GAAG,cAAc;AAAA,IAC3D;AACA,UAAM,kBAAkB,OAAO;AAAA,MAC7B,MAAM;AAAA,MACN,OAAO,CAAC,YAAY;AAAA,IACtB;AACA,UAAM,eAAe,OAAO;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM;AAAA,QACJ;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO,CAAC,YAAY;AAAA,QACtB;AAAA,QACA,eAAe,QAAQ,OAAO;AAAA,MAChC;AAAA,IACF;AACA,UAAM,aAAa,oBAAoB,MAAM,IAAI,aAAa,IAAI,gBAAgB;AAClF,WAAO,cAAc,KAAK;AAAA,MACxB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU,MAAM,mBAAmB,QAAQ,UAAU,IAAI;AAAA,MACzD,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAEA,QAAM,mBAAmB,YAAU;AACjC,UAAM,YAAY,OAAK;AACrB,UAAI,oBAAoB,kBAAkB,MAAM,CAAC,GAAG;AAClD,UAAE;AAAA,MACJ;AAAA,IACF;AACA,WAAO;AAAA,MACL,eAAe,MAAM,MAAM,QAAQ,KAAK;AAAA,MACxC,kBAAkB,MAAM,QAAQ,MAAM;AAAA,MACtC,mBAAmB,MAAM,QAAQ,MAAM;AAAA,MACvC,sBAAsB,MAAM,MAAM,QAAQ,IAAI;AAAA,IAChD,GAAG,CAAC,MAAMV,UAAS,OAAO,WAAWA,OAAM,MAAM,UAAU,IAAI,CAAC,CAAC;AAAA,EACnE;AAEA,QAAM,QAAQ,CAAC,OAAO,aAAa,QAAQ,OAAO,QAAQ,EAAE,OAAO;AAEnE,QAAM,YAAY;AAClB,QAAM,aAAa,mBAAiB;AAClC,UAAM,UAAU,CAAC,MAAMF,UAAS,OAAO,MAAMA,KAAI,EAAE,OAAO,UAAQ,SAAS,MAAM,EAAE,IAAI,CAAC;AACxF,UAAM,kBAAkB,UAAQ,QAAQ,MAAM,SAAS,KAAK,QAAQ,MAAM,SAAS;AACnF,WAAO,cAAc,SAAS,KAAK,OAAO,eAAe,eAAe,IAAI,SAAS,KAAK,aAAa,IAAI,SAAS,KAAK;AAAA,EAC3H;AACA,QAAM,WAAW,CAACY,QAAO,eAAekB,cAAa;AACnD,QAAI,cAAc,UAAU,GAAG;AAC7B,aAAO,SAAS,KAAK;AAAA,IACvB,OAAO;AACL,aAAO,YAAYlB,QAAOkB,UAAS,uBAAuBA,UAAS,oBAAoB,EAAE,IAAI,CAAAf,aAAW;AAAA,QACtG,QAAAA;AAAA,QACA,OAAO;AAAA,MACT,EAAE;AAAA,IACJ;AAAA,EACF;AAEA,QAAM,SAAS,CAAAP,WAAS;AAAA,IACtB,SAASA;AAAA,IACT,UAAU,SAAS,KAAK;AAAA,IACxB,YAAY,SAAS,KAAK;AAAA,IAC1B,WAAW,CAACA,KAAI;AAAA,EAClB;AACA,QAAM,UAAU,CAAC,eAAeI,QAAOJ,WAAU;AAAA,IAC/C,SAASA;AAAA,IACT,UAAU,SAASI,QAAO,eAAe,QAAQ;AAAA,IACjD,YAAY,WAAW,aAAa;AAAA,IACpC,WAAW,UAAU,aAAa;AAAA,EACpC;AAEA,QAAM,sBAAsB,YAAU;AACpC,UAAM,UAAU,KAAK,SAAS,KAAK,CAAC;AACpC,UAAM,iBAAiB,KAAK,CAAC,CAAC;AAC9B,QAAI,mBAAmB,SAAS,KAAK;AACrC,UAAM,YAAY,MAAM,SAAS;AACjC,UAAM,yBAAyB,CAAAT,SAAO,iBAAiB,OAAO,aAAW,CAAC,QAAQA,IAAG,CAAC;AACtF,UAAM,WAAW,MAAM,0BAA0B,kBAAkB,MAAM,GAAG,UAAU,MAAM,CAAC;AAC7F,UAAM,SAAS,MAAM,0BAA0B,gBAAgB,MAAM,GAAG,UAAU,MAAM,CAAC;AACzF,UAAM,cAAc,MAAM,SAAS,EAAE,KAAK,wBAAsB,QAAQ,MAAM,MAAM,kBAAkB,GAAG,OAAO,EAAE,KAAK,KAAK,GAAG,CAAC,YAAY,aAAa;AACvJ,UAAI,GAAG,YAAY,QAAQ,GAAG;AAC5B,YAAI,UAAU,kBAAkB,GAAG;AACjC,iBAAO,SAAS,KAAK,OAAO,kBAAkB,CAAC;AAAA,QACjD,OAAO;AACL,iBAAO,SAAS,KAAK,QAAQ,sBAAsB,MAAM,GAAG,YAAY,kBAAkB,CAAC;AAAA,QAC7F;AAAA,MACF;AACA,aAAO,SAAS,KAAK;AAAA,IACvB,CAAC,CAAC,CAAC;AACH,UAAM,sBAAsB,CAAA4B,aAAW;AACrC,YAAM,WAAW,MAAMA,SAAQ,OAAO;AACtC,aAAO,SAAS,IAAI,CAAAnB,WAAS;AAC3B,cAAM,YAAY,UAAU,UAAUA,MAAK;AAC3C,cAAM,gBAAgB,QAAQ,WAAWmB,QAAO,EAAE,MAAM,CAAC,CAAC;AAC1D,cAAM,SAAS,MAAM,eAAe,CAAC,KAAKvB,UAAS;AACjD,cAAIA,MAAK,UAAU;AACjB,gBAAI,QAAQ;AACZ,gBAAIA,MAAK,WAAW,GAAG;AACrB,kBAAI,UAAU;AAAA,YAChB,WAAWA,MAAK,SAASA,MAAK,WAAW,UAAU,KAAK,SAAS;AAC/D,kBAAI,SAAS;AAAA,YACf;AAAA,UACF;AACA,iBAAO;AAAA,QACT,GAAG;AAAA,UACD,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV,CAAC;AACD,eAAO;AAAA,UACL,WAAW,mBAAmB,WAAWuB,QAAO,EAAE,OAAO;AAAA,UACzD,aAAa,qBAAqB,WAAWA,QAAO,EAAE,OAAO;AAAA,UAC7D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,eAAe,MAAM;AACzB,cAAQ,IAAI,OAAO,WAAW,EAAE,CAAC;AACjC,yBAAmB,QAAQ,IAAI,EAAE,KAAK,mBAAmB;AACzD,WAAK,eAAe,IAAI,GAAG,IAAI;AAAA,IACjC;AACA,UAAM,eAAe,aAAW;AAC9B,cAAQ;AACR,qBAAe,IAAI,eAAe,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AACzD,aAAO,MAAM;AACX,uBAAe,IAAI,OAAO,eAAe,IAAI,GAAG,OAAK,MAAM,OAAO,CAAC;AAAA,MACrE;AAAA,IACF;AACA,UAAM,UAAU,CAACP,MAAK,eAAe,aAAa,MAAM,QAAQ,IAAI,EAAE,KAAK,MAAM;AAC/E,MAAAA,KAAI,WAAW,KAAK;AAAA,IACtB,GAAG,CAAAO,aAAW;AACZ,MAAAP,KAAI,WAAW,CAAC,WAAWO,QAAO,KAAK,OAAO,UAAU,WAAW,CAAC;AAAA,IACtE,CAAC,CAAC;AACF,UAAM,oBAAoB,CAACP,MAAK,YAAY,aAAa,aAAa,MAAM,QAAQ,IAAI,EAAE,KAAK,MAAM;AACnG,MAAAA,KAAI,WAAW,KAAK;AACpB,MAAAA,KAAI,UAAU,KAAK;AAAA,IACrB,GAAG,CAAAO,aAAW;AACZ,MAAAP,KAAI,WAAW,CAAC,WAAWO,QAAO,KAAK,OAAO,UAAU,WAAW,CAAC;AACpE,MAAAP,KAAI,UAAU,SAASO,QAAO,CAAC;AAAA,IACjC,CAAC,CAAC;AACF,UAAM,uBAAuB,mBAAiB,iBAAiB,OAAO,aAAW,QAAQ,OAAO,aAAa,CAAC;AAC9G,UAAM,eAAe,CAAAP,SAAO,QAAQA,MAAK,OAAK,KAAK;AACnD,UAAM,mBAAmB,CAAAA,SAAO,QAAQA,MAAK,CAAAO,aAAW,UAAUA,SAAQ,OAAO,CAAC;AAClF,UAAM,gBAAgB,mBAAiB,CAAAP,SAAO,QAAQA,MAAK,CAAAO,aAAW,UAAUA,SAAQ,OAAO,KAAK,qBAAqB,aAAa,CAAC;AACvI,UAAM,mBAAmB,sBAAoB,CAAAP,SAAO,QAAQA,MAAK,CAAAO,aAAW,UAAUA,SAAQ,OAAO,KAAK,iBAAiB,EAAE,OAAO,CAAC;AACrI,UAAM,yBAAyB,CAAC,kBAAkB,kBAAkB,CAAAP,SAAO,QAAQA,MAAK,CAAAO,aAAW,UAAUA,SAAQ,OAAO,KAAK,iBAAiB,EAAE,OAAO,KAAK,qBAAqB,aAAa,CAAC;AACnM,UAAM,mBAAmB,CAAAP,SAAO,QAAQA,MAAK,cAAY,uBAAuB,WAAW,CAAC;AAC5F,UAAM,qBAAqB,CAAAA,SAAO,QAAQA,MAAK,cAAY,uBAAuB,aAAa,CAAC;AAChG,UAAM,0BAA0B,CAAAA,SAAO;AACrC,aAAO,kBAAkBA,MAAK,OAAO,CAAAO,aAAW;AAC9C,cAAM,WAAW,MAAMA,SAAQ,SAAS,UAAU,MAAM,CAAC;AACzD,eAAO,SAAS,OAAO,CAAAnB,WAAS,MAAMA,QAAO,SAAS,CAAC;AAAA,MACzD,CAAC;AAAA,IACH;AACA,UAAM,sBAAsB,CAAC,SAAS,eAAe,CAAAY,SAAO;AAC1D,aAAO,kBAAkBA,MAAK,CAAAO,aAAW,UAAUA,SAAQ,OAAO,GAAG,MAAM,OAAO,kBAAkB,OAAO,MAAM,UAAU;AAAA,IAC7H;AACA,UAAM,yBAAyB,oBAAoB,mBAAmB,QAAQ;AAC9E,UAAM,4BAA4B,oBAAoB,mBAAmB,IAAI;AAC7E,WAAO,GAAG,8CAA8C,YAAY;AACpE,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AAEA,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAE/D,QAAM,gBAAgB;AACtB,QAAM,eAAe,gBAAgB;AACrC,QAAM,kBAAkB,gBAAgB;AACxC,QAAM,UAAU,CAAA/B,UAAQ;AACtB,QAAI;AACJ,UAAM,SAAS,KAAK,OAAO,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AACrE,WAAO,QAAQ,OAAO,UAAQ,SAAS,KAAK,KAAK,QAAQA,KAAI,CAAC,CAAC;AAAA,EACjE;AACA,QAAM,UAAU,MAAM,QAAQ,YAAY;AAC1C,QAAM,aAAa,MAAM,QAAQ,eAAe;AAEhD,QAAM,oBAAoB,YAAU,CAAAwB,SAAO;AACzC,UAAM,cAAc,MAAM;AACxB,MAAAA,KAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,IAC9C;AACA,WAAO,GAAG,cAAc,WAAW;AACnC,gBAAY;AACZ,WAAO,MAAM;AACX,aAAO,IAAI,cAAc,WAAW;AAAA,IACtC;AAAA,EACF;AACA,QAAM,aAAa,CAAC,QAAQ,qBAAqB;AAC/C,WAAO,GAAG,SAAS,cAAc,SAAS;AAAA,MACxC,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,kBAAkB,MAAM;AAAA,MACjC,OAAO,cAAY,SAAS,uEAAuE;AAAA,IACrG,CAAC;AACD,UAAM,MAAM,aAAW,MAAM,OAAO,YAAY,OAAO;AACvD,UAAM,wBAAwB,CAACtB,OAAM,SAAS;AAC5C,UAAI,OAAO,sBAAsB,KAAK,OAAO,GAAG;AAC9C,eAAO,GAAG,SAAS,UAAUA,OAAM;AAAA,UACjC,GAAG;AAAA,UACH,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,OAAO;AAAA,QACxE,CAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,8BAA8B,CAACA,OAAM,SAAS;AAClD,UAAI,OAAO,sBAAsB,KAAK,OAAO,GAAG;AAC9C,eAAO,GAAG,SAAS,gBAAgBA,OAAM;AAAA,UACvC,GAAG;AAAA,UACH,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,OAAO;AAAA,QACxE,CAAC;AAAA,MACH;AAAA,IACF;AACA,0BAAsB,cAAc;AAAA,MAClC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,eAAe;AAAA,MACnC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,kBAAkB;AAAA,MACtC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,mBAAmB;AAAA,MACvC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,mBAAmB;AAAA,MACvC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,wBAAwB;AAAA,MAC5C,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,uBAAuB;AAAA,MAC3C,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,kBAAkB;AAAA,MACtC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,iBAAiB;AAAA,MACrC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,wBAAwB;AAAA,MAC5C,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB,cAAc,SAAS;AAAA,IACnD,CAAC;AACD,0BAAsB,uBAAuB;AAAA,MAC3C,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB,cAAc,QAAQ;AAAA,IAClD,CAAC;AACD,0BAAsB,kBAAkB;AAAA,MACtC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB,cAAc,OAAO;AAAA,IACjD,CAAC;AACD,0BAAsB,eAAe;AAAA,MACnC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,gBAAgB;AAAA,MACpC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,0BAAsB,uBAAuB;AAAA,MAC3C,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,IACpD,CAAC;AACD,0BAAsB,sBAAsB;AAAA,MAC1C,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,IACpD,CAAC;AACD,0BAAsB,eAAe;AAAA,MACnC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB,cAAc,OAAO;AAAA,IACjD,CAAC;AACD,0BAAsB,gBAAgB;AAAA,MACpC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB,cAAc,OAAO;AAAA,IACjD,CAAC;AACD,0BAAsB,uBAAuB;AAAA,MAC3C,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB,uBAAuB,YAAY,SAAS;AAAA,IACxE,CAAC;AACD,0BAAsB,sBAAsB;AAAA,MAC1C,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iBAAiB,uBAAuB,YAAY,QAAQ;AAAA,IACvE,CAAC;AACD,0BAAsB,qBAAqB;AAAA,MACzC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,kBAAkB,MAAM;AAAA,IACnC,CAAC;AACD,UAAM,iBAAiB,eAAe,kBAAkB,MAAM,CAAC;AAC/D,QAAI,eAAe,WAAW,KAAK,OAAO,sBAAsB,qBAAqB,GAAG;AACtF,aAAO,GAAG,SAAS,cAAc,cAAc;AAAA,QAC7C,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO,0BAA0B,QAAQ,gBAAgB,cAAc,WAAS,OAAO,YAAY,uBAAuB,OAAO,KAAK,CAAC;AAAA,QACvI,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,UAAM,qBAAqB,eAAe,iBAAiB,MAAM,CAAC;AAClE,QAAI,mBAAmB,WAAW,KAAK,OAAO,sBAAsB,yBAAyB,GAAG;AAC9F,aAAO,GAAG,SAAS,cAAc,kBAAkB;AAAA,QACjD,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO,0BAA0B,QAAQ,oBAAoB,kBAAkB,WAAS,OAAO,YAAY,2BAA2B,OAAO,KAAK,CAAC;AAAA,QACnJ,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,sBAAsB,wBAAwB,GAAG;AAC1D,aAAO,GAAG,SAAS,cAAc,mBAAmB;AAAA,QAClD,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO,0BAA0B,QAAQ,qBAAqB,0BAA0B,oBAAoB,QAAQ,gBAAgB,CAAC;AAAA,QACrI,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AACD,aAAO,GAAG,SAAS,cAAc,wBAAwB;AAAA,QACvD,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO,0BAA0B,QAAQ,qBAAqB,MAAM,GAAG,wBAAwB,oBAAoB,QAAQ,cAAc,CAAC;AAAA,QAC1I,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AACD,aAAO,GAAG,SAAS,cAAc,wBAAwB;AAAA,QACvD,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO,0BAA0B,QAAQ,qBAAqB,MAAM,GAAG,wBAAwB,oBAAoB,QAAQ,cAAc,CAAC;AAAA,QAC1I,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AACD,aAAO,GAAG,SAAS,cAAc,4BAA4B;AAAA,QAC3D,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO,cAAY,SAAS,eAAe,QAAQ,2BAA2B,MAAM,GAAG,kBAAkB,CAAC;AAAA,QAC1G,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AACD,aAAO,GAAG,SAAS,cAAc,wBAAwB;AAAA,QACvD,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO,cAAY,SAAS,eAAe,QAAQ,uBAAuB,MAAM,GAAG,cAAc,CAAC;AAAA,QAClG,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,gCAA4B,gBAAgB;AAAA,MAC1C,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,gCAA4B,kBAAkB;AAAA,MAC5C,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU,gBAAgB,MAAM;AAAA,MAChC,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,gCAA4B,kBAAkB;AAAA,MAC5C,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU,mBAAmB,MAAM;AAAA,MACnC,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AAAA,EACH;AACA,QAAM,cAAc,YAAU;AAC5B,UAAM,kBAAkB,CAAAU,WAAS,OAAO,IAAI,GAAGA,QAAO,OAAO,KAAK,OAAO,QAAQ,EAAE,SAASA,MAAK,KAAK,OAAO,IAAI,WAAWA,OAAM,UAAU;AAC5I,UAAM,UAAU,WAAW,MAAM;AACjC,QAAI,QAAQ,SAAS,GAAG;AACtB,aAAO,GAAG,SAAS,kBAAkB,SAAS;AAAA,QAC5C,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,kBAAkB,YAAU,CAAAY,SAAO;AACvC,UAAM,cAAc,MAAM;AACxB,MAAAA,KAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,IAC9C;AACA,WAAO,GAAG,cAAc,WAAW;AACnC,gBAAY;AACZ,WAAO,MAAM;AACX,aAAO,IAAI,cAAc,WAAW;AAAA,IACtC;AAAA,EACF;AACA,QAAM,eAAe,CAAC,QAAQ,qBAAqB;AACjD,UAAM,MAAM,aAAW,MAAM,OAAO,YAAY,OAAO;AACvD,UAAM,sBAAsB,CAACtB,OAAM,SAAS;AAC1C,UAAI,OAAO,sBAAsB,KAAK,OAAO,GAAG;AAC9C,eAAO,GAAG,SAAS,YAAYA,OAAM;AAAA,UACnC,GAAG;AAAA,UACH,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,OAAO;AAAA,QACxE,CAAC;AACD,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,4BAA4B,CAACA,OAAM,SAAS;AAChD,UAAI,OAAO,sBAAsB,KAAK,OAAO,GAAG;AAC9C,eAAO,GAAG,SAAS,kBAAkBA,OAAM;AAAA,UACzC,GAAG;AAAA,UACH,UAAU,WAAW,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,OAAO;AAAA,QACxE,CAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,oBAAoB,UAAQ;AAChC,aAAO,YAAY,kBAAkB,OAAO;AAAA,QAC1C,MAAM,KAAK;AAAA,QACX,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH;AACA,UAAM,kBAAkB;AAAA,MACtB,oBAAoB,wBAAwB;AAAA,QAC1C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,MACD,oBAAoB,uBAAuB;AAAA,QACzC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,MACD,oBAAoB,kBAAkB;AAAA,QACpC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,MACD,oBAAoB,iBAAiB;AAAA,QACnC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,MACD,oBAAoB,eAAe;AAAA,QACjC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,MACD,oBAAoB,gBAAgB;AAAA,QAClC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,MACD,oBAAoB,uBAAuB;AAAA,QACzC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,MACpD,CAAC;AAAA,MACD,oBAAoB,sBAAsB;AAAA,QACxC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,MACpD,CAAC;AAAA,IACH;AACA,UAAM,qBAAqB;AAAA,MACzB,oBAAoB,2BAA2B;AAAA,QAC7C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB,cAAc,SAAS;AAAA,MACnD,CAAC;AAAA,MACD,oBAAoB,0BAA0B;AAAA,QAC5C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB,cAAc,QAAQ;AAAA,MAClD,CAAC;AAAA,MACD,oBAAoB,qBAAqB;AAAA,QACvC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB,cAAc,OAAO;AAAA,MACjD,CAAC;AAAA,MACD,oBAAoB,kBAAkB;AAAA,QACpC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB,cAAc,OAAO;AAAA,MACjD,CAAC;AAAA,MACD,oBAAoB,mBAAmB;AAAA,QACrC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB,cAAc,OAAO;AAAA,MACjD,CAAC;AAAA,MACD,oBAAoB,0BAA0B;AAAA,QAC5C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB,uBAAuB,YAAY,SAAS;AAAA,MACxE,CAAC;AAAA,MACD,oBAAoB,yBAAyB;AAAA,QAC3C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB,uBAAuB,YAAY,QAAQ;AAAA,MACvE,CAAC;AAAA,IACH;AACA,UAAM,mBAAmB;AAAA,MACvB,oBAAoB,kBAAkB;AAAA,QACpC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,MACD,oBAAoB,mBAAmB;AAAA,QACrC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,MACD,oBAAoB,mBAAmB;AAAA,QACrC,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,QAAI,CAAC,aAAa,MAAM,GAAG;AACzB,aAAO,GAAG,SAAS,YAAY,eAAe;AAAA,QAC5C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU,IAAI,sBAAsB;AAAA,QACpC,SAAS,gBAAgB,MAAM;AAAA,MACjC,CAAC;AAAA,IACH,OAAO;AACL,aAAO,GAAG,SAAS,kBAAkB,eAAe;AAAA,QAClD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,MAAM,CAAC;AAAA,UACpB,MAAM;AAAA,UACN,WAAW;AAAA,UACX,UAAU;AAAA,QACZ,CAAC;AAAA,QACH,SAAS,gBAAgB,MAAM;AAAA,MACjC,CAAC;AAAA,IACH;AACA,WAAO,GAAG,SAAS,YAAY,qBAAqB;AAAA,MAClD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU,IAAI,sBAAsB;AAAA,MACpC,SAAS,gBAAgB,MAAM;AAAA,IACjC,CAAC;AACD,wBAAoB,cAAc;AAAA,MAChC,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,MAC1B,SAAS;AAAA,IACX,CAAC;AACD,wBAAoB,eAAe;AAAA,MACjC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,iBAAiB;AAAA,MAC1B,SAAS;AAAA,IACX,CAAC;AACD,QAAI,SAAS,iBAAiB,IAAI,GAAG;AACnC,aAAO,GAAG,SAAS,kBAAkB,OAAO;AAAA,QAC1C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,SAAS,yIAAyI;AAAA,MACrK,CAAC;AAAA,IACH;AACA,QAAI,SAAS,oBAAoB,IAAI,GAAG;AACtC,aAAO,GAAG,SAAS,kBAAkB,UAAU;AAAA,QAC7C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,SAAS,gJAAgJ;AAAA,MAC5K,CAAC;AAAA,IACH;AACA,QAAI,SAAS,kBAAkB,IAAI,GAAG;AACpC,aAAO,GAAG,SAAS,kBAAkB,QAAQ;AAAA,QAC3C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,SAAS,gDAAgD;AAAA,MAC5E,CAAC;AAAA,IACH;AACA,WAAO,GAAG,SAAS,eAAe,SAAS;AAAA,MACzC,QAAQ,MAAM;AACZ,yBAAiB,aAAa;AAC9B,eAAO,iBAAiB,QAAQ,EAAE,KAAK,SAAS,EAAE,GAAG,aAAW;AAC9D,cAAI,KAAK,QAAQ,OAAO,MAAM,WAAW;AACvC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,UAAM,iBAAiB,eAAe,kBAAkB,MAAM,CAAC;AAC/D,QAAI,eAAe,WAAW,KAAK,OAAO,sBAAsB,qBAAqB,GAAG;AACtF,aAAO,GAAG,SAAS,kBAAkB,cAAc;AAAA,QACjD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,MAAM,eAAe,QAAQ,gBAAgB,cAAc,WAAS,OAAO,YAAY,uBAAuB,OAAO,KAAK,CAAC;AAAA,QAC5I,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,UAAM,qBAAqB,eAAe,iBAAiB,MAAM,CAAC;AAClE,QAAI,mBAAmB,WAAW,KAAK,OAAO,sBAAsB,yBAAyB,GAAG;AAC9F,aAAO,GAAG,SAAS,kBAAkB,kBAAkB;AAAA,QACrD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,MAAM,eAAe,QAAQ,oBAAoB,kBAAkB,WAAS,OAAO,YAAY,2BAA2B,OAAO,KAAK,CAAC;AAAA,QACxJ,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,sBAAsB,wBAAwB,GAAG;AAC1D,aAAO,GAAG,SAAS,kBAAkB,mBAAmB;AAAA,QACtD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,MAAM,eAAe,QAAQ,qBAAqB,0BAA0B,oBAAoB,QAAQ,gBAAgB,CAAC;AAAA,QAC1I,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AACD,aAAO,GAAG,SAAS,kBAAkB,wBAAwB;AAAA,QAC3D,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,MAAM,eAAe,QAAQ,qBAAqB,MAAM,GAAG,wBAAwB,oBAAoB,QAAQ,cAAc,CAAC;AAAA,QAC/I,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AACD,aAAO,GAAG,SAAS,kBAAkB,wBAAwB;AAAA,QAC3D,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,MAAM,eAAe,QAAQ,qBAAqB,MAAM,GAAG,wBAAwB,oBAAoB,QAAQ,cAAc,CAAC;AAAA,QAC/I,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AACD,aAAO,GAAG,SAAS,kBAAkB,4BAA4B;AAAA,QAC/D,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,MAAM,eAAe,QAAQ,2BAA2B,MAAM,GAAG,kBAAkB;AAAA,QACpG,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AACD,aAAO,GAAG,SAAS,kBAAkB,wBAAwB;AAAA,QAC3D,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,MAAM,eAAe,QAAQ,uBAAuB,MAAM,GAAG,cAAc;AAAA,QAC5F,SAAS,iBAAiB;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,8BAA0B,gBAAgB;AAAA,MACxC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,8BAA0B,kBAAkB;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU,gBAAgB,MAAM;AAAA,MAChC,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,8BAA0B,kBAAkB;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU,mBAAmB,MAAM;AAAA,MACnC,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AAAA,EACH;AAEA,QAAM,SAAS,YAAU;AACvB,UAAM,mBAAmB,oBAAoB,MAAM;AACnD,aAAS,MAAM;AACf,qBAAiB,MAAM;AACvB,iBAAa,QAAQ,gBAAgB;AACrC,eAAW,QAAQ,gBAAgB;AACnC,gBAAY,MAAM;AAAA,EACpB;AACA,MAAI,WAAW,MAAM;AACnB,aAAS,IAAI,SAAS,MAAM;AAAA,EAC9B;AAEA,WAAS;AAEb,GAAG;", "names": ["type", "all", "name", "key", "is", "ancestor", "selector", "size", "cell", "rows", "columns", "cells", "table", "parent", "detail", "bounds", "last", "children", "get", "set", "document", "universe", "head", "rgbaColour", "api", "adt", "dom", "elm", "hasAdvancedRowTab", "hasAdvancedCellTab", "ephemera", "targets"]}