#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0\node_modules\knex\bin\node_modules;D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0\node_modules\knex\node_modules;D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0\node_modules;D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0/node_modules/knex/bin/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0/node_modules/knex/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../knex/bin/cli.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../knex/bin/cli.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../knex/bin/cli.js" $args
  } else {
    & "node$exe"  "$basedir/../knex/bin/cli.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
