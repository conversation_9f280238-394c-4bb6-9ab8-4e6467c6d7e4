import{f as m,l as o,m as a}from"./match-Bpy-C_Mf.js";import{c as t}from"./index.DUmRo3Ep.entry.js";import{f as e}from"./formatRelative-Dv7OIDgt.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";const i={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},r={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},s={full:"{{date}} 'à' {{time}}",long:"{{date}} 'à' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},f={date:t({formats:i,defaultWidth:"full"}),time:t({formats:r,defaultWidth:"full"}),dateTime:t({formats:s,defaultWidth:"full"})},z={code:"fr",formatDistance:m,formatLong:f,formatRelative:e,localize:o,match:a,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{z as default,z as fr};
