import{g as a}from"./index-C0qcAVKU.js";import{e as p}from"./index.DUmRo3Ep.entry.js";function l(u,f){for(var o=0;o<f.length;o++){const i=f[o];if(typeof i!="string"&&!Array.isArray(i)){for(const r in i)if(r!=="default"&&!(r in u)){const e=Object.getOwnPropertyDescriptor(i,r);e&&Object.defineProperty(u,r,e.get?e:{enumerable:!0,get:()=>i[r]})}}}return Object.freeze(Object.defineProperty(u,Symbol.toStringTag,{value:"Module"}))}var y={exports:{}};(function(u,f){(function(o){o(p())})(function(o){o.defineOption("autoRefresh",!1,function(e,t){e.state.autoRefresh&&(r(e,e.state.autoRefresh),e.state.autoRefresh=null),t&&e.display.wrapper.offsetHeight==0&&i(e,e.state.autoRefresh={delay:t.delay||250})});function i(e,t){function n(){e.display.wrapper.offsetHeight?(r(e,t),e.display.lastWrapHeight!=e.display.wrapper.clientHeight&&e.refresh()):t.timeout=setTimeout(n,t.delay)}t.timeout=setTimeout(n,t.delay),t.hurry=function(){clearTimeout(t.timeout),t.timeout=setTimeout(n,50)},o.on(window,"mouseup",t.hurry),o.on(window,"keyup",t.hurry)}function r(e,t){clearTimeout(t.timeout),o.off(window,"mouseup",t.hurry),o.off(window,"keyup",t.hurry)}})})();var s=y.exports;const h=a(s),g=l({__proto__:null,default:h},[s]);export{g as a};
