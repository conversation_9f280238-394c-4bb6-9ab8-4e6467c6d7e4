import{g as f}from"./index-C0qcAVKU.js";import{r as i}from"./stex-CgsXXWqD.js";function c(r,n){for(var o=0;o<n.length;o++){const e=n[o];if(typeof e!="string"&&!Array.isArray(e)){for(const t in e)if(t!=="default"&&!(t in r)){const s=Object.getOwnPropertyDescriptor(e,t);s&&Object.defineProperty(r,t,s.get?s:{enumerable:!0,get:()=>e[t]})}}}return Object.freeze(Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}))}var a=i();const p=f(a),l=c({__proto__:null,default:p},[a]);export{l as s};
