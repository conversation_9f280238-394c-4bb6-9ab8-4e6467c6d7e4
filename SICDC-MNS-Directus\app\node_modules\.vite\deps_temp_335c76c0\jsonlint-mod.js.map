{"version": 3, "sources": ["../../../../node_modules/.pnpm/jsonlint-mod@1.7.6/node_modules/jsonlint-mod/web/jsonlint.js"], "sourcesContent": ["var jsonlint = (function(){var require=true,module=false;var exports={};/*\n    json_parse.js\n    2016-05-02\n\n    Public Domain.\n\n    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\n    This file creates a json_parse function.\n\n        json_parse(text, reviver)\n            This method parses a JSON text to produce an object or array.\n            It can throw a SyntaxError exception.\n\n            The optional reviver parameter is a function that can filter and\n            transform the results. It receives each of the keys and values,\n            and its return value is used instead of the original value.\n            If it returns what it received, then the structure is not modified.\n            If it returns undefined then the member is deleted.\n\n            Example:\n\n            // Parse the text. Values that look like ISO date strings will\n            // be converted to Date objects.\n\n            myData = json_parse(text, function (key, value) {\n                var a;\n                if (typeof value === \"string\") {\n                    a =\n/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n                    if (a) {\n                        return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n                            +a[5], +a[6]));\n                    }\n                }\n                return value;\n            });\n\n    This is a reference implementation. You are free to copy, modify, or\n    redistribute.\n\n    This code should be minified before deployment.\n    See http://javascript.crockford.com/jsmin.html\n\n    USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n    NOT CONTROL.\n*/\n\n/*jslint for */\n\n/*property\n    at, b, call, charAt, f, fromCharCode, hasOwnProperty, message, n, name,\n    prototype, push, r, t, text\n*/\n\nvar ___dougJSONParse = (function () {\n    \"use strict\";\n\n// This is a function that can parse a JSON text, producing a JavaScript\n// data structure. It is a simple, recursive descent parser. It does not use\n// eval or regular expressions, so it can be used as a model for implementing\n// a JSON parser in other languages.\n\n// We are defining the function inside of another function to avoid creating\n// global variables.\n\n    var at;     // The index of the current character\n    var ch;     // The current character\n    var escapee = {\n        \"\\\"\": \"\\\"\",\n        \"\\\\\": \"\\\\\",\n        \"/\": \"/\",\n        b: \"\\b\",\n        f: \"\\f\",\n        n: \"\\n\",\n        r: \"\\r\",\n        t: \"\\t\"\n    };\n    var text;\n\n    var error = function (m) {\n\n// Call error when something is wrong.\n\n        throw {\n            name: \"SyntaxError\",\n            message: m,\n            at: at,\n            text: text\n        };\n    };\n\n    var next = function (c) {\n\n// If a c parameter is provided, verify that it matches the current character.\n\n        if (c && c !== ch) {\n            error(\"Expected '\" + c + \"' instead of '\" + ch + \"'\");\n        }\n\n// Get the next character. When there are no more characters,\n// return the empty string.\n\n        ch = text.charAt(at);\n        at += 1;\n        return ch;\n    };\n\n    var number = function () {\n\n// Parse a number value.\n\n        var value;\n        var string = \"\";\n\n        if (ch === \"-\") {\n            string = \"-\";\n            next(\"-\");\n        }\n        while (ch >= \"0\" && ch <= \"9\") {\n            string += ch;\n            next();\n        }\n        if (ch === \".\") {\n            string += \".\";\n            while (next() && ch >= \"0\" && ch <= \"9\") {\n                string += ch;\n            }\n        }\n        if (ch === \"e\" || ch === \"E\") {\n            string += ch;\n            next();\n            if (ch === \"-\" || ch === \"+\") {\n                string += ch;\n                next();\n            }\n            while (ch >= \"0\" && ch <= \"9\") {\n                string += ch;\n                next();\n            }\n        }\n        value = +string;\n        if (!isFinite(value)) {\n            error(\"Bad number\");\n        } else {\n            return value;\n        }\n    };\n\n    var string = function () {\n\n// Parse a string value.\n\n        var hex;\n        var i;\n        var value = \"\";\n        var uffff;\n\n// When parsing for string values, we must look for \" and \\ characters.\n\n        if (ch === \"\\\"\") {\n            while (next()) {\n                if (ch === \"\\\"\") {\n                    next();\n                    return value;\n                }\n                if (ch === \"\\\\\") {\n                    next();\n                    if (ch === \"u\") {\n                        uffff = 0;\n                        for (i = 0; i < 4; i += 1) {\n                            hex = parseInt(next(), 16);\n                            if (!isFinite(hex)) {\n                                break;\n                            }\n                            uffff = uffff * 16 + hex;\n                        }\n                        value += String.fromCharCode(uffff);\n                    } else if (typeof escapee[ch] === \"string\") {\n                        value += escapee[ch];\n                    } else {\n                        break;\n                    }\n                } else {\n                    value += ch;\n                }\n            }\n        }\n        error(\"Bad string\");\n    };\n\n    var white = function () {\n\n// Skip whitespace.\n\n        while (ch && ch <= \" \") {\n            next();\n        }\n    };\n\n    var word = function () {\n\n// true, false, or null.\n\n        switch (ch) {\n        case \"t\":\n            next(\"t\");\n            next(\"r\");\n            next(\"u\");\n            next(\"e\");\n            return true;\n        case \"f\":\n            next(\"f\");\n            next(\"a\");\n            next(\"l\");\n            next(\"s\");\n            next(\"e\");\n            return false;\n        case \"n\":\n            next(\"n\");\n            next(\"u\");\n            next(\"l\");\n            next(\"l\");\n            return null;\n        }\n        error(\"Unexpected '\" + ch + \"'\");\n    };\n\n    var value;  // Place holder for the value function.\n\n    var array = function () {\n\n// Parse an array value.\n\n        var arr = [];\n\n        if (ch === \"[\") {\n            next(\"[\");\n            white();\n            if (ch === \"]\") {\n                next(\"]\");\n                return arr;   // empty array\n            }\n            while (ch) {\n                arr.push(value());\n                white();\n                if (ch === \"]\") {\n                    next(\"]\");\n                    return arr;\n                }\n                next(\",\");\n                white();\n            }\n        }\n        error(\"Bad array\");\n    };\n\n    var object = function () {\n\n// Parse an object value.\n\n        var key;\n        var obj = {};\n\n        if (ch === \"{\") {\n            next(\"{\");\n            white();\n            if (ch === \"}\") {\n                next(\"}\");\n                return obj;   // empty object\n            }\n            while (ch) {\n                key = string();\n                white();\n                next(\":\");\n                if (Object.hasOwnProperty.call(obj, key)) {\n                    error(\"Duplicate key '\" + key + \"'\");\n                }\n                obj[key] = value();\n                white();\n                if (ch === \"}\") {\n                    next(\"}\");\n                    return obj;\n                }\n                next(\",\");\n                white();\n            }\n        }\n        error(\"Bad object\");\n    };\n\n    value = function () {\n\n// Parse a JSON value. It could be an object, an array, a string, a number,\n// or a word.\n\n        white();\n        switch (ch) {\n        case \"{\":\n            return object();\n        case \"[\":\n            return array();\n        case \"\\\"\":\n            return string();\n        case \"-\":\n            return number();\n        default:\n            return (ch >= \"0\" && ch <= \"9\")\n                ? number()\n                : word();\n        }\n    };\n\n// Return the json_parse function. It will have access to all of the above\n// functions and variables.\n\n    return function (source, reviver) {\n        var result;\n\n        text = source;\n        at = 0;\n        ch = \" \";\n        result = value();\n        white();\n        if (ch) {\n            error(\"Syntax error\");\n        }\n\n// If there is a reviver function, we recursively walk the new structure,\n// passing each name/value pair to the reviver function for possible\n// transformation, starting with a temporary root object that holds the result\n// in an empty key. If there is not a reviver function, we simply return the\n// result.\n\n        return (typeof reviver === \"function\")\n            ? (function walk(holder, key) {\n                var k;\n                var v;\n                var val = holder[key];\n                if (val && typeof val === \"object\") {\n                    for (k in val) {\n                        if (Object.prototype.hasOwnProperty.call(val, k)) {\n                            v = walk(val, k);\n                            if (v !== undefined) {\n                                val[k] = v;\n                            } else {\n                                delete val[k];\n                            }\n                        }\n                    }\n                }\n                return reviver.call(holder, key, val);\n            }({\"\": result}, \"\"))\n            : result;\n    };\n}());\n\nif(typeof module === 'object' && module.exports) {\n    module.exports = ___dougJSONParse;\n}\n/* Jison generated parser */\nvar jsonlint = (function(){\nvar parser = {trace: function trace() { },\nyy: {},\nsymbols_: {\"error\":2,\"JSONString\":3,\"STRING\":4,\"JSONNumber\":5,\"NUMBER\":6,\"JSONNullLiteral\":7,\"NULL\":8,\"JSONBooleanLiteral\":9,\"TRUE\":10,\"FALSE\":11,\"JSONText\":12,\"JSONValue\":13,\"EOF\":14,\"JSONObject\":15,\"JSONArray\":16,\"{\":17,\"}\":18,\"JSONMemberList\":19,\"JSONMember\":20,\":\":21,\",\":22,\"[\":23,\"]\":24,\"JSONElementList\":25,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"STRING\",6:\"NUMBER\",8:\"NULL\",10:\"TRUE\",11:\"FALSE\",14:\"EOF\",17:\"{\",18:\"}\",21:\":\",22:\",\",23:\"[\",24:\"]\"},\nproductions_: [0,[3,1],[5,1],[7,1],[9,1],[9,1],[12,2],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[15,2],[15,3],[20,3],[19,1],[19,3],[16,2],[16,3],[25,1],[25,3]],\nperformAction: function anonymous(yytext,yyleng,yylineno,yy,yystate,$$,_$) {\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 1: // replace escaped characters with actual character\n          this.$ = yytext.replace(/\\\\(\\\\|\")/g, \"$\"+\"1\")\n                     .replace(/\\\\n/g,'\\n')\n                     .replace(/\\\\r/g,'\\r')\n                     .replace(/\\\\t/g,'\\t')\n                     .replace(/\\\\v/g,'\\v')\n                     .replace(/\\\\f/g,'\\f')\n                     .replace(/\\\\b/g,'\\b');\n\nbreak;\ncase 2:this.$ = Number(yytext);\nbreak;\ncase 3:this.$ = null;\nbreak;\ncase 4:this.$ = true;\nbreak;\ncase 5:this.$ = false;\nbreak;\ncase 6:return this.$ = $$[$0-1];\nbreak;\ncase 13:this.$ = {};\nbreak;\ncase 14:this.$ = $$[$0-1];\nbreak;\ncase 15:this.$ = [$$[$0-2], $$[$0]];\nbreak;\ncase 16:this.$ = {}; this.$[$$[$0][0]] = $$[$0][1];\nbreak;\ncase 17:this.$ = $$[$0-2]; $$[$0-2][$$[$0][0]] = $$[$0][1];\nbreak;\ncase 18:this.$ = [];\nbreak;\ncase 19:this.$ = $$[$0-1];\nbreak;\ncase 20:this.$ = [$$[$0]];\nbreak;\ncase 21:this.$ = $$[$0-2]; $$[$0-2].push($$[$0]);\nbreak;\n}\n},\ntable: [{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],12:1,13:2,15:7,16:8,17:[1,14],23:[1,15]},{1:[3]},{14:[1,16]},{14:[2,7],18:[2,7],22:[2,7],24:[2,7]},{14:[2,8],18:[2,8],22:[2,8],24:[2,8]},{14:[2,9],18:[2,9],22:[2,9],24:[2,9]},{14:[2,10],18:[2,10],22:[2,10],24:[2,10]},{14:[2,11],18:[2,11],22:[2,11],24:[2,11]},{14:[2,12],18:[2,12],22:[2,12],24:[2,12]},{14:[2,3],18:[2,3],22:[2,3],24:[2,3]},{14:[2,4],18:[2,4],22:[2,4],24:[2,4]},{14:[2,5],18:[2,5],22:[2,5],24:[2,5]},{14:[2,1],18:[2,1],21:[2,1],22:[2,1],24:[2,1]},{14:[2,2],18:[2,2],22:[2,2],24:[2,2]},{3:20,4:[1,12],18:[1,17],19:18,20:19},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:23,15:7,16:8,17:[1,14],23:[1,15],24:[1,21],25:22},{1:[2,6]},{14:[2,13],18:[2,13],22:[2,13],24:[2,13]},{18:[1,24],22:[1,25]},{18:[2,16],22:[2,16]},{21:[1,26]},{14:[2,18],18:[2,18],22:[2,18],24:[2,18]},{22:[1,28],24:[1,27]},{22:[2,20],24:[2,20]},{14:[2,14],18:[2,14],22:[2,14],24:[2,14]},{3:20,4:[1,12],20:29},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:30,15:7,16:8,17:[1,14],23:[1,15]},{14:[2,19],18:[2,19],22:[2,19],24:[2,19]},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:31,15:7,16:8,17:[1,14],23:[1,15]},{18:[2,17],22:[2,17]},{18:[2,15],22:[2,15]},{22:[2,21],24:[2,21]}],\ndefaultActions: {16:[2,6]},\nparseError: function parseError(str, hash) {\n    throw new Error(str);\n},\nparse: function parse(input) {\n    var self = this,\n        stack = [0],\n        vstack = [null], // semantic value stack\n        lstack = [], // location stack\n        table = this.table,\n        yytext = '',\n        yylineno = 0,\n        yyleng = 0,\n        recovering = 0,\n        TERROR = 2,\n        EOF = 1;\n\n    //this.reductionCount = this.shiftCount = 0;\n\n    this.lexer.setInput(input);\n    this.lexer.yy = this.yy;\n    this.yy.lexer = this.lexer;\n    if (typeof this.lexer.yylloc == 'undefined')\n        this.lexer.yylloc = {};\n    var yyloc = this.lexer.yylloc;\n    lstack.push(yyloc);\n\n    if (typeof this.yy.parseError === 'function')\n        this.parseError = this.yy.parseError;\n\n    function popStack (n) {\n        stack.length = stack.length - 2*n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n\n    function lex() {\n        var token;\n        token = self.lexer.lex() || 1; // $end = 1\n        // if token isn't its numeric value, convert\n        if (typeof token !== 'number') {\n            token = self.symbols_[token] || token;\n        }\n        return token;\n    }\n\n    var symbol, preErrorSymbol, state, action, a, r, yyval={},p,len,newState, expected;\n    while (true) {\n        // retreive state number from top of stack\n        state = stack[stack.length-1];\n\n        // use default actions if available\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol == null)\n                symbol = lex();\n            // read action for current state and first input\n            action = table[state] && table[state][symbol];\n        }\n\n        // handle parse error\n        _handle_error:\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n\n            if (!recovering) {\n                // Report error\n                expected = [];\n                for (p in table[state]) if (this.terminals_[p] && p > 2) {\n                    expected.push(\"'\"+this.terminals_[p]+\"'\");\n                }\n                var errStr = '';\n                if (this.lexer.showPosition) {\n                    errStr = 'Parse error on line '+(yylineno+1)+\":\\n\"+this.lexer.showPosition()+\"\\nExpecting \"+expected.join(', ') + \", got '\" + this.terminals_[symbol]+ \"'\";\n                } else {\n                    errStr = 'Parse error on line '+(yylineno+1)+\": Unexpected \" +\n                                  (symbol == 1 /*EOF*/ ? \"end of input\" :\n                                              (\"'\"+(this.terminals_[symbol] || symbol)+\"'\"));\n                }\n                this.parseError(errStr,\n                    {text: this.lexer.match, token: this.terminals_[symbol] || symbol, line: this.lexer.yylineno, loc: yyloc, expected: expected});\n            }\n\n            // just recovered from another error\n            if (recovering == 3) {\n                if (symbol == EOF) {\n                    throw new Error(errStr || 'Parsing halted.');\n                }\n\n                // discard current lookahead and grab another\n                yyleng = this.lexer.yyleng;\n                yytext = this.lexer.yytext;\n                yylineno = this.lexer.yylineno;\n                yyloc = this.lexer.yylloc;\n                symbol = lex();\n            }\n\n            // try to recover from error\n            while (1) {\n                // check for error recovery rule in this state\n                if ((TERROR.toString()) in table[state]) {\n                    break;\n                }\n                if (state == 0) {\n                    throw new Error(errStr || 'Parsing halted.');\n                }\n                popStack(1);\n                state = stack[stack.length-1];\n            }\n\n            preErrorSymbol = symbol; // save the lookahead token\n            symbol = TERROR;         // insert generic error symbol as new lookahead\n            state = stack[stack.length-1];\n            action = table[state] && table[state][TERROR];\n            recovering = 3; // allow 3 real symbols to be shifted before reporting a new error\n        }\n\n        // this shouldn't happen, unless resolve defaults are off\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: '+state+', token: '+symbol);\n        }\n\n        switch (action[0]) {\n\n            case 1: // shift\n                //this.shiftCount++;\n\n                stack.push(symbol);\n                vstack.push(this.lexer.yytext);\n                lstack.push(this.lexer.yylloc);\n                stack.push(action[1]); // push state\n                symbol = null;\n                if (!preErrorSymbol) { // normal execution/no error\n                    yyleng = this.lexer.yyleng;\n                    yytext = this.lexer.yytext;\n                    yylineno = this.lexer.yylineno;\n                    yyloc = this.lexer.yylloc;\n                    if (recovering > 0)\n                        recovering--;\n                } else { // error just occurred, resume old lookahead f/ before error\n                    symbol = preErrorSymbol;\n                    preErrorSymbol = null;\n                }\n                break;\n\n            case 2: // reduce\n                //this.reductionCount++;\n\n                len = this.productions_[action[1]][1];\n\n                // perform semantic action\n                yyval.$ = vstack[vstack.length-len]; // default to $$ = $1\n                // default location, uses first token for firsts, last for lasts\n                yyval._$ = {\n                    first_line: lstack[lstack.length-(len||1)].first_line,\n                    last_line: lstack[lstack.length-1].last_line,\n                    first_column: lstack[lstack.length-(len||1)].first_column,\n                    last_column: lstack[lstack.length-1].last_column\n                };\n                r = this.performAction.call(yyval, yytext, yyleng, yylineno, this.yy, action[1], vstack, lstack);\n\n                if (typeof r !== 'undefined') {\n                    return r;\n                }\n\n                // pop off stack\n                if (len) {\n                    stack = stack.slice(0,-1*len*2);\n                    vstack = vstack.slice(0, -1*len);\n                    lstack = lstack.slice(0, -1*len);\n                }\n\n                stack.push(this.productions_[action[1]][0]);    // push nonterminal (reduce)\n                vstack.push(yyval.$);\n                lstack.push(yyval._$);\n                // goto new state = table[STATE][NONTERMINAL]\n                newState = table[stack[stack.length-2]][stack[stack.length-1]];\n                stack.push(newState);\n                break;\n\n            case 3: // accept\n                return true;\n        }\n\n    }\n\n    return true;\n}};\n/* Jison generated lexer */\nvar lexer = (function(){\nvar lexer = ({EOF:1,\nparseError:function parseError(str, hash) {\n        if (this.yy.parseError) {\n            this.yy.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\nsetInput:function (input) {\n        this._input = input;\n        this._more = this._less = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {first_line:1,first_column:0,last_line:1,last_column:0};\n        return this;\n    },\ninput:function () {\n        var ch = this._input[0];\n        this.yytext+=ch;\n        this.yyleng++;\n        this.match+=ch;\n        this.matched+=ch;\n        var lines = ch.match(/\\n/);\n        if (lines) this.yylineno++;\n        this._input = this._input.slice(1);\n        return ch;\n    },\nunput:function (ch) {\n        this._input = ch + this._input;\n        return this;\n    },\nmore:function () {\n        this._more = true;\n        return this;\n    },\nless:function (n) {\n        this._input = this.match.slice(n) + this._input;\n    },\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20)+(next.length > 20 ? '...':'')).replace(/\\n/g, \"\");\n    },\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c+\"^\";\n    },\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) this.done = true;\n\n        var token,\n            match,\n            tempMatch,\n            index,\n            col,\n            lines;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i=0;i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (!this.options.flex) break;\n            }\n        }\n        if (match) {\n            lines = match[0].match(/\\n.*/g);\n            if (lines) this.yylineno += lines.length;\n            this.yylloc = {first_line: this.yylloc.last_line,\n                           last_line: this.yylineno+1,\n                           first_column: this.yylloc.last_column,\n                           last_column: lines ? lines[lines.length-1].length-1 : this.yylloc.last_column + match[0].length}\n            this.yytext += match[0];\n            this.match += match[0];\n            this.yyleng = this.yytext.length;\n            this._more = false;\n            this._input = this._input.slice(match[0].length);\n            this.matched += match[0];\n            token = this.performAction.call(this, this.yy, this, rules[index],this.conditionStack[this.conditionStack.length-1]);\n            if (this.done && this._input) this.done = false;\n            if (token) return token;\n            else return;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            this.parseError('Lexical error on line '+(this.yylineno+1)+'. Unrecognized text.\\n'+this.showPosition(),\n                    {text: \"\", token: null, line: this.yylineno});\n        }\n    },\nlex:function lex() {\n        var r = this.next();\n        if (typeof r !== 'undefined') {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\nbegin:function begin(condition) {\n        this.conditionStack.push(condition);\n    },\npopState:function popState() {\n        return this.conditionStack.pop();\n    },\n_currentRules:function _currentRules() {\n        return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules;\n    },\ntopState:function () {\n        return this.conditionStack[this.conditionStack.length-2];\n    },\npushState:function begin(condition) {\n        this.begin(condition);\n    }});\nlexer.options = {};\nlexer.performAction = function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\n\nvar YYSTATE=YY_START\nswitch($avoiding_name_collisions) {\ncase 0:/* skip whitespace */\nbreak;\ncase 1:return 6\nbreak;\ncase 2:yy_.yytext = yy_.yytext.substr(1,yy_.yyleng-2); return 4\nbreak;\ncase 3:return 17\nbreak;\ncase 4:return 18\nbreak;\ncase 5:return 23\nbreak;\ncase 6:return 24\nbreak;\ncase 7:return 22\nbreak;\ncase 8:return 21\nbreak;\ncase 9:return 10\nbreak;\ncase 10:return 11\nbreak;\ncase 11:return 8\nbreak;\ncase 12:return 14\nbreak;\ncase 13:return 'INVALID'\nbreak;\n}\n};\nlexer.rules = [/^(?:\\s+)/,/^(?:(-?([0-9]|[1-9][0-9]+))(\\.[0-9]+)?([eE][-+]?[0-9]+)?\\b)/,/^(?:\"(?:\\\\[\\\\\"bfnrt/]|\\\\u[a-fA-F0-9]{4}|[^\\\\\\0-\\x09\\x0a-\\x1f\"])*\")/,/^(?:\\{)/,/^(?:\\})/,/^(?:\\[)/,/^(?:\\])/,/^(?:,)/,/^(?::)/,/^(?:true\\b)/,/^(?:false\\b)/,/^(?:null\\b)/,/^(?:$)/,/^(?:.)/];\nlexer.conditions = {\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13],\"inclusive\":true}};\n\n\n;\nreturn lexer;})()\nparser.lexer = lexer;\nreturn parser;\n})();\n\nvar origParse = jsonlint.parse;\n\njsonlint.parse = function(input) {\n    var result = origParse.call(jsonlint, input);\n    var dougJSONParse = typeof ___dougJSONParse === 'undefined' ? require('./doug-json-parse') : ___dougJSONParse;\n    try {\n        dougJSONParse(input);\n    } catch(e) {\n        if(/Duplicate key|Bad string|Unexpected/.test(e.message)) {\n            var linesUntilError = input.substring(0, e.at).split('\\n');\n            var line = linesUntilError.length;\n            var col = linesUntilError[line - 1].length - 1;\n\n            this.parseError(e.message, {line: line, col: col, message: e.message.replace(/./, function(l) { return l.toLowerCase(); })});\n            throw SyntaxError(e.message + ' on line ' + line);\n        }\n    }\n\n    return result;\n}\n\nif (typeof require !== 'undefined' && typeof exports !== 'undefined') {\nexports.parser = jsonlint;\nexports.parse = function () { return jsonlint.parse.apply(jsonlint, arguments); }\nexports.main = function commonjsMain(args) {\n    if (!args[1])\n        throw new Error('Usage: '+args[0]+' FILE');\n    if (typeof process !== 'undefined') {\n        var source = require('fs').readFileSync(require('path').join(process.cwd(), args[1]), \"utf8\");\n    } else {\n        var cwd = require(\"file\").path(require(\"file\").cwd());\n        var source = cwd.join(args[1]).read({charset: \"utf-8\"});\n    }\n    return exports.parser.parse(source);\n}\nif (typeof module !== 'undefined' && require.main === module) {\n  exports.main(typeof process !== 'undefined' ? process.argv.slice(1) : require(\"system\").args);\n}\n}\nreturn exports;})();if(typeof module === 'object' && module.exports) module.exports = jsonlint;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAI,WAAY,WAAU;AAAC,UAAIA,WAAQ,MAAKC,UAAO;AAAM,UAAIC,WAAQ,CAAC;AAuDtE,UAAI,mBAAoB,WAAY;AAChC;AAUA,YAAI;AACJ,YAAI;AACJ,YAAI,UAAU;AAAA,UACV,KAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACP;AACA,YAAI;AAEJ,YAAI,QAAQ,SAAU,GAAG;AAIrB,gBAAM;AAAA,YACF,MAAM;AAAA,YACN,SAAS;AAAA,YACT;AAAA,YACA;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,OAAO,SAAU,GAAG;AAIpB,cAAI,KAAK,MAAM,IAAI;AACf,kBAAM,eAAe,IAAI,mBAAmB,KAAK,GAAG;AAAA,UACxD;AAKA,eAAK,KAAK,OAAO,EAAE;AACnB,gBAAM;AACN,iBAAO;AAAA,QACX;AAEA,YAAI,SAAS,WAAY;AAIrB,cAAIC;AACJ,cAAIC,UAAS;AAEb,cAAI,OAAO,KAAK;AACZ,YAAAA,UAAS;AACT,iBAAK,GAAG;AAAA,UACZ;AACA,iBAAO,MAAM,OAAO,MAAM,KAAK;AAC3B,YAAAA,WAAU;AACV,iBAAK;AAAA,UACT;AACA,cAAI,OAAO,KAAK;AACZ,YAAAA,WAAU;AACV,mBAAO,KAAK,KAAK,MAAM,OAAO,MAAM,KAAK;AACrC,cAAAA,WAAU;AAAA,YACd;AAAA,UACJ;AACA,cAAI,OAAO,OAAO,OAAO,KAAK;AAC1B,YAAAA,WAAU;AACV,iBAAK;AACL,gBAAI,OAAO,OAAO,OAAO,KAAK;AAC1B,cAAAA,WAAU;AACV,mBAAK;AAAA,YACT;AACA,mBAAO,MAAM,OAAO,MAAM,KAAK;AAC3B,cAAAA,WAAU;AACV,mBAAK;AAAA,YACT;AAAA,UACJ;AACA,UAAAD,SAAQ,CAACC;AACT,cAAI,CAAC,SAASD,MAAK,GAAG;AAClB,kBAAM,YAAY;AAAA,UACtB,OAAO;AACH,mBAAOA;AAAA,UACX;AAAA,QACJ;AAEA,YAAI,SAAS,WAAY;AAIrB,cAAI;AACJ,cAAI;AACJ,cAAIA,SAAQ;AACZ,cAAI;AAIJ,cAAI,OAAO,KAAM;AACb,mBAAO,KAAK,GAAG;AACX,kBAAI,OAAO,KAAM;AACb,qBAAK;AACL,uBAAOA;AAAA,cACX;AACA,kBAAI,OAAO,MAAM;AACb,qBAAK;AACL,oBAAI,OAAO,KAAK;AACZ,0BAAQ;AACR,uBAAK,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AACvB,0BAAM,SAAS,KAAK,GAAG,EAAE;AACzB,wBAAI,CAAC,SAAS,GAAG,GAAG;AAChB;AAAA,oBACJ;AACA,4BAAQ,QAAQ,KAAK;AAAA,kBACzB;AACA,kBAAAA,UAAS,OAAO,aAAa,KAAK;AAAA,gBACtC,WAAW,OAAO,QAAQ,EAAE,MAAM,UAAU;AACxC,kBAAAA,UAAS,QAAQ,EAAE;AAAA,gBACvB,OAAO;AACH;AAAA,gBACJ;AAAA,cACJ,OAAO;AACH,gBAAAA,UAAS;AAAA,cACb;AAAA,YACJ;AAAA,UACJ;AACA,gBAAM,YAAY;AAAA,QACtB;AAEA,YAAI,QAAQ,WAAY;AAIpB,iBAAO,MAAM,MAAM,KAAK;AACpB,iBAAK;AAAA,UACT;AAAA,QACJ;AAEA,YAAI,OAAO,WAAY;AAInB,kBAAQ,IAAI;AAAA,YACZ,KAAK;AACD,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,qBAAO;AAAA,YACX,KAAK;AACD,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,qBAAO;AAAA,YACX,KAAK;AACD,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,qBAAO;AAAA,UACX;AACA,gBAAM,iBAAiB,KAAK,GAAG;AAAA,QACnC;AAEA,YAAI;AAEJ,YAAI,QAAQ,WAAY;AAIpB,cAAI,MAAM,CAAC;AAEX,cAAI,OAAO,KAAK;AACZ,iBAAK,GAAG;AACR,kBAAM;AACN,gBAAI,OAAO,KAAK;AACZ,mBAAK,GAAG;AACR,qBAAO;AAAA,YACX;AACA,mBAAO,IAAI;AACP,kBAAI,KAAK,MAAM,CAAC;AAChB,oBAAM;AACN,kBAAI,OAAO,KAAK;AACZ,qBAAK,GAAG;AACR,uBAAO;AAAA,cACX;AACA,mBAAK,GAAG;AACR,oBAAM;AAAA,YACV;AAAA,UACJ;AACA,gBAAM,WAAW;AAAA,QACrB;AAEA,YAAI,SAAS,WAAY;AAIrB,cAAI;AACJ,cAAI,MAAM,CAAC;AAEX,cAAI,OAAO,KAAK;AACZ,iBAAK,GAAG;AACR,kBAAM;AACN,gBAAI,OAAO,KAAK;AACZ,mBAAK,GAAG;AACR,qBAAO;AAAA,YACX;AACA,mBAAO,IAAI;AACP,oBAAM,OAAO;AACb,oBAAM;AACN,mBAAK,GAAG;AACR,kBAAI,OAAO,eAAe,KAAK,KAAK,GAAG,GAAG;AACtC,sBAAM,oBAAoB,MAAM,GAAG;AAAA,cACvC;AACA,kBAAI,GAAG,IAAI,MAAM;AACjB,oBAAM;AACN,kBAAI,OAAO,KAAK;AACZ,qBAAK,GAAG;AACR,uBAAO;AAAA,cACX;AACA,mBAAK,GAAG;AACR,oBAAM;AAAA,YACV;AAAA,UACJ;AACA,gBAAM,YAAY;AAAA,QACtB;AAEA,gBAAQ,WAAY;AAKhB,gBAAM;AACN,kBAAQ,IAAI;AAAA,YACZ,KAAK;AACD,qBAAO,OAAO;AAAA,YAClB,KAAK;AACD,qBAAO,MAAM;AAAA,YACjB,KAAK;AACD,qBAAO,OAAO;AAAA,YAClB,KAAK;AACD,qBAAO,OAAO;AAAA,YAClB;AACI,qBAAQ,MAAM,OAAO,MAAM,MACrB,OAAO,IACP,KAAK;AAAA,UACf;AAAA,QACJ;AAKA,eAAO,SAAU,QAAQ,SAAS;AAC9B,cAAI;AAEJ,iBAAO;AACP,eAAK;AACL,eAAK;AACL,mBAAS,MAAM;AACf,gBAAM;AACN,cAAI,IAAI;AACJ,kBAAM,cAAc;AAAA,UACxB;AAQA,iBAAQ,OAAO,YAAY,aACpB,SAAS,KAAK,QAAQ,KAAK;AAC1B,gBAAI;AACJ,gBAAI;AACJ,gBAAI,MAAM,OAAO,GAAG;AACpB,gBAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,mBAAK,KAAK,KAAK;AACX,oBAAI,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,GAAG;AAC9C,sBAAI,KAAK,KAAK,CAAC;AACf,sBAAI,MAAM,QAAW;AACjB,wBAAI,CAAC,IAAI;AAAA,kBACb,OAAO;AACH,2BAAO,IAAI,CAAC;AAAA,kBAChB;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AACA,mBAAO,QAAQ,KAAK,QAAQ,KAAK,GAAG;AAAA,UACxC,EAAE,EAAC,IAAI,OAAM,GAAG,EAAE,IAChB;AAAA,QACV;AAAA,MACJ,EAAE;AAEF,UAAG,OAAOF,YAAW,YAAYA,QAAO,SAAS;AAC7C,QAAAA,QAAO,UAAU;AAAA,MACrB;AAEA,UAAII,YAAY,WAAU;AAC1B,YAAI,SAAS;AAAA,UAAC,OAAO,SAAS,QAAQ;AAAA,UAAE;AAAA,UACxC,IAAI,CAAC;AAAA,UACL,UAAU,EAAC,SAAQ,GAAE,cAAa,GAAE,UAAS,GAAE,cAAa,GAAE,UAAS,GAAE,mBAAkB,GAAE,QAAO,GAAE,sBAAqB,GAAE,QAAO,IAAG,SAAQ,IAAG,YAAW,IAAG,aAAY,IAAG,OAAM,IAAG,cAAa,IAAG,aAAY,IAAG,KAAI,IAAG,KAAI,IAAG,kBAAiB,IAAG,cAAa,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,mBAAkB,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,UAC9U,YAAY,EAAC,GAAE,SAAQ,GAAE,UAAS,GAAE,UAAS,GAAE,QAAO,IAAG,QAAO,IAAG,SAAQ,IAAG,OAAM,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG;AAAA,UAC7H,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,UAC9J,eAAe,SAAS,UAAU,QAAO,QAAO,UAAS,IAAG,SAAQ,IAAG,IAAI;AAE3E,gBAAI,KAAK,GAAG,SAAS;AACrB,oBAAQ,SAAS;AAAA,cACjB,KAAK;AACK,qBAAK,IAAI,OAAO,QAAQ,aAAa,IAAO,EAChC,QAAQ,QAAO,IAAI,EACnB,QAAQ,QAAO,IAAI,EACnB,QAAQ,QAAO,GAAI,EACnB,QAAQ,QAAO,IAAI,EACnB,QAAQ,QAAO,IAAI,EACnB,QAAQ,QAAO,IAAI;AAEzC;AAAA,cACA,KAAK;AAAE,qBAAK,IAAI,OAAO,MAAM;AAC7B;AAAA,cACA,KAAK;AAAE,qBAAK,IAAI;AAChB;AAAA,cACA,KAAK;AAAE,qBAAK,IAAI;AAChB;AAAA,cACA,KAAK;AAAE,qBAAK,IAAI;AAChB;AAAA,cACA,KAAK;AAAE,uBAAO,KAAK,IAAI,GAAG,KAAG,CAAC;AAC9B;AAAA,cACA,KAAK;AAAG,qBAAK,IAAI,CAAC;AAClB;AAAA,cACA,KAAK;AAAG,qBAAK,IAAI,GAAG,KAAG,CAAC;AACxB;AAAA,cACA,KAAK;AAAG,qBAAK,IAAI,CAAC,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAClC;AAAA,cACA,KAAK;AAAG,qBAAK,IAAI,CAAC;AAAG,qBAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;AACjD;AAAA,cACA,KAAK;AAAG,qBAAK,IAAI,GAAG,KAAG,CAAC;AAAG,mBAAG,KAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;AACzD;AAAA,cACA,KAAK;AAAG,qBAAK,IAAI,CAAC;AAClB;AAAA,cACA,KAAK;AAAG,qBAAK,IAAI,GAAG,KAAG,CAAC;AACxB;AAAA,cACA,KAAK;AAAG,qBAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AACxB;AAAA,cACA,KAAK;AAAG,qBAAK,IAAI,GAAG,KAAG,CAAC;AAAG,mBAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AAC/C;AAAA,YACA;AAAA,UACA;AAAA,UACA,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,IAAG,GAAE,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,GAAE,GAAE,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,GAAE,IAAG,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,GAAE,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,IAAG,GAAE,CAAC,GAAE,EAAE,GAAE,IAAG,GAAE,GAAE,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,GAAE,IAAG,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,GAAE,IAAG,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC;AAAA,UAC1wC,gBAAgB,EAAC,IAAG,CAAC,GAAE,CAAC,EAAC;AAAA,UACzB,YAAY,SAAS,WAAW,KAAK,MAAM;AACvC,kBAAM,IAAI,MAAM,GAAG;AAAA,UACvB;AAAA,UACA,OAAO,SAAS,MAAM,OAAO;AACzB,gBAAI,OAAO,MACP,QAAQ,CAAC,CAAC,GACV,SAAS,CAAC,IAAI,GACd,SAAS,CAAC,GACV,QAAQ,KAAK,OACb,SAAS,IACT,WAAW,GACX,SAAS,GACT,aAAa,GACb,SAAS,GACT,MAAM;AAIV,iBAAK,MAAM,SAAS,KAAK;AACzB,iBAAK,MAAM,KAAK,KAAK;AACrB,iBAAK,GAAG,QAAQ,KAAK;AACrB,gBAAI,OAAO,KAAK,MAAM,UAAU;AAC5B,mBAAK,MAAM,SAAS,CAAC;AACzB,gBAAI,QAAQ,KAAK,MAAM;AACvB,mBAAO,KAAK,KAAK;AAEjB,gBAAI,OAAO,KAAK,GAAG,eAAe;AAC9B,mBAAK,aAAa,KAAK,GAAG;AAE9B,qBAAS,SAAU,GAAG;AAClB,oBAAM,SAAS,MAAM,SAAS,IAAE;AAChC,qBAAO,SAAS,OAAO,SAAS;AAChC,qBAAO,SAAS,OAAO,SAAS;AAAA,YACpC;AAEA,qBAAS,MAAM;AACX,kBAAI;AACJ,sBAAQ,KAAK,MAAM,IAAI,KAAK;AAE5B,kBAAI,OAAO,UAAU,UAAU;AAC3B,wBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,cACpC;AACA,qBAAO;AAAA,YACX;AAEA,gBAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAM,CAAC,GAAE,GAAE,KAAI,UAAU;AAC1E,mBAAO,MAAM;AAET,sBAAQ,MAAM,MAAM,SAAO,CAAC;AAG5B,kBAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,yBAAS,KAAK,eAAe,KAAK;AAAA,cACtC,OAAO;AACH,oBAAI,UAAU;AACV,2BAAS,IAAI;AAEjB,yBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,cAChD;AAGA;AACA,oBAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAE/D,sBAAI,CAAC,YAAY;AAEb,+BAAW,CAAC;AACZ,yBAAK,KAAK,MAAM,KAAK;AAAG,0BAAI,KAAK,WAAW,CAAC,KAAK,IAAI,GAAG;AACrD,iCAAS,KAAK,MAAI,KAAK,WAAW,CAAC,IAAE,GAAG;AAAA,sBAC5C;AACA,wBAAI,SAAS;AACb,wBAAI,KAAK,MAAM,cAAc;AACzB,+BAAS,0BAAwB,WAAS,KAAG,QAAM,KAAK,MAAM,aAAa,IAAE,iBAAe,SAAS,KAAK,IAAI,IAAI,YAAY,KAAK,WAAW,MAAM,IAAG;AAAA,oBAC3J,OAAO;AACH,+BAAS,0BAAwB,WAAS,KAAG,mBAC9B,UAAU,IAAY,iBACV,OAAK,KAAK,WAAW,MAAM,KAAK,UAAQ;AAAA,oBACvE;AACA,yBAAK;AAAA,sBAAW;AAAA,sBACZ,EAAC,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,WAAW,MAAM,KAAK,QAAQ,MAAM,KAAK,MAAM,UAAU,KAAK,OAAO,SAAkB;AAAA,oBAAC;AAAA,kBACrI;AAGA,sBAAI,cAAc,GAAG;AACjB,wBAAI,UAAU,KAAK;AACf,4BAAM,IAAI,MAAM,UAAU,iBAAiB;AAAA,oBAC/C;AAGA,6BAAS,KAAK,MAAM;AACpB,6BAAS,KAAK,MAAM;AACpB,+BAAW,KAAK,MAAM;AACtB,4BAAQ,KAAK,MAAM;AACnB,6BAAS,IAAI;AAAA,kBACjB;AAGA,yBAAO,GAAG;AAEN,wBAAK,OAAO,SAAS,KAAM,MAAM,KAAK,GAAG;AACrC;AAAA,oBACJ;AACA,wBAAI,SAAS,GAAG;AACZ,4BAAM,IAAI,MAAM,UAAU,iBAAiB;AAAA,oBAC/C;AACA,6BAAS,CAAC;AACV,4BAAQ,MAAM,MAAM,SAAO,CAAC;AAAA,kBAChC;AAEA,mCAAiB;AACjB,2BAAS;AACT,0BAAQ,MAAM,MAAM,SAAO,CAAC;AAC5B,2BAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAC5C,+BAAa;AAAA,gBACjB;AAGA,kBAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,sBAAM,IAAI,MAAM,sDAAoD,QAAM,cAAY,MAAM;AAAA,cAChG;AAEA,sBAAQ,OAAO,CAAC,GAAG;AAAA,gBAEf,KAAK;AAGD,wBAAM,KAAK,MAAM;AACjB,yBAAO,KAAK,KAAK,MAAM,MAAM;AAC7B,yBAAO,KAAK,KAAK,MAAM,MAAM;AAC7B,wBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,2BAAS;AACT,sBAAI,CAAC,gBAAgB;AACjB,6BAAS,KAAK,MAAM;AACpB,6BAAS,KAAK,MAAM;AACpB,+BAAW,KAAK,MAAM;AACtB,4BAAQ,KAAK,MAAM;AACnB,wBAAI,aAAa;AACb;AAAA,kBACR,OAAO;AACH,6BAAS;AACT,qCAAiB;AAAA,kBACrB;AACA;AAAA,gBAEJ,KAAK;AAGD,wBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AAGpC,wBAAM,IAAI,OAAO,OAAO,SAAO,GAAG;AAElC,wBAAM,KAAK;AAAA,oBACP,YAAY,OAAO,OAAO,UAAQ,OAAK,EAAE,EAAE;AAAA,oBAC3C,WAAW,OAAO,OAAO,SAAO,CAAC,EAAE;AAAA,oBACnC,cAAc,OAAO,OAAO,UAAQ,OAAK,EAAE,EAAE;AAAA,oBAC7C,aAAa,OAAO,OAAO,SAAO,CAAC,EAAE;AAAA,kBACzC;AACA,sBAAI,KAAK,cAAc,KAAK,OAAO,QAAQ,QAAQ,UAAU,KAAK,IAAI,OAAO,CAAC,GAAG,QAAQ,MAAM;AAE/F,sBAAI,OAAO,MAAM,aAAa;AAC1B,2BAAO;AAAA,kBACX;AAGA,sBAAI,KAAK;AACL,4BAAQ,MAAM,MAAM,GAAE,KAAG,MAAI,CAAC;AAC9B,6BAAS,OAAO,MAAM,GAAG,KAAG,GAAG;AAC/B,6BAAS,OAAO,MAAM,GAAG,KAAG,GAAG;AAAA,kBACnC;AAEA,wBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,yBAAO,KAAK,MAAM,CAAC;AACnB,yBAAO,KAAK,MAAM,EAAE;AAEpB,6BAAW,MAAM,MAAM,MAAM,SAAO,CAAC,CAAC,EAAE,MAAM,MAAM,SAAO,CAAC,CAAC;AAC7D,wBAAM,KAAK,QAAQ;AACnB;AAAA,gBAEJ,KAAK;AACD,yBAAO;AAAA,cACf;AAAA,YAEJ;AAEA,mBAAO;AAAA,UACX;AAAA,QAAC;AAED,YAAI,QAAS,WAAU;AACvB,cAAIC,SAAS;AAAA,YAAC,KAAI;AAAA,YAClB,YAAW,SAAS,WAAW,KAAK,MAAM;AAClC,kBAAI,KAAK,GAAG,YAAY;AACpB,qBAAK,GAAG,WAAW,KAAK,IAAI;AAAA,cAChC,OAAO;AACH,sBAAM,IAAI,MAAM,GAAG;AAAA,cACvB;AAAA,YACJ;AAAA,YACJ,UAAS,SAAU,OAAO;AAClB,mBAAK,SAAS;AACd,mBAAK,QAAQ,KAAK,QAAQ,KAAK,OAAO;AACtC,mBAAK,WAAW,KAAK,SAAS;AAC9B,mBAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,mBAAK,iBAAiB,CAAC,SAAS;AAChC,mBAAK,SAAS,EAAC,YAAW,GAAE,cAAa,GAAE,WAAU,GAAE,aAAY,EAAC;AACpE,qBAAO;AAAA,YACX;AAAA,YACJ,OAAM,WAAY;AACV,kBAAI,KAAK,KAAK,OAAO,CAAC;AACtB,mBAAK,UAAQ;AACb,mBAAK;AACL,mBAAK,SAAO;AACZ,mBAAK,WAAS;AACd,kBAAI,QAAQ,GAAG,MAAM,IAAI;AACzB,kBAAI;AAAO,qBAAK;AAChB,mBAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,qBAAO;AAAA,YACX;AAAA,YACJ,OAAM,SAAU,IAAI;AACZ,mBAAK,SAAS,KAAK,KAAK;AACxB,qBAAO;AAAA,YACX;AAAA,YACJ,MAAK,WAAY;AACT,mBAAK,QAAQ;AACb,qBAAO;AAAA,YACX;AAAA,YACJ,MAAK,SAAU,GAAG;AACV,mBAAK,SAAS,KAAK,MAAM,MAAM,CAAC,IAAI,KAAK;AAAA,YAC7C;AAAA,YACJ,WAAU,WAAY;AACd,kBAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,sBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,YAC7E;AAAA,YACJ,eAAc,WAAY;AAClB,kBAAI,OAAO,KAAK;AAChB,kBAAI,KAAK,SAAS,IAAI;AAClB,wBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,cAChD;AACA,sBAAQ,KAAK,OAAO,GAAE,EAAE,KAAG,KAAK,SAAS,KAAK,QAAM,KAAK,QAAQ,OAAO,EAAE;AAAA,YAC9E;AAAA,YACJ,cAAa,WAAY;AACjB,kBAAI,MAAM,KAAK,UAAU;AACzB,kBAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,qBAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAE;AAAA,YACjD;AAAA,YACJ,MAAK,WAAY;AACT,kBAAI,KAAK,MAAM;AACX,uBAAO,KAAK;AAAA,cAChB;AACA,kBAAI,CAAC,KAAK;AAAQ,qBAAK,OAAO;AAE9B,kBAAI,OACA,OACA,WACA,OACA,KACA;AACJ,kBAAI,CAAC,KAAK,OAAO;AACb,qBAAK,SAAS;AACd,qBAAK,QAAQ;AAAA,cACjB;AACA,kBAAI,QAAQ,KAAK,cAAc;AAC/B,uBAAS,IAAE,GAAE,IAAI,MAAM,QAAQ,KAAK;AAChC,4BAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,oBAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,0BAAQ;AACR,0BAAQ;AACR,sBAAI,CAAC,KAAK,QAAQ;AAAM;AAAA,gBAC5B;AAAA,cACJ;AACA,kBAAI,OAAO;AACP,wBAAQ,MAAM,CAAC,EAAE,MAAM,OAAO;AAC9B,oBAAI;AAAO,uBAAK,YAAY,MAAM;AAClC,qBAAK,SAAS;AAAA,kBAAC,YAAY,KAAK,OAAO;AAAA,kBACxB,WAAW,KAAK,WAAS;AAAA,kBACzB,cAAc,KAAK,OAAO;AAAA,kBAC1B,aAAa,QAAQ,MAAM,MAAM,SAAO,CAAC,EAAE,SAAO,IAAI,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,gBAAM;AAC9G,qBAAK,UAAU,MAAM,CAAC;AACtB,qBAAK,SAAS,MAAM,CAAC;AACrB,qBAAK,SAAS,KAAK,OAAO;AAC1B,qBAAK,QAAQ;AACb,qBAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,qBAAK,WAAW,MAAM,CAAC;AACvB,wBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,MAAM,KAAK,GAAE,KAAK,eAAe,KAAK,eAAe,SAAO,CAAC,CAAC;AACnH,oBAAI,KAAK,QAAQ,KAAK;AAAQ,uBAAK,OAAO;AAC1C,oBAAI;AAAO,yBAAO;AAAA;AACb;AAAA,cACT;AACA,kBAAI,KAAK,WAAW,IAAI;AACpB,uBAAO,KAAK;AAAA,cAChB,OAAO;AACH,qBAAK;AAAA,kBAAW,4BAA0B,KAAK,WAAS,KAAG,2BAAyB,KAAK,aAAa;AAAA,kBAC9F,EAAC,MAAM,IAAI,OAAO,MAAM,MAAM,KAAK,SAAQ;AAAA,gBAAC;AAAA,cACxD;AAAA,YACJ;AAAA,YACJ,KAAI,SAAS,MAAM;AACX,kBAAI,IAAI,KAAK,KAAK;AAClB,kBAAI,OAAO,MAAM,aAAa;AAC1B,uBAAO;AAAA,cACX,OAAO;AACH,uBAAO,KAAK,IAAI;AAAA,cACpB;AAAA,YACJ;AAAA,YACJ,OAAM,SAAS,MAAM,WAAW;AACxB,mBAAK,eAAe,KAAK,SAAS;AAAA,YACtC;AAAA,YACJ,UAAS,SAAS,WAAW;AACrB,qBAAO,KAAK,eAAe,IAAI;AAAA,YACnC;AAAA,YACJ,eAAc,SAAS,gBAAgB;AAC/B,qBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAO,CAAC,CAAC,EAAE;AAAA,YAC9E;AAAA,YACJ,UAAS,WAAY;AACb,qBAAO,KAAK,eAAe,KAAK,eAAe,SAAO,CAAC;AAAA,YAC3D;AAAA,YACJ,WAAU,SAAS,MAAM,WAAW;AAC5B,mBAAK,MAAM,SAAS;AAAA,YACxB;AAAA,UAAC;AACL,UAAAA,OAAM,UAAU,CAAC;AACjB,UAAAA,OAAM,gBAAgB,SAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAEpF,gBAAI,UAAQ;AACZ,oBAAO,2BAA2B;AAAA,cAClC,KAAK;AACL;AAAA,cACA,KAAK;AAAE,uBAAO;AACd;AAAA,cACA,KAAK;AAAE,oBAAI,SAAS,IAAI,OAAO,OAAO,GAAE,IAAI,SAAO,CAAC;AAAG,uBAAO;AAC9D;AAAA,cACA,KAAK;AAAE,uBAAO;AACd;AAAA,cACA,KAAK;AAAE,uBAAO;AACd;AAAA,cACA,KAAK;AAAE,uBAAO;AACd;AAAA,cACA,KAAK;AAAE,uBAAO;AACd;AAAA,cACA,KAAK;AAAE,uBAAO;AACd;AAAA,cACA,KAAK;AAAE,uBAAO;AACd;AAAA,cACA,KAAK;AAAE,uBAAO;AACd;AAAA,cACA,KAAK;AAAG,uBAAO;AACf;AAAA,cACA,KAAK;AAAG,uBAAO;AACf;AAAA,cACA,KAAK;AAAG,uBAAO;AACf;AAAA,cACA,KAAK;AAAG,uBAAO;AACf;AAAA,YACA;AAAA,UACA;AACA,UAAAA,OAAM,QAAQ,CAAC,YAAW,+DAA8D,sEAAqE,WAAU,WAAU,WAAU,WAAU,UAAS,UAAS,eAAc,gBAAe,eAAc,UAAS,QAAQ;AACnR,UAAAA,OAAM,aAAa,EAAC,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAG1F;AACA,iBAAOA;AAAA,QAAM,EAAG;AAChB,eAAO,QAAQ;AACf,eAAO;AAAA,MACP,EAAG;AAEH,UAAI,YAAYD,UAAS;AAEzB,MAAAA,UAAS,QAAQ,SAAS,OAAO;AAC7B,YAAI,SAAS,UAAU,KAAKA,WAAU,KAAK;AAC3C,YAAI,gBAAgB,OAAO,qBAAqB,cAAcL,SAAQ,mBAAmB,IAAI;AAC7F,YAAI;AACA,wBAAc,KAAK;AAAA,QACvB,SAAQ,GAAG;AACP,cAAG,sCAAsC,KAAK,EAAE,OAAO,GAAG;AACtD,gBAAI,kBAAkB,MAAM,UAAU,GAAG,EAAE,EAAE,EAAE,MAAM,IAAI;AACzD,gBAAI,OAAO,gBAAgB;AAC3B,gBAAI,MAAM,gBAAgB,OAAO,CAAC,EAAE,SAAS;AAE7C,iBAAK,WAAW,EAAE,SAAS,EAAC,MAAY,KAAU,SAAS,EAAE,QAAQ,QAAQ,KAAK,SAAS,GAAG;AAAE,qBAAO,EAAE,YAAY;AAAA,YAAG,CAAC,EAAC,CAAC;AAC3H,kBAAM,YAAY,EAAE,UAAU,cAAc,IAAI;AAAA,UACpD;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAEA,UAAI,OAAOA,aAAY,eAAe,OAAOE,aAAY,aAAa;AACtE,QAAAA,SAAQ,SAASG;AACjB,QAAAH,SAAQ,QAAQ,WAAY;AAAE,iBAAOG,UAAS,MAAM,MAAMA,WAAU,SAAS;AAAA,QAAG;AAChF,QAAAH,SAAQ,OAAO,SAAS,aAAa,MAAM;AACvC,cAAI,CAAC,KAAK,CAAC;AACP,kBAAM,IAAI,MAAM,YAAU,KAAK,CAAC,IAAE,OAAO;AAC7C,cAAI,OAAO,YAAY,aAAa;AAChC,gBAAI,SAASF,SAAQ,IAAI,EAAE,aAAaA,SAAQ,MAAM,EAAE,KAAK,QAAQ,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,MAAM;AAAA,UAChG,OAAO;AACH,gBAAI,MAAMA,SAAQ,MAAM,EAAE,KAAKA,SAAQ,MAAM,EAAE,IAAI,CAAC;AACpD,gBAAI,SAAS,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,EAAC,SAAS,QAAO,CAAC;AAAA,UAC1D;AACA,iBAAOE,SAAQ,OAAO,MAAM,MAAM;AAAA,QACtC;AACA,YAAI,OAAOD,YAAW,eAAeD,SAAQ,SAASC,SAAQ;AAC5D,UAAAC,SAAQ,KAAK,OAAO,YAAY,cAAc,QAAQ,KAAK,MAAM,CAAC,IAAIF,SAAQ,QAAQ,EAAE,IAAI;AAAA,QAC9F;AAAA,MACA;AACA,aAAOE;AAAA,IAAQ,EAAG;AAAE,QAAG,OAAO,WAAW,YAAY,OAAO;AAAS,aAAO,UAAU;AAAA;AAAA;", "names": ["require", "module", "exports", "value", "string", "jsonlint", "lexer"]}