import{g as c}from"./index-C0qcAVKU.js";import{e as p}from"./index.DUmRo3Ep.entry.js";function s(o,a){for(var n=0;n<a.length;n++){const r=a[n];if(typeof r!="string"&&!Array.isArray(r)){for(const l in r)if(l!=="default"&&!(l in o)){const i=Object.getOwnPropertyDescriptor(r,l);i&&Object.defineProperty(o,l,i.get?i:{enumerable:!0,get:()=>r[l]})}}}return Object.freeze(Object.defineProperty(o,Symbol.toStringTag,{value:"Module"}))}var y={exports:{}};(function(o,a){(function(n){n(p())})(function(n){n.defineMode("yaml",function(){var r=["true","false","on","off","yes","no"],l=new RegExp("\\b(("+r.join(")|(")+"))$","i");return{token:function(i,e){var f=i.peek(),t=e.escaped;if(e.escaped=!1,f=="#"&&(i.pos==0||/\s/.test(i.string.charAt(i.pos-1))))return i.skipToEnd(),"comment";if(i.match(/^('([^']|\\.)*'?|"([^"]|\\.)*"?)/))return"string";if(e.literal&&i.indentation()>e.keyCol)return i.skipToEnd(),"string";if(e.literal&&(e.literal=!1),i.sol()){if(e.keyCol=0,e.pair=!1,e.pairStart=!1,i.match("---")||i.match("..."))return"def";if(i.match(/\s*-\s+/))return"meta"}if(i.match(/^(\{|\}|\[|\])/))return f=="{"?e.inlinePairs++:f=="}"?e.inlinePairs--:f=="["?e.inlineList++:e.inlineList--,"meta";if(e.inlineList>0&&!t&&f==",")return i.next(),"meta";if(e.inlinePairs>0&&!t&&f==",")return e.keyCol=0,e.pair=!1,e.pairStart=!1,i.next(),"meta";if(e.pairStart){if(i.match(/^\s*(\||\>)\s*/))return e.literal=!0,"meta";if(i.match(/^\s*(\&|\*)[a-z0-9\._-]+\b/i))return"variable-2";if(e.inlinePairs==0&&i.match(/^\s*-?[0-9\.\,]+\s?$/)||e.inlinePairs>0&&i.match(/^\s*-?[0-9\.\,]+\s?(?=(,|}))/))return"number";if(i.match(l))return"keyword"}return!e.pair&&i.match(/^\s*(?:[,\[\]{}&*!|>'"%@`][^\s'":]|[^\s,\[\]{}#&*!|>'"%@`])[^#:]*(?=:($|\s))/)?(e.pair=!0,e.keyCol=i.indentation(),"atom"):e.pair&&i.match(/^:\s*/)?(e.pairStart=!0,"meta"):(e.pairStart=!1,e.escaped=f=="\\",i.next(),null)},startState:function(){return{pair:!1,pairStart:!1,keyCol:0,inlinePairs:0,inlineList:0,literal:!1,escaped:!1}},lineComment:"#",fold:"indent"}}),n.defineMIME("text/x-yaml","yaml"),n.defineMIME("text/yaml","yaml")})})();var u=y.exports;const d=c(u),h=s({__proto__:null,default:d},[u]);export{u as a,h as y};
