{"version": 3, "sources": ["../../../../node_modules/.pnpm/json-to-graphql-query@2.2.5/node_modules/json-to-graphql-query/src/types/EnumType.ts", "../../../../node_modules/.pnpm/json-to-graphql-query@2.2.5/node_modules/json-to-graphql-query/src/types/VariableType.ts", "../../../../node_modules/.pnpm/json-to-graphql-query@2.2.5/node_modules/json-to-graphql-query/src/jsonToGraphQLQuery.ts", "../../../../node_modules/.pnpm/json-to-graphql-query@2.2.5/node_modules/json-to-graphql-query/src/index.ts"], "sourcesContent": ["class EnumType {\n    constructor(public value: string) {}\n}\n\nexport {EnumType};\n", "class VariableType {\n    constructor(public value: string) {}\n\n    toJSON() {\n        return `$${this.value}`;\n    }\n}\n\nexport {VariableType};\n", "import { EnumType } from './types/EnumType';\nimport { VariableType } from './types/VariableType';\n\nexport const configFields = [\n    '__args', '__alias', '__aliasFor', '__variables', '__directives', '__on', '__all_on', '__typeName', '__name'\n];\n\nfunction stringify(obj_from_json: any): string {\n    if (obj_from_json instanceof EnumType) {\n        return obj_from_json.value;\n    }\n    // variables should be prefixed with dollar sign and not quoted\n    else if (obj_from_json instanceof VariableType) {\n        return `$${obj_from_json.value}`;\n    }\n    // Cheers to Derek: https://stackoverflow.com/questions/11233498/json-stringify-without-quotes-on-properties\n    else if (typeof obj_from_json !== 'object' || obj_from_json === null) {\n        // not an object, stringify using native function\n        return JSON.stringify(obj_from_json);\n    }\n    else if (Array.isArray(obj_from_json)) {\n        return `[${obj_from_json.map((item) => stringify(item)).join(', ')}]`;\n    }\n    // Implements recursive object serialization according to JSON spec\n    // but without quotes around the keys.\n    const props: string = Object\n        .keys(obj_from_json)\n        .map((key) => `${key}: ${stringify(obj_from_json[key])}`)\n        .join(', ');\n\n    return `{${props}}`;\n}\n\nfunction buildArgs(argsObj: any): string {\n    const args = [];\n    for (const argName in argsObj) {\n        args.push(`${argName}: ${stringify(argsObj[argName])}`);\n    }\n    return args.join(', ');\n}\n\nfunction buildVariables(varsObj: any): string {\n    const args = [];\n    for (const varName in varsObj) {\n        args.push(`$${varName}: ${varsObj[varName]}`);\n    }\n    return args.join(', ');\n}\n\nfunction buildDirectives(dirsObj: any): string {\n    const directiveName = Object.keys(dirsObj)[0];\n    const directiveValue = dirsObj[directiveName];\n    if (typeof directiveValue === 'boolean' || (typeof directiveValue === 'object' && Object.keys(directiveValue).length === 0)) {\n        return directiveName;\n    }\n    else if (typeof directiveValue === 'object') {\n        const args = [];\n        for (const argName in directiveValue) {\n            const argVal = stringify(directiveValue[argName]).replace(/\"/g, '');\n            args.push(`${argName}: ${argVal}`);\n        }\n        return `${directiveName}(${args.join(', ')})`;\n    }\n    else {\n        throw new Error(`Unsupported type for directive: ${typeof directiveValue}. Types allowed: object, boolean.\\n` +\n            `Offending object: ${JSON.stringify(dirsObj)}`);\n    }\n}\n\nfunction getIndent(level: number): string {\n    return Array((level * 4) + 1).join(' ');\n}\n\nfunction filterNonConfigFields(fieldName: string, ignoreFields: string[]) {\n    // Returns true if fieldName is not a 'configField'.\n    return configFields.indexOf(fieldName) == -1 && ignoreFields.indexOf(fieldName) == -1;\n}\n\nfunction convertQuery(node: any, level: number, output: [string, number][], options: IJsonToGraphQLOptions) {\n    Object.keys(node)\n        .filter((key) => filterNonConfigFields(key, options.ignoreFields!))\n        .forEach((key) => {\n            let value = node[key];\n            if (typeof value === 'object') {\n                if (Array.isArray(value)) {\n                    value = value.find((item) => item && typeof item === 'object');\n                    if (!value) {\n                        output.push([`${key}`, level]);\n                        return;\n                    }\n                }\n\n                // Check if the object would be empty\n                if (value && Object.keys(value).filter(k => value[k] !== false || options.includeFalsyKeys).length === 0) {\n                    // If so, we don't include it into the query\n                    return;\n                }\n\n                const fieldCount = Object.keys(value)\n                    .filter((keyCount) => filterNonConfigFields(keyCount, options.ignoreFields!)).length;\n                const subFields = fieldCount > 0;\n                const argsExist = typeof value.__args === 'object' && Object.keys(value.__args).length > 0;\n                const directivesExist = typeof value.__directives === 'object';\n                const fullFragmentsExist = value.__all_on instanceof Array;\n                const partialFragmentsExist = typeof value.__on === 'object';\n\n                let token = `${key}`;\n\n                if (typeof value.__name === 'string') {\n                    token = `${token} ${value.__name}`;\n                }\n\n                if (typeof value.__aliasFor === 'string') {\n                    token = `${token}: ${value.__aliasFor}`;\n                }\n\n                if (typeof value.__variables === 'object' && Object.keys(value.__variables).length > 0) {\n                    token = `${token} (${buildVariables(value.__variables)})`;\n                }\n                else if (argsExist || directivesExist) {\n                    let argsStr = '';\n                    let dirsStr = '';\n                    if (directivesExist) {\n                        dirsStr = Object.entries(value.__directives)\n                            .map(item => `@${buildDirectives({ [item[0]]: item[1] })}`)\n                            .join(' ')\n                    }\n                    if (argsExist) {\n                        argsStr = `(${buildArgs(value.__args)})`;\n                    }\n                    const spacer = directivesExist && argsExist ? ' ' : '';\n                    token = `${token} ${argsStr}${spacer}${dirsStr}`;\n                }\n\n                output.push([token + (subFields || partialFragmentsExist || fullFragmentsExist ? ' {' : ''), level]);\n                convertQuery(value, level + 1, output, options);\n\n                if (fullFragmentsExist) {\n                    value.__all_on.forEach((fullFragment: string) => {\n                        output.push([`...${fullFragment}`, level + 1]);\n                    });\n                }\n                if (partialFragmentsExist) {\n                    const inlineFragments: { __typeName: string }[]\n                        = value.__on instanceof Array ? value.__on : [value.__on];\n                    inlineFragments.forEach((inlineFragment) => {\n                        const name = inlineFragment.__typeName;\n                        output.push([`... on ${name} {`, level + 1]);\n                        convertQuery(inlineFragment, level + 2, output, options);\n                        output.push(['}', level + 1]);\n                    });\n                }\n\n                if (subFields || partialFragmentsExist || fullFragmentsExist) {\n                    output.push(['}', level]);\n                }\n\n            } else if (options.includeFalsyKeys === true || value) {\n                output.push([`${key}`, level]);\n            }\n        });\n}\n\nexport interface IJsonToGraphQLOptions {\n    pretty?: boolean;\n    ignoreFields?: string[];\n    includeFalsyKeys?: boolean;\n}\n\nexport function jsonToGraphQLQuery(query: any, options: IJsonToGraphQLOptions = {}) {\n    if (!query || typeof query != 'object') {\n        throw new Error('query object not specified');\n    }\n    if (Object.keys(query).length == 0) {\n        throw new Error('query object has no data');\n    }\n    if (!(options.ignoreFields instanceof Array)) {\n        options.ignoreFields = [];\n    }\n\n    const queryLines: [string, number][] = [];\n    convertQuery(query, 0, queryLines, options);\n\n    let output = '';\n    queryLines.forEach(([line, level]) => {\n        if (options.pretty) {\n            if (output) { output += '\\n'; }\n            output += getIndent(level) + line;\n        }\n        else {\n            if (output) { output += ' '; }\n            output += line;\n        }\n    });\n    return output;\n}\n", "\nexport * from './jsonToGraphQLQuery';\nexport {EnumType} from './types/EnumType';\nexport {VariableType} from './types/VariableType';\n"], "mappings": ";;;;;;;;;;AAAA,QAAA,WAAA,2BAAA;AACI,eAAAA,UAAmB,OAAa;AAAb,aAAA,QAAA;MAAgB;AACvC,aAAAA;IAAA,EAFA;AAIQ,YAAA,WAAA;;;;;;;;;;ACJR,QAAA,eAAA,WAAA;AACI,eAAAC,cAAmB,OAAa;AAAb,aAAA,QAAA;MAAgB;AAEnC,MAAAA,cAAA,UAAA,SAAA,WAAA;AACI,eAAO,IAAA,OAAI,KAAK,KAAK;MACzB;AACJ,aAAAA;IAAA,EANA;AAQQ,YAAA,eAAA;;;;;;;;;;ACRR,QAAA,aAAA;AACA,QAAA,iBAAA;AAEa,YAAA,eAAe;MACxB;MAAU;MAAW;MAAc;MAAe;MAAgB;MAAQ;MAAY;MAAc;;AAGxG,aAAS,UAAU,eAAkB;AACjC,UAAI,yBAAyB,WAAA,UAAU;AACnC,eAAO,cAAc;iBAGhB,yBAAyB,eAAA,cAAc;AAC5C,eAAO,IAAA,OAAI,cAAc,KAAK;iBAGzB,OAAO,kBAAkB,YAAY,kBAAkB,MAAM;AAElE,eAAO,KAAK,UAAU,aAAa;iBAE9B,MAAM,QAAQ,aAAa,GAAG;AACnC,eAAO,IAAA,OAAI,cAAc,IAAI,SAAC,MAAI;AAAK,iBAAA,UAAU,IAAI;QAAd,CAAe,EAAE,KAAK,IAAI,GAAC,GAAA;;AAItE,UAAM,QAAgB,OACjB,KAAK,aAAa,EAClB,IAAI,SAAC,KAAG;AAAK,eAAA,GAAA,OAAG,KAAG,IAAA,EAAA,OAAK,UAAU,cAAc,GAAG,CAAC,CAAC;MAAxC,CAA0C,EACvD,KAAK,IAAI;AAEd,aAAO,IAAA,OAAI,OAAK,GAAA;IACpB;AAEA,aAAS,UAAU,SAAY;AAC3B,UAAM,OAAO,CAAA;AACb,eAAW,WAAW,SAAS;AAC3B,aAAK,KAAK,GAAA,OAAG,SAAO,IAAA,EAAA,OAAK,UAAU,QAAQ,OAAO,CAAC,CAAC,CAAE;;AAE1D,aAAO,KAAK,KAAK,IAAI;IACzB;AAEA,aAAS,eAAe,SAAY;AAChC,UAAM,OAAO,CAAA;AACb,eAAW,WAAW,SAAS;AAC3B,aAAK,KAAK,IAAA,OAAI,SAAO,IAAA,EAAA,OAAK,QAAQ,OAAO,CAAC,CAAE;;AAEhD,aAAO,KAAK,KAAK,IAAI;IACzB;AAEA,aAAS,gBAAgB,SAAY;AACjC,UAAM,gBAAgB,OAAO,KAAK,OAAO,EAAE,CAAC;AAC5C,UAAM,iBAAiB,QAAQ,aAAa;AAC5C,UAAI,OAAO,mBAAmB,aAAc,OAAO,mBAAmB,YAAY,OAAO,KAAK,cAAc,EAAE,WAAW,GAAI;AACzH,eAAO;iBAEF,OAAO,mBAAmB,UAAU;AACzC,YAAM,OAAO,CAAA;AACb,iBAAW,WAAW,gBAAgB;AAClC,cAAM,SAAS,UAAU,eAAe,OAAO,CAAC,EAAE,QAAQ,MAAM,EAAE;AAClE,eAAK,KAAK,GAAA,OAAG,SAAO,IAAA,EAAA,OAAK,MAAM,CAAE;;AAErC,eAAO,GAAA,OAAG,eAAa,GAAA,EAAA,OAAI,KAAK,KAAK,IAAI,GAAC,GAAA;aAEzC;AACD,cAAM,IAAI,MAAM,mCAAA,OAAmC,OAAO,gBAAc,qCAAA,IACpE,qBAAA,OAAqB,KAAK,UAAU,OAAO,CAAC,CAAE;;IAE1D;AAEA,aAAS,UAAU,OAAa;AAC5B,aAAO,MAAO,QAAQ,IAAK,CAAC,EAAE,KAAK,GAAG;IAC1C;AAEA,aAAS,sBAAsB,WAAmB,cAAsB;AAEpE,aAAO,QAAA,aAAa,QAAQ,SAAS,KAAK,MAAM,aAAa,QAAQ,SAAS,KAAK;IACvF;AAEA,aAAS,aAAa,MAAW,OAAe,QAA4B,SAA8B;AACtG,aAAO,KAAK,IAAI,EACX,OAAO,SAAC,KAAG;AAAK,eAAA,sBAAsB,KAAK,QAAQ,YAAa;MAAhD,CAAiD,EACjE,QAAQ,SAAC,KAAG;AACT,YAAI,QAAQ,KAAK,GAAG;AACpB,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,oBAAQ,MAAM,KAAK,SAAC,MAAI;AAAK,qBAAA,QAAQ,OAAO,SAAS;YAAxB,CAAgC;AAC7D,gBAAI,CAAC,OAAO;AACR,qBAAO,KAAK,CAAC,GAAA,OAAG,GAAG,GAAI,KAAK,CAAC;AAC7B;;;AAKR,cAAI,SAAS,OAAO,KAAK,KAAK,EAAE,OAAO,SAAA,GAAC;AAAI,mBAAA,MAAM,CAAC,MAAM,SAAS,QAAQ;UAA9B,CAA8C,EAAE,WAAW,GAAG;AAEtG;;AAGJ,cAAM,aAAa,OAAO,KAAK,KAAK,EAC/B,OAAO,SAAC,UAAQ;AAAK,mBAAA,sBAAsB,UAAU,QAAQ,YAAa;UAArD,CAAsD,EAAE;AAClF,cAAM,YAAY,aAAa;AAC/B,cAAM,YAAY,OAAO,MAAM,WAAW,YAAY,OAAO,KAAK,MAAM,MAAM,EAAE,SAAS;AACzF,cAAM,kBAAkB,OAAO,MAAM,iBAAiB;AACtD,cAAM,qBAAqB,MAAM,oBAAoB;AACrD,cAAM,wBAAwB,OAAO,MAAM,SAAS;AAEpD,cAAI,QAAQ,GAAA,OAAG,GAAG;AAElB,cAAI,OAAO,MAAM,WAAW,UAAU;AAClC,oBAAQ,GAAA,OAAG,OAAK,GAAA,EAAA,OAAI,MAAM,MAAM;;AAGpC,cAAI,OAAO,MAAM,eAAe,UAAU;AACtC,oBAAQ,GAAA,OAAG,OAAK,IAAA,EAAA,OAAK,MAAM,UAAU;;AAGzC,cAAI,OAAO,MAAM,gBAAgB,YAAY,OAAO,KAAK,MAAM,WAAW,EAAE,SAAS,GAAG;AACpF,oBAAQ,GAAA,OAAG,OAAK,IAAA,EAAA,OAAK,eAAe,MAAM,WAAW,GAAC,GAAA;qBAEjD,aAAa,iBAAiB;AACnC,gBAAI,UAAU;AACd,gBAAI,UAAU;AACd,gBAAI,iBAAiB;AACjB,wBAAU,OAAO,QAAQ,MAAM,YAAY,EACtC,IAAI,SAAA,MAAI;;AAAI,uBAAA,IAAA,OAAI,iBAAe,KAAA,CAAA,GAAG,GAAC,KAAK,CAAC,CAAC,IAAG,KAAK,CAAC,GAAC,GAAA,CAAG;cAA3C,CAA6C,EACzD,KAAK,GAAG;;AAEjB,gBAAI,WAAW;AACX,wBAAU,IAAA,OAAI,UAAU,MAAM,MAAM,GAAC,GAAA;;AAEzC,gBAAM,SAAS,mBAAmB,YAAY,MAAM;AACpD,oBAAQ,GAAA,OAAG,OAAK,GAAA,EAAA,OAAI,OAAO,EAAA,OAAG,MAAM,EAAA,OAAG,OAAO;;AAGlD,iBAAO,KAAK,CAAC,SAAS,aAAa,yBAAyB,qBAAqB,OAAO,KAAK,KAAK,CAAC;AACnG,uBAAa,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAE9C,cAAI,oBAAoB;AACpB,kBAAM,SAAS,QAAQ,SAAC,cAAoB;AACxC,qBAAO,KAAK,CAAC,MAAA,OAAM,YAAY,GAAI,QAAQ,CAAC,CAAC;YACjD,CAAC;;AAEL,cAAI,uBAAuB;AACvB,gBAAM,kBACA,MAAM,gBAAgB,QAAQ,MAAM,OAAO,CAAC,MAAM,IAAI;AAC5D,4BAAgB,QAAQ,SAAC,gBAAc;AACnC,kBAAM,OAAO,eAAe;AAC5B,qBAAO,KAAK,CAAC,UAAA,OAAU,MAAI,IAAA,GAAM,QAAQ,CAAC,CAAC;AAC3C,2BAAa,gBAAgB,QAAQ,GAAG,QAAQ,OAAO;AACvD,qBAAO,KAAK,CAAC,KAAK,QAAQ,CAAC,CAAC;YAChC,CAAC;;AAGL,cAAI,aAAa,yBAAyB,oBAAoB;AAC1D,mBAAO,KAAK,CAAC,KAAK,KAAK,CAAC;;mBAGrB,QAAQ,qBAAqB,QAAQ,OAAO;AACnD,iBAAO,KAAK,CAAC,GAAA,OAAG,GAAG,GAAI,KAAK,CAAC;;MAErC,CAAC;IACT;AAQA,aAAgB,mBAAmB,OAAY,SAAmC;AAAnC,UAAA,YAAA,QAAA;AAAA,kBAAA,CAAA;MAAmC;AAC9E,UAAI,CAAC,SAAS,OAAO,SAAS,UAAU;AACpC,cAAM,IAAI,MAAM,4BAA4B;;AAEhD,UAAI,OAAO,KAAK,KAAK,EAAE,UAAU,GAAG;AAChC,cAAM,IAAI,MAAM,0BAA0B;;AAE9C,UAAI,EAAE,QAAQ,wBAAwB,QAAQ;AAC1C,gBAAQ,eAAe,CAAA;;AAG3B,UAAM,aAAiC,CAAA;AACvC,mBAAa,OAAO,GAAG,YAAY,OAAO;AAE1C,UAAI,SAAS;AACb,iBAAW,QAAQ,SAAC,IAAa;YAAZ,OAAI,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AAC5B,YAAI,QAAQ,QAAQ;AAChB,cAAI,QAAQ;AAAE,sBAAU;;AACxB,oBAAU,UAAU,KAAK,IAAI;eAE5B;AACD,cAAI,QAAQ;AAAE,sBAAU;;AACxB,oBAAU;;MAElB,CAAC;AACD,aAAO;IACX;AA1BA,YAAA,qBAAA;;;;;;;;;;;;;;;;;;;;;;;;;ACxKA,iBAAA,8BAAA,OAAA;AACA,QAAA,aAAA;AAAQ,WAAA,eAAA,SAAA,YAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,WAAA;IAAQ,EAAA,CAAA;AAChB,QAAA,iBAAA;AAAQ,WAAA,eAAA,SAAA,gBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,eAAA;IAAY,EAAA,CAAA;;;", "names": ["EnumType", "VariableType"]}