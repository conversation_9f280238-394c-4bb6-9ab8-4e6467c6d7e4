import{c as u,b as r,i as h,j as i}from"./index.DUmRo3Ep.entry.js";import{i as l}from"./isSameWeek-DOt1VTFz.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";function v(t,e){return e===1&&t.one?t.one:e>=2&&e<=4&&t.twoFour?t.twoFour:t.other}function d(t,e,n){return v(t,e)[n].replace("{{count}}",String(e))}function b(t){return["lessThan","about","over","almost"].filter(function(n){return!!t.match(new RegExp("^"+n))})[0]}function m(t){let e="";return t==="almost"&&(e="takmer"),t==="about"&&(e="približne"),e.length>0?e+" ":""}function p(t){let e="";return t==="lessThan"&&(e="menej než"),t==="over"&&(e="viac než"),e.length>0?e+" ":""}function k(t){return t.charAt(0).toLowerCase()+t.slice(1)}const g={xSeconds:{one:{present:"sekunda",past:"sekundou",future:"sekundu"},twoFour:{present:"{{count}} sekundy",past:"{{count}} sekundami",future:"{{count}} sekundy"},other:{present:"{{count}} sekúnd",past:"{{count}} sekundami",future:"{{count}} sekúnd"}},halfAMinute:{other:{present:"pol minúty",past:"pol minútou",future:"pol minúty"}},xMinutes:{one:{present:"minúta",past:"minútou",future:"minútu"},twoFour:{present:"{{count}} minúty",past:"{{count}} minútami",future:"{{count}} minúty"},other:{present:"{{count}} minút",past:"{{count}} minútami",future:"{{count}} minút"}},xHours:{one:{present:"hodina",past:"hodinou",future:"hodinu"},twoFour:{present:"{{count}} hodiny",past:"{{count}} hodinami",future:"{{count}} hodiny"},other:{present:"{{count}} hodín",past:"{{count}} hodinami",future:"{{count}} hodín"}},xDays:{one:{present:"deň",past:"dňom",future:"deň"},twoFour:{present:"{{count}} dni",past:"{{count}} dňami",future:"{{count}} dni"},other:{present:"{{count}} dní",past:"{{count}} dňami",future:"{{count}} dní"}},xWeeks:{one:{present:"týždeň",past:"týždňom",future:"týždeň"},twoFour:{present:"{{count}} týždne",past:"{{count}} týždňami",future:"{{count}} týždne"},other:{present:"{{count}} týždňov",past:"{{count}} týždňami",future:"{{count}} týždňov"}},xMonths:{one:{present:"mesiac",past:"mesiacom",future:"mesiac"},twoFour:{present:"{{count}} mesiace",past:"{{count}} mesiacmi",future:"{{count}} mesiace"},other:{present:"{{count}} mesiacov",past:"{{count}} mesiacmi",future:"{{count}} mesiacov"}},xYears:{one:{present:"rok",past:"rokom",future:"rok"},twoFour:{present:"{{count}} roky",past:"{{count}} rokmi",future:"{{count}} roky"},other:{present:"{{count}} rokov",past:"{{count}} rokmi",future:"{{count}} rokov"}}},w=(t,e,n)=>{const o=b(t)||"",a=k(t.substring(o.length)),s=g[a];return n!=null&&n.addSuffix?n.comparison&&n.comparison>0?m(o)+"o "+p(o)+d(s,e,"future"):m(o)+"pred "+p(o)+d(s,e,"past"):m(o)+p(o)+d(s,e,"present")},y={full:"EEEE d. MMMM y",long:"d. MMMM y",medium:"d. M. y",short:"d. M. y"},P={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},j={full:"{{date}}, {{time}}",long:"{{date}}, {{time}}",medium:"{{date}}, {{time}}",short:"{{date}} {{time}}"},M={date:u({formats:y,defaultWidth:"full"}),time:u({formats:P,defaultWidth:"full"}),dateTime:u({formats:j,defaultWidth:"full"})},c=["nedeľu","pondelok","utorok","stredu","štvrtok","piatok","sobotu"];function W(t){const e=c[t];switch(t){case 0:case 3:case 6:return"'minulú "+e+" o' p";default:return"'minulý' eeee 'o' p"}}function f(t){const e=c[t];return t===4?"'vo' eeee 'o' p":"'v "+e+" o' p"}function F(t){const e=c[t];switch(t){case 0:case 4:case 6:return"'budúcu "+e+" o' p";default:return"'budúci' eeee 'o' p"}}const x={lastWeek:(t,e,n)=>{const o=t.getDay();return l(t,e,n)?f(o):W(o)},yesterday:"'včera o' p",today:"'dnes o' p",tomorrow:"'zajtra o' p",nextWeek:(t,e,n)=>{const o=t.getDay();return l(t,e,n)?f(o):F(o)},other:"P"},D=(t,e,n,o)=>{const a=x[t];return typeof a=="function"?a(e,n,o):a},K={narrow:["pred Kr.","po Kr."],abbreviated:["pred Kr.","po Kr."],wide:["pred Kristom","po Kristovi"]},z={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. štvrťrok","2. štvrťrok","3. štvrťrok","4. štvrťrok"]},V={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","feb","mar","apr","máj","jún","júl","aug","sep","okt","nov","dec"],wide:["január","február","marec","apríl","máj","jún","júl","august","september","október","november","december"]},A={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","feb","mar","apr","máj","jún","júl","aug","sep","okt","nov","dec"],wide:["januára","februára","marca","apríla","mája","júna","júla","augusta","septembra","októbra","novembra","decembra"]},L={narrow:["n","p","u","s","š","p","s"],short:["ne","po","ut","st","št","pi","so"],abbreviated:["ne","po","ut","st","št","pi","so"],wide:["nedeľa","pondelok","utorok","streda","štvrtok","piatok","sobota"]},E={narrow:{am:"AM",pm:"PM",midnight:"poln.",noon:"pol.",morning:"ráno",afternoon:"pop.",evening:"več.",night:"noc"},abbreviated:{am:"AM",pm:"PM",midnight:"poln.",noon:"pol.",morning:"ráno",afternoon:"popol.",evening:"večer",night:"noc"},wide:{am:"AM",pm:"PM",midnight:"polnoc",noon:"poludnie",morning:"ráno",afternoon:"popoludnie",evening:"večer",night:"noc"}},Q={narrow:{am:"AM",pm:"PM",midnight:"o poln.",noon:"nap.",morning:"ráno",afternoon:"pop.",evening:"več.",night:"v n."},abbreviated:{am:"AM",pm:"PM",midnight:"o poln.",noon:"napol.",morning:"ráno",afternoon:"popol.",evening:"večer",night:"v noci"},wide:{am:"AM",pm:"PM",midnight:"o polnoci",noon:"napoludnie",morning:"ráno",afternoon:"popoludní",evening:"večer",night:"v noci"}},T=(t,e)=>Number(t)+".",C={ordinalNumber:T,era:r({values:K,defaultWidth:"wide"}),quarter:r({values:z,defaultWidth:"wide",argumentCallback:t=>t-1}),month:r({values:V,defaultWidth:"wide",formattingValues:A,defaultFormattingWidth:"wide"}),day:r({values:L,defaultWidth:"wide"}),dayPeriod:r({values:E,defaultWidth:"wide",formattingValues:Q,defaultFormattingWidth:"wide"})},H=/^(\d+)\.?/i,N=/\d+/i,S={narrow:/^(pred Kr\.|pred n\. l\.|po Kr\.|n\. l\.)/i,abbreviated:/^(pred Kr\.|pred n\. l\.|po Kr\.|n\. l\.)/i,wide:/^(pred Kristom|pred na[šs][íi]m letopo[čc]tom|po Kristovi|n[áa][šs]ho letopo[čc]tu)/i},q={any:[/^pr/i,/^(po|n)/i]},R={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]\. [šs]tvr[ťt]rok/i},O={any:[/1/i,/2/i,/3/i,/4/i]},G={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|m[áa]j|j[úu]n|j[úu]l|aug|sep|okt|nov|dec)/i,wide:/^(janu[áa]ra?|febru[áa]ra?|(marec|marca)|apr[íi]la?|m[áa]ja?|j[úu]na?|j[úu]la?|augusta?|(september|septembra)|(okt[óo]ber|okt[óo]bra)|(november|novembra)|(december|decembra))/i},I={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^m[áa]j/i,/^j[úu]n/i,/^j[úu]l/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Y={narrow:/^[npusšp]/i,short:/^(ne|po|ut|st|št|pi|so)/i,abbreviated:/^(ne|po|ut|st|št|pi|so)/i,wide:/^(nede[ľl]a|pondelok|utorok|streda|[šs]tvrtok|piatok|sobota])/i},_={narrow:[/^n/i,/^p/i,/^u/i,/^s/i,/^š/i,/^p/i,/^s/i],any:[/^n/i,/^po/i,/^u/i,/^st/i,/^(št|stv)/i,/^pi/i,/^so/i]},B={narrow:/^(am|pm|(o )?poln\.?|(nap\.?|pol\.?)|r[áa]no|pop\.?|ve[čc]\.?|(v n\.?|noc))/i,abbreviated:/^(am|pm|(o )?poln\.?|(napol\.?|pol\.?)|r[áa]no|pop\.?|ve[čc]er|(v )?noci?)/i,any:/^(am|pm|(o )?polnoci?|(na)?poludnie|r[áa]no|popoludn(ie|í|i)|ve[čc]er|(v )?noci?)/i},J={any:{am:/^am/i,pm:/^pm/i,midnight:/poln/i,noon:/^(nap|(na)?pol(\.|u))/i,morning:/^r[áa]no/i,afternoon:/^pop/i,evening:/^ve[čc]/i,night:/^(noc|v n\.)/i}},U={ordinalNumber:h({matchPattern:H,parsePattern:N,valueCallback:t=>parseInt(t,10)}),era:i({matchPatterns:S,defaultMatchWidth:"wide",parsePatterns:q,defaultParseWidth:"any"}),quarter:i({matchPatterns:R,defaultMatchWidth:"wide",parsePatterns:O,defaultParseWidth:"any",valueCallback:t=>t+1}),month:i({matchPatterns:G,defaultMatchWidth:"wide",parsePatterns:I,defaultParseWidth:"any"}),day:i({matchPatterns:Y,defaultMatchWidth:"wide",parsePatterns:_,defaultParseWidth:"any"}),dayPeriod:i({matchPatterns:B,defaultMatchWidth:"any",parsePatterns:J,defaultParseWidth:"any"})},rt={code:"sk",formatDistance:w,formatLong:M,formatRelative:D,localize:C,match:U,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{rt as default,rt as sk};
