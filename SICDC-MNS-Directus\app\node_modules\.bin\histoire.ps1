#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\histoire@0.17.17_@types+node@18.19.33_sass@1.77.4_terser@5.31.0_vite@5.2.11_@types+node@18.19_3qlsc35ejh6klcrroixr4q2t7a\node_modules\histoire\node_modules;D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\histoire@0.17.17_@types+node@18.19.33_sass@1.77.4_terser@5.31.0_vite@5.2.11_@types+node@18.19_3qlsc35ejh6klcrroixr4q2t7a\node_modules;D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/histoire@0.17.17_@types+node@18.19.33_sass@1.77.4_terser@5.31.0_vite@5.2.11_@types+node@18.19_3qlsc35ejh6klcrroixr4q2t7a/node_modules/histoire/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/histoire@0.17.17_@types+node@18.19.33_sass@1.77.4_terser@5.31.0_vite@5.2.11_@types+node@18.19_3qlsc35ejh6klcrroixr4q2t7a/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../histoire/bin.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../histoire/bin.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../histoire/bin.mjs" $args
  } else {
    & "node$exe"  "$basedir/../histoire/bin.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
