#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/histoire@0.17.17_@types+node@18.19.33_sass@1.77.4_terser@5.31.0_vite@5.2.11_@types+node@18.19_3qlsc35ejh6klcrroixr4q2t7a/node_modules/histoire/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/histoire@0.17.17_@types+node@18.19.33_sass@1.77.4_terser@5.31.0_vite@5.2.11_@types+node@18.19_3qlsc35ejh6klcrroixr4q2t7a/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/histoire@0.17.17_@types+node@18.19.33_sass@1.77.4_terser@5.31.0_vite@5.2.11_@types+node@18.19_3qlsc35ejh6klcrroixr4q2t7a/node_modules/histoire/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/histoire@0.17.17_@types+node@18.19.33_sass@1.77.4_terser@5.31.0_vite@5.2.11_@types+node@18.19_3qlsc35ejh6klcrroixr4q2t7a/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../histoire/bin.mjs" "$@"
else
  exec node  "$basedir/../histoire/bin.mjs" "$@"
fi
