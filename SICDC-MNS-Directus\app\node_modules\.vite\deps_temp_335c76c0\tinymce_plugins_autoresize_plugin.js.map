{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/autoresize/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const fireResizeEditor = editor => editor.dispatch('ResizeEditor');\n\n    const option = name => editor => editor.options.get(name);\n    const register$1 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('autoresize_overflow_padding', {\n        processor: 'number',\n        default: 1\n      });\n      registerOption('autoresize_bottom_margin', {\n        processor: 'number',\n        default: 50\n      });\n    };\n    const getMinHeight = option('min_height');\n    const getMaxHeight = option('max_height');\n    const getAutoResizeOverflowPadding = option('autoresize_overflow_padding');\n    const getAutoResizeBottomMargin = option('autoresize_bottom_margin');\n\n    const isFullscreen = editor => editor.plugins.fullscreen && editor.plugins.fullscreen.isFullscreen();\n    const toggleScrolling = (editor, state) => {\n      const body = editor.getBody();\n      if (body) {\n        body.style.overflowY = state ? '' : 'hidden';\n        if (!state) {\n          body.scrollTop = 0;\n        }\n      }\n    };\n    const parseCssValueToInt = (dom, elm, name, computed) => {\n      var _a;\n      const value = parseInt((_a = dom.getStyle(elm, name, computed)) !== null && _a !== void 0 ? _a : '', 10);\n      return isNaN(value) ? 0 : value;\n    };\n    const shouldScrollIntoView = trigger => {\n      if ((trigger === null || trigger === void 0 ? void 0 : trigger.type.toLowerCase()) === 'setcontent') {\n        const setContentEvent = trigger;\n        return setContentEvent.selection === true || setContentEvent.paste === true;\n      } else {\n        return false;\n      }\n    };\n    const resize = (editor, oldSize, trigger, getExtraMarginBottom) => {\n      var _a;\n      const dom = editor.dom;\n      const doc = editor.getDoc();\n      if (!doc) {\n        return;\n      }\n      if (isFullscreen(editor)) {\n        toggleScrolling(editor, true);\n        return;\n      }\n      const docEle = doc.documentElement;\n      const resizeBottomMargin = getExtraMarginBottom ? getExtraMarginBottom() : getAutoResizeOverflowPadding(editor);\n      const minHeight = (_a = getMinHeight(editor)) !== null && _a !== void 0 ? _a : editor.getElement().offsetHeight;\n      let resizeHeight = minHeight;\n      const marginTop = parseCssValueToInt(dom, docEle, 'margin-top', true);\n      const marginBottom = parseCssValueToInt(dom, docEle, 'margin-bottom', true);\n      let contentHeight = docEle.offsetHeight + marginTop + marginBottom + resizeBottomMargin;\n      if (contentHeight < 0) {\n        contentHeight = 0;\n      }\n      const containerHeight = editor.getContainer().offsetHeight;\n      const contentAreaHeight = editor.getContentAreaContainer().offsetHeight;\n      const chromeHeight = containerHeight - contentAreaHeight;\n      if (contentHeight + chromeHeight > minHeight) {\n        resizeHeight = contentHeight + chromeHeight;\n      }\n      const maxHeight = getMaxHeight(editor);\n      if (maxHeight && resizeHeight > maxHeight) {\n        resizeHeight = maxHeight;\n        toggleScrolling(editor, true);\n      } else {\n        toggleScrolling(editor, false);\n      }\n      if (resizeHeight !== oldSize.get()) {\n        const deltaSize = resizeHeight - oldSize.get();\n        dom.setStyle(editor.getContainer(), 'height', resizeHeight + 'px');\n        oldSize.set(resizeHeight);\n        fireResizeEditor(editor);\n        if (global.browser.isSafari() && (global.os.isMacOS() || global.os.isiOS())) {\n          const win = editor.getWin();\n          win.scrollTo(win.pageXOffset, win.pageYOffset);\n        }\n        if (editor.hasFocus() && shouldScrollIntoView(trigger)) {\n          editor.selection.scrollIntoView();\n        }\n        if ((global.browser.isSafari() || global.browser.isChromium()) && deltaSize < 0) {\n          resize(editor, oldSize, trigger, getExtraMarginBottom);\n        }\n      }\n    };\n    const setup = (editor, oldSize) => {\n      let getExtraMarginBottom = () => getAutoResizeBottomMargin(editor);\n      let resizeCounter;\n      let sizeAfterFirstResize;\n      editor.on('init', e => {\n        resizeCounter = 0;\n        const overflowPadding = getAutoResizeOverflowPadding(editor);\n        const dom = editor.dom;\n        dom.setStyles(editor.getDoc().documentElement, { height: 'auto' });\n        if (global.browser.isEdge() || global.browser.isIE()) {\n          dom.setStyles(editor.getBody(), {\n            'paddingLeft': overflowPadding,\n            'paddingRight': overflowPadding,\n            'min-height': 0\n          });\n        } else {\n          dom.setStyles(editor.getBody(), {\n            paddingLeft: overflowPadding,\n            paddingRight: overflowPadding\n          });\n        }\n        resize(editor, oldSize, e, getExtraMarginBottom);\n        resizeCounter += 1;\n      });\n      editor.on('NodeChange SetContent keyup FullscreenStateChanged ResizeContent', e => {\n        if (resizeCounter === 1) {\n          sizeAfterFirstResize = editor.getContainer().offsetHeight;\n          resize(editor, oldSize, e, getExtraMarginBottom);\n          resizeCounter += 1;\n        } else if (resizeCounter === 2) {\n          const isLooping = sizeAfterFirstResize < editor.getContainer().offsetHeight;\n          if (isLooping) {\n            const dom = editor.dom;\n            const doc = editor.getDoc();\n            dom.setStyles(doc.documentElement, { 'min-height': 0 });\n            dom.setStyles(editor.getBody(), { 'min-height': 'inherit' });\n          }\n          getExtraMarginBottom = isLooping ? constant(0) : getExtraMarginBottom;\n          resizeCounter += 1;\n        } else {\n          resize(editor, oldSize, e, getExtraMarginBottom);\n        }\n      });\n    };\n\n    const register = (editor, oldSize) => {\n      editor.addCommand('mceAutoResize', () => {\n        resize(editor, oldSize);\n      });\n    };\n\n    var Plugin = () => {\n      global$1.add('autoresize', editor => {\n        register$1(editor);\n        if (!editor.options.isSet('resize')) {\n          editor.options.set('resize', false);\n        }\n        if (!editor.inline) {\n          const oldSize = Cell(0);\n          register(editor, oldSize);\n          setup(editor, oldSize);\n        }\n      });\n    };\n\n    Plugin();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,QAAM,OAAO,aAAW;AACtB,QAAI,QAAQ;AACZ,UAAM,MAAM,MAAM;AAChB,aAAO;AAAA,IACT;AACA,UAAM,MAAM,OAAK;AACf,cAAQ;AAAA,IACV;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,QAAM,WAAW,WAAS;AACxB,WAAO,MAAM;AACX,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAErD,QAAM,mBAAmB,YAAU,OAAO,SAAS,cAAc;AAEjE,QAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,QAAM,aAAa,YAAU;AAC3B,UAAM,iBAAiB,OAAO,QAAQ;AACtC,mBAAe,+BAA+B;AAAA,MAC5C,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,4BAA4B;AAAA,MACzC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,QAAM,eAAe,OAAO,YAAY;AACxC,QAAM,eAAe,OAAO,YAAY;AACxC,QAAM,+BAA+B,OAAO,6BAA6B;AACzE,QAAM,4BAA4B,OAAO,0BAA0B;AAEnE,QAAM,eAAe,YAAU,OAAO,QAAQ,cAAc,OAAO,QAAQ,WAAW,aAAa;AACnG,QAAM,kBAAkB,CAAC,QAAQ,UAAU;AACzC,UAAM,OAAO,OAAO,QAAQ;AAC5B,QAAI,MAAM;AACR,WAAK,MAAM,YAAY,QAAQ,KAAK;AACpC,UAAI,CAAC,OAAO;AACV,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,QAAM,qBAAqB,CAAC,KAAK,KAAK,MAAM,aAAa;AACvD,QAAI;AACJ,UAAM,QAAQ,UAAU,KAAK,IAAI,SAAS,KAAK,MAAM,QAAQ,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,EAAE;AACvG,WAAO,MAAM,KAAK,IAAI,IAAI;AAAA,EAC5B;AACA,QAAM,uBAAuB,aAAW;AACtC,SAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,YAAY,OAAO,cAAc;AACnG,YAAM,kBAAkB;AACxB,aAAO,gBAAgB,cAAc,QAAQ,gBAAgB,UAAU;AAAA,IACzE,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,SAAS,CAAC,QAAQ,SAAS,SAAS,yBAAyB;AACjE,QAAI;AACJ,UAAM,MAAM,OAAO;AACnB,UAAM,MAAM,OAAO,OAAO;AAC1B,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,QAAI,aAAa,MAAM,GAAG;AACxB,sBAAgB,QAAQ,IAAI;AAC5B;AAAA,IACF;AACA,UAAM,SAAS,IAAI;AACnB,UAAM,qBAAqB,uBAAuB,qBAAqB,IAAI,6BAA6B,MAAM;AAC9G,UAAM,aAAa,KAAK,aAAa,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,OAAO,WAAW,EAAE;AACnG,QAAI,eAAe;AACnB,UAAM,YAAY,mBAAmB,KAAK,QAAQ,cAAc,IAAI;AACpE,UAAM,eAAe,mBAAmB,KAAK,QAAQ,iBAAiB,IAAI;AAC1E,QAAI,gBAAgB,OAAO,eAAe,YAAY,eAAe;AACrE,QAAI,gBAAgB,GAAG;AACrB,sBAAgB;AAAA,IAClB;AACA,UAAM,kBAAkB,OAAO,aAAa,EAAE;AAC9C,UAAM,oBAAoB,OAAO,wBAAwB,EAAE;AAC3D,UAAM,eAAe,kBAAkB;AACvC,QAAI,gBAAgB,eAAe,WAAW;AAC5C,qBAAe,gBAAgB;AAAA,IACjC;AACA,UAAM,YAAY,aAAa,MAAM;AACrC,QAAI,aAAa,eAAe,WAAW;AACzC,qBAAe;AACf,sBAAgB,QAAQ,IAAI;AAAA,IAC9B,OAAO;AACL,sBAAgB,QAAQ,KAAK;AAAA,IAC/B;AACA,QAAI,iBAAiB,QAAQ,IAAI,GAAG;AAClC,YAAM,YAAY,eAAe,QAAQ,IAAI;AAC7C,UAAI,SAAS,OAAO,aAAa,GAAG,UAAU,eAAe,IAAI;AACjE,cAAQ,IAAI,YAAY;AACxB,uBAAiB,MAAM;AACvB,UAAI,OAAO,QAAQ,SAAS,MAAM,OAAO,GAAG,QAAQ,KAAK,OAAO,GAAG,MAAM,IAAI;AAC3E,cAAM,MAAM,OAAO,OAAO;AAC1B,YAAI,SAAS,IAAI,aAAa,IAAI,WAAW;AAAA,MAC/C;AACA,UAAI,OAAO,SAAS,KAAK,qBAAqB,OAAO,GAAG;AACtD,eAAO,UAAU,eAAe;AAAA,MAClC;AACA,WAAK,OAAO,QAAQ,SAAS,KAAK,OAAO,QAAQ,WAAW,MAAM,YAAY,GAAG;AAC/E,eAAO,QAAQ,SAAS,SAAS,oBAAoB;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,QAAQ,YAAY;AACjC,QAAI,uBAAuB,MAAM,0BAA0B,MAAM;AACjE,QAAI;AACJ,QAAI;AACJ,WAAO,GAAG,QAAQ,OAAK;AACrB,sBAAgB;AAChB,YAAM,kBAAkB,6BAA6B,MAAM;AAC3D,YAAM,MAAM,OAAO;AACnB,UAAI,UAAU,OAAO,OAAO,EAAE,iBAAiB,EAAE,QAAQ,OAAO,CAAC;AACjE,UAAI,OAAO,QAAQ,OAAO,KAAK,OAAO,QAAQ,KAAK,GAAG;AACpD,YAAI,UAAU,OAAO,QAAQ,GAAG;AAAA,UAC9B,eAAe;AAAA,UACf,gBAAgB;AAAA,UAChB,cAAc;AAAA,QAChB,CAAC;AAAA,MACH,OAAO;AACL,YAAI,UAAU,OAAO,QAAQ,GAAG;AAAA,UAC9B,aAAa;AAAA,UACb,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AACA,aAAO,QAAQ,SAAS,GAAG,oBAAoB;AAC/C,uBAAiB;AAAA,IACnB,CAAC;AACD,WAAO,GAAG,oEAAoE,OAAK;AACjF,UAAI,kBAAkB,GAAG;AACvB,+BAAuB,OAAO,aAAa,EAAE;AAC7C,eAAO,QAAQ,SAAS,GAAG,oBAAoB;AAC/C,yBAAiB;AAAA,MACnB,WAAW,kBAAkB,GAAG;AAC9B,cAAM,YAAY,uBAAuB,OAAO,aAAa,EAAE;AAC/D,YAAI,WAAW;AACb,gBAAM,MAAM,OAAO;AACnB,gBAAM,MAAM,OAAO,OAAO;AAC1B,cAAI,UAAU,IAAI,iBAAiB,EAAE,cAAc,EAAE,CAAC;AACtD,cAAI,UAAU,OAAO,QAAQ,GAAG,EAAE,cAAc,UAAU,CAAC;AAAA,QAC7D;AACA,+BAAuB,YAAY,SAAS,CAAC,IAAI;AACjD,yBAAiB;AAAA,MACnB,OAAO;AACL,eAAO,QAAQ,SAAS,GAAG,oBAAoB;AAAA,MACjD;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,CAAC,QAAQ,YAAY;AACpC,WAAO,WAAW,iBAAiB,MAAM;AACvC,aAAO,QAAQ,OAAO;AAAA,IACxB,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,MAAM;AACjB,aAAS,IAAI,cAAc,YAAU;AACnC,iBAAW,MAAM;AACjB,UAAI,CAAC,OAAO,QAAQ,MAAM,QAAQ,GAAG;AACnC,eAAO,QAAQ,IAAI,UAAU,KAAK;AAAA,MACpC;AACA,UAAI,CAAC,OAAO,QAAQ;AAClB,cAAM,UAAU,KAAK,CAAC;AACtB,iBAAS,QAAQ,OAAO;AACxB,cAAM,QAAQ,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AAEX,GAAG;", "names": []}