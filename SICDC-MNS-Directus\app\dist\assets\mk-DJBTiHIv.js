import{c as i,b as o,i as m,j as s}from"./index.DUmRo3Ep.entry.js";import{i as d}from"./isSameWeek-DOt1VTFz.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";const l={lessThanXSeconds:{one:"помалку од секунда",other:"помалку од {{count}} секунди"},xSeconds:{one:"1 секунда",other:"{{count}} секунди"},halfAMinute:"половина минута",lessThanXMinutes:{one:"помалку од минута",other:"помалку од {{count}} минути"},xMinutes:{one:"1 минута",other:"{{count}} минути"},aboutXHours:{one:"околу 1 час",other:"околу {{count}} часа"},xHours:{one:"1 час",other:"{{count}} часа"},xDays:{one:"1 ден",other:"{{count}} дена"},aboutXWeeks:{one:"околу 1 недела",other:"околу {{count}} месеци"},xWeeks:{one:"1 недела",other:"{{count}} недели"},aboutXMonths:{one:"околу 1 месец",other:"околу {{count}} недели"},xMonths:{one:"1 месец",other:"{{count}} месеци"},aboutXYears:{one:"околу 1 година",other:"околу {{count}} години"},xYears:{one:"1 година",other:"{{count}} години"},overXYears:{one:"повеќе од 1 година",other:"повеќе од {{count}} години"},almostXYears:{one:"безмалку 1 година",other:"безмалку {{count}} години"}},h=(e,t,r)=>{let a;const n=l[e];return typeof n=="string"?a=n:t===1?a=n.one:a=n.other.replace("{{count}}",String(t)),r!=null&&r.addSuffix?r.comparison&&r.comparison>0?"за "+a:"пред "+a:a},f={full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"dd/MM/yyyy"},y={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"H:mm"},P={any:"{{date}} {{time}}"},w={date:i({formats:f,defaultWidth:"full"}),time:i({formats:y,defaultWidth:"full"}),dateTime:i({formats:P,defaultWidth:"any"})},c=["недела","понеделник","вторник","среда","четврток","петок","сабота"];function b(e){const t=c[e];switch(e){case 0:case 3:case 6:return"'минатата "+t+" во' p";case 1:case 2:case 4:case 5:return"'минатиот "+t+" во' p"}}function u(e){const t=c[e];switch(e){case 0:case 3:case 6:return"'ова "+t+" вo' p";case 1:case 2:case 4:case 5:return"'овој "+t+" вo' p"}}function p(e){const t=c[e];switch(e){case 0:case 3:case 6:return"'следната "+t+" вo' p";case 1:case 2:case 4:case 5:return"'следниот "+t+" вo' p"}}const M={lastWeek:(e,t,r)=>{const a=e.getDay();return d(e,t,r)?u(a):b(a)},yesterday:"'вчера во' p",today:"'денес во' p",tomorrow:"'утре во' p",nextWeek:(e,t,r)=>{const a=e.getDay();return d(e,t,r)?u(a):p(a)},other:"P"},W=(e,t,r,a)=>{const n=M[e];return typeof n=="function"?n(t,r,a):n},v={narrow:["пр.н.е.","н.е."],abbreviated:["пред н. е.","н. е."],wide:["пред нашата ера","нашата ера"]},k={narrow:["1","2","3","4"],abbreviated:["1-ви кв.","2-ри кв.","3-ти кв.","4-ти кв."],wide:["1-ви квартал","2-ри квартал","3-ти квартал","4-ти квартал"]},g={abbreviated:["јан","фев","мар","апр","мај","јун","јул","авг","септ","окт","ноем","дек"],wide:["јануари","февруари","март","април","мај","јуни","јули","август","септември","октомври","ноември","декември"]},x={narrow:["Н","П","В","С","Ч","П","С"],short:["не","по","вт","ср","че","пе","са"],abbreviated:["нед","пон","вто","сре","чет","пет","саб"],wide:["недела","понеделник","вторник","среда","четврток","петок","сабота"]},D={wide:{am:"претпладне",pm:"попладне",midnight:"полноќ",noon:"напладне",morning:"наутро",afternoon:"попладне",evening:"навечер",night:"ноќе"}},H=(e,t)=>{const r=Number(e),a=r%100;if(a>20||a<10)switch(a%10){case 1:return r+"-ви";case 2:return r+"-ри";case 7:case 8:return r+"-ми"}return r+"-ти"},z={ordinalNumber:H,era:o({values:v,defaultWidth:"wide"}),quarter:o({values:k,defaultWidth:"wide",argumentCallback:e=>e-1}),month:o({values:g,defaultWidth:"wide"}),day:o({values:x,defaultWidth:"wide"}),dayPeriod:o({values:D,defaultWidth:"wide"})},F=/^(\d+)(-?[врмт][и])?/i,X=/\d+/i,E={narrow:/^((пр)?н\.?\s?е\.?)/i,abbreviated:/^((пр)?н\.?\s?е\.?)/i,wide:/^(пред нашата ера|нашата ера)/i},L={any:[/^п/i,/^н/i]},S={narrow:/^[1234]/i,abbreviated:/^[1234](-?[врт]?и?)? кв.?/i,wide:/^[1234](-?[врт]?и?)? квартал/i},V={any:[/1/i,/2/i,/3/i,/4/i]},N={narrow:/^[нпвсч]/i,short:/^(не|по|вт|ср|че|пе|са)/i,abbreviated:/^(нед|пон|вто|сре|чет|пет|саб)/i,wide:/^(недела|понеделник|вторник|среда|четврток|петок|сабота)/i},C={narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н[ед]/i,/^п[он]/i,/^вт/i,/^ср/i,/^ч[ет]/i,/^п[ет]/i,/^с[аб]/i]},T={abbreviated:/^(јан|фев|мар|апр|мај|јун|јул|авг|сеп|окт|ноем|дек)/i,wide:/^(јануари|февруари|март|април|мај|јуни|јули|август|септември|октомври|ноември|декември)/i},Y={any:[/^ја/i,/^Ф/i,/^мар/i,/^ап/i,/^мај/i,/^јун/i,/^јул/i,/^ав/i,/^се/i,/^окт/i,/^но/i,/^де/i]},q={any:/^(претп|попл|полноќ|утро|пладне|вечер|ноќ)/i},O={any:{am:/претпладне/i,pm:/попладне/i,midnight:/полноќ/i,noon:/напладне/i,morning:/наутро/i,afternoon:/попладне/i,evening:/навечер/i,night:/ноќе/i}},R={ordinalNumber:m({matchPattern:F,parsePattern:X,valueCallback:e=>parseInt(e,10)}),era:s({matchPatterns:E,defaultMatchWidth:"wide",parsePatterns:L,defaultParseWidth:"any"}),quarter:s({matchPatterns:S,defaultMatchWidth:"wide",parsePatterns:V,defaultParseWidth:"any",valueCallback:e=>e+1}),month:s({matchPatterns:T,defaultMatchWidth:"wide",parsePatterns:Y,defaultParseWidth:"any"}),day:s({matchPatterns:N,defaultMatchWidth:"wide",parsePatterns:C,defaultParseWidth:"any"}),dayPeriod:s({matchPatterns:q,defaultMatchWidth:"any",parsePatterns:O,defaultParseWidth:"any"})},K={code:"mk",formatDistance:h,formatLong:w,formatRelative:W,localize:z,match:R,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{K as default,K as mk};
