#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/copyfiles@2.4.1/node_modules/copyfiles/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/copyfiles@2.4.1/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/copyfiles@2.4.1/node_modules/copyfiles/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/copyfiles@2.4.1/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../copyfiles/copyfiles" "$@"
else
  exec node  "$basedir/../copyfiles/copyfiles" "$@"
fi
