{"version": 3, "sources": ["../../../../node_modules/.pnpm/html-entities@2.5.2/node_modules/html-entities/src/named-references.ts", "../../../../node_modules/.pnpm/html-entities@2.5.2/node_modules/html-entities/src/numeric-unicode-map.ts", "../../../../node_modules/.pnpm/html-entities@2.5.2/node_modules/html-entities/src/surrogate-pairs.ts", "../../../../node_modules/.pnpm/html-entities@2.5.2/node_modules/html-entities/src/index.ts"], "sourcesContent": ["// This file is autogenerated by tools/process-named-references.ts\n/* eslint-disable */\n\nexport type NamedReferences = {\n    [K in 'xml' | 'html4' | 'html5']: {\n        entities: Record<string, string>;\n        characters: Record<string, string>;\n    }\n};\nexport const bodyRegExps = {\n    xml: /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+);?/g,\n    html4: /&notin;|&(?:nbsp|iexcl|cent|pound|curren|yen|brvbar|sect|uml|copy|ordf|laquo|not|shy|reg|macr|deg|plusmn|sup2|sup3|acute|micro|para|middot|cedil|sup1|ordm|raquo|frac14|frac12|frac34|iquest|Agrave|Aacute|Acirc|Atilde|Auml|Aring|AElig|Ccedil|Egrave|Eacute|Ecirc|Euml|Igrave|Iacute|Icirc|Iuml|ETH|Ntilde|Ograve|Oacute|Ocirc|Otilde|Ouml|times|Oslash|Ugrave|Uacute|Ucirc|Uuml|Yacute|THORN|szlig|agrave|aacute|acirc|atilde|auml|aring|aelig|ccedil|egrave|eacute|ecirc|euml|igrave|iacute|icirc|iuml|eth|ntilde|ograve|oacute|ocirc|otilde|ouml|divide|oslash|ugrave|uacute|ucirc|uuml|yacute|thorn|yuml|quot|amp|lt|gt|#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+);?/g,\n    html5: /&centerdot;|&copysr;|&divideontimes;|&gtcc;|&gtcir;|&gtdot;|&gtlPar;|&gtquest;|&gtrapprox;|&gtrarr;|&gtrdot;|&gtreqless;|&gtreqqless;|&gtrless;|&gtrsim;|&ltcc;|&ltcir;|&ltdot;|&lthree;|&ltimes;|&ltlarr;|&ltquest;|&ltrPar;|&ltri;|&ltrie;|&ltrif;|&notin;|&notinE;|&notindot;|&notinva;|&notinvb;|&notinvc;|&notni;|&notniva;|&notnivb;|&notnivc;|&parallel;|&timesb;|&timesbar;|&timesd;|&(?:AElig|AMP|Aacute|Acirc|Agrave|Aring|Atilde|Auml|COPY|Ccedil|ETH|Eacute|Ecirc|Egrave|Euml|GT|Iacute|Icirc|Igrave|Iuml|LT|Ntilde|Oacute|Ocirc|Ograve|Oslash|Otilde|Ouml|QUOT|REG|THORN|Uacute|Ucirc|Ugrave|Uuml|Yacute|aacute|acirc|acute|aelig|agrave|amp|aring|atilde|auml|brvbar|ccedil|cedil|cent|copy|curren|deg|divide|eacute|ecirc|egrave|eth|euml|frac12|frac14|frac34|gt|iacute|icirc|iexcl|igrave|iquest|iuml|laquo|lt|macr|micro|middot|nbsp|not|ntilde|oacute|ocirc|ograve|ordf|ordm|oslash|otilde|ouml|para|plusmn|pound|quot|raquo|reg|sect|shy|sup1|sup2|sup3|szlig|thorn|times|uacute|ucirc|ugrave|uml|uuml|yacute|yen|yuml|#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+);?/g\n};\nexport const namedReferences: NamedReferences = {\n    \"xml\": {\n        \"entities\": {\n            \"&lt;\": \"<\",\n            \"&gt;\": \">\",\n            \"&quot;\": \"\\\"\",\n            \"&apos;\": \"'\",\n            \"&amp;\": \"&\"\n        },\n        \"characters\": {\n            \"<\": \"&lt;\",\n            \">\": \"&gt;\",\n            \"\\\"\": \"&quot;\",\n            \"'\": \"&apos;\",\n            \"&\": \"&amp;\"\n        }\n    },\n    \"html4\": {\n        \"entities\": {\n            \"&apos;\": \"'\",\n            \"&nbsp\": \" \",\n            \"&nbsp;\": \" \",\n            \"&iexcl\": \"¡\",\n            \"&iexcl;\": \"¡\",\n            \"&cent\": \"¢\",\n            \"&cent;\": \"¢\",\n            \"&pound\": \"£\",\n            \"&pound;\": \"£\",\n            \"&curren\": \"¤\",\n            \"&curren;\": \"¤\",\n            \"&yen\": \"¥\",\n            \"&yen;\": \"¥\",\n            \"&brvbar\": \"¦\",\n            \"&brvbar;\": \"¦\",\n            \"&sect\": \"§\",\n            \"&sect;\": \"§\",\n            \"&uml\": \"¨\",\n            \"&uml;\": \"¨\",\n            \"&copy\": \"©\",\n            \"&copy;\": \"©\",\n            \"&ordf\": \"ª\",\n            \"&ordf;\": \"ª\",\n            \"&laquo\": \"«\",\n            \"&laquo;\": \"«\",\n            \"&not\": \"¬\",\n            \"&not;\": \"¬\",\n            \"&shy\": \"­\",\n            \"&shy;\": \"­\",\n            \"&reg\": \"®\",\n            \"&reg;\": \"®\",\n            \"&macr\": \"¯\",\n            \"&macr;\": \"¯\",\n            \"&deg\": \"°\",\n            \"&deg;\": \"°\",\n            \"&plusmn\": \"±\",\n            \"&plusmn;\": \"±\",\n            \"&sup2\": \"²\",\n            \"&sup2;\": \"²\",\n            \"&sup3\": \"³\",\n            \"&sup3;\": \"³\",\n            \"&acute\": \"´\",\n            \"&acute;\": \"´\",\n            \"&micro\": \"µ\",\n            \"&micro;\": \"µ\",\n            \"&para\": \"¶\",\n            \"&para;\": \"¶\",\n            \"&middot\": \"·\",\n            \"&middot;\": \"·\",\n            \"&cedil\": \"¸\",\n            \"&cedil;\": \"¸\",\n            \"&sup1\": \"¹\",\n            \"&sup1;\": \"¹\",\n            \"&ordm\": \"º\",\n            \"&ordm;\": \"º\",\n            \"&raquo\": \"»\",\n            \"&raquo;\": \"»\",\n            \"&frac14\": \"¼\",\n            \"&frac14;\": \"¼\",\n            \"&frac12\": \"½\",\n            \"&frac12;\": \"½\",\n            \"&frac34\": \"¾\",\n            \"&frac34;\": \"¾\",\n            \"&iquest\": \"¿\",\n            \"&iquest;\": \"¿\",\n            \"&Agrave\": \"À\",\n            \"&Agrave;\": \"À\",\n            \"&Aacute\": \"Á\",\n            \"&Aacute;\": \"Á\",\n            \"&Acirc\": \"Â\",\n            \"&Acirc;\": \"Â\",\n            \"&Atilde\": \"Ã\",\n            \"&Atilde;\": \"Ã\",\n            \"&Auml\": \"Ä\",\n            \"&Auml;\": \"Ä\",\n            \"&Aring\": \"Å\",\n            \"&Aring;\": \"Å\",\n            \"&AElig\": \"Æ\",\n            \"&AElig;\": \"Æ\",\n            \"&Ccedil\": \"Ç\",\n            \"&Ccedil;\": \"Ç\",\n            \"&Egrave\": \"È\",\n            \"&Egrave;\": \"È\",\n            \"&Eacute\": \"É\",\n            \"&Eacute;\": \"É\",\n            \"&Ecirc\": \"Ê\",\n            \"&Ecirc;\": \"Ê\",\n            \"&Euml\": \"Ë\",\n            \"&Euml;\": \"Ë\",\n            \"&Igrave\": \"Ì\",\n            \"&Igrave;\": \"Ì\",\n            \"&Iacute\": \"Í\",\n            \"&Iacute;\": \"Í\",\n            \"&Icirc\": \"Î\",\n            \"&Icirc;\": \"Î\",\n            \"&Iuml\": \"Ï\",\n            \"&Iuml;\": \"Ï\",\n            \"&ETH\": \"Ð\",\n            \"&ETH;\": \"Ð\",\n            \"&Ntilde\": \"Ñ\",\n            \"&Ntilde;\": \"Ñ\",\n            \"&Ograve\": \"Ò\",\n            \"&Ograve;\": \"Ò\",\n            \"&Oacute\": \"Ó\",\n            \"&Oacute;\": \"Ó\",\n            \"&Ocirc\": \"Ô\",\n            \"&Ocirc;\": \"Ô\",\n            \"&Otilde\": \"Õ\",\n            \"&Otilde;\": \"Õ\",\n            \"&Ouml\": \"Ö\",\n            \"&Ouml;\": \"Ö\",\n            \"&times\": \"×\",\n            \"&times;\": \"×\",\n            \"&Oslash\": \"Ø\",\n            \"&Oslash;\": \"Ø\",\n            \"&Ugrave\": \"Ù\",\n            \"&Ugrave;\": \"Ù\",\n            \"&Uacute\": \"Ú\",\n            \"&Uacute;\": \"Ú\",\n            \"&Ucirc\": \"Û\",\n            \"&Ucirc;\": \"Û\",\n            \"&Uuml\": \"Ü\",\n            \"&Uuml;\": \"Ü\",\n            \"&Yacute\": \"Ý\",\n            \"&Yacute;\": \"Ý\",\n            \"&THORN\": \"Þ\",\n            \"&THORN;\": \"Þ\",\n            \"&szlig\": \"ß\",\n            \"&szlig;\": \"ß\",\n            \"&agrave\": \"à\",\n            \"&agrave;\": \"à\",\n            \"&aacute\": \"á\",\n            \"&aacute;\": \"á\",\n            \"&acirc\": \"â\",\n            \"&acirc;\": \"â\",\n            \"&atilde\": \"ã\",\n            \"&atilde;\": \"ã\",\n            \"&auml\": \"ä\",\n            \"&auml;\": \"ä\",\n            \"&aring\": \"å\",\n            \"&aring;\": \"å\",\n            \"&aelig\": \"æ\",\n            \"&aelig;\": \"æ\",\n            \"&ccedil\": \"ç\",\n            \"&ccedil;\": \"ç\",\n            \"&egrave\": \"è\",\n            \"&egrave;\": \"è\",\n            \"&eacute\": \"é\",\n            \"&eacute;\": \"é\",\n            \"&ecirc\": \"ê\",\n            \"&ecirc;\": \"ê\",\n            \"&euml\": \"ë\",\n            \"&euml;\": \"ë\",\n            \"&igrave\": \"ì\",\n            \"&igrave;\": \"ì\",\n            \"&iacute\": \"í\",\n            \"&iacute;\": \"í\",\n            \"&icirc\": \"î\",\n            \"&icirc;\": \"î\",\n            \"&iuml\": \"ï\",\n            \"&iuml;\": \"ï\",\n            \"&eth\": \"ð\",\n            \"&eth;\": \"ð\",\n            \"&ntilde\": \"ñ\",\n            \"&ntilde;\": \"ñ\",\n            \"&ograve\": \"ò\",\n            \"&ograve;\": \"ò\",\n            \"&oacute\": \"ó\",\n            \"&oacute;\": \"ó\",\n            \"&ocirc\": \"ô\",\n            \"&ocirc;\": \"ô\",\n            \"&otilde\": \"õ\",\n            \"&otilde;\": \"õ\",\n            \"&ouml\": \"ö\",\n            \"&ouml;\": \"ö\",\n            \"&divide\": \"÷\",\n            \"&divide;\": \"÷\",\n            \"&oslash\": \"ø\",\n            \"&oslash;\": \"ø\",\n            \"&ugrave\": \"ù\",\n            \"&ugrave;\": \"ù\",\n            \"&uacute\": \"ú\",\n            \"&uacute;\": \"ú\",\n            \"&ucirc\": \"û\",\n            \"&ucirc;\": \"û\",\n            \"&uuml\": \"ü\",\n            \"&uuml;\": \"ü\",\n            \"&yacute\": \"ý\",\n            \"&yacute;\": \"ý\",\n            \"&thorn\": \"þ\",\n            \"&thorn;\": \"þ\",\n            \"&yuml\": \"ÿ\",\n            \"&yuml;\": \"ÿ\",\n            \"&quot\": \"\\\"\",\n            \"&quot;\": \"\\\"\",\n            \"&amp\": \"&\",\n            \"&amp;\": \"&\",\n            \"&lt\": \"<\",\n            \"&lt;\": \"<\",\n            \"&gt\": \">\",\n            \"&gt;\": \">\",\n            \"&OElig;\": \"Œ\",\n            \"&oelig;\": \"œ\",\n            \"&Scaron;\": \"Š\",\n            \"&scaron;\": \"š\",\n            \"&Yuml;\": \"Ÿ\",\n            \"&circ;\": \"ˆ\",\n            \"&tilde;\": \"˜\",\n            \"&ensp;\": \" \",\n            \"&emsp;\": \" \",\n            \"&thinsp;\": \" \",\n            \"&zwnj;\": \"‌\",\n            \"&zwj;\": \"‍\",\n            \"&lrm;\": \"‎\",\n            \"&rlm;\": \"‏\",\n            \"&ndash;\": \"–\",\n            \"&mdash;\": \"—\",\n            \"&lsquo;\": \"‘\",\n            \"&rsquo;\": \"’\",\n            \"&sbquo;\": \"‚\",\n            \"&ldquo;\": \"“\",\n            \"&rdquo;\": \"”\",\n            \"&bdquo;\": \"„\",\n            \"&dagger;\": \"†\",\n            \"&Dagger;\": \"‡\",\n            \"&permil;\": \"‰\",\n            \"&lsaquo;\": \"‹\",\n            \"&rsaquo;\": \"›\",\n            \"&euro;\": \"€\",\n            \"&fnof;\": \"ƒ\",\n            \"&Alpha;\": \"Α\",\n            \"&Beta;\": \"Β\",\n            \"&Gamma;\": \"Γ\",\n            \"&Delta;\": \"Δ\",\n            \"&Epsilon;\": \"Ε\",\n            \"&Zeta;\": \"Ζ\",\n            \"&Eta;\": \"Η\",\n            \"&Theta;\": \"Θ\",\n            \"&Iota;\": \"Ι\",\n            \"&Kappa;\": \"Κ\",\n            \"&Lambda;\": \"Λ\",\n            \"&Mu;\": \"Μ\",\n            \"&Nu;\": \"Ν\",\n            \"&Xi;\": \"Ξ\",\n            \"&Omicron;\": \"Ο\",\n            \"&Pi;\": \"Π\",\n            \"&Rho;\": \"Ρ\",\n            \"&Sigma;\": \"Σ\",\n            \"&Tau;\": \"Τ\",\n            \"&Upsilon;\": \"Υ\",\n            \"&Phi;\": \"Φ\",\n            \"&Chi;\": \"Χ\",\n            \"&Psi;\": \"Ψ\",\n            \"&Omega;\": \"Ω\",\n            \"&alpha;\": \"α\",\n            \"&beta;\": \"β\",\n            \"&gamma;\": \"γ\",\n            \"&delta;\": \"δ\",\n            \"&epsilon;\": \"ε\",\n            \"&zeta;\": \"ζ\",\n            \"&eta;\": \"η\",\n            \"&theta;\": \"θ\",\n            \"&iota;\": \"ι\",\n            \"&kappa;\": \"κ\",\n            \"&lambda;\": \"λ\",\n            \"&mu;\": \"μ\",\n            \"&nu;\": \"ν\",\n            \"&xi;\": \"ξ\",\n            \"&omicron;\": \"ο\",\n            \"&pi;\": \"π\",\n            \"&rho;\": \"ρ\",\n            \"&sigmaf;\": \"ς\",\n            \"&sigma;\": \"σ\",\n            \"&tau;\": \"τ\",\n            \"&upsilon;\": \"υ\",\n            \"&phi;\": \"φ\",\n            \"&chi;\": \"χ\",\n            \"&psi;\": \"ψ\",\n            \"&omega;\": \"ω\",\n            \"&thetasym;\": \"ϑ\",\n            \"&upsih;\": \"ϒ\",\n            \"&piv;\": \"ϖ\",\n            \"&bull;\": \"•\",\n            \"&hellip;\": \"…\",\n            \"&prime;\": \"′\",\n            \"&Prime;\": \"″\",\n            \"&oline;\": \"‾\",\n            \"&frasl;\": \"⁄\",\n            \"&weierp;\": \"℘\",\n            \"&image;\": \"ℑ\",\n            \"&real;\": \"ℜ\",\n            \"&trade;\": \"™\",\n            \"&alefsym;\": \"ℵ\",\n            \"&larr;\": \"←\",\n            \"&uarr;\": \"↑\",\n            \"&rarr;\": \"→\",\n            \"&darr;\": \"↓\",\n            \"&harr;\": \"↔\",\n            \"&crarr;\": \"↵\",\n            \"&lArr;\": \"⇐\",\n            \"&uArr;\": \"⇑\",\n            \"&rArr;\": \"⇒\",\n            \"&dArr;\": \"⇓\",\n            \"&hArr;\": \"⇔\",\n            \"&forall;\": \"∀\",\n            \"&part;\": \"∂\",\n            \"&exist;\": \"∃\",\n            \"&empty;\": \"∅\",\n            \"&nabla;\": \"∇\",\n            \"&isin;\": \"∈\",\n            \"&notin;\": \"∉\",\n            \"&ni;\": \"∋\",\n            \"&prod;\": \"∏\",\n            \"&sum;\": \"∑\",\n            \"&minus;\": \"−\",\n            \"&lowast;\": \"∗\",\n            \"&radic;\": \"√\",\n            \"&prop;\": \"∝\",\n            \"&infin;\": \"∞\",\n            \"&ang;\": \"∠\",\n            \"&and;\": \"∧\",\n            \"&or;\": \"∨\",\n            \"&cap;\": \"∩\",\n            \"&cup;\": \"∪\",\n            \"&int;\": \"∫\",\n            \"&there4;\": \"∴\",\n            \"&sim;\": \"∼\",\n            \"&cong;\": \"≅\",\n            \"&asymp;\": \"≈\",\n            \"&ne;\": \"≠\",\n            \"&equiv;\": \"≡\",\n            \"&le;\": \"≤\",\n            \"&ge;\": \"≥\",\n            \"&sub;\": \"⊂\",\n            \"&sup;\": \"⊃\",\n            \"&nsub;\": \"⊄\",\n            \"&sube;\": \"⊆\",\n            \"&supe;\": \"⊇\",\n            \"&oplus;\": \"⊕\",\n            \"&otimes;\": \"⊗\",\n            \"&perp;\": \"⊥\",\n            \"&sdot;\": \"⋅\",\n            \"&lceil;\": \"⌈\",\n            \"&rceil;\": \"⌉\",\n            \"&lfloor;\": \"⌊\",\n            \"&rfloor;\": \"⌋\",\n            \"&lang;\": \"〈\",\n            \"&rang;\": \"〉\",\n            \"&loz;\": \"◊\",\n            \"&spades;\": \"♠\",\n            \"&clubs;\": \"♣\",\n            \"&hearts;\": \"♥\",\n            \"&diams;\": \"♦\"\n        },\n        \"characters\": {\n            \"'\": \"&apos;\",\n            \" \": \"&nbsp;\",\n            \"¡\": \"&iexcl;\",\n            \"¢\": \"&cent;\",\n            \"£\": \"&pound;\",\n            \"¤\": \"&curren;\",\n            \"¥\": \"&yen;\",\n            \"¦\": \"&brvbar;\",\n            \"§\": \"&sect;\",\n            \"¨\": \"&uml;\",\n            \"©\": \"&copy;\",\n            \"ª\": \"&ordf;\",\n            \"«\": \"&laquo;\",\n            \"¬\": \"&not;\",\n            \"­\": \"&shy;\",\n            \"®\": \"&reg;\",\n            \"¯\": \"&macr;\",\n            \"°\": \"&deg;\",\n            \"±\": \"&plusmn;\",\n            \"²\": \"&sup2;\",\n            \"³\": \"&sup3;\",\n            \"´\": \"&acute;\",\n            \"µ\": \"&micro;\",\n            \"¶\": \"&para;\",\n            \"·\": \"&middot;\",\n            \"¸\": \"&cedil;\",\n            \"¹\": \"&sup1;\",\n            \"º\": \"&ordm;\",\n            \"»\": \"&raquo;\",\n            \"¼\": \"&frac14;\",\n            \"½\": \"&frac12;\",\n            \"¾\": \"&frac34;\",\n            \"¿\": \"&iquest;\",\n            \"À\": \"&Agrave;\",\n            \"Á\": \"&Aacute;\",\n            \"Â\": \"&Acirc;\",\n            \"Ã\": \"&Atilde;\",\n            \"Ä\": \"&Auml;\",\n            \"Å\": \"&Aring;\",\n            \"Æ\": \"&AElig;\",\n            \"Ç\": \"&Ccedil;\",\n            \"È\": \"&Egrave;\",\n            \"É\": \"&Eacute;\",\n            \"Ê\": \"&Ecirc;\",\n            \"Ë\": \"&Euml;\",\n            \"Ì\": \"&Igrave;\",\n            \"Í\": \"&Iacute;\",\n            \"Î\": \"&Icirc;\",\n            \"Ï\": \"&Iuml;\",\n            \"Ð\": \"&ETH;\",\n            \"Ñ\": \"&Ntilde;\",\n            \"Ò\": \"&Ograve;\",\n            \"Ó\": \"&Oacute;\",\n            \"Ô\": \"&Ocirc;\",\n            \"Õ\": \"&Otilde;\",\n            \"Ö\": \"&Ouml;\",\n            \"×\": \"&times;\",\n            \"Ø\": \"&Oslash;\",\n            \"Ù\": \"&Ugrave;\",\n            \"Ú\": \"&Uacute;\",\n            \"Û\": \"&Ucirc;\",\n            \"Ü\": \"&Uuml;\",\n            \"Ý\": \"&Yacute;\",\n            \"Þ\": \"&THORN;\",\n            \"ß\": \"&szlig;\",\n            \"à\": \"&agrave;\",\n            \"á\": \"&aacute;\",\n            \"â\": \"&acirc;\",\n            \"ã\": \"&atilde;\",\n            \"ä\": \"&auml;\",\n            \"å\": \"&aring;\",\n            \"æ\": \"&aelig;\",\n            \"ç\": \"&ccedil;\",\n            \"è\": \"&egrave;\",\n            \"é\": \"&eacute;\",\n            \"ê\": \"&ecirc;\",\n            \"ë\": \"&euml;\",\n            \"ì\": \"&igrave;\",\n            \"í\": \"&iacute;\",\n            \"î\": \"&icirc;\",\n            \"ï\": \"&iuml;\",\n            \"ð\": \"&eth;\",\n            \"ñ\": \"&ntilde;\",\n            \"ò\": \"&ograve;\",\n            \"ó\": \"&oacute;\",\n            \"ô\": \"&ocirc;\",\n            \"õ\": \"&otilde;\",\n            \"ö\": \"&ouml;\",\n            \"÷\": \"&divide;\",\n            \"ø\": \"&oslash;\",\n            \"ù\": \"&ugrave;\",\n            \"ú\": \"&uacute;\",\n            \"û\": \"&ucirc;\",\n            \"ü\": \"&uuml;\",\n            \"ý\": \"&yacute;\",\n            \"þ\": \"&thorn;\",\n            \"ÿ\": \"&yuml;\",\n            \"\\\"\": \"&quot;\",\n            \"&\": \"&amp;\",\n            \"<\": \"&lt;\",\n            \">\": \"&gt;\",\n            \"Œ\": \"&OElig;\",\n            \"œ\": \"&oelig;\",\n            \"Š\": \"&Scaron;\",\n            \"š\": \"&scaron;\",\n            \"Ÿ\": \"&Yuml;\",\n            \"ˆ\": \"&circ;\",\n            \"˜\": \"&tilde;\",\n            \" \": \"&ensp;\",\n            \" \": \"&emsp;\",\n            \" \": \"&thinsp;\",\n            \"‌\": \"&zwnj;\",\n            \"‍\": \"&zwj;\",\n            \"‎\": \"&lrm;\",\n            \"‏\": \"&rlm;\",\n            \"–\": \"&ndash;\",\n            \"—\": \"&mdash;\",\n            \"‘\": \"&lsquo;\",\n            \"’\": \"&rsquo;\",\n            \"‚\": \"&sbquo;\",\n            \"“\": \"&ldquo;\",\n            \"”\": \"&rdquo;\",\n            \"„\": \"&bdquo;\",\n            \"†\": \"&dagger;\",\n            \"‡\": \"&Dagger;\",\n            \"‰\": \"&permil;\",\n            \"‹\": \"&lsaquo;\",\n            \"›\": \"&rsaquo;\",\n            \"€\": \"&euro;\",\n            \"ƒ\": \"&fnof;\",\n            \"Α\": \"&Alpha;\",\n            \"Β\": \"&Beta;\",\n            \"Γ\": \"&Gamma;\",\n            \"Δ\": \"&Delta;\",\n            \"Ε\": \"&Epsilon;\",\n            \"Ζ\": \"&Zeta;\",\n            \"Η\": \"&Eta;\",\n            \"Θ\": \"&Theta;\",\n            \"Ι\": \"&Iota;\",\n            \"Κ\": \"&Kappa;\",\n            \"Λ\": \"&Lambda;\",\n            \"Μ\": \"&Mu;\",\n            \"Ν\": \"&Nu;\",\n            \"Ξ\": \"&Xi;\",\n            \"Ο\": \"&Omicron;\",\n            \"Π\": \"&Pi;\",\n            \"Ρ\": \"&Rho;\",\n            \"Σ\": \"&Sigma;\",\n            \"Τ\": \"&Tau;\",\n            \"Υ\": \"&Upsilon;\",\n            \"Φ\": \"&Phi;\",\n            \"Χ\": \"&Chi;\",\n            \"Ψ\": \"&Psi;\",\n            \"Ω\": \"&Omega;\",\n            \"α\": \"&alpha;\",\n            \"β\": \"&beta;\",\n            \"γ\": \"&gamma;\",\n            \"δ\": \"&delta;\",\n            \"ε\": \"&epsilon;\",\n            \"ζ\": \"&zeta;\",\n            \"η\": \"&eta;\",\n            \"θ\": \"&theta;\",\n            \"ι\": \"&iota;\",\n            \"κ\": \"&kappa;\",\n            \"λ\": \"&lambda;\",\n            \"μ\": \"&mu;\",\n            \"ν\": \"&nu;\",\n            \"ξ\": \"&xi;\",\n            \"ο\": \"&omicron;\",\n            \"π\": \"&pi;\",\n            \"ρ\": \"&rho;\",\n            \"ς\": \"&sigmaf;\",\n            \"σ\": \"&sigma;\",\n            \"τ\": \"&tau;\",\n            \"υ\": \"&upsilon;\",\n            \"φ\": \"&phi;\",\n            \"χ\": \"&chi;\",\n            \"ψ\": \"&psi;\",\n            \"ω\": \"&omega;\",\n            \"ϑ\": \"&thetasym;\",\n            \"ϒ\": \"&upsih;\",\n            \"ϖ\": \"&piv;\",\n            \"•\": \"&bull;\",\n            \"…\": \"&hellip;\",\n            \"′\": \"&prime;\",\n            \"″\": \"&Prime;\",\n            \"‾\": \"&oline;\",\n            \"⁄\": \"&frasl;\",\n            \"℘\": \"&weierp;\",\n            \"ℑ\": \"&image;\",\n            \"ℜ\": \"&real;\",\n            \"™\": \"&trade;\",\n            \"ℵ\": \"&alefsym;\",\n            \"←\": \"&larr;\",\n            \"↑\": \"&uarr;\",\n            \"→\": \"&rarr;\",\n            \"↓\": \"&darr;\",\n            \"↔\": \"&harr;\",\n            \"↵\": \"&crarr;\",\n            \"⇐\": \"&lArr;\",\n            \"⇑\": \"&uArr;\",\n            \"⇒\": \"&rArr;\",\n            \"⇓\": \"&dArr;\",\n            \"⇔\": \"&hArr;\",\n            \"∀\": \"&forall;\",\n            \"∂\": \"&part;\",\n            \"∃\": \"&exist;\",\n            \"∅\": \"&empty;\",\n            \"∇\": \"&nabla;\",\n            \"∈\": \"&isin;\",\n            \"∉\": \"&notin;\",\n            \"∋\": \"&ni;\",\n            \"∏\": \"&prod;\",\n            \"∑\": \"&sum;\",\n            \"−\": \"&minus;\",\n            \"∗\": \"&lowast;\",\n            \"√\": \"&radic;\",\n            \"∝\": \"&prop;\",\n            \"∞\": \"&infin;\",\n            \"∠\": \"&ang;\",\n            \"∧\": \"&and;\",\n            \"∨\": \"&or;\",\n            \"∩\": \"&cap;\",\n            \"∪\": \"&cup;\",\n            \"∫\": \"&int;\",\n            \"∴\": \"&there4;\",\n            \"∼\": \"&sim;\",\n            \"≅\": \"&cong;\",\n            \"≈\": \"&asymp;\",\n            \"≠\": \"&ne;\",\n            \"≡\": \"&equiv;\",\n            \"≤\": \"&le;\",\n            \"≥\": \"&ge;\",\n            \"⊂\": \"&sub;\",\n            \"⊃\": \"&sup;\",\n            \"⊄\": \"&nsub;\",\n            \"⊆\": \"&sube;\",\n            \"⊇\": \"&supe;\",\n            \"⊕\": \"&oplus;\",\n            \"⊗\": \"&otimes;\",\n            \"⊥\": \"&perp;\",\n            \"⋅\": \"&sdot;\",\n            \"⌈\": \"&lceil;\",\n            \"⌉\": \"&rceil;\",\n            \"⌊\": \"&lfloor;\",\n            \"⌋\": \"&rfloor;\",\n            \"〈\": \"&lang;\",\n            \"〉\": \"&rang;\",\n            \"◊\": \"&loz;\",\n            \"♠\": \"&spades;\",\n            \"♣\": \"&clubs;\",\n            \"♥\": \"&hearts;\",\n            \"♦\": \"&diams;\"\n        }\n    },\n    \"html5\": {\n        \"entities\": {\n            \"&AElig\": \"Æ\",\n            \"&AElig;\": \"Æ\",\n            \"&AMP\": \"&\",\n            \"&AMP;\": \"&\",\n            \"&Aacute\": \"Á\",\n            \"&Aacute;\": \"Á\",\n            \"&Abreve;\": \"Ă\",\n            \"&Acirc\": \"Â\",\n            \"&Acirc;\": \"Â\",\n            \"&Acy;\": \"А\",\n            \"&Afr;\": \"𝔄\",\n            \"&Agrave\": \"À\",\n            \"&Agrave;\": \"À\",\n            \"&Alpha;\": \"Α\",\n            \"&Amacr;\": \"Ā\",\n            \"&And;\": \"⩓\",\n            \"&Aogon;\": \"Ą\",\n            \"&Aopf;\": \"𝔸\",\n            \"&ApplyFunction;\": \"⁡\",\n            \"&Aring\": \"Å\",\n            \"&Aring;\": \"Å\",\n            \"&Ascr;\": \"𝒜\",\n            \"&Assign;\": \"≔\",\n            \"&Atilde\": \"Ã\",\n            \"&Atilde;\": \"Ã\",\n            \"&Auml\": \"Ä\",\n            \"&Auml;\": \"Ä\",\n            \"&Backslash;\": \"∖\",\n            \"&Barv;\": \"⫧\",\n            \"&Barwed;\": \"⌆\",\n            \"&Bcy;\": \"Б\",\n            \"&Because;\": \"∵\",\n            \"&Bernoullis;\": \"ℬ\",\n            \"&Beta;\": \"Β\",\n            \"&Bfr;\": \"𝔅\",\n            \"&Bopf;\": \"𝔹\",\n            \"&Breve;\": \"˘\",\n            \"&Bscr;\": \"ℬ\",\n            \"&Bumpeq;\": \"≎\",\n            \"&CHcy;\": \"Ч\",\n            \"&COPY\": \"©\",\n            \"&COPY;\": \"©\",\n            \"&Cacute;\": \"Ć\",\n            \"&Cap;\": \"⋒\",\n            \"&CapitalDifferentialD;\": \"ⅅ\",\n            \"&Cayleys;\": \"ℭ\",\n            \"&Ccaron;\": \"Č\",\n            \"&Ccedil\": \"Ç\",\n            \"&Ccedil;\": \"Ç\",\n            \"&Ccirc;\": \"Ĉ\",\n            \"&Cconint;\": \"∰\",\n            \"&Cdot;\": \"Ċ\",\n            \"&Cedilla;\": \"¸\",\n            \"&CenterDot;\": \"·\",\n            \"&Cfr;\": \"ℭ\",\n            \"&Chi;\": \"Χ\",\n            \"&CircleDot;\": \"⊙\",\n            \"&CircleMinus;\": \"⊖\",\n            \"&CirclePlus;\": \"⊕\",\n            \"&CircleTimes;\": \"⊗\",\n            \"&ClockwiseContourIntegral;\": \"∲\",\n            \"&CloseCurlyDoubleQuote;\": \"”\",\n            \"&CloseCurlyQuote;\": \"’\",\n            \"&Colon;\": \"∷\",\n            \"&Colone;\": \"⩴\",\n            \"&Congruent;\": \"≡\",\n            \"&Conint;\": \"∯\",\n            \"&ContourIntegral;\": \"∮\",\n            \"&Copf;\": \"ℂ\",\n            \"&Coproduct;\": \"∐\",\n            \"&CounterClockwiseContourIntegral;\": \"∳\",\n            \"&Cross;\": \"⨯\",\n            \"&Cscr;\": \"𝒞\",\n            \"&Cup;\": \"⋓\",\n            \"&CupCap;\": \"≍\",\n            \"&DD;\": \"ⅅ\",\n            \"&DDotrahd;\": \"⤑\",\n            \"&DJcy;\": \"Ђ\",\n            \"&DScy;\": \"Ѕ\",\n            \"&DZcy;\": \"Џ\",\n            \"&Dagger;\": \"‡\",\n            \"&Darr;\": \"↡\",\n            \"&Dashv;\": \"⫤\",\n            \"&Dcaron;\": \"Ď\",\n            \"&Dcy;\": \"Д\",\n            \"&Del;\": \"∇\",\n            \"&Delta;\": \"Δ\",\n            \"&Dfr;\": \"𝔇\",\n            \"&DiacriticalAcute;\": \"´\",\n            \"&DiacriticalDot;\": \"˙\",\n            \"&DiacriticalDoubleAcute;\": \"˝\",\n            \"&DiacriticalGrave;\": \"`\",\n            \"&DiacriticalTilde;\": \"˜\",\n            \"&Diamond;\": \"⋄\",\n            \"&DifferentialD;\": \"ⅆ\",\n            \"&Dopf;\": \"𝔻\",\n            \"&Dot;\": \"¨\",\n            \"&DotDot;\": \"⃜\",\n            \"&DotEqual;\": \"≐\",\n            \"&DoubleContourIntegral;\": \"∯\",\n            \"&DoubleDot;\": \"¨\",\n            \"&DoubleDownArrow;\": \"⇓\",\n            \"&DoubleLeftArrow;\": \"⇐\",\n            \"&DoubleLeftRightArrow;\": \"⇔\",\n            \"&DoubleLeftTee;\": \"⫤\",\n            \"&DoubleLongLeftArrow;\": \"⟸\",\n            \"&DoubleLongLeftRightArrow;\": \"⟺\",\n            \"&DoubleLongRightArrow;\": \"⟹\",\n            \"&DoubleRightArrow;\": \"⇒\",\n            \"&DoubleRightTee;\": \"⊨\",\n            \"&DoubleUpArrow;\": \"⇑\",\n            \"&DoubleUpDownArrow;\": \"⇕\",\n            \"&DoubleVerticalBar;\": \"∥\",\n            \"&DownArrow;\": \"↓\",\n            \"&DownArrowBar;\": \"⤓\",\n            \"&DownArrowUpArrow;\": \"⇵\",\n            \"&DownBreve;\": \"̑\",\n            \"&DownLeftRightVector;\": \"⥐\",\n            \"&DownLeftTeeVector;\": \"⥞\",\n            \"&DownLeftVector;\": \"↽\",\n            \"&DownLeftVectorBar;\": \"⥖\",\n            \"&DownRightTeeVector;\": \"⥟\",\n            \"&DownRightVector;\": \"⇁\",\n            \"&DownRightVectorBar;\": \"⥗\",\n            \"&DownTee;\": \"⊤\",\n            \"&DownTeeArrow;\": \"↧\",\n            \"&Downarrow;\": \"⇓\",\n            \"&Dscr;\": \"𝒟\",\n            \"&Dstrok;\": \"Đ\",\n            \"&ENG;\": \"Ŋ\",\n            \"&ETH\": \"Ð\",\n            \"&ETH;\": \"Ð\",\n            \"&Eacute\": \"É\",\n            \"&Eacute;\": \"É\",\n            \"&Ecaron;\": \"Ě\",\n            \"&Ecirc\": \"Ê\",\n            \"&Ecirc;\": \"Ê\",\n            \"&Ecy;\": \"Э\",\n            \"&Edot;\": \"Ė\",\n            \"&Efr;\": \"𝔈\",\n            \"&Egrave\": \"È\",\n            \"&Egrave;\": \"È\",\n            \"&Element;\": \"∈\",\n            \"&Emacr;\": \"Ē\",\n            \"&EmptySmallSquare;\": \"◻\",\n            \"&EmptyVerySmallSquare;\": \"▫\",\n            \"&Eogon;\": \"Ę\",\n            \"&Eopf;\": \"𝔼\",\n            \"&Epsilon;\": \"Ε\",\n            \"&Equal;\": \"⩵\",\n            \"&EqualTilde;\": \"≂\",\n            \"&Equilibrium;\": \"⇌\",\n            \"&Escr;\": \"ℰ\",\n            \"&Esim;\": \"⩳\",\n            \"&Eta;\": \"Η\",\n            \"&Euml\": \"Ë\",\n            \"&Euml;\": \"Ë\",\n            \"&Exists;\": \"∃\",\n            \"&ExponentialE;\": \"ⅇ\",\n            \"&Fcy;\": \"Ф\",\n            \"&Ffr;\": \"𝔉\",\n            \"&FilledSmallSquare;\": \"◼\",\n            \"&FilledVerySmallSquare;\": \"▪\",\n            \"&Fopf;\": \"𝔽\",\n            \"&ForAll;\": \"∀\",\n            \"&Fouriertrf;\": \"ℱ\",\n            \"&Fscr;\": \"ℱ\",\n            \"&GJcy;\": \"Ѓ\",\n            \"&GT\": \">\",\n            \"&GT;\": \">\",\n            \"&Gamma;\": \"Γ\",\n            \"&Gammad;\": \"Ϝ\",\n            \"&Gbreve;\": \"Ğ\",\n            \"&Gcedil;\": \"Ģ\",\n            \"&Gcirc;\": \"Ĝ\",\n            \"&Gcy;\": \"Г\",\n            \"&Gdot;\": \"Ġ\",\n            \"&Gfr;\": \"𝔊\",\n            \"&Gg;\": \"⋙\",\n            \"&Gopf;\": \"𝔾\",\n            \"&GreaterEqual;\": \"≥\",\n            \"&GreaterEqualLess;\": \"⋛\",\n            \"&GreaterFullEqual;\": \"≧\",\n            \"&GreaterGreater;\": \"⪢\",\n            \"&GreaterLess;\": \"≷\",\n            \"&GreaterSlantEqual;\": \"⩾\",\n            \"&GreaterTilde;\": \"≳\",\n            \"&Gscr;\": \"𝒢\",\n            \"&Gt;\": \"≫\",\n            \"&HARDcy;\": \"Ъ\",\n            \"&Hacek;\": \"ˇ\",\n            \"&Hat;\": \"^\",\n            \"&Hcirc;\": \"Ĥ\",\n            \"&Hfr;\": \"ℌ\",\n            \"&HilbertSpace;\": \"ℋ\",\n            \"&Hopf;\": \"ℍ\",\n            \"&HorizontalLine;\": \"─\",\n            \"&Hscr;\": \"ℋ\",\n            \"&Hstrok;\": \"Ħ\",\n            \"&HumpDownHump;\": \"≎\",\n            \"&HumpEqual;\": \"≏\",\n            \"&IEcy;\": \"Е\",\n            \"&IJlig;\": \"Ĳ\",\n            \"&IOcy;\": \"Ё\",\n            \"&Iacute\": \"Í\",\n            \"&Iacute;\": \"Í\",\n            \"&Icirc\": \"Î\",\n            \"&Icirc;\": \"Î\",\n            \"&Icy;\": \"И\",\n            \"&Idot;\": \"İ\",\n            \"&Ifr;\": \"ℑ\",\n            \"&Igrave\": \"Ì\",\n            \"&Igrave;\": \"Ì\",\n            \"&Im;\": \"ℑ\",\n            \"&Imacr;\": \"Ī\",\n            \"&ImaginaryI;\": \"ⅈ\",\n            \"&Implies;\": \"⇒\",\n            \"&Int;\": \"∬\",\n            \"&Integral;\": \"∫\",\n            \"&Intersection;\": \"⋂\",\n            \"&InvisibleComma;\": \"⁣\",\n            \"&InvisibleTimes;\": \"⁢\",\n            \"&Iogon;\": \"Į\",\n            \"&Iopf;\": \"𝕀\",\n            \"&Iota;\": \"Ι\",\n            \"&Iscr;\": \"ℐ\",\n            \"&Itilde;\": \"Ĩ\",\n            \"&Iukcy;\": \"І\",\n            \"&Iuml\": \"Ï\",\n            \"&Iuml;\": \"Ï\",\n            \"&Jcirc;\": \"Ĵ\",\n            \"&Jcy;\": \"Й\",\n            \"&Jfr;\": \"𝔍\",\n            \"&Jopf;\": \"𝕁\",\n            \"&Jscr;\": \"𝒥\",\n            \"&Jsercy;\": \"Ј\",\n            \"&Jukcy;\": \"Є\",\n            \"&KHcy;\": \"Х\",\n            \"&KJcy;\": \"Ќ\",\n            \"&Kappa;\": \"Κ\",\n            \"&Kcedil;\": \"Ķ\",\n            \"&Kcy;\": \"К\",\n            \"&Kfr;\": \"𝔎\",\n            \"&Kopf;\": \"𝕂\",\n            \"&Kscr;\": \"𝒦\",\n            \"&LJcy;\": \"Љ\",\n            \"&LT\": \"<\",\n            \"&LT;\": \"<\",\n            \"&Lacute;\": \"Ĺ\",\n            \"&Lambda;\": \"Λ\",\n            \"&Lang;\": \"⟪\",\n            \"&Laplacetrf;\": \"ℒ\",\n            \"&Larr;\": \"↞\",\n            \"&Lcaron;\": \"Ľ\",\n            \"&Lcedil;\": \"Ļ\",\n            \"&Lcy;\": \"Л\",\n            \"&LeftAngleBracket;\": \"⟨\",\n            \"&LeftArrow;\": \"←\",\n            \"&LeftArrowBar;\": \"⇤\",\n            \"&LeftArrowRightArrow;\": \"⇆\",\n            \"&LeftCeiling;\": \"⌈\",\n            \"&LeftDoubleBracket;\": \"⟦\",\n            \"&LeftDownTeeVector;\": \"⥡\",\n            \"&LeftDownVector;\": \"⇃\",\n            \"&LeftDownVectorBar;\": \"⥙\",\n            \"&LeftFloor;\": \"⌊\",\n            \"&LeftRightArrow;\": \"↔\",\n            \"&LeftRightVector;\": \"⥎\",\n            \"&LeftTee;\": \"⊣\",\n            \"&LeftTeeArrow;\": \"↤\",\n            \"&LeftTeeVector;\": \"⥚\",\n            \"&LeftTriangle;\": \"⊲\",\n            \"&LeftTriangleBar;\": \"⧏\",\n            \"&LeftTriangleEqual;\": \"⊴\",\n            \"&LeftUpDownVector;\": \"⥑\",\n            \"&LeftUpTeeVector;\": \"⥠\",\n            \"&LeftUpVector;\": \"↿\",\n            \"&LeftUpVectorBar;\": \"⥘\",\n            \"&LeftVector;\": \"↼\",\n            \"&LeftVectorBar;\": \"⥒\",\n            \"&Leftarrow;\": \"⇐\",\n            \"&Leftrightarrow;\": \"⇔\",\n            \"&LessEqualGreater;\": \"⋚\",\n            \"&LessFullEqual;\": \"≦\",\n            \"&LessGreater;\": \"≶\",\n            \"&LessLess;\": \"⪡\",\n            \"&LessSlantEqual;\": \"⩽\",\n            \"&LessTilde;\": \"≲\",\n            \"&Lfr;\": \"𝔏\",\n            \"&Ll;\": \"⋘\",\n            \"&Lleftarrow;\": \"⇚\",\n            \"&Lmidot;\": \"Ŀ\",\n            \"&LongLeftArrow;\": \"⟵\",\n            \"&LongLeftRightArrow;\": \"⟷\",\n            \"&LongRightArrow;\": \"⟶\",\n            \"&Longleftarrow;\": \"⟸\",\n            \"&Longleftrightarrow;\": \"⟺\",\n            \"&Longrightarrow;\": \"⟹\",\n            \"&Lopf;\": \"𝕃\",\n            \"&LowerLeftArrow;\": \"↙\",\n            \"&LowerRightArrow;\": \"↘\",\n            \"&Lscr;\": \"ℒ\",\n            \"&Lsh;\": \"↰\",\n            \"&Lstrok;\": \"Ł\",\n            \"&Lt;\": \"≪\",\n            \"&Map;\": \"⤅\",\n            \"&Mcy;\": \"М\",\n            \"&MediumSpace;\": \" \",\n            \"&Mellintrf;\": \"ℳ\",\n            \"&Mfr;\": \"𝔐\",\n            \"&MinusPlus;\": \"∓\",\n            \"&Mopf;\": \"𝕄\",\n            \"&Mscr;\": \"ℳ\",\n            \"&Mu;\": \"Μ\",\n            \"&NJcy;\": \"Њ\",\n            \"&Nacute;\": \"Ń\",\n            \"&Ncaron;\": \"Ň\",\n            \"&Ncedil;\": \"Ņ\",\n            \"&Ncy;\": \"Н\",\n            \"&NegativeMediumSpace;\": \"​\",\n            \"&NegativeThickSpace;\": \"​\",\n            \"&NegativeThinSpace;\": \"​\",\n            \"&NegativeVeryThinSpace;\": \"​\",\n            \"&NestedGreaterGreater;\": \"≫\",\n            \"&NestedLessLess;\": \"≪\",\n            \"&NewLine;\": \"\\n\",\n            \"&Nfr;\": \"𝔑\",\n            \"&NoBreak;\": \"⁠\",\n            \"&NonBreakingSpace;\": \" \",\n            \"&Nopf;\": \"ℕ\",\n            \"&Not;\": \"⫬\",\n            \"&NotCongruent;\": \"≢\",\n            \"&NotCupCap;\": \"≭\",\n            \"&NotDoubleVerticalBar;\": \"∦\",\n            \"&NotElement;\": \"∉\",\n            \"&NotEqual;\": \"≠\",\n            \"&NotEqualTilde;\": \"≂̸\",\n            \"&NotExists;\": \"∄\",\n            \"&NotGreater;\": \"≯\",\n            \"&NotGreaterEqual;\": \"≱\",\n            \"&NotGreaterFullEqual;\": \"≧̸\",\n            \"&NotGreaterGreater;\": \"≫̸\",\n            \"&NotGreaterLess;\": \"≹\",\n            \"&NotGreaterSlantEqual;\": \"⩾̸\",\n            \"&NotGreaterTilde;\": \"≵\",\n            \"&NotHumpDownHump;\": \"≎̸\",\n            \"&NotHumpEqual;\": \"≏̸\",\n            \"&NotLeftTriangle;\": \"⋪\",\n            \"&NotLeftTriangleBar;\": \"⧏̸\",\n            \"&NotLeftTriangleEqual;\": \"⋬\",\n            \"&NotLess;\": \"≮\",\n            \"&NotLessEqual;\": \"≰\",\n            \"&NotLessGreater;\": \"≸\",\n            \"&NotLessLess;\": \"≪̸\",\n            \"&NotLessSlantEqual;\": \"⩽̸\",\n            \"&NotLessTilde;\": \"≴\",\n            \"&NotNestedGreaterGreater;\": \"⪢̸\",\n            \"&NotNestedLessLess;\": \"⪡̸\",\n            \"&NotPrecedes;\": \"⊀\",\n            \"&NotPrecedesEqual;\": \"⪯̸\",\n            \"&NotPrecedesSlantEqual;\": \"⋠\",\n            \"&NotReverseElement;\": \"∌\",\n            \"&NotRightTriangle;\": \"⋫\",\n            \"&NotRightTriangleBar;\": \"⧐̸\",\n            \"&NotRightTriangleEqual;\": \"⋭\",\n            \"&NotSquareSubset;\": \"⊏̸\",\n            \"&NotSquareSubsetEqual;\": \"⋢\",\n            \"&NotSquareSuperset;\": \"⊐̸\",\n            \"&NotSquareSupersetEqual;\": \"⋣\",\n            \"&NotSubset;\": \"⊂⃒\",\n            \"&NotSubsetEqual;\": \"⊈\",\n            \"&NotSucceeds;\": \"⊁\",\n            \"&NotSucceedsEqual;\": \"⪰̸\",\n            \"&NotSucceedsSlantEqual;\": \"⋡\",\n            \"&NotSucceedsTilde;\": \"≿̸\",\n            \"&NotSuperset;\": \"⊃⃒\",\n            \"&NotSupersetEqual;\": \"⊉\",\n            \"&NotTilde;\": \"≁\",\n            \"&NotTildeEqual;\": \"≄\",\n            \"&NotTildeFullEqual;\": \"≇\",\n            \"&NotTildeTilde;\": \"≉\",\n            \"&NotVerticalBar;\": \"∤\",\n            \"&Nscr;\": \"𝒩\",\n            \"&Ntilde\": \"Ñ\",\n            \"&Ntilde;\": \"Ñ\",\n            \"&Nu;\": \"Ν\",\n            \"&OElig;\": \"Œ\",\n            \"&Oacute\": \"Ó\",\n            \"&Oacute;\": \"Ó\",\n            \"&Ocirc\": \"Ô\",\n            \"&Ocirc;\": \"Ô\",\n            \"&Ocy;\": \"О\",\n            \"&Odblac;\": \"Ő\",\n            \"&Ofr;\": \"𝔒\",\n            \"&Ograve\": \"Ò\",\n            \"&Ograve;\": \"Ò\",\n            \"&Omacr;\": \"Ō\",\n            \"&Omega;\": \"Ω\",\n            \"&Omicron;\": \"Ο\",\n            \"&Oopf;\": \"𝕆\",\n            \"&OpenCurlyDoubleQuote;\": \"“\",\n            \"&OpenCurlyQuote;\": \"‘\",\n            \"&Or;\": \"⩔\",\n            \"&Oscr;\": \"𝒪\",\n            \"&Oslash\": \"Ø\",\n            \"&Oslash;\": \"Ø\",\n            \"&Otilde\": \"Õ\",\n            \"&Otilde;\": \"Õ\",\n            \"&Otimes;\": \"⨷\",\n            \"&Ouml\": \"Ö\",\n            \"&Ouml;\": \"Ö\",\n            \"&OverBar;\": \"‾\",\n            \"&OverBrace;\": \"⏞\",\n            \"&OverBracket;\": \"⎴\",\n            \"&OverParenthesis;\": \"⏜\",\n            \"&PartialD;\": \"∂\",\n            \"&Pcy;\": \"П\",\n            \"&Pfr;\": \"𝔓\",\n            \"&Phi;\": \"Φ\",\n            \"&Pi;\": \"Π\",\n            \"&PlusMinus;\": \"±\",\n            \"&Poincareplane;\": \"ℌ\",\n            \"&Popf;\": \"ℙ\",\n            \"&Pr;\": \"⪻\",\n            \"&Precedes;\": \"≺\",\n            \"&PrecedesEqual;\": \"⪯\",\n            \"&PrecedesSlantEqual;\": \"≼\",\n            \"&PrecedesTilde;\": \"≾\",\n            \"&Prime;\": \"″\",\n            \"&Product;\": \"∏\",\n            \"&Proportion;\": \"∷\",\n            \"&Proportional;\": \"∝\",\n            \"&Pscr;\": \"𝒫\",\n            \"&Psi;\": \"Ψ\",\n            \"&QUOT\": \"\\\"\",\n            \"&QUOT;\": \"\\\"\",\n            \"&Qfr;\": \"𝔔\",\n            \"&Qopf;\": \"ℚ\",\n            \"&Qscr;\": \"𝒬\",\n            \"&RBarr;\": \"⤐\",\n            \"&REG\": \"®\",\n            \"&REG;\": \"®\",\n            \"&Racute;\": \"Ŕ\",\n            \"&Rang;\": \"⟫\",\n            \"&Rarr;\": \"↠\",\n            \"&Rarrtl;\": \"⤖\",\n            \"&Rcaron;\": \"Ř\",\n            \"&Rcedil;\": \"Ŗ\",\n            \"&Rcy;\": \"Р\",\n            \"&Re;\": \"ℜ\",\n            \"&ReverseElement;\": \"∋\",\n            \"&ReverseEquilibrium;\": \"⇋\",\n            \"&ReverseUpEquilibrium;\": \"⥯\",\n            \"&Rfr;\": \"ℜ\",\n            \"&Rho;\": \"Ρ\",\n            \"&RightAngleBracket;\": \"⟩\",\n            \"&RightArrow;\": \"→\",\n            \"&RightArrowBar;\": \"⇥\",\n            \"&RightArrowLeftArrow;\": \"⇄\",\n            \"&RightCeiling;\": \"⌉\",\n            \"&RightDoubleBracket;\": \"⟧\",\n            \"&RightDownTeeVector;\": \"⥝\",\n            \"&RightDownVector;\": \"⇂\",\n            \"&RightDownVectorBar;\": \"⥕\",\n            \"&RightFloor;\": \"⌋\",\n            \"&RightTee;\": \"⊢\",\n            \"&RightTeeArrow;\": \"↦\",\n            \"&RightTeeVector;\": \"⥛\",\n            \"&RightTriangle;\": \"⊳\",\n            \"&RightTriangleBar;\": \"⧐\",\n            \"&RightTriangleEqual;\": \"⊵\",\n            \"&RightUpDownVector;\": \"⥏\",\n            \"&RightUpTeeVector;\": \"⥜\",\n            \"&RightUpVector;\": \"↾\",\n            \"&RightUpVectorBar;\": \"⥔\",\n            \"&RightVector;\": \"⇀\",\n            \"&RightVectorBar;\": \"⥓\",\n            \"&Rightarrow;\": \"⇒\",\n            \"&Ropf;\": \"ℝ\",\n            \"&RoundImplies;\": \"⥰\",\n            \"&Rrightarrow;\": \"⇛\",\n            \"&Rscr;\": \"ℛ\",\n            \"&Rsh;\": \"↱\",\n            \"&RuleDelayed;\": \"⧴\",\n            \"&SHCHcy;\": \"Щ\",\n            \"&SHcy;\": \"Ш\",\n            \"&SOFTcy;\": \"Ь\",\n            \"&Sacute;\": \"Ś\",\n            \"&Sc;\": \"⪼\",\n            \"&Scaron;\": \"Š\",\n            \"&Scedil;\": \"Ş\",\n            \"&Scirc;\": \"Ŝ\",\n            \"&Scy;\": \"С\",\n            \"&Sfr;\": \"𝔖\",\n            \"&ShortDownArrow;\": \"↓\",\n            \"&ShortLeftArrow;\": \"←\",\n            \"&ShortRightArrow;\": \"→\",\n            \"&ShortUpArrow;\": \"↑\",\n            \"&Sigma;\": \"Σ\",\n            \"&SmallCircle;\": \"∘\",\n            \"&Sopf;\": \"𝕊\",\n            \"&Sqrt;\": \"√\",\n            \"&Square;\": \"□\",\n            \"&SquareIntersection;\": \"⊓\",\n            \"&SquareSubset;\": \"⊏\",\n            \"&SquareSubsetEqual;\": \"⊑\",\n            \"&SquareSuperset;\": \"⊐\",\n            \"&SquareSupersetEqual;\": \"⊒\",\n            \"&SquareUnion;\": \"⊔\",\n            \"&Sscr;\": \"𝒮\",\n            \"&Star;\": \"⋆\",\n            \"&Sub;\": \"⋐\",\n            \"&Subset;\": \"⋐\",\n            \"&SubsetEqual;\": \"⊆\",\n            \"&Succeeds;\": \"≻\",\n            \"&SucceedsEqual;\": \"⪰\",\n            \"&SucceedsSlantEqual;\": \"≽\",\n            \"&SucceedsTilde;\": \"≿\",\n            \"&SuchThat;\": \"∋\",\n            \"&Sum;\": \"∑\",\n            \"&Sup;\": \"⋑\",\n            \"&Superset;\": \"⊃\",\n            \"&SupersetEqual;\": \"⊇\",\n            \"&Supset;\": \"⋑\",\n            \"&THORN\": \"Þ\",\n            \"&THORN;\": \"Þ\",\n            \"&TRADE;\": \"™\",\n            \"&TSHcy;\": \"Ћ\",\n            \"&TScy;\": \"Ц\",\n            \"&Tab;\": \"\\t\",\n            \"&Tau;\": \"Τ\",\n            \"&Tcaron;\": \"Ť\",\n            \"&Tcedil;\": \"Ţ\",\n            \"&Tcy;\": \"Т\",\n            \"&Tfr;\": \"𝔗\",\n            \"&Therefore;\": \"∴\",\n            \"&Theta;\": \"Θ\",\n            \"&ThickSpace;\": \"  \",\n            \"&ThinSpace;\": \" \",\n            \"&Tilde;\": \"∼\",\n            \"&TildeEqual;\": \"≃\",\n            \"&TildeFullEqual;\": \"≅\",\n            \"&TildeTilde;\": \"≈\",\n            \"&Topf;\": \"𝕋\",\n            \"&TripleDot;\": \"⃛\",\n            \"&Tscr;\": \"𝒯\",\n            \"&Tstrok;\": \"Ŧ\",\n            \"&Uacute\": \"Ú\",\n            \"&Uacute;\": \"Ú\",\n            \"&Uarr;\": \"↟\",\n            \"&Uarrocir;\": \"⥉\",\n            \"&Ubrcy;\": \"Ў\",\n            \"&Ubreve;\": \"Ŭ\",\n            \"&Ucirc\": \"Û\",\n            \"&Ucirc;\": \"Û\",\n            \"&Ucy;\": \"У\",\n            \"&Udblac;\": \"Ű\",\n            \"&Ufr;\": \"𝔘\",\n            \"&Ugrave\": \"Ù\",\n            \"&Ugrave;\": \"Ù\",\n            \"&Umacr;\": \"Ū\",\n            \"&UnderBar;\": \"_\",\n            \"&UnderBrace;\": \"⏟\",\n            \"&UnderBracket;\": \"⎵\",\n            \"&UnderParenthesis;\": \"⏝\",\n            \"&Union;\": \"⋃\",\n            \"&UnionPlus;\": \"⊎\",\n            \"&Uogon;\": \"Ų\",\n            \"&Uopf;\": \"𝕌\",\n            \"&UpArrow;\": \"↑\",\n            \"&UpArrowBar;\": \"⤒\",\n            \"&UpArrowDownArrow;\": \"⇅\",\n            \"&UpDownArrow;\": \"↕\",\n            \"&UpEquilibrium;\": \"⥮\",\n            \"&UpTee;\": \"⊥\",\n            \"&UpTeeArrow;\": \"↥\",\n            \"&Uparrow;\": \"⇑\",\n            \"&Updownarrow;\": \"⇕\",\n            \"&UpperLeftArrow;\": \"↖\",\n            \"&UpperRightArrow;\": \"↗\",\n            \"&Upsi;\": \"ϒ\",\n            \"&Upsilon;\": \"Υ\",\n            \"&Uring;\": \"Ů\",\n            \"&Uscr;\": \"𝒰\",\n            \"&Utilde;\": \"Ũ\",\n            \"&Uuml\": \"Ü\",\n            \"&Uuml;\": \"Ü\",\n            \"&VDash;\": \"⊫\",\n            \"&Vbar;\": \"⫫\",\n            \"&Vcy;\": \"В\",\n            \"&Vdash;\": \"⊩\",\n            \"&Vdashl;\": \"⫦\",\n            \"&Vee;\": \"⋁\",\n            \"&Verbar;\": \"‖\",\n            \"&Vert;\": \"‖\",\n            \"&VerticalBar;\": \"∣\",\n            \"&VerticalLine;\": \"|\",\n            \"&VerticalSeparator;\": \"❘\",\n            \"&VerticalTilde;\": \"≀\",\n            \"&VeryThinSpace;\": \" \",\n            \"&Vfr;\": \"𝔙\",\n            \"&Vopf;\": \"𝕍\",\n            \"&Vscr;\": \"𝒱\",\n            \"&Vvdash;\": \"⊪\",\n            \"&Wcirc;\": \"Ŵ\",\n            \"&Wedge;\": \"⋀\",\n            \"&Wfr;\": \"𝔚\",\n            \"&Wopf;\": \"𝕎\",\n            \"&Wscr;\": \"𝒲\",\n            \"&Xfr;\": \"𝔛\",\n            \"&Xi;\": \"Ξ\",\n            \"&Xopf;\": \"𝕏\",\n            \"&Xscr;\": \"𝒳\",\n            \"&YAcy;\": \"Я\",\n            \"&YIcy;\": \"Ї\",\n            \"&YUcy;\": \"Ю\",\n            \"&Yacute\": \"Ý\",\n            \"&Yacute;\": \"Ý\",\n            \"&Ycirc;\": \"Ŷ\",\n            \"&Ycy;\": \"Ы\",\n            \"&Yfr;\": \"𝔜\",\n            \"&Yopf;\": \"𝕐\",\n            \"&Yscr;\": \"𝒴\",\n            \"&Yuml;\": \"Ÿ\",\n            \"&ZHcy;\": \"Ж\",\n            \"&Zacute;\": \"Ź\",\n            \"&Zcaron;\": \"Ž\",\n            \"&Zcy;\": \"З\",\n            \"&Zdot;\": \"Ż\",\n            \"&ZeroWidthSpace;\": \"​\",\n            \"&Zeta;\": \"Ζ\",\n            \"&Zfr;\": \"ℨ\",\n            \"&Zopf;\": \"ℤ\",\n            \"&Zscr;\": \"𝒵\",\n            \"&aacute\": \"á\",\n            \"&aacute;\": \"á\",\n            \"&abreve;\": \"ă\",\n            \"&ac;\": \"∾\",\n            \"&acE;\": \"∾̳\",\n            \"&acd;\": \"∿\",\n            \"&acirc\": \"â\",\n            \"&acirc;\": \"â\",\n            \"&acute\": \"´\",\n            \"&acute;\": \"´\",\n            \"&acy;\": \"а\",\n            \"&aelig\": \"æ\",\n            \"&aelig;\": \"æ\",\n            \"&af;\": \"⁡\",\n            \"&afr;\": \"𝔞\",\n            \"&agrave\": \"à\",\n            \"&agrave;\": \"à\",\n            \"&alefsym;\": \"ℵ\",\n            \"&aleph;\": \"ℵ\",\n            \"&alpha;\": \"α\",\n            \"&amacr;\": \"ā\",\n            \"&amalg;\": \"⨿\",\n            \"&amp\": \"&\",\n            \"&amp;\": \"&\",\n            \"&and;\": \"∧\",\n            \"&andand;\": \"⩕\",\n            \"&andd;\": \"⩜\",\n            \"&andslope;\": \"⩘\",\n            \"&andv;\": \"⩚\",\n            \"&ang;\": \"∠\",\n            \"&ange;\": \"⦤\",\n            \"&angle;\": \"∠\",\n            \"&angmsd;\": \"∡\",\n            \"&angmsdaa;\": \"⦨\",\n            \"&angmsdab;\": \"⦩\",\n            \"&angmsdac;\": \"⦪\",\n            \"&angmsdad;\": \"⦫\",\n            \"&angmsdae;\": \"⦬\",\n            \"&angmsdaf;\": \"⦭\",\n            \"&angmsdag;\": \"⦮\",\n            \"&angmsdah;\": \"⦯\",\n            \"&angrt;\": \"∟\",\n            \"&angrtvb;\": \"⊾\",\n            \"&angrtvbd;\": \"⦝\",\n            \"&angsph;\": \"∢\",\n            \"&angst;\": \"Å\",\n            \"&angzarr;\": \"⍼\",\n            \"&aogon;\": \"ą\",\n            \"&aopf;\": \"𝕒\",\n            \"&ap;\": \"≈\",\n            \"&apE;\": \"⩰\",\n            \"&apacir;\": \"⩯\",\n            \"&ape;\": \"≊\",\n            \"&apid;\": \"≋\",\n            \"&apos;\": \"'\",\n            \"&approx;\": \"≈\",\n            \"&approxeq;\": \"≊\",\n            \"&aring\": \"å\",\n            \"&aring;\": \"å\",\n            \"&ascr;\": \"𝒶\",\n            \"&ast;\": \"*\",\n            \"&asymp;\": \"≈\",\n            \"&asympeq;\": \"≍\",\n            \"&atilde\": \"ã\",\n            \"&atilde;\": \"ã\",\n            \"&auml\": \"ä\",\n            \"&auml;\": \"ä\",\n            \"&awconint;\": \"∳\",\n            \"&awint;\": \"⨑\",\n            \"&bNot;\": \"⫭\",\n            \"&backcong;\": \"≌\",\n            \"&backepsilon;\": \"϶\",\n            \"&backprime;\": \"‵\",\n            \"&backsim;\": \"∽\",\n            \"&backsimeq;\": \"⋍\",\n            \"&barvee;\": \"⊽\",\n            \"&barwed;\": \"⌅\",\n            \"&barwedge;\": \"⌅\",\n            \"&bbrk;\": \"⎵\",\n            \"&bbrktbrk;\": \"⎶\",\n            \"&bcong;\": \"≌\",\n            \"&bcy;\": \"б\",\n            \"&bdquo;\": \"„\",\n            \"&becaus;\": \"∵\",\n            \"&because;\": \"∵\",\n            \"&bemptyv;\": \"⦰\",\n            \"&bepsi;\": \"϶\",\n            \"&bernou;\": \"ℬ\",\n            \"&beta;\": \"β\",\n            \"&beth;\": \"ℶ\",\n            \"&between;\": \"≬\",\n            \"&bfr;\": \"𝔟\",\n            \"&bigcap;\": \"⋂\",\n            \"&bigcirc;\": \"◯\",\n            \"&bigcup;\": \"⋃\",\n            \"&bigodot;\": \"⨀\",\n            \"&bigoplus;\": \"⨁\",\n            \"&bigotimes;\": \"⨂\",\n            \"&bigsqcup;\": \"⨆\",\n            \"&bigstar;\": \"★\",\n            \"&bigtriangledown;\": \"▽\",\n            \"&bigtriangleup;\": \"△\",\n            \"&biguplus;\": \"⨄\",\n            \"&bigvee;\": \"⋁\",\n            \"&bigwedge;\": \"⋀\",\n            \"&bkarow;\": \"⤍\",\n            \"&blacklozenge;\": \"⧫\",\n            \"&blacksquare;\": \"▪\",\n            \"&blacktriangle;\": \"▴\",\n            \"&blacktriangledown;\": \"▾\",\n            \"&blacktriangleleft;\": \"◂\",\n            \"&blacktriangleright;\": \"▸\",\n            \"&blank;\": \"␣\",\n            \"&blk12;\": \"▒\",\n            \"&blk14;\": \"░\",\n            \"&blk34;\": \"▓\",\n            \"&block;\": \"█\",\n            \"&bne;\": \"=⃥\",\n            \"&bnequiv;\": \"≡⃥\",\n            \"&bnot;\": \"⌐\",\n            \"&bopf;\": \"𝕓\",\n            \"&bot;\": \"⊥\",\n            \"&bottom;\": \"⊥\",\n            \"&bowtie;\": \"⋈\",\n            \"&boxDL;\": \"╗\",\n            \"&boxDR;\": \"╔\",\n            \"&boxDl;\": \"╖\",\n            \"&boxDr;\": \"╓\",\n            \"&boxH;\": \"═\",\n            \"&boxHD;\": \"╦\",\n            \"&boxHU;\": \"╩\",\n            \"&boxHd;\": \"╤\",\n            \"&boxHu;\": \"╧\",\n            \"&boxUL;\": \"╝\",\n            \"&boxUR;\": \"╚\",\n            \"&boxUl;\": \"╜\",\n            \"&boxUr;\": \"╙\",\n            \"&boxV;\": \"║\",\n            \"&boxVH;\": \"╬\",\n            \"&boxVL;\": \"╣\",\n            \"&boxVR;\": \"╠\",\n            \"&boxVh;\": \"╫\",\n            \"&boxVl;\": \"╢\",\n            \"&boxVr;\": \"╟\",\n            \"&boxbox;\": \"⧉\",\n            \"&boxdL;\": \"╕\",\n            \"&boxdR;\": \"╒\",\n            \"&boxdl;\": \"┐\",\n            \"&boxdr;\": \"┌\",\n            \"&boxh;\": \"─\",\n            \"&boxhD;\": \"╥\",\n            \"&boxhU;\": \"╨\",\n            \"&boxhd;\": \"┬\",\n            \"&boxhu;\": \"┴\",\n            \"&boxminus;\": \"⊟\",\n            \"&boxplus;\": \"⊞\",\n            \"&boxtimes;\": \"⊠\",\n            \"&boxuL;\": \"╛\",\n            \"&boxuR;\": \"╘\",\n            \"&boxul;\": \"┘\",\n            \"&boxur;\": \"└\",\n            \"&boxv;\": \"│\",\n            \"&boxvH;\": \"╪\",\n            \"&boxvL;\": \"╡\",\n            \"&boxvR;\": \"╞\",\n            \"&boxvh;\": \"┼\",\n            \"&boxvl;\": \"┤\",\n            \"&boxvr;\": \"├\",\n            \"&bprime;\": \"‵\",\n            \"&breve;\": \"˘\",\n            \"&brvbar\": \"¦\",\n            \"&brvbar;\": \"¦\",\n            \"&bscr;\": \"𝒷\",\n            \"&bsemi;\": \"⁏\",\n            \"&bsim;\": \"∽\",\n            \"&bsime;\": \"⋍\",\n            \"&bsol;\": \"\\\\\",\n            \"&bsolb;\": \"⧅\",\n            \"&bsolhsub;\": \"⟈\",\n            \"&bull;\": \"•\",\n            \"&bullet;\": \"•\",\n            \"&bump;\": \"≎\",\n            \"&bumpE;\": \"⪮\",\n            \"&bumpe;\": \"≏\",\n            \"&bumpeq;\": \"≏\",\n            \"&cacute;\": \"ć\",\n            \"&cap;\": \"∩\",\n            \"&capand;\": \"⩄\",\n            \"&capbrcup;\": \"⩉\",\n            \"&capcap;\": \"⩋\",\n            \"&capcup;\": \"⩇\",\n            \"&capdot;\": \"⩀\",\n            \"&caps;\": \"∩︀\",\n            \"&caret;\": \"⁁\",\n            \"&caron;\": \"ˇ\",\n            \"&ccaps;\": \"⩍\",\n            \"&ccaron;\": \"č\",\n            \"&ccedil\": \"ç\",\n            \"&ccedil;\": \"ç\",\n            \"&ccirc;\": \"ĉ\",\n            \"&ccups;\": \"⩌\",\n            \"&ccupssm;\": \"⩐\",\n            \"&cdot;\": \"ċ\",\n            \"&cedil\": \"¸\",\n            \"&cedil;\": \"¸\",\n            \"&cemptyv;\": \"⦲\",\n            \"&cent\": \"¢\",\n            \"&cent;\": \"¢\",\n            \"&centerdot;\": \"·\",\n            \"&cfr;\": \"𝔠\",\n            \"&chcy;\": \"ч\",\n            \"&check;\": \"✓\",\n            \"&checkmark;\": \"✓\",\n            \"&chi;\": \"χ\",\n            \"&cir;\": \"○\",\n            \"&cirE;\": \"⧃\",\n            \"&circ;\": \"ˆ\",\n            \"&circeq;\": \"≗\",\n            \"&circlearrowleft;\": \"↺\",\n            \"&circlearrowright;\": \"↻\",\n            \"&circledR;\": \"®\",\n            \"&circledS;\": \"Ⓢ\",\n            \"&circledast;\": \"⊛\",\n            \"&circledcirc;\": \"⊚\",\n            \"&circleddash;\": \"⊝\",\n            \"&cire;\": \"≗\",\n            \"&cirfnint;\": \"⨐\",\n            \"&cirmid;\": \"⫯\",\n            \"&cirscir;\": \"⧂\",\n            \"&clubs;\": \"♣\",\n            \"&clubsuit;\": \"♣\",\n            \"&colon;\": \":\",\n            \"&colone;\": \"≔\",\n            \"&coloneq;\": \"≔\",\n            \"&comma;\": \",\",\n            \"&commat;\": \"@\",\n            \"&comp;\": \"∁\",\n            \"&compfn;\": \"∘\",\n            \"&complement;\": \"∁\",\n            \"&complexes;\": \"ℂ\",\n            \"&cong;\": \"≅\",\n            \"&congdot;\": \"⩭\",\n            \"&conint;\": \"∮\",\n            \"&copf;\": \"𝕔\",\n            \"&coprod;\": \"∐\",\n            \"&copy\": \"©\",\n            \"&copy;\": \"©\",\n            \"&copysr;\": \"℗\",\n            \"&crarr;\": \"↵\",\n            \"&cross;\": \"✗\",\n            \"&cscr;\": \"𝒸\",\n            \"&csub;\": \"⫏\",\n            \"&csube;\": \"⫑\",\n            \"&csup;\": \"⫐\",\n            \"&csupe;\": \"⫒\",\n            \"&ctdot;\": \"⋯\",\n            \"&cudarrl;\": \"⤸\",\n            \"&cudarrr;\": \"⤵\",\n            \"&cuepr;\": \"⋞\",\n            \"&cuesc;\": \"⋟\",\n            \"&cularr;\": \"↶\",\n            \"&cularrp;\": \"⤽\",\n            \"&cup;\": \"∪\",\n            \"&cupbrcap;\": \"⩈\",\n            \"&cupcap;\": \"⩆\",\n            \"&cupcup;\": \"⩊\",\n            \"&cupdot;\": \"⊍\",\n            \"&cupor;\": \"⩅\",\n            \"&cups;\": \"∪︀\",\n            \"&curarr;\": \"↷\",\n            \"&curarrm;\": \"⤼\",\n            \"&curlyeqprec;\": \"⋞\",\n            \"&curlyeqsucc;\": \"⋟\",\n            \"&curlyvee;\": \"⋎\",\n            \"&curlywedge;\": \"⋏\",\n            \"&curren\": \"¤\",\n            \"&curren;\": \"¤\",\n            \"&curvearrowleft;\": \"↶\",\n            \"&curvearrowright;\": \"↷\",\n            \"&cuvee;\": \"⋎\",\n            \"&cuwed;\": \"⋏\",\n            \"&cwconint;\": \"∲\",\n            \"&cwint;\": \"∱\",\n            \"&cylcty;\": \"⌭\",\n            \"&dArr;\": \"⇓\",\n            \"&dHar;\": \"⥥\",\n            \"&dagger;\": \"†\",\n            \"&daleth;\": \"ℸ\",\n            \"&darr;\": \"↓\",\n            \"&dash;\": \"‐\",\n            \"&dashv;\": \"⊣\",\n            \"&dbkarow;\": \"⤏\",\n            \"&dblac;\": \"˝\",\n            \"&dcaron;\": \"ď\",\n            \"&dcy;\": \"д\",\n            \"&dd;\": \"ⅆ\",\n            \"&ddagger;\": \"‡\",\n            \"&ddarr;\": \"⇊\",\n            \"&ddotseq;\": \"⩷\",\n            \"&deg\": \"°\",\n            \"&deg;\": \"°\",\n            \"&delta;\": \"δ\",\n            \"&demptyv;\": \"⦱\",\n            \"&dfisht;\": \"⥿\",\n            \"&dfr;\": \"𝔡\",\n            \"&dharl;\": \"⇃\",\n            \"&dharr;\": \"⇂\",\n            \"&diam;\": \"⋄\",\n            \"&diamond;\": \"⋄\",\n            \"&diamondsuit;\": \"♦\",\n            \"&diams;\": \"♦\",\n            \"&die;\": \"¨\",\n            \"&digamma;\": \"ϝ\",\n            \"&disin;\": \"⋲\",\n            \"&div;\": \"÷\",\n            \"&divide\": \"÷\",\n            \"&divide;\": \"÷\",\n            \"&divideontimes;\": \"⋇\",\n            \"&divonx;\": \"⋇\",\n            \"&djcy;\": \"ђ\",\n            \"&dlcorn;\": \"⌞\",\n            \"&dlcrop;\": \"⌍\",\n            \"&dollar;\": \"$\",\n            \"&dopf;\": \"𝕕\",\n            \"&dot;\": \"˙\",\n            \"&doteq;\": \"≐\",\n            \"&doteqdot;\": \"≑\",\n            \"&dotminus;\": \"∸\",\n            \"&dotplus;\": \"∔\",\n            \"&dotsquare;\": \"⊡\",\n            \"&doublebarwedge;\": \"⌆\",\n            \"&downarrow;\": \"↓\",\n            \"&downdownarrows;\": \"⇊\",\n            \"&downharpoonleft;\": \"⇃\",\n            \"&downharpoonright;\": \"⇂\",\n            \"&drbkarow;\": \"⤐\",\n            \"&drcorn;\": \"⌟\",\n            \"&drcrop;\": \"⌌\",\n            \"&dscr;\": \"𝒹\",\n            \"&dscy;\": \"ѕ\",\n            \"&dsol;\": \"⧶\",\n            \"&dstrok;\": \"đ\",\n            \"&dtdot;\": \"⋱\",\n            \"&dtri;\": \"▿\",\n            \"&dtrif;\": \"▾\",\n            \"&duarr;\": \"⇵\",\n            \"&duhar;\": \"⥯\",\n            \"&dwangle;\": \"⦦\",\n            \"&dzcy;\": \"џ\",\n            \"&dzigrarr;\": \"⟿\",\n            \"&eDDot;\": \"⩷\",\n            \"&eDot;\": \"≑\",\n            \"&eacute\": \"é\",\n            \"&eacute;\": \"é\",\n            \"&easter;\": \"⩮\",\n            \"&ecaron;\": \"ě\",\n            \"&ecir;\": \"≖\",\n            \"&ecirc\": \"ê\",\n            \"&ecirc;\": \"ê\",\n            \"&ecolon;\": \"≕\",\n            \"&ecy;\": \"э\",\n            \"&edot;\": \"ė\",\n            \"&ee;\": \"ⅇ\",\n            \"&efDot;\": \"≒\",\n            \"&efr;\": \"𝔢\",\n            \"&eg;\": \"⪚\",\n            \"&egrave\": \"è\",\n            \"&egrave;\": \"è\",\n            \"&egs;\": \"⪖\",\n            \"&egsdot;\": \"⪘\",\n            \"&el;\": \"⪙\",\n            \"&elinters;\": \"⏧\",\n            \"&ell;\": \"ℓ\",\n            \"&els;\": \"⪕\",\n            \"&elsdot;\": \"⪗\",\n            \"&emacr;\": \"ē\",\n            \"&empty;\": \"∅\",\n            \"&emptyset;\": \"∅\",\n            \"&emptyv;\": \"∅\",\n            \"&emsp13;\": \" \",\n            \"&emsp14;\": \" \",\n            \"&emsp;\": \" \",\n            \"&eng;\": \"ŋ\",\n            \"&ensp;\": \" \",\n            \"&eogon;\": \"ę\",\n            \"&eopf;\": \"𝕖\",\n            \"&epar;\": \"⋕\",\n            \"&eparsl;\": \"⧣\",\n            \"&eplus;\": \"⩱\",\n            \"&epsi;\": \"ε\",\n            \"&epsilon;\": \"ε\",\n            \"&epsiv;\": \"ϵ\",\n            \"&eqcirc;\": \"≖\",\n            \"&eqcolon;\": \"≕\",\n            \"&eqsim;\": \"≂\",\n            \"&eqslantgtr;\": \"⪖\",\n            \"&eqslantless;\": \"⪕\",\n            \"&equals;\": \"=\",\n            \"&equest;\": \"≟\",\n            \"&equiv;\": \"≡\",\n            \"&equivDD;\": \"⩸\",\n            \"&eqvparsl;\": \"⧥\",\n            \"&erDot;\": \"≓\",\n            \"&erarr;\": \"⥱\",\n            \"&escr;\": \"ℯ\",\n            \"&esdot;\": \"≐\",\n            \"&esim;\": \"≂\",\n            \"&eta;\": \"η\",\n            \"&eth\": \"ð\",\n            \"&eth;\": \"ð\",\n            \"&euml\": \"ë\",\n            \"&euml;\": \"ë\",\n            \"&euro;\": \"€\",\n            \"&excl;\": \"!\",\n            \"&exist;\": \"∃\",\n            \"&expectation;\": \"ℰ\",\n            \"&exponentiale;\": \"ⅇ\",\n            \"&fallingdotseq;\": \"≒\",\n            \"&fcy;\": \"ф\",\n            \"&female;\": \"♀\",\n            \"&ffilig;\": \"ﬃ\",\n            \"&fflig;\": \"ﬀ\",\n            \"&ffllig;\": \"ﬄ\",\n            \"&ffr;\": \"𝔣\",\n            \"&filig;\": \"ﬁ\",\n            \"&fjlig;\": \"fj\",\n            \"&flat;\": \"♭\",\n            \"&fllig;\": \"ﬂ\",\n            \"&fltns;\": \"▱\",\n            \"&fnof;\": \"ƒ\",\n            \"&fopf;\": \"𝕗\",\n            \"&forall;\": \"∀\",\n            \"&fork;\": \"⋔\",\n            \"&forkv;\": \"⫙\",\n            \"&fpartint;\": \"⨍\",\n            \"&frac12\": \"½\",\n            \"&frac12;\": \"½\",\n            \"&frac13;\": \"⅓\",\n            \"&frac14\": \"¼\",\n            \"&frac14;\": \"¼\",\n            \"&frac15;\": \"⅕\",\n            \"&frac16;\": \"⅙\",\n            \"&frac18;\": \"⅛\",\n            \"&frac23;\": \"⅔\",\n            \"&frac25;\": \"⅖\",\n            \"&frac34\": \"¾\",\n            \"&frac34;\": \"¾\",\n            \"&frac35;\": \"⅗\",\n            \"&frac38;\": \"⅜\",\n            \"&frac45;\": \"⅘\",\n            \"&frac56;\": \"⅚\",\n            \"&frac58;\": \"⅝\",\n            \"&frac78;\": \"⅞\",\n            \"&frasl;\": \"⁄\",\n            \"&frown;\": \"⌢\",\n            \"&fscr;\": \"𝒻\",\n            \"&gE;\": \"≧\",\n            \"&gEl;\": \"⪌\",\n            \"&gacute;\": \"ǵ\",\n            \"&gamma;\": \"γ\",\n            \"&gammad;\": \"ϝ\",\n            \"&gap;\": \"⪆\",\n            \"&gbreve;\": \"ğ\",\n            \"&gcirc;\": \"ĝ\",\n            \"&gcy;\": \"г\",\n            \"&gdot;\": \"ġ\",\n            \"&ge;\": \"≥\",\n            \"&gel;\": \"⋛\",\n            \"&geq;\": \"≥\",\n            \"&geqq;\": \"≧\",\n            \"&geqslant;\": \"⩾\",\n            \"&ges;\": \"⩾\",\n            \"&gescc;\": \"⪩\",\n            \"&gesdot;\": \"⪀\",\n            \"&gesdoto;\": \"⪂\",\n            \"&gesdotol;\": \"⪄\",\n            \"&gesl;\": \"⋛︀\",\n            \"&gesles;\": \"⪔\",\n            \"&gfr;\": \"𝔤\",\n            \"&gg;\": \"≫\",\n            \"&ggg;\": \"⋙\",\n            \"&gimel;\": \"ℷ\",\n            \"&gjcy;\": \"ѓ\",\n            \"&gl;\": \"≷\",\n            \"&glE;\": \"⪒\",\n            \"&gla;\": \"⪥\",\n            \"&glj;\": \"⪤\",\n            \"&gnE;\": \"≩\",\n            \"&gnap;\": \"⪊\",\n            \"&gnapprox;\": \"⪊\",\n            \"&gne;\": \"⪈\",\n            \"&gneq;\": \"⪈\",\n            \"&gneqq;\": \"≩\",\n            \"&gnsim;\": \"⋧\",\n            \"&gopf;\": \"𝕘\",\n            \"&grave;\": \"`\",\n            \"&gscr;\": \"ℊ\",\n            \"&gsim;\": \"≳\",\n            \"&gsime;\": \"⪎\",\n            \"&gsiml;\": \"⪐\",\n            \"&gt\": \">\",\n            \"&gt;\": \">\",\n            \"&gtcc;\": \"⪧\",\n            \"&gtcir;\": \"⩺\",\n            \"&gtdot;\": \"⋗\",\n            \"&gtlPar;\": \"⦕\",\n            \"&gtquest;\": \"⩼\",\n            \"&gtrapprox;\": \"⪆\",\n            \"&gtrarr;\": \"⥸\",\n            \"&gtrdot;\": \"⋗\",\n            \"&gtreqless;\": \"⋛\",\n            \"&gtreqqless;\": \"⪌\",\n            \"&gtrless;\": \"≷\",\n            \"&gtrsim;\": \"≳\",\n            \"&gvertneqq;\": \"≩︀\",\n            \"&gvnE;\": \"≩︀\",\n            \"&hArr;\": \"⇔\",\n            \"&hairsp;\": \" \",\n            \"&half;\": \"½\",\n            \"&hamilt;\": \"ℋ\",\n            \"&hardcy;\": \"ъ\",\n            \"&harr;\": \"↔\",\n            \"&harrcir;\": \"⥈\",\n            \"&harrw;\": \"↭\",\n            \"&hbar;\": \"ℏ\",\n            \"&hcirc;\": \"ĥ\",\n            \"&hearts;\": \"♥\",\n            \"&heartsuit;\": \"♥\",\n            \"&hellip;\": \"…\",\n            \"&hercon;\": \"⊹\",\n            \"&hfr;\": \"𝔥\",\n            \"&hksearow;\": \"⤥\",\n            \"&hkswarow;\": \"⤦\",\n            \"&hoarr;\": \"⇿\",\n            \"&homtht;\": \"∻\",\n            \"&hookleftarrow;\": \"↩\",\n            \"&hookrightarrow;\": \"↪\",\n            \"&hopf;\": \"𝕙\",\n            \"&horbar;\": \"―\",\n            \"&hscr;\": \"𝒽\",\n            \"&hslash;\": \"ℏ\",\n            \"&hstrok;\": \"ħ\",\n            \"&hybull;\": \"⁃\",\n            \"&hyphen;\": \"‐\",\n            \"&iacute\": \"í\",\n            \"&iacute;\": \"í\",\n            \"&ic;\": \"⁣\",\n            \"&icirc\": \"î\",\n            \"&icirc;\": \"î\",\n            \"&icy;\": \"и\",\n            \"&iecy;\": \"е\",\n            \"&iexcl\": \"¡\",\n            \"&iexcl;\": \"¡\",\n            \"&iff;\": \"⇔\",\n            \"&ifr;\": \"𝔦\",\n            \"&igrave\": \"ì\",\n            \"&igrave;\": \"ì\",\n            \"&ii;\": \"ⅈ\",\n            \"&iiiint;\": \"⨌\",\n            \"&iiint;\": \"∭\",\n            \"&iinfin;\": \"⧜\",\n            \"&iiota;\": \"℩\",\n            \"&ijlig;\": \"ĳ\",\n            \"&imacr;\": \"ī\",\n            \"&image;\": \"ℑ\",\n            \"&imagline;\": \"ℐ\",\n            \"&imagpart;\": \"ℑ\",\n            \"&imath;\": \"ı\",\n            \"&imof;\": \"⊷\",\n            \"&imped;\": \"Ƶ\",\n            \"&in;\": \"∈\",\n            \"&incare;\": \"℅\",\n            \"&infin;\": \"∞\",\n            \"&infintie;\": \"⧝\",\n            \"&inodot;\": \"ı\",\n            \"&int;\": \"∫\",\n            \"&intcal;\": \"⊺\",\n            \"&integers;\": \"ℤ\",\n            \"&intercal;\": \"⊺\",\n            \"&intlarhk;\": \"⨗\",\n            \"&intprod;\": \"⨼\",\n            \"&iocy;\": \"ё\",\n            \"&iogon;\": \"į\",\n            \"&iopf;\": \"𝕚\",\n            \"&iota;\": \"ι\",\n            \"&iprod;\": \"⨼\",\n            \"&iquest\": \"¿\",\n            \"&iquest;\": \"¿\",\n            \"&iscr;\": \"𝒾\",\n            \"&isin;\": \"∈\",\n            \"&isinE;\": \"⋹\",\n            \"&isindot;\": \"⋵\",\n            \"&isins;\": \"⋴\",\n            \"&isinsv;\": \"⋳\",\n            \"&isinv;\": \"∈\",\n            \"&it;\": \"⁢\",\n            \"&itilde;\": \"ĩ\",\n            \"&iukcy;\": \"і\",\n            \"&iuml\": \"ï\",\n            \"&iuml;\": \"ï\",\n            \"&jcirc;\": \"ĵ\",\n            \"&jcy;\": \"й\",\n            \"&jfr;\": \"𝔧\",\n            \"&jmath;\": \"ȷ\",\n            \"&jopf;\": \"𝕛\",\n            \"&jscr;\": \"𝒿\",\n            \"&jsercy;\": \"ј\",\n            \"&jukcy;\": \"є\",\n            \"&kappa;\": \"κ\",\n            \"&kappav;\": \"ϰ\",\n            \"&kcedil;\": \"ķ\",\n            \"&kcy;\": \"к\",\n            \"&kfr;\": \"𝔨\",\n            \"&kgreen;\": \"ĸ\",\n            \"&khcy;\": \"х\",\n            \"&kjcy;\": \"ќ\",\n            \"&kopf;\": \"𝕜\",\n            \"&kscr;\": \"𝓀\",\n            \"&lAarr;\": \"⇚\",\n            \"&lArr;\": \"⇐\",\n            \"&lAtail;\": \"⤛\",\n            \"&lBarr;\": \"⤎\",\n            \"&lE;\": \"≦\",\n            \"&lEg;\": \"⪋\",\n            \"&lHar;\": \"⥢\",\n            \"&lacute;\": \"ĺ\",\n            \"&laemptyv;\": \"⦴\",\n            \"&lagran;\": \"ℒ\",\n            \"&lambda;\": \"λ\",\n            \"&lang;\": \"⟨\",\n            \"&langd;\": \"⦑\",\n            \"&langle;\": \"⟨\",\n            \"&lap;\": \"⪅\",\n            \"&laquo\": \"«\",\n            \"&laquo;\": \"«\",\n            \"&larr;\": \"←\",\n            \"&larrb;\": \"⇤\",\n            \"&larrbfs;\": \"⤟\",\n            \"&larrfs;\": \"⤝\",\n            \"&larrhk;\": \"↩\",\n            \"&larrlp;\": \"↫\",\n            \"&larrpl;\": \"⤹\",\n            \"&larrsim;\": \"⥳\",\n            \"&larrtl;\": \"↢\",\n            \"&lat;\": \"⪫\",\n            \"&latail;\": \"⤙\",\n            \"&late;\": \"⪭\",\n            \"&lates;\": \"⪭︀\",\n            \"&lbarr;\": \"⤌\",\n            \"&lbbrk;\": \"❲\",\n            \"&lbrace;\": \"{\",\n            \"&lbrack;\": \"[\",\n            \"&lbrke;\": \"⦋\",\n            \"&lbrksld;\": \"⦏\",\n            \"&lbrkslu;\": \"⦍\",\n            \"&lcaron;\": \"ľ\",\n            \"&lcedil;\": \"ļ\",\n            \"&lceil;\": \"⌈\",\n            \"&lcub;\": \"{\",\n            \"&lcy;\": \"л\",\n            \"&ldca;\": \"⤶\",\n            \"&ldquo;\": \"“\",\n            \"&ldquor;\": \"„\",\n            \"&ldrdhar;\": \"⥧\",\n            \"&ldrushar;\": \"⥋\",\n            \"&ldsh;\": \"↲\",\n            \"&le;\": \"≤\",\n            \"&leftarrow;\": \"←\",\n            \"&leftarrowtail;\": \"↢\",\n            \"&leftharpoondown;\": \"↽\",\n            \"&leftharpoonup;\": \"↼\",\n            \"&leftleftarrows;\": \"⇇\",\n            \"&leftrightarrow;\": \"↔\",\n            \"&leftrightarrows;\": \"⇆\",\n            \"&leftrightharpoons;\": \"⇋\",\n            \"&leftrightsquigarrow;\": \"↭\",\n            \"&leftthreetimes;\": \"⋋\",\n            \"&leg;\": \"⋚\",\n            \"&leq;\": \"≤\",\n            \"&leqq;\": \"≦\",\n            \"&leqslant;\": \"⩽\",\n            \"&les;\": \"⩽\",\n            \"&lescc;\": \"⪨\",\n            \"&lesdot;\": \"⩿\",\n            \"&lesdoto;\": \"⪁\",\n            \"&lesdotor;\": \"⪃\",\n            \"&lesg;\": \"⋚︀\",\n            \"&lesges;\": \"⪓\",\n            \"&lessapprox;\": \"⪅\",\n            \"&lessdot;\": \"⋖\",\n            \"&lesseqgtr;\": \"⋚\",\n            \"&lesseqqgtr;\": \"⪋\",\n            \"&lessgtr;\": \"≶\",\n            \"&lesssim;\": \"≲\",\n            \"&lfisht;\": \"⥼\",\n            \"&lfloor;\": \"⌊\",\n            \"&lfr;\": \"𝔩\",\n            \"&lg;\": \"≶\",\n            \"&lgE;\": \"⪑\",\n            \"&lhard;\": \"↽\",\n            \"&lharu;\": \"↼\",\n            \"&lharul;\": \"⥪\",\n            \"&lhblk;\": \"▄\",\n            \"&ljcy;\": \"љ\",\n            \"&ll;\": \"≪\",\n            \"&llarr;\": \"⇇\",\n            \"&llcorner;\": \"⌞\",\n            \"&llhard;\": \"⥫\",\n            \"&lltri;\": \"◺\",\n            \"&lmidot;\": \"ŀ\",\n            \"&lmoust;\": \"⎰\",\n            \"&lmoustache;\": \"⎰\",\n            \"&lnE;\": \"≨\",\n            \"&lnap;\": \"⪉\",\n            \"&lnapprox;\": \"⪉\",\n            \"&lne;\": \"⪇\",\n            \"&lneq;\": \"⪇\",\n            \"&lneqq;\": \"≨\",\n            \"&lnsim;\": \"⋦\",\n            \"&loang;\": \"⟬\",\n            \"&loarr;\": \"⇽\",\n            \"&lobrk;\": \"⟦\",\n            \"&longleftarrow;\": \"⟵\",\n            \"&longleftrightarrow;\": \"⟷\",\n            \"&longmapsto;\": \"⟼\",\n            \"&longrightarrow;\": \"⟶\",\n            \"&looparrowleft;\": \"↫\",\n            \"&looparrowright;\": \"↬\",\n            \"&lopar;\": \"⦅\",\n            \"&lopf;\": \"𝕝\",\n            \"&loplus;\": \"⨭\",\n            \"&lotimes;\": \"⨴\",\n            \"&lowast;\": \"∗\",\n            \"&lowbar;\": \"_\",\n            \"&loz;\": \"◊\",\n            \"&lozenge;\": \"◊\",\n            \"&lozf;\": \"⧫\",\n            \"&lpar;\": \"(\",\n            \"&lparlt;\": \"⦓\",\n            \"&lrarr;\": \"⇆\",\n            \"&lrcorner;\": \"⌟\",\n            \"&lrhar;\": \"⇋\",\n            \"&lrhard;\": \"⥭\",\n            \"&lrm;\": \"‎\",\n            \"&lrtri;\": \"⊿\",\n            \"&lsaquo;\": \"‹\",\n            \"&lscr;\": \"𝓁\",\n            \"&lsh;\": \"↰\",\n            \"&lsim;\": \"≲\",\n            \"&lsime;\": \"⪍\",\n            \"&lsimg;\": \"⪏\",\n            \"&lsqb;\": \"[\",\n            \"&lsquo;\": \"‘\",\n            \"&lsquor;\": \"‚\",\n            \"&lstrok;\": \"ł\",\n            \"&lt\": \"<\",\n            \"&lt;\": \"<\",\n            \"&ltcc;\": \"⪦\",\n            \"&ltcir;\": \"⩹\",\n            \"&ltdot;\": \"⋖\",\n            \"&lthree;\": \"⋋\",\n            \"&ltimes;\": \"⋉\",\n            \"&ltlarr;\": \"⥶\",\n            \"&ltquest;\": \"⩻\",\n            \"&ltrPar;\": \"⦖\",\n            \"&ltri;\": \"◃\",\n            \"&ltrie;\": \"⊴\",\n            \"&ltrif;\": \"◂\",\n            \"&lurdshar;\": \"⥊\",\n            \"&luruhar;\": \"⥦\",\n            \"&lvertneqq;\": \"≨︀\",\n            \"&lvnE;\": \"≨︀\",\n            \"&mDDot;\": \"∺\",\n            \"&macr\": \"¯\",\n            \"&macr;\": \"¯\",\n            \"&male;\": \"♂\",\n            \"&malt;\": \"✠\",\n            \"&maltese;\": \"✠\",\n            \"&map;\": \"↦\",\n            \"&mapsto;\": \"↦\",\n            \"&mapstodown;\": \"↧\",\n            \"&mapstoleft;\": \"↤\",\n            \"&mapstoup;\": \"↥\",\n            \"&marker;\": \"▮\",\n            \"&mcomma;\": \"⨩\",\n            \"&mcy;\": \"м\",\n            \"&mdash;\": \"—\",\n            \"&measuredangle;\": \"∡\",\n            \"&mfr;\": \"𝔪\",\n            \"&mho;\": \"℧\",\n            \"&micro\": \"µ\",\n            \"&micro;\": \"µ\",\n            \"&mid;\": \"∣\",\n            \"&midast;\": \"*\",\n            \"&midcir;\": \"⫰\",\n            \"&middot\": \"·\",\n            \"&middot;\": \"·\",\n            \"&minus;\": \"−\",\n            \"&minusb;\": \"⊟\",\n            \"&minusd;\": \"∸\",\n            \"&minusdu;\": \"⨪\",\n            \"&mlcp;\": \"⫛\",\n            \"&mldr;\": \"…\",\n            \"&mnplus;\": \"∓\",\n            \"&models;\": \"⊧\",\n            \"&mopf;\": \"𝕞\",\n            \"&mp;\": \"∓\",\n            \"&mscr;\": \"𝓂\",\n            \"&mstpos;\": \"∾\",\n            \"&mu;\": \"μ\",\n            \"&multimap;\": \"⊸\",\n            \"&mumap;\": \"⊸\",\n            \"&nGg;\": \"⋙̸\",\n            \"&nGt;\": \"≫⃒\",\n            \"&nGtv;\": \"≫̸\",\n            \"&nLeftarrow;\": \"⇍\",\n            \"&nLeftrightarrow;\": \"⇎\",\n            \"&nLl;\": \"⋘̸\",\n            \"&nLt;\": \"≪⃒\",\n            \"&nLtv;\": \"≪̸\",\n            \"&nRightarrow;\": \"⇏\",\n            \"&nVDash;\": \"⊯\",\n            \"&nVdash;\": \"⊮\",\n            \"&nabla;\": \"∇\",\n            \"&nacute;\": \"ń\",\n            \"&nang;\": \"∠⃒\",\n            \"&nap;\": \"≉\",\n            \"&napE;\": \"⩰̸\",\n            \"&napid;\": \"≋̸\",\n            \"&napos;\": \"ŉ\",\n            \"&napprox;\": \"≉\",\n            \"&natur;\": \"♮\",\n            \"&natural;\": \"♮\",\n            \"&naturals;\": \"ℕ\",\n            \"&nbsp\": \" \",\n            \"&nbsp;\": \" \",\n            \"&nbump;\": \"≎̸\",\n            \"&nbumpe;\": \"≏̸\",\n            \"&ncap;\": \"⩃\",\n            \"&ncaron;\": \"ň\",\n            \"&ncedil;\": \"ņ\",\n            \"&ncong;\": \"≇\",\n            \"&ncongdot;\": \"⩭̸\",\n            \"&ncup;\": \"⩂\",\n            \"&ncy;\": \"н\",\n            \"&ndash;\": \"–\",\n            \"&ne;\": \"≠\",\n            \"&neArr;\": \"⇗\",\n            \"&nearhk;\": \"⤤\",\n            \"&nearr;\": \"↗\",\n            \"&nearrow;\": \"↗\",\n            \"&nedot;\": \"≐̸\",\n            \"&nequiv;\": \"≢\",\n            \"&nesear;\": \"⤨\",\n            \"&nesim;\": \"≂̸\",\n            \"&nexist;\": \"∄\",\n            \"&nexists;\": \"∄\",\n            \"&nfr;\": \"𝔫\",\n            \"&ngE;\": \"≧̸\",\n            \"&nge;\": \"≱\",\n            \"&ngeq;\": \"≱\",\n            \"&ngeqq;\": \"≧̸\",\n            \"&ngeqslant;\": \"⩾̸\",\n            \"&nges;\": \"⩾̸\",\n            \"&ngsim;\": \"≵\",\n            \"&ngt;\": \"≯\",\n            \"&ngtr;\": \"≯\",\n            \"&nhArr;\": \"⇎\",\n            \"&nharr;\": \"↮\",\n            \"&nhpar;\": \"⫲\",\n            \"&ni;\": \"∋\",\n            \"&nis;\": \"⋼\",\n            \"&nisd;\": \"⋺\",\n            \"&niv;\": \"∋\",\n            \"&njcy;\": \"њ\",\n            \"&nlArr;\": \"⇍\",\n            \"&nlE;\": \"≦̸\",\n            \"&nlarr;\": \"↚\",\n            \"&nldr;\": \"‥\",\n            \"&nle;\": \"≰\",\n            \"&nleftarrow;\": \"↚\",\n            \"&nleftrightarrow;\": \"↮\",\n            \"&nleq;\": \"≰\",\n            \"&nleqq;\": \"≦̸\",\n            \"&nleqslant;\": \"⩽̸\",\n            \"&nles;\": \"⩽̸\",\n            \"&nless;\": \"≮\",\n            \"&nlsim;\": \"≴\",\n            \"&nlt;\": \"≮\",\n            \"&nltri;\": \"⋪\",\n            \"&nltrie;\": \"⋬\",\n            \"&nmid;\": \"∤\",\n            \"&nopf;\": \"𝕟\",\n            \"&not\": \"¬\",\n            \"&not;\": \"¬\",\n            \"&notin;\": \"∉\",\n            \"&notinE;\": \"⋹̸\",\n            \"&notindot;\": \"⋵̸\",\n            \"&notinva;\": \"∉\",\n            \"&notinvb;\": \"⋷\",\n            \"&notinvc;\": \"⋶\",\n            \"&notni;\": \"∌\",\n            \"&notniva;\": \"∌\",\n            \"&notnivb;\": \"⋾\",\n            \"&notnivc;\": \"⋽\",\n            \"&npar;\": \"∦\",\n            \"&nparallel;\": \"∦\",\n            \"&nparsl;\": \"⫽⃥\",\n            \"&npart;\": \"∂̸\",\n            \"&npolint;\": \"⨔\",\n            \"&npr;\": \"⊀\",\n            \"&nprcue;\": \"⋠\",\n            \"&npre;\": \"⪯̸\",\n            \"&nprec;\": \"⊀\",\n            \"&npreceq;\": \"⪯̸\",\n            \"&nrArr;\": \"⇏\",\n            \"&nrarr;\": \"↛\",\n            \"&nrarrc;\": \"⤳̸\",\n            \"&nrarrw;\": \"↝̸\",\n            \"&nrightarrow;\": \"↛\",\n            \"&nrtri;\": \"⋫\",\n            \"&nrtrie;\": \"⋭\",\n            \"&nsc;\": \"⊁\",\n            \"&nsccue;\": \"⋡\",\n            \"&nsce;\": \"⪰̸\",\n            \"&nscr;\": \"𝓃\",\n            \"&nshortmid;\": \"∤\",\n            \"&nshortparallel;\": \"∦\",\n            \"&nsim;\": \"≁\",\n            \"&nsime;\": \"≄\",\n            \"&nsimeq;\": \"≄\",\n            \"&nsmid;\": \"∤\",\n            \"&nspar;\": \"∦\",\n            \"&nsqsube;\": \"⋢\",\n            \"&nsqsupe;\": \"⋣\",\n            \"&nsub;\": \"⊄\",\n            \"&nsubE;\": \"⫅̸\",\n            \"&nsube;\": \"⊈\",\n            \"&nsubset;\": \"⊂⃒\",\n            \"&nsubseteq;\": \"⊈\",\n            \"&nsubseteqq;\": \"⫅̸\",\n            \"&nsucc;\": \"⊁\",\n            \"&nsucceq;\": \"⪰̸\",\n            \"&nsup;\": \"⊅\",\n            \"&nsupE;\": \"⫆̸\",\n            \"&nsupe;\": \"⊉\",\n            \"&nsupset;\": \"⊃⃒\",\n            \"&nsupseteq;\": \"⊉\",\n            \"&nsupseteqq;\": \"⫆̸\",\n            \"&ntgl;\": \"≹\",\n            \"&ntilde\": \"ñ\",\n            \"&ntilde;\": \"ñ\",\n            \"&ntlg;\": \"≸\",\n            \"&ntriangleleft;\": \"⋪\",\n            \"&ntrianglelefteq;\": \"⋬\",\n            \"&ntriangleright;\": \"⋫\",\n            \"&ntrianglerighteq;\": \"⋭\",\n            \"&nu;\": \"ν\",\n            \"&num;\": \"#\",\n            \"&numero;\": \"№\",\n            \"&numsp;\": \" \",\n            \"&nvDash;\": \"⊭\",\n            \"&nvHarr;\": \"⤄\",\n            \"&nvap;\": \"≍⃒\",\n            \"&nvdash;\": \"⊬\",\n            \"&nvge;\": \"≥⃒\",\n            \"&nvgt;\": \">⃒\",\n            \"&nvinfin;\": \"⧞\",\n            \"&nvlArr;\": \"⤂\",\n            \"&nvle;\": \"≤⃒\",\n            \"&nvlt;\": \"<⃒\",\n            \"&nvltrie;\": \"⊴⃒\",\n            \"&nvrArr;\": \"⤃\",\n            \"&nvrtrie;\": \"⊵⃒\",\n            \"&nvsim;\": \"∼⃒\",\n            \"&nwArr;\": \"⇖\",\n            \"&nwarhk;\": \"⤣\",\n            \"&nwarr;\": \"↖\",\n            \"&nwarrow;\": \"↖\",\n            \"&nwnear;\": \"⤧\",\n            \"&oS;\": \"Ⓢ\",\n            \"&oacute\": \"ó\",\n            \"&oacute;\": \"ó\",\n            \"&oast;\": \"⊛\",\n            \"&ocir;\": \"⊚\",\n            \"&ocirc\": \"ô\",\n            \"&ocirc;\": \"ô\",\n            \"&ocy;\": \"о\",\n            \"&odash;\": \"⊝\",\n            \"&odblac;\": \"ő\",\n            \"&odiv;\": \"⨸\",\n            \"&odot;\": \"⊙\",\n            \"&odsold;\": \"⦼\",\n            \"&oelig;\": \"œ\",\n            \"&ofcir;\": \"⦿\",\n            \"&ofr;\": \"𝔬\",\n            \"&ogon;\": \"˛\",\n            \"&ograve\": \"ò\",\n            \"&ograve;\": \"ò\",\n            \"&ogt;\": \"⧁\",\n            \"&ohbar;\": \"⦵\",\n            \"&ohm;\": \"Ω\",\n            \"&oint;\": \"∮\",\n            \"&olarr;\": \"↺\",\n            \"&olcir;\": \"⦾\",\n            \"&olcross;\": \"⦻\",\n            \"&oline;\": \"‾\",\n            \"&olt;\": \"⧀\",\n            \"&omacr;\": \"ō\",\n            \"&omega;\": \"ω\",\n            \"&omicron;\": \"ο\",\n            \"&omid;\": \"⦶\",\n            \"&ominus;\": \"⊖\",\n            \"&oopf;\": \"𝕠\",\n            \"&opar;\": \"⦷\",\n            \"&operp;\": \"⦹\",\n            \"&oplus;\": \"⊕\",\n            \"&or;\": \"∨\",\n            \"&orarr;\": \"↻\",\n            \"&ord;\": \"⩝\",\n            \"&order;\": \"ℴ\",\n            \"&orderof;\": \"ℴ\",\n            \"&ordf\": \"ª\",\n            \"&ordf;\": \"ª\",\n            \"&ordm\": \"º\",\n            \"&ordm;\": \"º\",\n            \"&origof;\": \"⊶\",\n            \"&oror;\": \"⩖\",\n            \"&orslope;\": \"⩗\",\n            \"&orv;\": \"⩛\",\n            \"&oscr;\": \"ℴ\",\n            \"&oslash\": \"ø\",\n            \"&oslash;\": \"ø\",\n            \"&osol;\": \"⊘\",\n            \"&otilde\": \"õ\",\n            \"&otilde;\": \"õ\",\n            \"&otimes;\": \"⊗\",\n            \"&otimesas;\": \"⨶\",\n            \"&ouml\": \"ö\",\n            \"&ouml;\": \"ö\",\n            \"&ovbar;\": \"⌽\",\n            \"&par;\": \"∥\",\n            \"&para\": \"¶\",\n            \"&para;\": \"¶\",\n            \"&parallel;\": \"∥\",\n            \"&parsim;\": \"⫳\",\n            \"&parsl;\": \"⫽\",\n            \"&part;\": \"∂\",\n            \"&pcy;\": \"п\",\n            \"&percnt;\": \"%\",\n            \"&period;\": \".\",\n            \"&permil;\": \"‰\",\n            \"&perp;\": \"⊥\",\n            \"&pertenk;\": \"‱\",\n            \"&pfr;\": \"𝔭\",\n            \"&phi;\": \"φ\",\n            \"&phiv;\": \"ϕ\",\n            \"&phmmat;\": \"ℳ\",\n            \"&phone;\": \"☎\",\n            \"&pi;\": \"π\",\n            \"&pitchfork;\": \"⋔\",\n            \"&piv;\": \"ϖ\",\n            \"&planck;\": \"ℏ\",\n            \"&planckh;\": \"ℎ\",\n            \"&plankv;\": \"ℏ\",\n            \"&plus;\": \"+\",\n            \"&plusacir;\": \"⨣\",\n            \"&plusb;\": \"⊞\",\n            \"&pluscir;\": \"⨢\",\n            \"&plusdo;\": \"∔\",\n            \"&plusdu;\": \"⨥\",\n            \"&pluse;\": \"⩲\",\n            \"&plusmn\": \"±\",\n            \"&plusmn;\": \"±\",\n            \"&plussim;\": \"⨦\",\n            \"&plustwo;\": \"⨧\",\n            \"&pm;\": \"±\",\n            \"&pointint;\": \"⨕\",\n            \"&popf;\": \"𝕡\",\n            \"&pound\": \"£\",\n            \"&pound;\": \"£\",\n            \"&pr;\": \"≺\",\n            \"&prE;\": \"⪳\",\n            \"&prap;\": \"⪷\",\n            \"&prcue;\": \"≼\",\n            \"&pre;\": \"⪯\",\n            \"&prec;\": \"≺\",\n            \"&precapprox;\": \"⪷\",\n            \"&preccurlyeq;\": \"≼\",\n            \"&preceq;\": \"⪯\",\n            \"&precnapprox;\": \"⪹\",\n            \"&precneqq;\": \"⪵\",\n            \"&precnsim;\": \"⋨\",\n            \"&precsim;\": \"≾\",\n            \"&prime;\": \"′\",\n            \"&primes;\": \"ℙ\",\n            \"&prnE;\": \"⪵\",\n            \"&prnap;\": \"⪹\",\n            \"&prnsim;\": \"⋨\",\n            \"&prod;\": \"∏\",\n            \"&profalar;\": \"⌮\",\n            \"&profline;\": \"⌒\",\n            \"&profsurf;\": \"⌓\",\n            \"&prop;\": \"∝\",\n            \"&propto;\": \"∝\",\n            \"&prsim;\": \"≾\",\n            \"&prurel;\": \"⊰\",\n            \"&pscr;\": \"𝓅\",\n            \"&psi;\": \"ψ\",\n            \"&puncsp;\": \" \",\n            \"&qfr;\": \"𝔮\",\n            \"&qint;\": \"⨌\",\n            \"&qopf;\": \"𝕢\",\n            \"&qprime;\": \"⁗\",\n            \"&qscr;\": \"𝓆\",\n            \"&quaternions;\": \"ℍ\",\n            \"&quatint;\": \"⨖\",\n            \"&quest;\": \"?\",\n            \"&questeq;\": \"≟\",\n            \"&quot\": \"\\\"\",\n            \"&quot;\": \"\\\"\",\n            \"&rAarr;\": \"⇛\",\n            \"&rArr;\": \"⇒\",\n            \"&rAtail;\": \"⤜\",\n            \"&rBarr;\": \"⤏\",\n            \"&rHar;\": \"⥤\",\n            \"&race;\": \"∽̱\",\n            \"&racute;\": \"ŕ\",\n            \"&radic;\": \"√\",\n            \"&raemptyv;\": \"⦳\",\n            \"&rang;\": \"⟩\",\n            \"&rangd;\": \"⦒\",\n            \"&range;\": \"⦥\",\n            \"&rangle;\": \"⟩\",\n            \"&raquo\": \"»\",\n            \"&raquo;\": \"»\",\n            \"&rarr;\": \"→\",\n            \"&rarrap;\": \"⥵\",\n            \"&rarrb;\": \"⇥\",\n            \"&rarrbfs;\": \"⤠\",\n            \"&rarrc;\": \"⤳\",\n            \"&rarrfs;\": \"⤞\",\n            \"&rarrhk;\": \"↪\",\n            \"&rarrlp;\": \"↬\",\n            \"&rarrpl;\": \"⥅\",\n            \"&rarrsim;\": \"⥴\",\n            \"&rarrtl;\": \"↣\",\n            \"&rarrw;\": \"↝\",\n            \"&ratail;\": \"⤚\",\n            \"&ratio;\": \"∶\",\n            \"&rationals;\": \"ℚ\",\n            \"&rbarr;\": \"⤍\",\n            \"&rbbrk;\": \"❳\",\n            \"&rbrace;\": \"}\",\n            \"&rbrack;\": \"]\",\n            \"&rbrke;\": \"⦌\",\n            \"&rbrksld;\": \"⦎\",\n            \"&rbrkslu;\": \"⦐\",\n            \"&rcaron;\": \"ř\",\n            \"&rcedil;\": \"ŗ\",\n            \"&rceil;\": \"⌉\",\n            \"&rcub;\": \"}\",\n            \"&rcy;\": \"р\",\n            \"&rdca;\": \"⤷\",\n            \"&rdldhar;\": \"⥩\",\n            \"&rdquo;\": \"”\",\n            \"&rdquor;\": \"”\",\n            \"&rdsh;\": \"↳\",\n            \"&real;\": \"ℜ\",\n            \"&realine;\": \"ℛ\",\n            \"&realpart;\": \"ℜ\",\n            \"&reals;\": \"ℝ\",\n            \"&rect;\": \"▭\",\n            \"&reg\": \"®\",\n            \"&reg;\": \"®\",\n            \"&rfisht;\": \"⥽\",\n            \"&rfloor;\": \"⌋\",\n            \"&rfr;\": \"𝔯\",\n            \"&rhard;\": \"⇁\",\n            \"&rharu;\": \"⇀\",\n            \"&rharul;\": \"⥬\",\n            \"&rho;\": \"ρ\",\n            \"&rhov;\": \"ϱ\",\n            \"&rightarrow;\": \"→\",\n            \"&rightarrowtail;\": \"↣\",\n            \"&rightharpoondown;\": \"⇁\",\n            \"&rightharpoonup;\": \"⇀\",\n            \"&rightleftarrows;\": \"⇄\",\n            \"&rightleftharpoons;\": \"⇌\",\n            \"&rightrightarrows;\": \"⇉\",\n            \"&rightsquigarrow;\": \"↝\",\n            \"&rightthreetimes;\": \"⋌\",\n            \"&ring;\": \"˚\",\n            \"&risingdotseq;\": \"≓\",\n            \"&rlarr;\": \"⇄\",\n            \"&rlhar;\": \"⇌\",\n            \"&rlm;\": \"‏\",\n            \"&rmoust;\": \"⎱\",\n            \"&rmoustache;\": \"⎱\",\n            \"&rnmid;\": \"⫮\",\n            \"&roang;\": \"⟭\",\n            \"&roarr;\": \"⇾\",\n            \"&robrk;\": \"⟧\",\n            \"&ropar;\": \"⦆\",\n            \"&ropf;\": \"𝕣\",\n            \"&roplus;\": \"⨮\",\n            \"&rotimes;\": \"⨵\",\n            \"&rpar;\": \")\",\n            \"&rpargt;\": \"⦔\",\n            \"&rppolint;\": \"⨒\",\n            \"&rrarr;\": \"⇉\",\n            \"&rsaquo;\": \"›\",\n            \"&rscr;\": \"𝓇\",\n            \"&rsh;\": \"↱\",\n            \"&rsqb;\": \"]\",\n            \"&rsquo;\": \"’\",\n            \"&rsquor;\": \"’\",\n            \"&rthree;\": \"⋌\",\n            \"&rtimes;\": \"⋊\",\n            \"&rtri;\": \"▹\",\n            \"&rtrie;\": \"⊵\",\n            \"&rtrif;\": \"▸\",\n            \"&rtriltri;\": \"⧎\",\n            \"&ruluhar;\": \"⥨\",\n            \"&rx;\": \"℞\",\n            \"&sacute;\": \"ś\",\n            \"&sbquo;\": \"‚\",\n            \"&sc;\": \"≻\",\n            \"&scE;\": \"⪴\",\n            \"&scap;\": \"⪸\",\n            \"&scaron;\": \"š\",\n            \"&sccue;\": \"≽\",\n            \"&sce;\": \"⪰\",\n            \"&scedil;\": \"ş\",\n            \"&scirc;\": \"ŝ\",\n            \"&scnE;\": \"⪶\",\n            \"&scnap;\": \"⪺\",\n            \"&scnsim;\": \"⋩\",\n            \"&scpolint;\": \"⨓\",\n            \"&scsim;\": \"≿\",\n            \"&scy;\": \"с\",\n            \"&sdot;\": \"⋅\",\n            \"&sdotb;\": \"⊡\",\n            \"&sdote;\": \"⩦\",\n            \"&seArr;\": \"⇘\",\n            \"&searhk;\": \"⤥\",\n            \"&searr;\": \"↘\",\n            \"&searrow;\": \"↘\",\n            \"&sect\": \"§\",\n            \"&sect;\": \"§\",\n            \"&semi;\": \";\",\n            \"&seswar;\": \"⤩\",\n            \"&setminus;\": \"∖\",\n            \"&setmn;\": \"∖\",\n            \"&sext;\": \"✶\",\n            \"&sfr;\": \"𝔰\",\n            \"&sfrown;\": \"⌢\",\n            \"&sharp;\": \"♯\",\n            \"&shchcy;\": \"щ\",\n            \"&shcy;\": \"ш\",\n            \"&shortmid;\": \"∣\",\n            \"&shortparallel;\": \"∥\",\n            \"&shy\": \"­\",\n            \"&shy;\": \"­\",\n            \"&sigma;\": \"σ\",\n            \"&sigmaf;\": \"ς\",\n            \"&sigmav;\": \"ς\",\n            \"&sim;\": \"∼\",\n            \"&simdot;\": \"⩪\",\n            \"&sime;\": \"≃\",\n            \"&simeq;\": \"≃\",\n            \"&simg;\": \"⪞\",\n            \"&simgE;\": \"⪠\",\n            \"&siml;\": \"⪝\",\n            \"&simlE;\": \"⪟\",\n            \"&simne;\": \"≆\",\n            \"&simplus;\": \"⨤\",\n            \"&simrarr;\": \"⥲\",\n            \"&slarr;\": \"←\",\n            \"&smallsetminus;\": \"∖\",\n            \"&smashp;\": \"⨳\",\n            \"&smeparsl;\": \"⧤\",\n            \"&smid;\": \"∣\",\n            \"&smile;\": \"⌣\",\n            \"&smt;\": \"⪪\",\n            \"&smte;\": \"⪬\",\n            \"&smtes;\": \"⪬︀\",\n            \"&softcy;\": \"ь\",\n            \"&sol;\": \"/\",\n            \"&solb;\": \"⧄\",\n            \"&solbar;\": \"⌿\",\n            \"&sopf;\": \"𝕤\",\n            \"&spades;\": \"♠\",\n            \"&spadesuit;\": \"♠\",\n            \"&spar;\": \"∥\",\n            \"&sqcap;\": \"⊓\",\n            \"&sqcaps;\": \"⊓︀\",\n            \"&sqcup;\": \"⊔\",\n            \"&sqcups;\": \"⊔︀\",\n            \"&sqsub;\": \"⊏\",\n            \"&sqsube;\": \"⊑\",\n            \"&sqsubset;\": \"⊏\",\n            \"&sqsubseteq;\": \"⊑\",\n            \"&sqsup;\": \"⊐\",\n            \"&sqsupe;\": \"⊒\",\n            \"&sqsupset;\": \"⊐\",\n            \"&sqsupseteq;\": \"⊒\",\n            \"&squ;\": \"□\",\n            \"&square;\": \"□\",\n            \"&squarf;\": \"▪\",\n            \"&squf;\": \"▪\",\n            \"&srarr;\": \"→\",\n            \"&sscr;\": \"𝓈\",\n            \"&ssetmn;\": \"∖\",\n            \"&ssmile;\": \"⌣\",\n            \"&sstarf;\": \"⋆\",\n            \"&star;\": \"☆\",\n            \"&starf;\": \"★\",\n            \"&straightepsilon;\": \"ϵ\",\n            \"&straightphi;\": \"ϕ\",\n            \"&strns;\": \"¯\",\n            \"&sub;\": \"⊂\",\n            \"&subE;\": \"⫅\",\n            \"&subdot;\": \"⪽\",\n            \"&sube;\": \"⊆\",\n            \"&subedot;\": \"⫃\",\n            \"&submult;\": \"⫁\",\n            \"&subnE;\": \"⫋\",\n            \"&subne;\": \"⊊\",\n            \"&subplus;\": \"⪿\",\n            \"&subrarr;\": \"⥹\",\n            \"&subset;\": \"⊂\",\n            \"&subseteq;\": \"⊆\",\n            \"&subseteqq;\": \"⫅\",\n            \"&subsetneq;\": \"⊊\",\n            \"&subsetneqq;\": \"⫋\",\n            \"&subsim;\": \"⫇\",\n            \"&subsub;\": \"⫕\",\n            \"&subsup;\": \"⫓\",\n            \"&succ;\": \"≻\",\n            \"&succapprox;\": \"⪸\",\n            \"&succcurlyeq;\": \"≽\",\n            \"&succeq;\": \"⪰\",\n            \"&succnapprox;\": \"⪺\",\n            \"&succneqq;\": \"⪶\",\n            \"&succnsim;\": \"⋩\",\n            \"&succsim;\": \"≿\",\n            \"&sum;\": \"∑\",\n            \"&sung;\": \"♪\",\n            \"&sup1\": \"¹\",\n            \"&sup1;\": \"¹\",\n            \"&sup2\": \"²\",\n            \"&sup2;\": \"²\",\n            \"&sup3\": \"³\",\n            \"&sup3;\": \"³\",\n            \"&sup;\": \"⊃\",\n            \"&supE;\": \"⫆\",\n            \"&supdot;\": \"⪾\",\n            \"&supdsub;\": \"⫘\",\n            \"&supe;\": \"⊇\",\n            \"&supedot;\": \"⫄\",\n            \"&suphsol;\": \"⟉\",\n            \"&suphsub;\": \"⫗\",\n            \"&suplarr;\": \"⥻\",\n            \"&supmult;\": \"⫂\",\n            \"&supnE;\": \"⫌\",\n            \"&supne;\": \"⊋\",\n            \"&supplus;\": \"⫀\",\n            \"&supset;\": \"⊃\",\n            \"&supseteq;\": \"⊇\",\n            \"&supseteqq;\": \"⫆\",\n            \"&supsetneq;\": \"⊋\",\n            \"&supsetneqq;\": \"⫌\",\n            \"&supsim;\": \"⫈\",\n            \"&supsub;\": \"⫔\",\n            \"&supsup;\": \"⫖\",\n            \"&swArr;\": \"⇙\",\n            \"&swarhk;\": \"⤦\",\n            \"&swarr;\": \"↙\",\n            \"&swarrow;\": \"↙\",\n            \"&swnwar;\": \"⤪\",\n            \"&szlig\": \"ß\",\n            \"&szlig;\": \"ß\",\n            \"&target;\": \"⌖\",\n            \"&tau;\": \"τ\",\n            \"&tbrk;\": \"⎴\",\n            \"&tcaron;\": \"ť\",\n            \"&tcedil;\": \"ţ\",\n            \"&tcy;\": \"т\",\n            \"&tdot;\": \"⃛\",\n            \"&telrec;\": \"⌕\",\n            \"&tfr;\": \"𝔱\",\n            \"&there4;\": \"∴\",\n            \"&therefore;\": \"∴\",\n            \"&theta;\": \"θ\",\n            \"&thetasym;\": \"ϑ\",\n            \"&thetav;\": \"ϑ\",\n            \"&thickapprox;\": \"≈\",\n            \"&thicksim;\": \"∼\",\n            \"&thinsp;\": \" \",\n            \"&thkap;\": \"≈\",\n            \"&thksim;\": \"∼\",\n            \"&thorn\": \"þ\",\n            \"&thorn;\": \"þ\",\n            \"&tilde;\": \"˜\",\n            \"&times\": \"×\",\n            \"&times;\": \"×\",\n            \"&timesb;\": \"⊠\",\n            \"&timesbar;\": \"⨱\",\n            \"&timesd;\": \"⨰\",\n            \"&tint;\": \"∭\",\n            \"&toea;\": \"⤨\",\n            \"&top;\": \"⊤\",\n            \"&topbot;\": \"⌶\",\n            \"&topcir;\": \"⫱\",\n            \"&topf;\": \"𝕥\",\n            \"&topfork;\": \"⫚\",\n            \"&tosa;\": \"⤩\",\n            \"&tprime;\": \"‴\",\n            \"&trade;\": \"™\",\n            \"&triangle;\": \"▵\",\n            \"&triangledown;\": \"▿\",\n            \"&triangleleft;\": \"◃\",\n            \"&trianglelefteq;\": \"⊴\",\n            \"&triangleq;\": \"≜\",\n            \"&triangleright;\": \"▹\",\n            \"&trianglerighteq;\": \"⊵\",\n            \"&tridot;\": \"◬\",\n            \"&trie;\": \"≜\",\n            \"&triminus;\": \"⨺\",\n            \"&triplus;\": \"⨹\",\n            \"&trisb;\": \"⧍\",\n            \"&tritime;\": \"⨻\",\n            \"&trpezium;\": \"⏢\",\n            \"&tscr;\": \"𝓉\",\n            \"&tscy;\": \"ц\",\n            \"&tshcy;\": \"ћ\",\n            \"&tstrok;\": \"ŧ\",\n            \"&twixt;\": \"≬\",\n            \"&twoheadleftarrow;\": \"↞\",\n            \"&twoheadrightarrow;\": \"↠\",\n            \"&uArr;\": \"⇑\",\n            \"&uHar;\": \"⥣\",\n            \"&uacute\": \"ú\",\n            \"&uacute;\": \"ú\",\n            \"&uarr;\": \"↑\",\n            \"&ubrcy;\": \"ў\",\n            \"&ubreve;\": \"ŭ\",\n            \"&ucirc\": \"û\",\n            \"&ucirc;\": \"û\",\n            \"&ucy;\": \"у\",\n            \"&udarr;\": \"⇅\",\n            \"&udblac;\": \"ű\",\n            \"&udhar;\": \"⥮\",\n            \"&ufisht;\": \"⥾\",\n            \"&ufr;\": \"𝔲\",\n            \"&ugrave\": \"ù\",\n            \"&ugrave;\": \"ù\",\n            \"&uharl;\": \"↿\",\n            \"&uharr;\": \"↾\",\n            \"&uhblk;\": \"▀\",\n            \"&ulcorn;\": \"⌜\",\n            \"&ulcorner;\": \"⌜\",\n            \"&ulcrop;\": \"⌏\",\n            \"&ultri;\": \"◸\",\n            \"&umacr;\": \"ū\",\n            \"&uml\": \"¨\",\n            \"&uml;\": \"¨\",\n            \"&uogon;\": \"ų\",\n            \"&uopf;\": \"𝕦\",\n            \"&uparrow;\": \"↑\",\n            \"&updownarrow;\": \"↕\",\n            \"&upharpoonleft;\": \"↿\",\n            \"&upharpoonright;\": \"↾\",\n            \"&uplus;\": \"⊎\",\n            \"&upsi;\": \"υ\",\n            \"&upsih;\": \"ϒ\",\n            \"&upsilon;\": \"υ\",\n            \"&upuparrows;\": \"⇈\",\n            \"&urcorn;\": \"⌝\",\n            \"&urcorner;\": \"⌝\",\n            \"&urcrop;\": \"⌎\",\n            \"&uring;\": \"ů\",\n            \"&urtri;\": \"◹\",\n            \"&uscr;\": \"𝓊\",\n            \"&utdot;\": \"⋰\",\n            \"&utilde;\": \"ũ\",\n            \"&utri;\": \"▵\",\n            \"&utrif;\": \"▴\",\n            \"&uuarr;\": \"⇈\",\n            \"&uuml\": \"ü\",\n            \"&uuml;\": \"ü\",\n            \"&uwangle;\": \"⦧\",\n            \"&vArr;\": \"⇕\",\n            \"&vBar;\": \"⫨\",\n            \"&vBarv;\": \"⫩\",\n            \"&vDash;\": \"⊨\",\n            \"&vangrt;\": \"⦜\",\n            \"&varepsilon;\": \"ϵ\",\n            \"&varkappa;\": \"ϰ\",\n            \"&varnothing;\": \"∅\",\n            \"&varphi;\": \"ϕ\",\n            \"&varpi;\": \"ϖ\",\n            \"&varpropto;\": \"∝\",\n            \"&varr;\": \"↕\",\n            \"&varrho;\": \"ϱ\",\n            \"&varsigma;\": \"ς\",\n            \"&varsubsetneq;\": \"⊊︀\",\n            \"&varsubsetneqq;\": \"⫋︀\",\n            \"&varsupsetneq;\": \"⊋︀\",\n            \"&varsupsetneqq;\": \"⫌︀\",\n            \"&vartheta;\": \"ϑ\",\n            \"&vartriangleleft;\": \"⊲\",\n            \"&vartriangleright;\": \"⊳\",\n            \"&vcy;\": \"в\",\n            \"&vdash;\": \"⊢\",\n            \"&vee;\": \"∨\",\n            \"&veebar;\": \"⊻\",\n            \"&veeeq;\": \"≚\",\n            \"&vellip;\": \"⋮\",\n            \"&verbar;\": \"|\",\n            \"&vert;\": \"|\",\n            \"&vfr;\": \"𝔳\",\n            \"&vltri;\": \"⊲\",\n            \"&vnsub;\": \"⊂⃒\",\n            \"&vnsup;\": \"⊃⃒\",\n            \"&vopf;\": \"𝕧\",\n            \"&vprop;\": \"∝\",\n            \"&vrtri;\": \"⊳\",\n            \"&vscr;\": \"𝓋\",\n            \"&vsubnE;\": \"⫋︀\",\n            \"&vsubne;\": \"⊊︀\",\n            \"&vsupnE;\": \"⫌︀\",\n            \"&vsupne;\": \"⊋︀\",\n            \"&vzigzag;\": \"⦚\",\n            \"&wcirc;\": \"ŵ\",\n            \"&wedbar;\": \"⩟\",\n            \"&wedge;\": \"∧\",\n            \"&wedgeq;\": \"≙\",\n            \"&weierp;\": \"℘\",\n            \"&wfr;\": \"𝔴\",\n            \"&wopf;\": \"𝕨\",\n            \"&wp;\": \"℘\",\n            \"&wr;\": \"≀\",\n            \"&wreath;\": \"≀\",\n            \"&wscr;\": \"𝓌\",\n            \"&xcap;\": \"⋂\",\n            \"&xcirc;\": \"◯\",\n            \"&xcup;\": \"⋃\",\n            \"&xdtri;\": \"▽\",\n            \"&xfr;\": \"𝔵\",\n            \"&xhArr;\": \"⟺\",\n            \"&xharr;\": \"⟷\",\n            \"&xi;\": \"ξ\",\n            \"&xlArr;\": \"⟸\",\n            \"&xlarr;\": \"⟵\",\n            \"&xmap;\": \"⟼\",\n            \"&xnis;\": \"⋻\",\n            \"&xodot;\": \"⨀\",\n            \"&xopf;\": \"𝕩\",\n            \"&xoplus;\": \"⨁\",\n            \"&xotime;\": \"⨂\",\n            \"&xrArr;\": \"⟹\",\n            \"&xrarr;\": \"⟶\",\n            \"&xscr;\": \"𝓍\",\n            \"&xsqcup;\": \"⨆\",\n            \"&xuplus;\": \"⨄\",\n            \"&xutri;\": \"△\",\n            \"&xvee;\": \"⋁\",\n            \"&xwedge;\": \"⋀\",\n            \"&yacute\": \"ý\",\n            \"&yacute;\": \"ý\",\n            \"&yacy;\": \"я\",\n            \"&ycirc;\": \"ŷ\",\n            \"&ycy;\": \"ы\",\n            \"&yen\": \"¥\",\n            \"&yen;\": \"¥\",\n            \"&yfr;\": \"𝔶\",\n            \"&yicy;\": \"ї\",\n            \"&yopf;\": \"𝕪\",\n            \"&yscr;\": \"𝓎\",\n            \"&yucy;\": \"ю\",\n            \"&yuml\": \"ÿ\",\n            \"&yuml;\": \"ÿ\",\n            \"&zacute;\": \"ź\",\n            \"&zcaron;\": \"ž\",\n            \"&zcy;\": \"з\",\n            \"&zdot;\": \"ż\",\n            \"&zeetrf;\": \"ℨ\",\n            \"&zeta;\": \"ζ\",\n            \"&zfr;\": \"𝔷\",\n            \"&zhcy;\": \"ж\",\n            \"&zigrarr;\": \"⇝\",\n            \"&zopf;\": \"𝕫\",\n            \"&zscr;\": \"𝓏\",\n            \"&zwj;\": \"‍\",\n            \"&zwnj;\": \"‌\"\n        },\n        \"characters\": {\n            \"Æ\": \"&AElig;\",\n            \"&\": \"&amp;\",\n            \"Á\": \"&Aacute;\",\n            \"Ă\": \"&Abreve;\",\n            \"Â\": \"&Acirc;\",\n            \"А\": \"&Acy;\",\n            \"𝔄\": \"&Afr;\",\n            \"À\": \"&Agrave;\",\n            \"Α\": \"&Alpha;\",\n            \"Ā\": \"&Amacr;\",\n            \"⩓\": \"&And;\",\n            \"Ą\": \"&Aogon;\",\n            \"𝔸\": \"&Aopf;\",\n            \"⁡\": \"&af;\",\n            \"Å\": \"&angst;\",\n            \"𝒜\": \"&Ascr;\",\n            \"≔\": \"&coloneq;\",\n            \"Ã\": \"&Atilde;\",\n            \"Ä\": \"&Auml;\",\n            \"∖\": \"&ssetmn;\",\n            \"⫧\": \"&Barv;\",\n            \"⌆\": \"&doublebarwedge;\",\n            \"Б\": \"&Bcy;\",\n            \"∵\": \"&because;\",\n            \"ℬ\": \"&bernou;\",\n            \"Β\": \"&Beta;\",\n            \"𝔅\": \"&Bfr;\",\n            \"𝔹\": \"&Bopf;\",\n            \"˘\": \"&breve;\",\n            \"≎\": \"&bump;\",\n            \"Ч\": \"&CHcy;\",\n            \"©\": \"&copy;\",\n            \"Ć\": \"&Cacute;\",\n            \"⋒\": \"&Cap;\",\n            \"ⅅ\": \"&DD;\",\n            \"ℭ\": \"&Cfr;\",\n            \"Č\": \"&Ccaron;\",\n            \"Ç\": \"&Ccedil;\",\n            \"Ĉ\": \"&Ccirc;\",\n            \"∰\": \"&Cconint;\",\n            \"Ċ\": \"&Cdot;\",\n            \"¸\": \"&cedil;\",\n            \"·\": \"&middot;\",\n            \"Χ\": \"&Chi;\",\n            \"⊙\": \"&odot;\",\n            \"⊖\": \"&ominus;\",\n            \"⊕\": \"&oplus;\",\n            \"⊗\": \"&otimes;\",\n            \"∲\": \"&cwconint;\",\n            \"”\": \"&rdquor;\",\n            \"’\": \"&rsquor;\",\n            \"∷\": \"&Proportion;\",\n            \"⩴\": \"&Colone;\",\n            \"≡\": \"&equiv;\",\n            \"∯\": \"&DoubleContourIntegral;\",\n            \"∮\": \"&oint;\",\n            \"ℂ\": \"&complexes;\",\n            \"∐\": \"&coprod;\",\n            \"∳\": \"&awconint;\",\n            \"⨯\": \"&Cross;\",\n            \"𝒞\": \"&Cscr;\",\n            \"⋓\": \"&Cup;\",\n            \"≍\": \"&asympeq;\",\n            \"⤑\": \"&DDotrahd;\",\n            \"Ђ\": \"&DJcy;\",\n            \"Ѕ\": \"&DScy;\",\n            \"Џ\": \"&DZcy;\",\n            \"‡\": \"&ddagger;\",\n            \"↡\": \"&Darr;\",\n            \"⫤\": \"&DoubleLeftTee;\",\n            \"Ď\": \"&Dcaron;\",\n            \"Д\": \"&Dcy;\",\n            \"∇\": \"&nabla;\",\n            \"Δ\": \"&Delta;\",\n            \"𝔇\": \"&Dfr;\",\n            \"´\": \"&acute;\",\n            \"˙\": \"&dot;\",\n            \"˝\": \"&dblac;\",\n            \"`\": \"&grave;\",\n            \"˜\": \"&tilde;\",\n            \"⋄\": \"&diamond;\",\n            \"ⅆ\": \"&dd;\",\n            \"𝔻\": \"&Dopf;\",\n            \"¨\": \"&uml;\",\n            \"⃜\": \"&DotDot;\",\n            \"≐\": \"&esdot;\",\n            \"⇓\": \"&dArr;\",\n            \"⇐\": \"&lArr;\",\n            \"⇔\": \"&iff;\",\n            \"⟸\": \"&xlArr;\",\n            \"⟺\": \"&xhArr;\",\n            \"⟹\": \"&xrArr;\",\n            \"⇒\": \"&rArr;\",\n            \"⊨\": \"&vDash;\",\n            \"⇑\": \"&uArr;\",\n            \"⇕\": \"&vArr;\",\n            \"∥\": \"&spar;\",\n            \"↓\": \"&downarrow;\",\n            \"⤓\": \"&DownArrowBar;\",\n            \"⇵\": \"&duarr;\",\n            \"̑\": \"&DownBreve;\",\n            \"⥐\": \"&DownLeftRightVector;\",\n            \"⥞\": \"&DownLeftTeeVector;\",\n            \"↽\": \"&lhard;\",\n            \"⥖\": \"&DownLeftVectorBar;\",\n            \"⥟\": \"&DownRightTeeVector;\",\n            \"⇁\": \"&rightharpoondown;\",\n            \"⥗\": \"&DownRightVectorBar;\",\n            \"⊤\": \"&top;\",\n            \"↧\": \"&mapstodown;\",\n            \"𝒟\": \"&Dscr;\",\n            \"Đ\": \"&Dstrok;\",\n            \"Ŋ\": \"&ENG;\",\n            \"Ð\": \"&ETH;\",\n            \"É\": \"&Eacute;\",\n            \"Ě\": \"&Ecaron;\",\n            \"Ê\": \"&Ecirc;\",\n            \"Э\": \"&Ecy;\",\n            \"Ė\": \"&Edot;\",\n            \"𝔈\": \"&Efr;\",\n            \"È\": \"&Egrave;\",\n            \"∈\": \"&isinv;\",\n            \"Ē\": \"&Emacr;\",\n            \"◻\": \"&EmptySmallSquare;\",\n            \"▫\": \"&EmptyVerySmallSquare;\",\n            \"Ę\": \"&Eogon;\",\n            \"𝔼\": \"&Eopf;\",\n            \"Ε\": \"&Epsilon;\",\n            \"⩵\": \"&Equal;\",\n            \"≂\": \"&esim;\",\n            \"⇌\": \"&rlhar;\",\n            \"ℰ\": \"&expectation;\",\n            \"⩳\": \"&Esim;\",\n            \"Η\": \"&Eta;\",\n            \"Ë\": \"&Euml;\",\n            \"∃\": \"&exist;\",\n            \"ⅇ\": \"&exponentiale;\",\n            \"Ф\": \"&Fcy;\",\n            \"𝔉\": \"&Ffr;\",\n            \"◼\": \"&FilledSmallSquare;\",\n            \"▪\": \"&squf;\",\n            \"𝔽\": \"&Fopf;\",\n            \"∀\": \"&forall;\",\n            \"ℱ\": \"&Fscr;\",\n            \"Ѓ\": \"&GJcy;\",\n            \">\": \"&gt;\",\n            \"Γ\": \"&Gamma;\",\n            \"Ϝ\": \"&Gammad;\",\n            \"Ğ\": \"&Gbreve;\",\n            \"Ģ\": \"&Gcedil;\",\n            \"Ĝ\": \"&Gcirc;\",\n            \"Г\": \"&Gcy;\",\n            \"Ġ\": \"&Gdot;\",\n            \"𝔊\": \"&Gfr;\",\n            \"⋙\": \"&ggg;\",\n            \"𝔾\": \"&Gopf;\",\n            \"≥\": \"&geq;\",\n            \"⋛\": \"&gtreqless;\",\n            \"≧\": \"&geqq;\",\n            \"⪢\": \"&GreaterGreater;\",\n            \"≷\": \"&gtrless;\",\n            \"⩾\": \"&ges;\",\n            \"≳\": \"&gtrsim;\",\n            \"𝒢\": \"&Gscr;\",\n            \"≫\": \"&gg;\",\n            \"Ъ\": \"&HARDcy;\",\n            \"ˇ\": \"&caron;\",\n            \"^\": \"&Hat;\",\n            \"Ĥ\": \"&Hcirc;\",\n            \"ℌ\": \"&Poincareplane;\",\n            \"ℋ\": \"&hamilt;\",\n            \"ℍ\": \"&quaternions;\",\n            \"─\": \"&boxh;\",\n            \"Ħ\": \"&Hstrok;\",\n            \"≏\": \"&bumpeq;\",\n            \"Е\": \"&IEcy;\",\n            \"Ĳ\": \"&IJlig;\",\n            \"Ё\": \"&IOcy;\",\n            \"Í\": \"&Iacute;\",\n            \"Î\": \"&Icirc;\",\n            \"И\": \"&Icy;\",\n            \"İ\": \"&Idot;\",\n            \"ℑ\": \"&imagpart;\",\n            \"Ì\": \"&Igrave;\",\n            \"Ī\": \"&Imacr;\",\n            \"ⅈ\": \"&ii;\",\n            \"∬\": \"&Int;\",\n            \"∫\": \"&int;\",\n            \"⋂\": \"&xcap;\",\n            \"⁣\": \"&ic;\",\n            \"⁢\": \"&it;\",\n            \"Į\": \"&Iogon;\",\n            \"𝕀\": \"&Iopf;\",\n            \"Ι\": \"&Iota;\",\n            \"ℐ\": \"&imagline;\",\n            \"Ĩ\": \"&Itilde;\",\n            \"І\": \"&Iukcy;\",\n            \"Ï\": \"&Iuml;\",\n            \"Ĵ\": \"&Jcirc;\",\n            \"Й\": \"&Jcy;\",\n            \"𝔍\": \"&Jfr;\",\n            \"𝕁\": \"&Jopf;\",\n            \"𝒥\": \"&Jscr;\",\n            \"Ј\": \"&Jsercy;\",\n            \"Є\": \"&Jukcy;\",\n            \"Х\": \"&KHcy;\",\n            \"Ќ\": \"&KJcy;\",\n            \"Κ\": \"&Kappa;\",\n            \"Ķ\": \"&Kcedil;\",\n            \"К\": \"&Kcy;\",\n            \"𝔎\": \"&Kfr;\",\n            \"𝕂\": \"&Kopf;\",\n            \"𝒦\": \"&Kscr;\",\n            \"Љ\": \"&LJcy;\",\n            \"<\": \"&lt;\",\n            \"Ĺ\": \"&Lacute;\",\n            \"Λ\": \"&Lambda;\",\n            \"⟪\": \"&Lang;\",\n            \"ℒ\": \"&lagran;\",\n            \"↞\": \"&twoheadleftarrow;\",\n            \"Ľ\": \"&Lcaron;\",\n            \"Ļ\": \"&Lcedil;\",\n            \"Л\": \"&Lcy;\",\n            \"⟨\": \"&langle;\",\n            \"←\": \"&slarr;\",\n            \"⇤\": \"&larrb;\",\n            \"⇆\": \"&lrarr;\",\n            \"⌈\": \"&lceil;\",\n            \"⟦\": \"&lobrk;\",\n            \"⥡\": \"&LeftDownTeeVector;\",\n            \"⇃\": \"&downharpoonleft;\",\n            \"⥙\": \"&LeftDownVectorBar;\",\n            \"⌊\": \"&lfloor;\",\n            \"↔\": \"&leftrightarrow;\",\n            \"⥎\": \"&LeftRightVector;\",\n            \"⊣\": \"&dashv;\",\n            \"↤\": \"&mapstoleft;\",\n            \"⥚\": \"&LeftTeeVector;\",\n            \"⊲\": \"&vltri;\",\n            \"⧏\": \"&LeftTriangleBar;\",\n            \"⊴\": \"&trianglelefteq;\",\n            \"⥑\": \"&LeftUpDownVector;\",\n            \"⥠\": \"&LeftUpTeeVector;\",\n            \"↿\": \"&upharpoonleft;\",\n            \"⥘\": \"&LeftUpVectorBar;\",\n            \"↼\": \"&lharu;\",\n            \"⥒\": \"&LeftVectorBar;\",\n            \"⋚\": \"&lesseqgtr;\",\n            \"≦\": \"&leqq;\",\n            \"≶\": \"&lg;\",\n            \"⪡\": \"&LessLess;\",\n            \"⩽\": \"&les;\",\n            \"≲\": \"&lsim;\",\n            \"𝔏\": \"&Lfr;\",\n            \"⋘\": \"&Ll;\",\n            \"⇚\": \"&lAarr;\",\n            \"Ŀ\": \"&Lmidot;\",\n            \"⟵\": \"&xlarr;\",\n            \"⟷\": \"&xharr;\",\n            \"⟶\": \"&xrarr;\",\n            \"𝕃\": \"&Lopf;\",\n            \"↙\": \"&swarrow;\",\n            \"↘\": \"&searrow;\",\n            \"↰\": \"&lsh;\",\n            \"Ł\": \"&Lstrok;\",\n            \"≪\": \"&ll;\",\n            \"⤅\": \"&Map;\",\n            \"М\": \"&Mcy;\",\n            \" \": \"&MediumSpace;\",\n            \"ℳ\": \"&phmmat;\",\n            \"𝔐\": \"&Mfr;\",\n            \"∓\": \"&mp;\",\n            \"𝕄\": \"&Mopf;\",\n            \"Μ\": \"&Mu;\",\n            \"Њ\": \"&NJcy;\",\n            \"Ń\": \"&Nacute;\",\n            \"Ň\": \"&Ncaron;\",\n            \"Ņ\": \"&Ncedil;\",\n            \"Н\": \"&Ncy;\",\n            \"​\": \"&ZeroWidthSpace;\",\n            \"\\n\": \"&NewLine;\",\n            \"𝔑\": \"&Nfr;\",\n            \"⁠\": \"&NoBreak;\",\n            \" \": \"&nbsp;\",\n            \"ℕ\": \"&naturals;\",\n            \"⫬\": \"&Not;\",\n            \"≢\": \"&nequiv;\",\n            \"≭\": \"&NotCupCap;\",\n            \"∦\": \"&nspar;\",\n            \"∉\": \"&notinva;\",\n            \"≠\": \"&ne;\",\n            \"≂̸\": \"&nesim;\",\n            \"∄\": \"&nexists;\",\n            \"≯\": \"&ngtr;\",\n            \"≱\": \"&ngeq;\",\n            \"≧̸\": \"&ngeqq;\",\n            \"≫̸\": \"&nGtv;\",\n            \"≹\": \"&ntgl;\",\n            \"⩾̸\": \"&nges;\",\n            \"≵\": \"&ngsim;\",\n            \"≎̸\": \"&nbump;\",\n            \"≏̸\": \"&nbumpe;\",\n            \"⋪\": \"&ntriangleleft;\",\n            \"⧏̸\": \"&NotLeftTriangleBar;\",\n            \"⋬\": \"&ntrianglelefteq;\",\n            \"≮\": \"&nlt;\",\n            \"≰\": \"&nleq;\",\n            \"≸\": \"&ntlg;\",\n            \"≪̸\": \"&nLtv;\",\n            \"⩽̸\": \"&nles;\",\n            \"≴\": \"&nlsim;\",\n            \"⪢̸\": \"&NotNestedGreaterGreater;\",\n            \"⪡̸\": \"&NotNestedLessLess;\",\n            \"⊀\": \"&nprec;\",\n            \"⪯̸\": \"&npreceq;\",\n            \"⋠\": \"&nprcue;\",\n            \"∌\": \"&notniva;\",\n            \"⋫\": \"&ntriangleright;\",\n            \"⧐̸\": \"&NotRightTriangleBar;\",\n            \"⋭\": \"&ntrianglerighteq;\",\n            \"⊏̸\": \"&NotSquareSubset;\",\n            \"⋢\": \"&nsqsube;\",\n            \"⊐̸\": \"&NotSquareSuperset;\",\n            \"⋣\": \"&nsqsupe;\",\n            \"⊂⃒\": \"&vnsub;\",\n            \"⊈\": \"&nsubseteq;\",\n            \"⊁\": \"&nsucc;\",\n            \"⪰̸\": \"&nsucceq;\",\n            \"⋡\": \"&nsccue;\",\n            \"≿̸\": \"&NotSucceedsTilde;\",\n            \"⊃⃒\": \"&vnsup;\",\n            \"⊉\": \"&nsupseteq;\",\n            \"≁\": \"&nsim;\",\n            \"≄\": \"&nsimeq;\",\n            \"≇\": \"&ncong;\",\n            \"≉\": \"&napprox;\",\n            \"∤\": \"&nsmid;\",\n            \"𝒩\": \"&Nscr;\",\n            \"Ñ\": \"&Ntilde;\",\n            \"Ν\": \"&Nu;\",\n            \"Œ\": \"&OElig;\",\n            \"Ó\": \"&Oacute;\",\n            \"Ô\": \"&Ocirc;\",\n            \"О\": \"&Ocy;\",\n            \"Ő\": \"&Odblac;\",\n            \"𝔒\": \"&Ofr;\",\n            \"Ò\": \"&Ograve;\",\n            \"Ō\": \"&Omacr;\",\n            \"Ω\": \"&ohm;\",\n            \"Ο\": \"&Omicron;\",\n            \"𝕆\": \"&Oopf;\",\n            \"“\": \"&ldquo;\",\n            \"‘\": \"&lsquo;\",\n            \"⩔\": \"&Or;\",\n            \"𝒪\": \"&Oscr;\",\n            \"Ø\": \"&Oslash;\",\n            \"Õ\": \"&Otilde;\",\n            \"⨷\": \"&Otimes;\",\n            \"Ö\": \"&Ouml;\",\n            \"‾\": \"&oline;\",\n            \"⏞\": \"&OverBrace;\",\n            \"⎴\": \"&tbrk;\",\n            \"⏜\": \"&OverParenthesis;\",\n            \"∂\": \"&part;\",\n            \"П\": \"&Pcy;\",\n            \"𝔓\": \"&Pfr;\",\n            \"Φ\": \"&Phi;\",\n            \"Π\": \"&Pi;\",\n            \"±\": \"&pm;\",\n            \"ℙ\": \"&primes;\",\n            \"⪻\": \"&Pr;\",\n            \"≺\": \"&prec;\",\n            \"⪯\": \"&preceq;\",\n            \"≼\": \"&preccurlyeq;\",\n            \"≾\": \"&prsim;\",\n            \"″\": \"&Prime;\",\n            \"∏\": \"&prod;\",\n            \"∝\": \"&vprop;\",\n            \"𝒫\": \"&Pscr;\",\n            \"Ψ\": \"&Psi;\",\n            \"\\\"\": \"&quot;\",\n            \"𝔔\": \"&Qfr;\",\n            \"ℚ\": \"&rationals;\",\n            \"𝒬\": \"&Qscr;\",\n            \"⤐\": \"&drbkarow;\",\n            \"®\": \"&reg;\",\n            \"Ŕ\": \"&Racute;\",\n            \"⟫\": \"&Rang;\",\n            \"↠\": \"&twoheadrightarrow;\",\n            \"⤖\": \"&Rarrtl;\",\n            \"Ř\": \"&Rcaron;\",\n            \"Ŗ\": \"&Rcedil;\",\n            \"Р\": \"&Rcy;\",\n            \"ℜ\": \"&realpart;\",\n            \"∋\": \"&niv;\",\n            \"⇋\": \"&lrhar;\",\n            \"⥯\": \"&duhar;\",\n            \"Ρ\": \"&Rho;\",\n            \"⟩\": \"&rangle;\",\n            \"→\": \"&srarr;\",\n            \"⇥\": \"&rarrb;\",\n            \"⇄\": \"&rlarr;\",\n            \"⌉\": \"&rceil;\",\n            \"⟧\": \"&robrk;\",\n            \"⥝\": \"&RightDownTeeVector;\",\n            \"⇂\": \"&downharpoonright;\",\n            \"⥕\": \"&RightDownVectorBar;\",\n            \"⌋\": \"&rfloor;\",\n            \"⊢\": \"&vdash;\",\n            \"↦\": \"&mapsto;\",\n            \"⥛\": \"&RightTeeVector;\",\n            \"⊳\": \"&vrtri;\",\n            \"⧐\": \"&RightTriangleBar;\",\n            \"⊵\": \"&trianglerighteq;\",\n            \"⥏\": \"&RightUpDownVector;\",\n            \"⥜\": \"&RightUpTeeVector;\",\n            \"↾\": \"&upharpoonright;\",\n            \"⥔\": \"&RightUpVectorBar;\",\n            \"⇀\": \"&rightharpoonup;\",\n            \"⥓\": \"&RightVectorBar;\",\n            \"ℝ\": \"&reals;\",\n            \"⥰\": \"&RoundImplies;\",\n            \"⇛\": \"&rAarr;\",\n            \"ℛ\": \"&realine;\",\n            \"↱\": \"&rsh;\",\n            \"⧴\": \"&RuleDelayed;\",\n            \"Щ\": \"&SHCHcy;\",\n            \"Ш\": \"&SHcy;\",\n            \"Ь\": \"&SOFTcy;\",\n            \"Ś\": \"&Sacute;\",\n            \"⪼\": \"&Sc;\",\n            \"Š\": \"&Scaron;\",\n            \"Ş\": \"&Scedil;\",\n            \"Ŝ\": \"&Scirc;\",\n            \"С\": \"&Scy;\",\n            \"𝔖\": \"&Sfr;\",\n            \"↑\": \"&uparrow;\",\n            \"Σ\": \"&Sigma;\",\n            \"∘\": \"&compfn;\",\n            \"𝕊\": \"&Sopf;\",\n            \"√\": \"&radic;\",\n            \"□\": \"&square;\",\n            \"⊓\": \"&sqcap;\",\n            \"⊏\": \"&sqsubset;\",\n            \"⊑\": \"&sqsubseteq;\",\n            \"⊐\": \"&sqsupset;\",\n            \"⊒\": \"&sqsupseteq;\",\n            \"⊔\": \"&sqcup;\",\n            \"𝒮\": \"&Sscr;\",\n            \"⋆\": \"&sstarf;\",\n            \"⋐\": \"&Subset;\",\n            \"⊆\": \"&subseteq;\",\n            \"≻\": \"&succ;\",\n            \"⪰\": \"&succeq;\",\n            \"≽\": \"&succcurlyeq;\",\n            \"≿\": \"&succsim;\",\n            \"∑\": \"&sum;\",\n            \"⋑\": \"&Supset;\",\n            \"⊃\": \"&supset;\",\n            \"⊇\": \"&supseteq;\",\n            \"Þ\": \"&THORN;\",\n            \"™\": \"&trade;\",\n            \"Ћ\": \"&TSHcy;\",\n            \"Ц\": \"&TScy;\",\n            \"\\t\": \"&Tab;\",\n            \"Τ\": \"&Tau;\",\n            \"Ť\": \"&Tcaron;\",\n            \"Ţ\": \"&Tcedil;\",\n            \"Т\": \"&Tcy;\",\n            \"𝔗\": \"&Tfr;\",\n            \"∴\": \"&therefore;\",\n            \"Θ\": \"&Theta;\",\n            \"  \": \"&ThickSpace;\",\n            \" \": \"&thinsp;\",\n            \"∼\": \"&thksim;\",\n            \"≃\": \"&simeq;\",\n            \"≅\": \"&cong;\",\n            \"≈\": \"&thkap;\",\n            \"𝕋\": \"&Topf;\",\n            \"⃛\": \"&tdot;\",\n            \"𝒯\": \"&Tscr;\",\n            \"Ŧ\": \"&Tstrok;\",\n            \"Ú\": \"&Uacute;\",\n            \"↟\": \"&Uarr;\",\n            \"⥉\": \"&Uarrocir;\",\n            \"Ў\": \"&Ubrcy;\",\n            \"Ŭ\": \"&Ubreve;\",\n            \"Û\": \"&Ucirc;\",\n            \"У\": \"&Ucy;\",\n            \"Ű\": \"&Udblac;\",\n            \"𝔘\": \"&Ufr;\",\n            \"Ù\": \"&Ugrave;\",\n            \"Ū\": \"&Umacr;\",\n            \"_\": \"&lowbar;\",\n            \"⏟\": \"&UnderBrace;\",\n            \"⎵\": \"&bbrk;\",\n            \"⏝\": \"&UnderParenthesis;\",\n            \"⋃\": \"&xcup;\",\n            \"⊎\": \"&uplus;\",\n            \"Ų\": \"&Uogon;\",\n            \"𝕌\": \"&Uopf;\",\n            \"⤒\": \"&UpArrowBar;\",\n            \"⇅\": \"&udarr;\",\n            \"↕\": \"&varr;\",\n            \"⥮\": \"&udhar;\",\n            \"⊥\": \"&perp;\",\n            \"↥\": \"&mapstoup;\",\n            \"↖\": \"&nwarrow;\",\n            \"↗\": \"&nearrow;\",\n            \"ϒ\": \"&upsih;\",\n            \"Υ\": \"&Upsilon;\",\n            \"Ů\": \"&Uring;\",\n            \"𝒰\": \"&Uscr;\",\n            \"Ũ\": \"&Utilde;\",\n            \"Ü\": \"&Uuml;\",\n            \"⊫\": \"&VDash;\",\n            \"⫫\": \"&Vbar;\",\n            \"В\": \"&Vcy;\",\n            \"⊩\": \"&Vdash;\",\n            \"⫦\": \"&Vdashl;\",\n            \"⋁\": \"&xvee;\",\n            \"‖\": \"&Vert;\",\n            \"∣\": \"&smid;\",\n            \"|\": \"&vert;\",\n            \"❘\": \"&VerticalSeparator;\",\n            \"≀\": \"&wreath;\",\n            \" \": \"&hairsp;\",\n            \"𝔙\": \"&Vfr;\",\n            \"𝕍\": \"&Vopf;\",\n            \"𝒱\": \"&Vscr;\",\n            \"⊪\": \"&Vvdash;\",\n            \"Ŵ\": \"&Wcirc;\",\n            \"⋀\": \"&xwedge;\",\n            \"𝔚\": \"&Wfr;\",\n            \"𝕎\": \"&Wopf;\",\n            \"𝒲\": \"&Wscr;\",\n            \"𝔛\": \"&Xfr;\",\n            \"Ξ\": \"&Xi;\",\n            \"𝕏\": \"&Xopf;\",\n            \"𝒳\": \"&Xscr;\",\n            \"Я\": \"&YAcy;\",\n            \"Ї\": \"&YIcy;\",\n            \"Ю\": \"&YUcy;\",\n            \"Ý\": \"&Yacute;\",\n            \"Ŷ\": \"&Ycirc;\",\n            \"Ы\": \"&Ycy;\",\n            \"𝔜\": \"&Yfr;\",\n            \"𝕐\": \"&Yopf;\",\n            \"𝒴\": \"&Yscr;\",\n            \"Ÿ\": \"&Yuml;\",\n            \"Ж\": \"&ZHcy;\",\n            \"Ź\": \"&Zacute;\",\n            \"Ž\": \"&Zcaron;\",\n            \"З\": \"&Zcy;\",\n            \"Ż\": \"&Zdot;\",\n            \"Ζ\": \"&Zeta;\",\n            \"ℨ\": \"&zeetrf;\",\n            \"ℤ\": \"&integers;\",\n            \"𝒵\": \"&Zscr;\",\n            \"á\": \"&aacute;\",\n            \"ă\": \"&abreve;\",\n            \"∾\": \"&mstpos;\",\n            \"∾̳\": \"&acE;\",\n            \"∿\": \"&acd;\",\n            \"â\": \"&acirc;\",\n            \"а\": \"&acy;\",\n            \"æ\": \"&aelig;\",\n            \"𝔞\": \"&afr;\",\n            \"à\": \"&agrave;\",\n            \"ℵ\": \"&aleph;\",\n            \"α\": \"&alpha;\",\n            \"ā\": \"&amacr;\",\n            \"⨿\": \"&amalg;\",\n            \"∧\": \"&wedge;\",\n            \"⩕\": \"&andand;\",\n            \"⩜\": \"&andd;\",\n            \"⩘\": \"&andslope;\",\n            \"⩚\": \"&andv;\",\n            \"∠\": \"&angle;\",\n            \"⦤\": \"&ange;\",\n            \"∡\": \"&measuredangle;\",\n            \"⦨\": \"&angmsdaa;\",\n            \"⦩\": \"&angmsdab;\",\n            \"⦪\": \"&angmsdac;\",\n            \"⦫\": \"&angmsdad;\",\n            \"⦬\": \"&angmsdae;\",\n            \"⦭\": \"&angmsdaf;\",\n            \"⦮\": \"&angmsdag;\",\n            \"⦯\": \"&angmsdah;\",\n            \"∟\": \"&angrt;\",\n            \"⊾\": \"&angrtvb;\",\n            \"⦝\": \"&angrtvbd;\",\n            \"∢\": \"&angsph;\",\n            \"⍼\": \"&angzarr;\",\n            \"ą\": \"&aogon;\",\n            \"𝕒\": \"&aopf;\",\n            \"⩰\": \"&apE;\",\n            \"⩯\": \"&apacir;\",\n            \"≊\": \"&approxeq;\",\n            \"≋\": \"&apid;\",\n            \"'\": \"&apos;\",\n            \"å\": \"&aring;\",\n            \"𝒶\": \"&ascr;\",\n            \"*\": \"&midast;\",\n            \"ã\": \"&atilde;\",\n            \"ä\": \"&auml;\",\n            \"⨑\": \"&awint;\",\n            \"⫭\": \"&bNot;\",\n            \"≌\": \"&bcong;\",\n            \"϶\": \"&bepsi;\",\n            \"‵\": \"&bprime;\",\n            \"∽\": \"&bsim;\",\n            \"⋍\": \"&bsime;\",\n            \"⊽\": \"&barvee;\",\n            \"⌅\": \"&barwedge;\",\n            \"⎶\": \"&bbrktbrk;\",\n            \"б\": \"&bcy;\",\n            \"„\": \"&ldquor;\",\n            \"⦰\": \"&bemptyv;\",\n            \"β\": \"&beta;\",\n            \"ℶ\": \"&beth;\",\n            \"≬\": \"&twixt;\",\n            \"𝔟\": \"&bfr;\",\n            \"◯\": \"&xcirc;\",\n            \"⨀\": \"&xodot;\",\n            \"⨁\": \"&xoplus;\",\n            \"⨂\": \"&xotime;\",\n            \"⨆\": \"&xsqcup;\",\n            \"★\": \"&starf;\",\n            \"▽\": \"&xdtri;\",\n            \"△\": \"&xutri;\",\n            \"⨄\": \"&xuplus;\",\n            \"⤍\": \"&rbarr;\",\n            \"⧫\": \"&lozf;\",\n            \"▴\": \"&utrif;\",\n            \"▾\": \"&dtrif;\",\n            \"◂\": \"&ltrif;\",\n            \"▸\": \"&rtrif;\",\n            \"␣\": \"&blank;\",\n            \"▒\": \"&blk12;\",\n            \"░\": \"&blk14;\",\n            \"▓\": \"&blk34;\",\n            \"█\": \"&block;\",\n            \"=⃥\": \"&bne;\",\n            \"≡⃥\": \"&bnequiv;\",\n            \"⌐\": \"&bnot;\",\n            \"𝕓\": \"&bopf;\",\n            \"⋈\": \"&bowtie;\",\n            \"╗\": \"&boxDL;\",\n            \"╔\": \"&boxDR;\",\n            \"╖\": \"&boxDl;\",\n            \"╓\": \"&boxDr;\",\n            \"═\": \"&boxH;\",\n            \"╦\": \"&boxHD;\",\n            \"╩\": \"&boxHU;\",\n            \"╤\": \"&boxHd;\",\n            \"╧\": \"&boxHu;\",\n            \"╝\": \"&boxUL;\",\n            \"╚\": \"&boxUR;\",\n            \"╜\": \"&boxUl;\",\n            \"╙\": \"&boxUr;\",\n            \"║\": \"&boxV;\",\n            \"╬\": \"&boxVH;\",\n            \"╣\": \"&boxVL;\",\n            \"╠\": \"&boxVR;\",\n            \"╫\": \"&boxVh;\",\n            \"╢\": \"&boxVl;\",\n            \"╟\": \"&boxVr;\",\n            \"⧉\": \"&boxbox;\",\n            \"╕\": \"&boxdL;\",\n            \"╒\": \"&boxdR;\",\n            \"┐\": \"&boxdl;\",\n            \"┌\": \"&boxdr;\",\n            \"╥\": \"&boxhD;\",\n            \"╨\": \"&boxhU;\",\n            \"┬\": \"&boxhd;\",\n            \"┴\": \"&boxhu;\",\n            \"⊟\": \"&minusb;\",\n            \"⊞\": \"&plusb;\",\n            \"⊠\": \"&timesb;\",\n            \"╛\": \"&boxuL;\",\n            \"╘\": \"&boxuR;\",\n            \"┘\": \"&boxul;\",\n            \"└\": \"&boxur;\",\n            \"│\": \"&boxv;\",\n            \"╪\": \"&boxvH;\",\n            \"╡\": \"&boxvL;\",\n            \"╞\": \"&boxvR;\",\n            \"┼\": \"&boxvh;\",\n            \"┤\": \"&boxvl;\",\n            \"├\": \"&boxvr;\",\n            \"¦\": \"&brvbar;\",\n            \"𝒷\": \"&bscr;\",\n            \"⁏\": \"&bsemi;\",\n            \"\\\\\": \"&bsol;\",\n            \"⧅\": \"&bsolb;\",\n            \"⟈\": \"&bsolhsub;\",\n            \"•\": \"&bullet;\",\n            \"⪮\": \"&bumpE;\",\n            \"ć\": \"&cacute;\",\n            \"∩\": \"&cap;\",\n            \"⩄\": \"&capand;\",\n            \"⩉\": \"&capbrcup;\",\n            \"⩋\": \"&capcap;\",\n            \"⩇\": \"&capcup;\",\n            \"⩀\": \"&capdot;\",\n            \"∩︀\": \"&caps;\",\n            \"⁁\": \"&caret;\",\n            \"⩍\": \"&ccaps;\",\n            \"č\": \"&ccaron;\",\n            \"ç\": \"&ccedil;\",\n            \"ĉ\": \"&ccirc;\",\n            \"⩌\": \"&ccups;\",\n            \"⩐\": \"&ccupssm;\",\n            \"ċ\": \"&cdot;\",\n            \"⦲\": \"&cemptyv;\",\n            \"¢\": \"&cent;\",\n            \"𝔠\": \"&cfr;\",\n            \"ч\": \"&chcy;\",\n            \"✓\": \"&checkmark;\",\n            \"χ\": \"&chi;\",\n            \"○\": \"&cir;\",\n            \"⧃\": \"&cirE;\",\n            \"ˆ\": \"&circ;\",\n            \"≗\": \"&cire;\",\n            \"↺\": \"&olarr;\",\n            \"↻\": \"&orarr;\",\n            \"Ⓢ\": \"&oS;\",\n            \"⊛\": \"&oast;\",\n            \"⊚\": \"&ocir;\",\n            \"⊝\": \"&odash;\",\n            \"⨐\": \"&cirfnint;\",\n            \"⫯\": \"&cirmid;\",\n            \"⧂\": \"&cirscir;\",\n            \"♣\": \"&clubsuit;\",\n            \":\": \"&colon;\",\n            \",\": \"&comma;\",\n            \"@\": \"&commat;\",\n            \"∁\": \"&complement;\",\n            \"⩭\": \"&congdot;\",\n            \"𝕔\": \"&copf;\",\n            \"℗\": \"&copysr;\",\n            \"↵\": \"&crarr;\",\n            \"✗\": \"&cross;\",\n            \"𝒸\": \"&cscr;\",\n            \"⫏\": \"&csub;\",\n            \"⫑\": \"&csube;\",\n            \"⫐\": \"&csup;\",\n            \"⫒\": \"&csupe;\",\n            \"⋯\": \"&ctdot;\",\n            \"⤸\": \"&cudarrl;\",\n            \"⤵\": \"&cudarrr;\",\n            \"⋞\": \"&curlyeqprec;\",\n            \"⋟\": \"&curlyeqsucc;\",\n            \"↶\": \"&curvearrowleft;\",\n            \"⤽\": \"&cularrp;\",\n            \"∪\": \"&cup;\",\n            \"⩈\": \"&cupbrcap;\",\n            \"⩆\": \"&cupcap;\",\n            \"⩊\": \"&cupcup;\",\n            \"⊍\": \"&cupdot;\",\n            \"⩅\": \"&cupor;\",\n            \"∪︀\": \"&cups;\",\n            \"↷\": \"&curvearrowright;\",\n            \"⤼\": \"&curarrm;\",\n            \"⋎\": \"&cuvee;\",\n            \"⋏\": \"&cuwed;\",\n            \"¤\": \"&curren;\",\n            \"∱\": \"&cwint;\",\n            \"⌭\": \"&cylcty;\",\n            \"⥥\": \"&dHar;\",\n            \"†\": \"&dagger;\",\n            \"ℸ\": \"&daleth;\",\n            \"‐\": \"&hyphen;\",\n            \"⤏\": \"&rBarr;\",\n            \"ď\": \"&dcaron;\",\n            \"д\": \"&dcy;\",\n            \"⇊\": \"&downdownarrows;\",\n            \"⩷\": \"&eDDot;\",\n            \"°\": \"&deg;\",\n            \"δ\": \"&delta;\",\n            \"⦱\": \"&demptyv;\",\n            \"⥿\": \"&dfisht;\",\n            \"𝔡\": \"&dfr;\",\n            \"♦\": \"&diams;\",\n            \"ϝ\": \"&gammad;\",\n            \"⋲\": \"&disin;\",\n            \"÷\": \"&divide;\",\n            \"⋇\": \"&divonx;\",\n            \"ђ\": \"&djcy;\",\n            \"⌞\": \"&llcorner;\",\n            \"⌍\": \"&dlcrop;\",\n            \"$\": \"&dollar;\",\n            \"𝕕\": \"&dopf;\",\n            \"≑\": \"&eDot;\",\n            \"∸\": \"&minusd;\",\n            \"∔\": \"&plusdo;\",\n            \"⊡\": \"&sdotb;\",\n            \"⌟\": \"&lrcorner;\",\n            \"⌌\": \"&drcrop;\",\n            \"𝒹\": \"&dscr;\",\n            \"ѕ\": \"&dscy;\",\n            \"⧶\": \"&dsol;\",\n            \"đ\": \"&dstrok;\",\n            \"⋱\": \"&dtdot;\",\n            \"▿\": \"&triangledown;\",\n            \"⦦\": \"&dwangle;\",\n            \"џ\": \"&dzcy;\",\n            \"⟿\": \"&dzigrarr;\",\n            \"é\": \"&eacute;\",\n            \"⩮\": \"&easter;\",\n            \"ě\": \"&ecaron;\",\n            \"≖\": \"&eqcirc;\",\n            \"ê\": \"&ecirc;\",\n            \"≕\": \"&eqcolon;\",\n            \"э\": \"&ecy;\",\n            \"ė\": \"&edot;\",\n            \"≒\": \"&fallingdotseq;\",\n            \"𝔢\": \"&efr;\",\n            \"⪚\": \"&eg;\",\n            \"è\": \"&egrave;\",\n            \"⪖\": \"&eqslantgtr;\",\n            \"⪘\": \"&egsdot;\",\n            \"⪙\": \"&el;\",\n            \"⏧\": \"&elinters;\",\n            \"ℓ\": \"&ell;\",\n            \"⪕\": \"&eqslantless;\",\n            \"⪗\": \"&elsdot;\",\n            \"ē\": \"&emacr;\",\n            \"∅\": \"&varnothing;\",\n            \" \": \"&emsp13;\",\n            \" \": \"&emsp14;\",\n            \" \": \"&emsp;\",\n            \"ŋ\": \"&eng;\",\n            \" \": \"&ensp;\",\n            \"ę\": \"&eogon;\",\n            \"𝕖\": \"&eopf;\",\n            \"⋕\": \"&epar;\",\n            \"⧣\": \"&eparsl;\",\n            \"⩱\": \"&eplus;\",\n            \"ε\": \"&epsilon;\",\n            \"ϵ\": \"&varepsilon;\",\n            \"=\": \"&equals;\",\n            \"≟\": \"&questeq;\",\n            \"⩸\": \"&equivDD;\",\n            \"⧥\": \"&eqvparsl;\",\n            \"≓\": \"&risingdotseq;\",\n            \"⥱\": \"&erarr;\",\n            \"ℯ\": \"&escr;\",\n            \"η\": \"&eta;\",\n            \"ð\": \"&eth;\",\n            \"ë\": \"&euml;\",\n            \"€\": \"&euro;\",\n            \"!\": \"&excl;\",\n            \"ф\": \"&fcy;\",\n            \"♀\": \"&female;\",\n            \"ﬃ\": \"&ffilig;\",\n            \"ﬀ\": \"&fflig;\",\n            \"ﬄ\": \"&ffllig;\",\n            \"𝔣\": \"&ffr;\",\n            \"ﬁ\": \"&filig;\",\n            \"fj\": \"&fjlig;\",\n            \"♭\": \"&flat;\",\n            \"ﬂ\": \"&fllig;\",\n            \"▱\": \"&fltns;\",\n            \"ƒ\": \"&fnof;\",\n            \"𝕗\": \"&fopf;\",\n            \"⋔\": \"&pitchfork;\",\n            \"⫙\": \"&forkv;\",\n            \"⨍\": \"&fpartint;\",\n            \"½\": \"&half;\",\n            \"⅓\": \"&frac13;\",\n            \"¼\": \"&frac14;\",\n            \"⅕\": \"&frac15;\",\n            \"⅙\": \"&frac16;\",\n            \"⅛\": \"&frac18;\",\n            \"⅔\": \"&frac23;\",\n            \"⅖\": \"&frac25;\",\n            \"¾\": \"&frac34;\",\n            \"⅗\": \"&frac35;\",\n            \"⅜\": \"&frac38;\",\n            \"⅘\": \"&frac45;\",\n            \"⅚\": \"&frac56;\",\n            \"⅝\": \"&frac58;\",\n            \"⅞\": \"&frac78;\",\n            \"⁄\": \"&frasl;\",\n            \"⌢\": \"&sfrown;\",\n            \"𝒻\": \"&fscr;\",\n            \"⪌\": \"&gtreqqless;\",\n            \"ǵ\": \"&gacute;\",\n            \"γ\": \"&gamma;\",\n            \"⪆\": \"&gtrapprox;\",\n            \"ğ\": \"&gbreve;\",\n            \"ĝ\": \"&gcirc;\",\n            \"г\": \"&gcy;\",\n            \"ġ\": \"&gdot;\",\n            \"⪩\": \"&gescc;\",\n            \"⪀\": \"&gesdot;\",\n            \"⪂\": \"&gesdoto;\",\n            \"⪄\": \"&gesdotol;\",\n            \"⋛︀\": \"&gesl;\",\n            \"⪔\": \"&gesles;\",\n            \"𝔤\": \"&gfr;\",\n            \"ℷ\": \"&gimel;\",\n            \"ѓ\": \"&gjcy;\",\n            \"⪒\": \"&glE;\",\n            \"⪥\": \"&gla;\",\n            \"⪤\": \"&glj;\",\n            \"≩\": \"&gneqq;\",\n            \"⪊\": \"&gnapprox;\",\n            \"⪈\": \"&gneq;\",\n            \"⋧\": \"&gnsim;\",\n            \"𝕘\": \"&gopf;\",\n            \"ℊ\": \"&gscr;\",\n            \"⪎\": \"&gsime;\",\n            \"⪐\": \"&gsiml;\",\n            \"⪧\": \"&gtcc;\",\n            \"⩺\": \"&gtcir;\",\n            \"⋗\": \"&gtrdot;\",\n            \"⦕\": \"&gtlPar;\",\n            \"⩼\": \"&gtquest;\",\n            \"⥸\": \"&gtrarr;\",\n            \"≩︀\": \"&gvnE;\",\n            \"ъ\": \"&hardcy;\",\n            \"⥈\": \"&harrcir;\",\n            \"↭\": \"&leftrightsquigarrow;\",\n            \"ℏ\": \"&plankv;\",\n            \"ĥ\": \"&hcirc;\",\n            \"♥\": \"&heartsuit;\",\n            \"…\": \"&mldr;\",\n            \"⊹\": \"&hercon;\",\n            \"𝔥\": \"&hfr;\",\n            \"⤥\": \"&searhk;\",\n            \"⤦\": \"&swarhk;\",\n            \"⇿\": \"&hoarr;\",\n            \"∻\": \"&homtht;\",\n            \"↩\": \"&larrhk;\",\n            \"↪\": \"&rarrhk;\",\n            \"𝕙\": \"&hopf;\",\n            \"―\": \"&horbar;\",\n            \"𝒽\": \"&hscr;\",\n            \"ħ\": \"&hstrok;\",\n            \"⁃\": \"&hybull;\",\n            \"í\": \"&iacute;\",\n            \"î\": \"&icirc;\",\n            \"и\": \"&icy;\",\n            \"е\": \"&iecy;\",\n            \"¡\": \"&iexcl;\",\n            \"𝔦\": \"&ifr;\",\n            \"ì\": \"&igrave;\",\n            \"⨌\": \"&qint;\",\n            \"∭\": \"&tint;\",\n            \"⧜\": \"&iinfin;\",\n            \"℩\": \"&iiota;\",\n            \"ĳ\": \"&ijlig;\",\n            \"ī\": \"&imacr;\",\n            \"ı\": \"&inodot;\",\n            \"⊷\": \"&imof;\",\n            \"Ƶ\": \"&imped;\",\n            \"℅\": \"&incare;\",\n            \"∞\": \"&infin;\",\n            \"⧝\": \"&infintie;\",\n            \"⊺\": \"&intercal;\",\n            \"⨗\": \"&intlarhk;\",\n            \"⨼\": \"&iprod;\",\n            \"ё\": \"&iocy;\",\n            \"į\": \"&iogon;\",\n            \"𝕚\": \"&iopf;\",\n            \"ι\": \"&iota;\",\n            \"¿\": \"&iquest;\",\n            \"𝒾\": \"&iscr;\",\n            \"⋹\": \"&isinE;\",\n            \"⋵\": \"&isindot;\",\n            \"⋴\": \"&isins;\",\n            \"⋳\": \"&isinsv;\",\n            \"ĩ\": \"&itilde;\",\n            \"і\": \"&iukcy;\",\n            \"ï\": \"&iuml;\",\n            \"ĵ\": \"&jcirc;\",\n            \"й\": \"&jcy;\",\n            \"𝔧\": \"&jfr;\",\n            \"ȷ\": \"&jmath;\",\n            \"𝕛\": \"&jopf;\",\n            \"𝒿\": \"&jscr;\",\n            \"ј\": \"&jsercy;\",\n            \"є\": \"&jukcy;\",\n            \"κ\": \"&kappa;\",\n            \"ϰ\": \"&varkappa;\",\n            \"ķ\": \"&kcedil;\",\n            \"к\": \"&kcy;\",\n            \"𝔨\": \"&kfr;\",\n            \"ĸ\": \"&kgreen;\",\n            \"х\": \"&khcy;\",\n            \"ќ\": \"&kjcy;\",\n            \"𝕜\": \"&kopf;\",\n            \"𝓀\": \"&kscr;\",\n            \"⤛\": \"&lAtail;\",\n            \"⤎\": \"&lBarr;\",\n            \"⪋\": \"&lesseqqgtr;\",\n            \"⥢\": \"&lHar;\",\n            \"ĺ\": \"&lacute;\",\n            \"⦴\": \"&laemptyv;\",\n            \"λ\": \"&lambda;\",\n            \"⦑\": \"&langd;\",\n            \"⪅\": \"&lessapprox;\",\n            \"«\": \"&laquo;\",\n            \"⤟\": \"&larrbfs;\",\n            \"⤝\": \"&larrfs;\",\n            \"↫\": \"&looparrowleft;\",\n            \"⤹\": \"&larrpl;\",\n            \"⥳\": \"&larrsim;\",\n            \"↢\": \"&leftarrowtail;\",\n            \"⪫\": \"&lat;\",\n            \"⤙\": \"&latail;\",\n            \"⪭\": \"&late;\",\n            \"⪭︀\": \"&lates;\",\n            \"⤌\": \"&lbarr;\",\n            \"❲\": \"&lbbrk;\",\n            \"{\": \"&lcub;\",\n            \"[\": \"&lsqb;\",\n            \"⦋\": \"&lbrke;\",\n            \"⦏\": \"&lbrksld;\",\n            \"⦍\": \"&lbrkslu;\",\n            \"ľ\": \"&lcaron;\",\n            \"ļ\": \"&lcedil;\",\n            \"л\": \"&lcy;\",\n            \"⤶\": \"&ldca;\",\n            \"⥧\": \"&ldrdhar;\",\n            \"⥋\": \"&ldrushar;\",\n            \"↲\": \"&ldsh;\",\n            \"≤\": \"&leq;\",\n            \"⇇\": \"&llarr;\",\n            \"⋋\": \"&lthree;\",\n            \"⪨\": \"&lescc;\",\n            \"⩿\": \"&lesdot;\",\n            \"⪁\": \"&lesdoto;\",\n            \"⪃\": \"&lesdotor;\",\n            \"⋚︀\": \"&lesg;\",\n            \"⪓\": \"&lesges;\",\n            \"⋖\": \"&ltdot;\",\n            \"⥼\": \"&lfisht;\",\n            \"𝔩\": \"&lfr;\",\n            \"⪑\": \"&lgE;\",\n            \"⥪\": \"&lharul;\",\n            \"▄\": \"&lhblk;\",\n            \"љ\": \"&ljcy;\",\n            \"⥫\": \"&llhard;\",\n            \"◺\": \"&lltri;\",\n            \"ŀ\": \"&lmidot;\",\n            \"⎰\": \"&lmoustache;\",\n            \"≨\": \"&lneqq;\",\n            \"⪉\": \"&lnapprox;\",\n            \"⪇\": \"&lneq;\",\n            \"⋦\": \"&lnsim;\",\n            \"⟬\": \"&loang;\",\n            \"⇽\": \"&loarr;\",\n            \"⟼\": \"&xmap;\",\n            \"↬\": \"&rarrlp;\",\n            \"⦅\": \"&lopar;\",\n            \"𝕝\": \"&lopf;\",\n            \"⨭\": \"&loplus;\",\n            \"⨴\": \"&lotimes;\",\n            \"∗\": \"&lowast;\",\n            \"◊\": \"&lozenge;\",\n            \"(\": \"&lpar;\",\n            \"⦓\": \"&lparlt;\",\n            \"⥭\": \"&lrhard;\",\n            \"‎\": \"&lrm;\",\n            \"⊿\": \"&lrtri;\",\n            \"‹\": \"&lsaquo;\",\n            \"𝓁\": \"&lscr;\",\n            \"⪍\": \"&lsime;\",\n            \"⪏\": \"&lsimg;\",\n            \"‚\": \"&sbquo;\",\n            \"ł\": \"&lstrok;\",\n            \"⪦\": \"&ltcc;\",\n            \"⩹\": \"&ltcir;\",\n            \"⋉\": \"&ltimes;\",\n            \"⥶\": \"&ltlarr;\",\n            \"⩻\": \"&ltquest;\",\n            \"⦖\": \"&ltrPar;\",\n            \"◃\": \"&triangleleft;\",\n            \"⥊\": \"&lurdshar;\",\n            \"⥦\": \"&luruhar;\",\n            \"≨︀\": \"&lvnE;\",\n            \"∺\": \"&mDDot;\",\n            \"¯\": \"&strns;\",\n            \"♂\": \"&male;\",\n            \"✠\": \"&maltese;\",\n            \"▮\": \"&marker;\",\n            \"⨩\": \"&mcomma;\",\n            \"м\": \"&mcy;\",\n            \"—\": \"&mdash;\",\n            \"𝔪\": \"&mfr;\",\n            \"℧\": \"&mho;\",\n            \"µ\": \"&micro;\",\n            \"⫰\": \"&midcir;\",\n            \"−\": \"&minus;\",\n            \"⨪\": \"&minusdu;\",\n            \"⫛\": \"&mlcp;\",\n            \"⊧\": \"&models;\",\n            \"𝕞\": \"&mopf;\",\n            \"𝓂\": \"&mscr;\",\n            \"μ\": \"&mu;\",\n            \"⊸\": \"&mumap;\",\n            \"⋙̸\": \"&nGg;\",\n            \"≫⃒\": \"&nGt;\",\n            \"⇍\": \"&nlArr;\",\n            \"⇎\": \"&nhArr;\",\n            \"⋘̸\": \"&nLl;\",\n            \"≪⃒\": \"&nLt;\",\n            \"⇏\": \"&nrArr;\",\n            \"⊯\": \"&nVDash;\",\n            \"⊮\": \"&nVdash;\",\n            \"ń\": \"&nacute;\",\n            \"∠⃒\": \"&nang;\",\n            \"⩰̸\": \"&napE;\",\n            \"≋̸\": \"&napid;\",\n            \"ŉ\": \"&napos;\",\n            \"♮\": \"&natural;\",\n            \"⩃\": \"&ncap;\",\n            \"ň\": \"&ncaron;\",\n            \"ņ\": \"&ncedil;\",\n            \"⩭̸\": \"&ncongdot;\",\n            \"⩂\": \"&ncup;\",\n            \"н\": \"&ncy;\",\n            \"–\": \"&ndash;\",\n            \"⇗\": \"&neArr;\",\n            \"⤤\": \"&nearhk;\",\n            \"≐̸\": \"&nedot;\",\n            \"⤨\": \"&toea;\",\n            \"𝔫\": \"&nfr;\",\n            \"↮\": \"&nleftrightarrow;\",\n            \"⫲\": \"&nhpar;\",\n            \"⋼\": \"&nis;\",\n            \"⋺\": \"&nisd;\",\n            \"њ\": \"&njcy;\",\n            \"≦̸\": \"&nleqq;\",\n            \"↚\": \"&nleftarrow;\",\n            \"‥\": \"&nldr;\",\n            \"𝕟\": \"&nopf;\",\n            \"¬\": \"&not;\",\n            \"⋹̸\": \"&notinE;\",\n            \"⋵̸\": \"&notindot;\",\n            \"⋷\": \"&notinvb;\",\n            \"⋶\": \"&notinvc;\",\n            \"⋾\": \"&notnivb;\",\n            \"⋽\": \"&notnivc;\",\n            \"⫽⃥\": \"&nparsl;\",\n            \"∂̸\": \"&npart;\",\n            \"⨔\": \"&npolint;\",\n            \"↛\": \"&nrightarrow;\",\n            \"⤳̸\": \"&nrarrc;\",\n            \"↝̸\": \"&nrarrw;\",\n            \"𝓃\": \"&nscr;\",\n            \"⊄\": \"&nsub;\",\n            \"⫅̸\": \"&nsubseteqq;\",\n            \"⊅\": \"&nsup;\",\n            \"⫆̸\": \"&nsupseteqq;\",\n            \"ñ\": \"&ntilde;\",\n            \"ν\": \"&nu;\",\n            \"#\": \"&num;\",\n            \"№\": \"&numero;\",\n            \" \": \"&numsp;\",\n            \"⊭\": \"&nvDash;\",\n            \"⤄\": \"&nvHarr;\",\n            \"≍⃒\": \"&nvap;\",\n            \"⊬\": \"&nvdash;\",\n            \"≥⃒\": \"&nvge;\",\n            \">⃒\": \"&nvgt;\",\n            \"⧞\": \"&nvinfin;\",\n            \"⤂\": \"&nvlArr;\",\n            \"≤⃒\": \"&nvle;\",\n            \"<⃒\": \"&nvlt;\",\n            \"⊴⃒\": \"&nvltrie;\",\n            \"⤃\": \"&nvrArr;\",\n            \"⊵⃒\": \"&nvrtrie;\",\n            \"∼⃒\": \"&nvsim;\",\n            \"⇖\": \"&nwArr;\",\n            \"⤣\": \"&nwarhk;\",\n            \"⤧\": \"&nwnear;\",\n            \"ó\": \"&oacute;\",\n            \"ô\": \"&ocirc;\",\n            \"о\": \"&ocy;\",\n            \"ő\": \"&odblac;\",\n            \"⨸\": \"&odiv;\",\n            \"⦼\": \"&odsold;\",\n            \"œ\": \"&oelig;\",\n            \"⦿\": \"&ofcir;\",\n            \"𝔬\": \"&ofr;\",\n            \"˛\": \"&ogon;\",\n            \"ò\": \"&ograve;\",\n            \"⧁\": \"&ogt;\",\n            \"⦵\": \"&ohbar;\",\n            \"⦾\": \"&olcir;\",\n            \"⦻\": \"&olcross;\",\n            \"⧀\": \"&olt;\",\n            \"ō\": \"&omacr;\",\n            \"ω\": \"&omega;\",\n            \"ο\": \"&omicron;\",\n            \"⦶\": \"&omid;\",\n            \"𝕠\": \"&oopf;\",\n            \"⦷\": \"&opar;\",\n            \"⦹\": \"&operp;\",\n            \"∨\": \"&vee;\",\n            \"⩝\": \"&ord;\",\n            \"ℴ\": \"&oscr;\",\n            \"ª\": \"&ordf;\",\n            \"º\": \"&ordm;\",\n            \"⊶\": \"&origof;\",\n            \"⩖\": \"&oror;\",\n            \"⩗\": \"&orslope;\",\n            \"⩛\": \"&orv;\",\n            \"ø\": \"&oslash;\",\n            \"⊘\": \"&osol;\",\n            \"õ\": \"&otilde;\",\n            \"⨶\": \"&otimesas;\",\n            \"ö\": \"&ouml;\",\n            \"⌽\": \"&ovbar;\",\n            \"¶\": \"&para;\",\n            \"⫳\": \"&parsim;\",\n            \"⫽\": \"&parsl;\",\n            \"п\": \"&pcy;\",\n            \"%\": \"&percnt;\",\n            \".\": \"&period;\",\n            \"‰\": \"&permil;\",\n            \"‱\": \"&pertenk;\",\n            \"𝔭\": \"&pfr;\",\n            \"φ\": \"&phi;\",\n            \"ϕ\": \"&varphi;\",\n            \"☎\": \"&phone;\",\n            \"π\": \"&pi;\",\n            \"ϖ\": \"&varpi;\",\n            \"ℎ\": \"&planckh;\",\n            \"+\": \"&plus;\",\n            \"⨣\": \"&plusacir;\",\n            \"⨢\": \"&pluscir;\",\n            \"⨥\": \"&plusdu;\",\n            \"⩲\": \"&pluse;\",\n            \"⨦\": \"&plussim;\",\n            \"⨧\": \"&plustwo;\",\n            \"⨕\": \"&pointint;\",\n            \"𝕡\": \"&popf;\",\n            \"£\": \"&pound;\",\n            \"⪳\": \"&prE;\",\n            \"⪷\": \"&precapprox;\",\n            \"⪹\": \"&prnap;\",\n            \"⪵\": \"&prnE;\",\n            \"⋨\": \"&prnsim;\",\n            \"′\": \"&prime;\",\n            \"⌮\": \"&profalar;\",\n            \"⌒\": \"&profline;\",\n            \"⌓\": \"&profsurf;\",\n            \"⊰\": \"&prurel;\",\n            \"𝓅\": \"&pscr;\",\n            \"ψ\": \"&psi;\",\n            \" \": \"&puncsp;\",\n            \"𝔮\": \"&qfr;\",\n            \"𝕢\": \"&qopf;\",\n            \"⁗\": \"&qprime;\",\n            \"𝓆\": \"&qscr;\",\n            \"⨖\": \"&quatint;\",\n            \"?\": \"&quest;\",\n            \"⤜\": \"&rAtail;\",\n            \"⥤\": \"&rHar;\",\n            \"∽̱\": \"&race;\",\n            \"ŕ\": \"&racute;\",\n            \"⦳\": \"&raemptyv;\",\n            \"⦒\": \"&rangd;\",\n            \"⦥\": \"&range;\",\n            \"»\": \"&raquo;\",\n            \"⥵\": \"&rarrap;\",\n            \"⤠\": \"&rarrbfs;\",\n            \"⤳\": \"&rarrc;\",\n            \"⤞\": \"&rarrfs;\",\n            \"⥅\": \"&rarrpl;\",\n            \"⥴\": \"&rarrsim;\",\n            \"↣\": \"&rightarrowtail;\",\n            \"↝\": \"&rightsquigarrow;\",\n            \"⤚\": \"&ratail;\",\n            \"∶\": \"&ratio;\",\n            \"❳\": \"&rbbrk;\",\n            \"}\": \"&rcub;\",\n            \"]\": \"&rsqb;\",\n            \"⦌\": \"&rbrke;\",\n            \"⦎\": \"&rbrksld;\",\n            \"⦐\": \"&rbrkslu;\",\n            \"ř\": \"&rcaron;\",\n            \"ŗ\": \"&rcedil;\",\n            \"р\": \"&rcy;\",\n            \"⤷\": \"&rdca;\",\n            \"⥩\": \"&rdldhar;\",\n            \"↳\": \"&rdsh;\",\n            \"▭\": \"&rect;\",\n            \"⥽\": \"&rfisht;\",\n            \"𝔯\": \"&rfr;\",\n            \"⥬\": \"&rharul;\",\n            \"ρ\": \"&rho;\",\n            \"ϱ\": \"&varrho;\",\n            \"⇉\": \"&rrarr;\",\n            \"⋌\": \"&rthree;\",\n            \"˚\": \"&ring;\",\n            \"‏\": \"&rlm;\",\n            \"⎱\": \"&rmoustache;\",\n            \"⫮\": \"&rnmid;\",\n            \"⟭\": \"&roang;\",\n            \"⇾\": \"&roarr;\",\n            \"⦆\": \"&ropar;\",\n            \"𝕣\": \"&ropf;\",\n            \"⨮\": \"&roplus;\",\n            \"⨵\": \"&rotimes;\",\n            \")\": \"&rpar;\",\n            \"⦔\": \"&rpargt;\",\n            \"⨒\": \"&rppolint;\",\n            \"›\": \"&rsaquo;\",\n            \"𝓇\": \"&rscr;\",\n            \"⋊\": \"&rtimes;\",\n            \"▹\": \"&triangleright;\",\n            \"⧎\": \"&rtriltri;\",\n            \"⥨\": \"&ruluhar;\",\n            \"℞\": \"&rx;\",\n            \"ś\": \"&sacute;\",\n            \"⪴\": \"&scE;\",\n            \"⪸\": \"&succapprox;\",\n            \"š\": \"&scaron;\",\n            \"ş\": \"&scedil;\",\n            \"ŝ\": \"&scirc;\",\n            \"⪶\": \"&succneqq;\",\n            \"⪺\": \"&succnapprox;\",\n            \"⋩\": \"&succnsim;\",\n            \"⨓\": \"&scpolint;\",\n            \"с\": \"&scy;\",\n            \"⋅\": \"&sdot;\",\n            \"⩦\": \"&sdote;\",\n            \"⇘\": \"&seArr;\",\n            \"§\": \"&sect;\",\n            \";\": \"&semi;\",\n            \"⤩\": \"&tosa;\",\n            \"✶\": \"&sext;\",\n            \"𝔰\": \"&sfr;\",\n            \"♯\": \"&sharp;\",\n            \"щ\": \"&shchcy;\",\n            \"ш\": \"&shcy;\",\n            \"­\": \"&shy;\",\n            \"σ\": \"&sigma;\",\n            \"ς\": \"&varsigma;\",\n            \"⩪\": \"&simdot;\",\n            \"⪞\": \"&simg;\",\n            \"⪠\": \"&simgE;\",\n            \"⪝\": \"&siml;\",\n            \"⪟\": \"&simlE;\",\n            \"≆\": \"&simne;\",\n            \"⨤\": \"&simplus;\",\n            \"⥲\": \"&simrarr;\",\n            \"⨳\": \"&smashp;\",\n            \"⧤\": \"&smeparsl;\",\n            \"⌣\": \"&ssmile;\",\n            \"⪪\": \"&smt;\",\n            \"⪬\": \"&smte;\",\n            \"⪬︀\": \"&smtes;\",\n            \"ь\": \"&softcy;\",\n            \"/\": \"&sol;\",\n            \"⧄\": \"&solb;\",\n            \"⌿\": \"&solbar;\",\n            \"𝕤\": \"&sopf;\",\n            \"♠\": \"&spadesuit;\",\n            \"⊓︀\": \"&sqcaps;\",\n            \"⊔︀\": \"&sqcups;\",\n            \"𝓈\": \"&sscr;\",\n            \"☆\": \"&star;\",\n            \"⊂\": \"&subset;\",\n            \"⫅\": \"&subseteqq;\",\n            \"⪽\": \"&subdot;\",\n            \"⫃\": \"&subedot;\",\n            \"⫁\": \"&submult;\",\n            \"⫋\": \"&subsetneqq;\",\n            \"⊊\": \"&subsetneq;\",\n            \"⪿\": \"&subplus;\",\n            \"⥹\": \"&subrarr;\",\n            \"⫇\": \"&subsim;\",\n            \"⫕\": \"&subsub;\",\n            \"⫓\": \"&subsup;\",\n            \"♪\": \"&sung;\",\n            \"¹\": \"&sup1;\",\n            \"²\": \"&sup2;\",\n            \"³\": \"&sup3;\",\n            \"⫆\": \"&supseteqq;\",\n            \"⪾\": \"&supdot;\",\n            \"⫘\": \"&supdsub;\",\n            \"⫄\": \"&supedot;\",\n            \"⟉\": \"&suphsol;\",\n            \"⫗\": \"&suphsub;\",\n            \"⥻\": \"&suplarr;\",\n            \"⫂\": \"&supmult;\",\n            \"⫌\": \"&supsetneqq;\",\n            \"⊋\": \"&supsetneq;\",\n            \"⫀\": \"&supplus;\",\n            \"⫈\": \"&supsim;\",\n            \"⫔\": \"&supsub;\",\n            \"⫖\": \"&supsup;\",\n            \"⇙\": \"&swArr;\",\n            \"⤪\": \"&swnwar;\",\n            \"ß\": \"&szlig;\",\n            \"⌖\": \"&target;\",\n            \"τ\": \"&tau;\",\n            \"ť\": \"&tcaron;\",\n            \"ţ\": \"&tcedil;\",\n            \"т\": \"&tcy;\",\n            \"⌕\": \"&telrec;\",\n            \"𝔱\": \"&tfr;\",\n            \"θ\": \"&theta;\",\n            \"ϑ\": \"&vartheta;\",\n            \"þ\": \"&thorn;\",\n            \"×\": \"&times;\",\n            \"⨱\": \"&timesbar;\",\n            \"⨰\": \"&timesd;\",\n            \"⌶\": \"&topbot;\",\n            \"⫱\": \"&topcir;\",\n            \"𝕥\": \"&topf;\",\n            \"⫚\": \"&topfork;\",\n            \"‴\": \"&tprime;\",\n            \"▵\": \"&utri;\",\n            \"≜\": \"&trie;\",\n            \"◬\": \"&tridot;\",\n            \"⨺\": \"&triminus;\",\n            \"⨹\": \"&triplus;\",\n            \"⧍\": \"&trisb;\",\n            \"⨻\": \"&tritime;\",\n            \"⏢\": \"&trpezium;\",\n            \"𝓉\": \"&tscr;\",\n            \"ц\": \"&tscy;\",\n            \"ћ\": \"&tshcy;\",\n            \"ŧ\": \"&tstrok;\",\n            \"⥣\": \"&uHar;\",\n            \"ú\": \"&uacute;\",\n            \"ў\": \"&ubrcy;\",\n            \"ŭ\": \"&ubreve;\",\n            \"û\": \"&ucirc;\",\n            \"у\": \"&ucy;\",\n            \"ű\": \"&udblac;\",\n            \"⥾\": \"&ufisht;\",\n            \"𝔲\": \"&ufr;\",\n            \"ù\": \"&ugrave;\",\n            \"▀\": \"&uhblk;\",\n            \"⌜\": \"&ulcorner;\",\n            \"⌏\": \"&ulcrop;\",\n            \"◸\": \"&ultri;\",\n            \"ū\": \"&umacr;\",\n            \"ų\": \"&uogon;\",\n            \"𝕦\": \"&uopf;\",\n            \"υ\": \"&upsilon;\",\n            \"⇈\": \"&uuarr;\",\n            \"⌝\": \"&urcorner;\",\n            \"⌎\": \"&urcrop;\",\n            \"ů\": \"&uring;\",\n            \"◹\": \"&urtri;\",\n            \"𝓊\": \"&uscr;\",\n            \"⋰\": \"&utdot;\",\n            \"ũ\": \"&utilde;\",\n            \"ü\": \"&uuml;\",\n            \"⦧\": \"&uwangle;\",\n            \"⫨\": \"&vBar;\",\n            \"⫩\": \"&vBarv;\",\n            \"⦜\": \"&vangrt;\",\n            \"⊊︀\": \"&vsubne;\",\n            \"⫋︀\": \"&vsubnE;\",\n            \"⊋︀\": \"&vsupne;\",\n            \"⫌︀\": \"&vsupnE;\",\n            \"в\": \"&vcy;\",\n            \"⊻\": \"&veebar;\",\n            \"≚\": \"&veeeq;\",\n            \"⋮\": \"&vellip;\",\n            \"𝔳\": \"&vfr;\",\n            \"𝕧\": \"&vopf;\",\n            \"𝓋\": \"&vscr;\",\n            \"⦚\": \"&vzigzag;\",\n            \"ŵ\": \"&wcirc;\",\n            \"⩟\": \"&wedbar;\",\n            \"≙\": \"&wedgeq;\",\n            \"℘\": \"&wp;\",\n            \"𝔴\": \"&wfr;\",\n            \"𝕨\": \"&wopf;\",\n            \"𝓌\": \"&wscr;\",\n            \"𝔵\": \"&xfr;\",\n            \"ξ\": \"&xi;\",\n            \"⋻\": \"&xnis;\",\n            \"𝕩\": \"&xopf;\",\n            \"𝓍\": \"&xscr;\",\n            \"ý\": \"&yacute;\",\n            \"я\": \"&yacy;\",\n            \"ŷ\": \"&ycirc;\",\n            \"ы\": \"&ycy;\",\n            \"¥\": \"&yen;\",\n            \"𝔶\": \"&yfr;\",\n            \"ї\": \"&yicy;\",\n            \"𝕪\": \"&yopf;\",\n            \"𝓎\": \"&yscr;\",\n            \"ю\": \"&yucy;\",\n            \"ÿ\": \"&yuml;\",\n            \"ź\": \"&zacute;\",\n            \"ž\": \"&zcaron;\",\n            \"з\": \"&zcy;\",\n            \"ż\": \"&zdot;\",\n            \"ζ\": \"&zeta;\",\n            \"𝔷\": \"&zfr;\",\n            \"ж\": \"&zhcy;\",\n            \"⇝\": \"&zigrarr;\",\n            \"𝕫\": \"&zopf;\",\n            \"𝓏\": \"&zscr;\",\n            \"‍\": \"&zwj;\",\n            \"‌\": \"&zwnj;\"\n        }\n    }\n};\n", "export const numericUnicodeMap: Record<number, number> = {\n    0: 65533,\n    128: 8364,\n    130: 8218,\n    131: 402,\n    132: 8222,\n    133: 8230,\n    134: 8224,\n    135: 8225,\n    136: 710,\n    137: 8240,\n    138: 352,\n    139: 8249,\n    140: 338,\n    142: 381,\n    145: 8216,\n    146: 8217,\n    147: 8220,\n    148: 8221,\n    149: 8226,\n    150: 8211,\n    151: 8212,\n    152: 732,\n    153: 8482,\n    154: 353,\n    155: 8250,\n    156: 339,\n    158: 382,\n    159: 376\n};\n", "export const fromCodePoint =\n    String.fromCodePoint ||\n    function (astralCodePoint: number) {\n        return String.fromCharCode(\n            Math.floor((astralCodePoint - 0x10000) / 0x400) + 0xd800,\n            ((astralCodePoint - 0x10000) % 0x400) + 0xdc00\n        );\n    };\n\nexport const getCodePoint = String.prototype.codePointAt\n    ? function (input: string, position: number) {\n          return input.codePointAt(position);\n      }\n    : function (input: string, position: number) {\n          return (input.charCodeAt(position) - 0xd800) * 0x400 + input.charCodeAt(position + 1) - 0xdc00 + 0x10000;\n      };\n\nexport const highSurrogateFrom = 0xd800;\nexport const highSurrogateTo = 0xdbff;\n", "import {bodyRegExps, namedReferences} from './named-references';\nimport {numericUnicodeMap} from './numeric-unicode-map';\nimport {fromCodePoint, getCodePoint} from './surrogate-pairs';\n\nconst allNamedReferences = {\n    ...namedReferences,\n    all: namedReferences.html5\n};\n\nfunction replaceUsingRegExp(macroText: string, macroRegExp: RegExp, macroReplacer: (input: string) => string): string {\n    macroRegExp.lastIndex = 0;\n    let replaceMatch = macroRegExp.exec(macroText);\n    let replaceResult;\n    if (replaceMatch) {\n        replaceResult = '';\n        let replaceLastIndex = 0;\n        do {\n            if (replaceLastIndex !== replaceMatch.index) {\n                replaceResult += macroText.substring(replaceLastIndex, replaceMatch.index);\n            }\n            const replaceInput = replaceMatch[0];\n            replaceResult += macroReplacer(replaceInput);\n            replaceLastIndex = replaceMatch.index + replaceInput.length;\n        } while ((replaceMatch = macroRegExp.exec(macroText)));\n\n        if (replaceLastIndex !== macroText.length) {\n            replaceResult += macroText.substring(replaceLastIndex);\n        }\n    } else {\n        replaceResult = macroText;\n    }\n    return replaceResult;\n}\n\nexport type Level = 'xml' | 'html4' | 'html5' | 'all';\n\ninterface CommonOptions {\n    level?: Level;\n}\n\nexport type EncodeMode = 'specialChars' | 'nonAscii' | 'nonAsciiPrintable' | 'nonAsciiPrintableOnly' | 'extensive';\n\nexport interface EncodeOptions extends CommonOptions {\n    mode?: EncodeMode;\n    numeric?: 'decimal' | 'hexadecimal';\n}\n\nexport type DecodeScope = 'strict' | 'body' | 'attribute';\n\nexport interface DecodeOptions extends CommonOptions {\n    scope?: DecodeScope;\n}\n\nconst encodeRegExps: Record<EncodeMode, RegExp> = {\n    specialChars: /[<>'\"&]/g,\n    nonAscii: /[<>'\"&\\u0080-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/g,\n    nonAsciiPrintable: /[<>'\"&\\x01-\\x08\\x11-\\x15\\x17-\\x1F\\x7f-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/g,\n    nonAsciiPrintableOnly: /[\\x01-\\x08\\x11-\\x15\\x17-\\x1F\\x7f-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/g,\n    extensive: /[\\x01-\\x0c\\x0e-\\x1f\\x21-\\x2c\\x2e-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\x7d\\x7f-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/g\n};\n\nconst defaultEncodeOptions: EncodeOptions = {\n    mode: 'specialChars',\n    level: 'all',\n    numeric: 'decimal'\n};\n\n/** Encodes all the necessary (specified by `level`) characters in the text */\nexport function encode(\n    text: string | undefined | null,\n    {mode = 'specialChars', numeric = 'decimal', level = 'all'}: EncodeOptions = defaultEncodeOptions\n) {\n    if (!text) {\n        return '';\n    }\n\n    const encodeRegExp = encodeRegExps[mode];\n    const references = allNamedReferences[level].characters;\n    const isHex = numeric === 'hexadecimal';\n\n    return replaceUsingRegExp(text, encodeRegExp, (input) => {\n        let result = references[input];\n        if (!result) {\n            const code = input.length > 1 ? getCodePoint(input, 0)! : input.charCodeAt(0);\n            result = (isHex ? '&#x' + code.toString(16) : '&#' + code) + ';';\n        }\n        return result;\n    });\n}\n\nconst defaultDecodeOptions: DecodeOptions = {\n    scope: 'body',\n    level: 'all'\n};\n\nconst strict = /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+);/g;\nconst attribute = /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+)[;=]?/g;\n\nconst baseDecodeRegExps: Record<Exclude<Level, 'all'>, Record<DecodeScope, RegExp>> = {\n    xml: {\n        strict,\n        attribute,\n        body: bodyRegExps.xml\n    },\n    html4: {\n        strict,\n        attribute,\n        body: bodyRegExps.html4\n    },\n    html5: {\n        strict,\n        attribute,\n        body: bodyRegExps.html5\n    }\n};\n\nconst decodeRegExps: Record<Level, Record<DecodeScope, RegExp>> = {\n    ...baseDecodeRegExps,\n    all: baseDecodeRegExps.html5\n};\n\nconst fromCharCode = String.fromCharCode;\nconst outOfBoundsChar = fromCharCode(65533);\n\nconst defaultDecodeEntityOptions: CommonOptions = {\n    level: 'all'\n};\n\nfunction getDecodedEntity(\n    entity: string,\n    references: Record<string, string>,\n    isAttribute: boolean,\n    isStrict: boolean\n): string {\n    let decodeResult = entity;\n    const decodeEntityLastChar = entity[entity.length - 1];\n    if (isAttribute && decodeEntityLastChar === '=') {\n        decodeResult = entity;\n    } else if (isStrict && decodeEntityLastChar !== ';') {\n        decodeResult = entity;\n    } else {\n        const decodeResultByReference = references[entity];\n        if (decodeResultByReference) {\n            decodeResult = decodeResultByReference;\n        } else if (entity[0] === '&' && entity[1] === '#') {\n            const decodeSecondChar = entity[2];\n            const decodeCode =\n                decodeSecondChar == 'x' || decodeSecondChar == 'X'\n                    ? parseInt(entity.substr(3), 16)\n                    : parseInt(entity.substr(2));\n\n            decodeResult =\n                decodeCode >= 0x10ffff\n                    ? outOfBoundsChar\n                    : decodeCode > 65535\n                    ? fromCodePoint(decodeCode)\n                    : fromCharCode(numericUnicodeMap[decodeCode] || decodeCode);\n        }\n    }\n    return decodeResult;\n}\n\n/** Decodes a single entity */\nexport function decodeEntity(\n    entity: string | undefined | null,\n    {level = 'all'}: CommonOptions = defaultDecodeEntityOptions\n): string {\n    if (!entity) {\n        return '';\n    }\n    return getDecodedEntity(entity, allNamedReferences[level].entities, false, false);\n}\n\n/** Decodes all entities in the text */\nexport function decode(\n    text: string | undefined | null,\n    {level = 'all', scope = level === 'xml' ? 'strict' : 'body'}: DecodeOptions = defaultDecodeOptions\n) {\n    if (!text) {\n        return '';\n    }\n\n    const decodeRegExp = decodeRegExps[level][scope];\n    const references = allNamedReferences[level].entities;\n    const isAttribute = scope === 'attribute';\n    const isStrict = scope === 'strict';\n\n    return replaceUsingRegExp(text, decodeRegExp, (entity) =>\n        getDecodedEntity(entity, references, isAttribute, isStrict)\n    );\n}\n"], "mappings": ";;;;;;;;;AASaA,YAAAC,cAAc,EACvBC,KAAK,8CACLC,OAAO,woBACPC,OAAO,ohCAAA;AAEEJ,YAAAK,kBAAmC,EAC5CH,KAAO,EACHI,UAAY,EACR,QAAQ,KACR,QAAQ,KACR,UAAU,KACV,UAAU,KACV,SAAS,IAAA,GAEbC,YAAc,EACV,KAAK,QACL,KAAK,QACL,KAAM,UACN,KAAK,UACL,KAAK,QAAA,EAAA,GAGbJ,OAAS,EACLG,UAAY,EACR,UAAU,KACV,SAAS,KACT,UAAU,KACV,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,QAAQ,KACR,SAAS,KACT,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,QAAQ,KACR,SAAS,KACT,SAAS,KACT,UAAU,KACV,SAAS,KACT,UAAU,KACV,UAAU,KACV,WAAW,KACX,QAAQ,KACR,SAAS,KACT,QAAQ,KACR,SAAS,KACT,QAAQ,KACR,SAAS,KACT,SAAS,KACT,UAAU,KACV,QAAQ,KACR,SAAS,KACT,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,SAAS,KACT,UAAU,KACV,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,SAAS,KACT,UAAU,KACV,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,QAAQ,KACR,SAAS,KACT,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,QAAQ,KACR,SAAS,KACT,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,SAAS,KACT,UAAU,KACV,QAAQ,KACR,SAAS,KACT,OAAO,KACP,QAAQ,KACR,OAAO,KACP,QAAQ,KACR,WAAW,KACX,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,UAAU,KACV,WAAW,KACX,UAAU,KACV,UAAU,KACV,YAAY,KACZ,UAAU,KACV,SAAS,KACT,SAAS,KACT,SAAS,KACT,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,aAAa,KACb,UAAU,KACV,SAAS,KACT,WAAW,KACX,UAAU,KACV,WAAW,KACX,YAAY,KACZ,QAAQ,KACR,QAAQ,KACR,QAAQ,KACR,aAAa,KACb,QAAQ,KACR,SAAS,KACT,WAAW,KACX,SAAS,KACT,aAAa,KACb,SAAS,KACT,SAAS,KACT,SAAS,KACT,WAAW,KACX,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,aAAa,KACb,UAAU,KACV,SAAS,KACT,WAAW,KACX,UAAU,KACV,WAAW,KACX,YAAY,KACZ,QAAQ,KACR,QAAQ,KACR,QAAQ,KACR,aAAa,KACb,QAAQ,KACR,SAAS,KACT,YAAY,KACZ,WAAW,KACX,SAAS,KACT,aAAa,KACb,SAAS,KACT,SAAS,KACT,SAAS,KACT,WAAW,KACX,cAAc,KACd,WAAW,KACX,SAAS,KACT,UAAU,KACV,YAAY,KACZ,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,UAAU,KACV,WAAW,KACX,aAAa,KACb,UAAU,KACV,UAAU,KACV,UAAU,KACV,UAAU,KACV,UAAU,KACV,WAAW,KACX,UAAU,KACV,UAAU,KACV,UAAU,KACV,UAAU,KACV,UAAU,KACV,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,UAAU,KACV,WAAW,KACX,QAAQ,KACR,UAAU,KACV,SAAS,KACT,WAAW,KACX,YAAY,KACZ,WAAW,KACX,UAAU,KACV,WAAW,KACX,SAAS,KACT,SAAS,KACT,QAAQ,KACR,SAAS,KACT,SAAS,KACT,SAAS,KACT,YAAY,KACZ,SAAS,KACT,UAAU,KACV,WAAW,KACX,QAAQ,KACR,WAAW,KACX,QAAQ,KACR,QAAQ,KACR,SAAS,KACT,SAAS,KACT,UAAU,KACV,UAAU,KACV,UAAU,KACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,UAAU,KACV,SAAS,KACT,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,IAAA,GAEfC,YAAc,EACV,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,SACL,KAAK,YACL,KAAK,UACL,KAAK,SACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,SACL,KAAK,SACL,KAAK,SACL,KAAK,UACL,KAAK,SACL,KAAK,YACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,SACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,SACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAM,UACN,KAAK,SACL,KAAK,QACL,KAAK,QACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,UACL,KAAK,SACL,KAAK,SACL,KAAK,SACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,aACL,KAAK,UACL,KAAK,SACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,QACL,KAAK,QACL,KAAK,QACL,KAAK,aACL,KAAK,QACL,KAAK,SACL,KAAK,WACL,KAAK,SACL,KAAK,aACL,KAAK,SACL,KAAK,SACL,KAAK,SACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,aACL,KAAK,UACL,KAAK,SACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,QACL,KAAK,QACL,KAAK,QACL,KAAK,aACL,KAAK,QACL,KAAK,SACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,aACL,KAAK,SACL,KAAK,SACL,KAAK,SACL,KAAK,WACL,KAAK,cACL,KAAK,WACL,KAAK,SACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,aACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,QACL,KAAK,UACL,KAAK,SACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,SACL,KAAK,SACL,KAAK,QACL,KAAK,SACL,KAAK,SACL,KAAK,SACL,KAAK,YACL,KAAK,SACL,KAAK,UACL,KAAK,WACL,KAAK,QACL,KAAK,WACL,KAAK,QACL,KAAK,QACL,KAAK,SACL,KAAK,SACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,UACL,KAAK,SACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,UAAA,EAAA,GAGbH,OAAS,EACLE,UAAY,EACR,UAAU,KACV,WAAW,KACX,QAAQ,KACR,SAAS,KACT,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,SAAS,MACT,WAAW,KACX,YAAY,KACZ,WAAW,KACX,WAAW,KACX,SAAS,KACT,WAAW,KACX,UAAU,MACV,mBAAmB,KACnB,UAAU,KACV,WAAW,KACX,UAAU,MACV,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,eAAe,KACf,UAAU,KACV,YAAY,KACZ,SAAS,KACT,aAAa,KACb,gBAAgB,KAChB,UAAU,KACV,SAAS,MACT,UAAU,MACV,WAAW,KACX,UAAU,KACV,YAAY,KACZ,UAAU,KACV,SAAS,KACT,UAAU,KACV,YAAY,KACZ,SAAS,KACT,0BAA0B,KAC1B,aAAa,KACb,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,aAAa,KACb,UAAU,KACV,aAAa,KACb,eAAe,KACf,SAAS,KACT,SAAS,KACT,eAAe,KACf,iBAAiB,KACjB,gBAAgB,KAChB,iBAAiB,KACjB,8BAA8B,KAC9B,2BAA2B,KAC3B,qBAAqB,KACrB,WAAW,KACX,YAAY,KACZ,eAAe,KACf,YAAY,KACZ,qBAAqB,KACrB,UAAU,KACV,eAAe,KACf,qCAAqC,KACrC,WAAW,KACX,UAAU,MACV,SAAS,KACT,YAAY,KACZ,QAAQ,KACR,cAAc,KACd,UAAU,KACV,UAAU,KACV,UAAU,KACV,YAAY,KACZ,UAAU,KACV,WAAW,KACX,YAAY,KACZ,SAAS,KACT,SAAS,KACT,WAAW,KACX,SAAS,MACT,sBAAsB,KACtB,oBAAoB,KACpB,4BAA4B,KAC5B,sBAAsB,KACtB,sBAAsB,KACtB,aAAa,KACb,mBAAmB,KACnB,UAAU,MACV,SAAS,KACT,YAAY,KACZ,cAAc,KACd,2BAA2B,KAC3B,eAAe,KACf,qBAAqB,KACrB,qBAAqB,KACrB,0BAA0B,KAC1B,mBAAmB,KACnB,yBAAyB,KACzB,8BAA8B,KAC9B,0BAA0B,KAC1B,sBAAsB,KACtB,oBAAoB,KACpB,mBAAmB,KACnB,uBAAuB,KACvB,uBAAuB,KACvB,eAAe,KACf,kBAAkB,KAClB,sBAAsB,KACtB,eAAe,KACf,yBAAyB,KACzB,uBAAuB,KACvB,oBAAoB,KACpB,uBAAuB,KACvB,wBAAwB,KACxB,qBAAqB,KACrB,wBAAwB,KACxB,aAAa,KACb,kBAAkB,KAClB,eAAe,KACf,UAAU,MACV,YAAY,KACZ,SAAS,KACT,QAAQ,KACR,SAAS,KACT,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,SAAS,MACT,WAAW,KACX,YAAY,KACZ,aAAa,KACb,WAAW,KACX,sBAAsB,KACtB,0BAA0B,KAC1B,WAAW,KACX,UAAU,MACV,aAAa,KACb,WAAW,KACX,gBAAgB,KAChB,iBAAiB,KACjB,UAAU,KACV,UAAU,KACV,SAAS,KACT,SAAS,KACT,UAAU,KACV,YAAY,KACZ,kBAAkB,KAClB,SAAS,KACT,SAAS,MACT,uBAAuB,KACvB,2BAA2B,KAC3B,UAAU,MACV,YAAY,KACZ,gBAAgB,KAChB,UAAU,KACV,UAAU,KACV,OAAO,KACP,QAAQ,KACR,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,SAAS,KACT,UAAU,KACV,SAAS,MACT,QAAQ,KACR,UAAU,MACV,kBAAkB,KAClB,sBAAsB,KACtB,sBAAsB,KACtB,oBAAoB,KACpB,iBAAiB,KACjB,uBAAuB,KACvB,kBAAkB,KAClB,UAAU,MACV,QAAQ,KACR,YAAY,KACZ,WAAW,KACX,SAAS,KACT,WAAW,KACX,SAAS,KACT,kBAAkB,KAClB,UAAU,KACV,oBAAoB,KACpB,UAAU,KACV,YAAY,KACZ,kBAAkB,KAClB,eAAe,KACf,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,SAAS,KACT,WAAW,KACX,YAAY,KACZ,QAAQ,KACR,WAAW,KACX,gBAAgB,KAChB,aAAa,KACb,SAAS,KACT,cAAc,KACd,kBAAkB,KAClB,oBAAoB,KACpB,oBAAoB,KACpB,WAAW,KACX,UAAU,MACV,UAAU,KACV,UAAU,KACV,YAAY,KACZ,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,SAAS,KACT,SAAS,MACT,UAAU,MACV,UAAU,MACV,YAAY,KACZ,WAAW,KACX,UAAU,KACV,UAAU,KACV,WAAW,KACX,YAAY,KACZ,SAAS,KACT,SAAS,MACT,UAAU,MACV,UAAU,MACV,UAAU,KACV,OAAO,KACP,QAAQ,KACR,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,gBAAgB,KAChB,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,sBAAsB,KACtB,eAAe,KACf,kBAAkB,KAClB,yBAAyB,KACzB,iBAAiB,KACjB,uBAAuB,KACvB,uBAAuB,KACvB,oBAAoB,KACpB,uBAAuB,KACvB,eAAe,KACf,oBAAoB,KACpB,qBAAqB,KACrB,aAAa,KACb,kBAAkB,KAClB,mBAAmB,KACnB,kBAAkB,KAClB,qBAAqB,KACrB,uBAAuB,KACvB,sBAAsB,KACtB,qBAAqB,KACrB,kBAAkB,KAClB,qBAAqB,KACrB,gBAAgB,KAChB,mBAAmB,KACnB,eAAe,KACf,oBAAoB,KACpB,sBAAsB,KACtB,mBAAmB,KACnB,iBAAiB,KACjB,cAAc,KACd,oBAAoB,KACpB,eAAe,KACf,SAAS,MACT,QAAQ,KACR,gBAAgB,KAChB,YAAY,KACZ,mBAAmB,KACnB,wBAAwB,KACxB,oBAAoB,KACpB,mBAAmB,KACnB,wBAAwB,KACxB,oBAAoB,KACpB,UAAU,MACV,oBAAoB,KACpB,qBAAqB,KACrB,UAAU,KACV,SAAS,KACT,YAAY,KACZ,QAAQ,KACR,SAAS,KACT,SAAS,KACT,iBAAiB,KACjB,eAAe,KACf,SAAS,MACT,eAAe,KACf,UAAU,MACV,UAAU,KACV,QAAQ,KACR,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,yBAAyB,KACzB,wBAAwB,KACxB,uBAAuB,KACvB,2BAA2B,KAC3B,0BAA0B,KAC1B,oBAAoB,KACpB,aAAa,MACb,SAAS,MACT,aAAa,KACb,sBAAsB,KACtB,UAAU,KACV,SAAS,KACT,kBAAkB,KAClB,eAAe,KACf,0BAA0B,KAC1B,gBAAgB,KAChB,cAAc,KACd,mBAAmB,MACnB,eAAe,KACf,gBAAgB,KAChB,qBAAqB,KACrB,yBAAyB,MACzB,uBAAuB,MACvB,oBAAoB,KACpB,0BAA0B,MAC1B,qBAAqB,KACrB,qBAAqB,MACrB,kBAAkB,MAClB,qBAAqB,KACrB,wBAAwB,MACxB,0BAA0B,KAC1B,aAAa,KACb,kBAAkB,KAClB,oBAAoB,KACpB,iBAAiB,MACjB,uBAAuB,MACvB,kBAAkB,KAClB,6BAA6B,MAC7B,uBAAuB,MACvB,iBAAiB,KACjB,sBAAsB,MACtB,2BAA2B,KAC3B,uBAAuB,KACvB,sBAAsB,KACtB,yBAAyB,MACzB,2BAA2B,KAC3B,qBAAqB,MACrB,0BAA0B,KAC1B,uBAAuB,MACvB,4BAA4B,KAC5B,eAAe,MACf,oBAAoB,KACpB,iBAAiB,KACjB,sBAAsB,MACtB,2BAA2B,KAC3B,sBAAsB,MACtB,iBAAiB,MACjB,sBAAsB,KACtB,cAAc,KACd,mBAAmB,KACnB,uBAAuB,KACvB,mBAAmB,KACnB,oBAAoB,KACpB,UAAU,MACV,WAAW,KACX,YAAY,KACZ,QAAQ,KACR,WAAW,KACX,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,YAAY,KACZ,SAAS,MACT,WAAW,KACX,YAAY,KACZ,WAAW,KACX,WAAW,KACX,aAAa,KACb,UAAU,MACV,0BAA0B,KAC1B,oBAAoB,KACpB,QAAQ,KACR,UAAU,MACV,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,UAAU,KACV,aAAa,KACb,eAAe,KACf,iBAAiB,KACjB,qBAAqB,KACrB,cAAc,KACd,SAAS,KACT,SAAS,MACT,SAAS,KACT,QAAQ,KACR,eAAe,KACf,mBAAmB,KACnB,UAAU,KACV,QAAQ,KACR,cAAc,KACd,mBAAmB,KACnB,wBAAwB,KACxB,mBAAmB,KACnB,WAAW,KACX,aAAa,KACb,gBAAgB,KAChB,kBAAkB,KAClB,UAAU,MACV,SAAS,KACT,SAAS,KACT,UAAU,KACV,SAAS,MACT,UAAU,KACV,UAAU,MACV,WAAW,KACX,QAAQ,KACR,SAAS,KACT,YAAY,KACZ,UAAU,KACV,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,QAAQ,KACR,oBAAoB,KACpB,wBAAwB,KACxB,0BAA0B,KAC1B,SAAS,KACT,SAAS,KACT,uBAAuB,KACvB,gBAAgB,KAChB,mBAAmB,KACnB,yBAAyB,KACzB,kBAAkB,KAClB,wBAAwB,KACxB,wBAAwB,KACxB,qBAAqB,KACrB,wBAAwB,KACxB,gBAAgB,KAChB,cAAc,KACd,mBAAmB,KACnB,oBAAoB,KACpB,mBAAmB,KACnB,sBAAsB,KACtB,wBAAwB,KACxB,uBAAuB,KACvB,sBAAsB,KACtB,mBAAmB,KACnB,sBAAsB,KACtB,iBAAiB,KACjB,oBAAoB,KACpB,gBAAgB,KAChB,UAAU,KACV,kBAAkB,KAClB,iBAAiB,KACjB,UAAU,KACV,SAAS,KACT,iBAAiB,KACjB,YAAY,KACZ,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,QAAQ,KACR,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,SAAS,KACT,SAAS,MACT,oBAAoB,KACpB,oBAAoB,KACpB,qBAAqB,KACrB,kBAAkB,KAClB,WAAW,KACX,iBAAiB,KACjB,UAAU,MACV,UAAU,KACV,YAAY,KACZ,wBAAwB,KACxB,kBAAkB,KAClB,uBAAuB,KACvB,oBAAoB,KACpB,yBAAyB,KACzB,iBAAiB,KACjB,UAAU,MACV,UAAU,KACV,SAAS,KACT,YAAY,KACZ,iBAAiB,KACjB,cAAc,KACd,mBAAmB,KACnB,wBAAwB,KACxB,mBAAmB,KACnB,cAAc,KACd,SAAS,KACT,SAAS,KACT,cAAc,KACd,mBAAmB,KACnB,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,UAAU,KACV,SAAS,KACT,SAAS,KACT,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,SAAS,MACT,eAAe,KACf,WAAW,KACX,gBAAgB,MAChB,eAAe,KACf,WAAW,KACX,gBAAgB,KAChB,oBAAoB,KACpB,gBAAgB,KAChB,UAAU,MACV,eAAe,KACf,UAAU,MACV,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,cAAc,KACd,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,YAAY,KACZ,SAAS,MACT,WAAW,KACX,YAAY,KACZ,WAAW,KACX,cAAc,KACd,gBAAgB,KAChB,kBAAkB,KAClB,sBAAsB,KACtB,WAAW,KACX,eAAe,KACf,WAAW,KACX,UAAU,MACV,aAAa,KACb,gBAAgB,KAChB,sBAAsB,KACtB,iBAAiB,KACjB,mBAAmB,KACnB,WAAW,KACX,gBAAgB,KAChB,aAAa,KACb,iBAAiB,KACjB,oBAAoB,KACpB,qBAAqB,KACrB,UAAU,KACV,aAAa,KACb,WAAW,KACX,UAAU,MACV,YAAY,KACZ,SAAS,KACT,UAAU,KACV,WAAW,KACX,UAAU,KACV,SAAS,KACT,WAAW,KACX,YAAY,KACZ,SAAS,KACT,YAAY,KACZ,UAAU,KACV,iBAAiB,KACjB,kBAAkB,KAClB,uBAAuB,KACvB,mBAAmB,KACnB,mBAAmB,KACnB,SAAS,MACT,UAAU,MACV,UAAU,MACV,YAAY,KACZ,WAAW,KACX,WAAW,KACX,SAAS,MACT,UAAU,MACV,UAAU,MACV,SAAS,MACT,QAAQ,KACR,UAAU,MACV,UAAU,MACV,UAAU,KACV,UAAU,KACV,UAAU,KACV,WAAW,KACX,YAAY,KACZ,WAAW,KACX,SAAS,KACT,SAAS,MACT,UAAU,MACV,UAAU,MACV,UAAU,KACV,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,UAAU,KACV,oBAAoB,KACpB,UAAU,KACV,SAAS,KACT,UAAU,KACV,UAAU,MACV,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,QAAQ,KACR,SAAS,MACT,SAAS,KACT,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,QAAQ,KACR,SAAS,MACT,WAAW,KACX,YAAY,KACZ,aAAa,KACb,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,QAAQ,KACR,SAAS,KACT,SAAS,KACT,YAAY,KACZ,UAAU,KACV,cAAc,KACd,UAAU,KACV,SAAS,KACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,cAAc,KACd,cAAc,KACd,cAAc,KACd,cAAc,KACd,cAAc,KACd,cAAc,KACd,cAAc,KACd,cAAc,KACd,WAAW,KACX,aAAa,KACb,cAAc,KACd,YAAY,KACZ,WAAW,KACX,aAAa,KACb,WAAW,KACX,UAAU,MACV,QAAQ,KACR,SAAS,KACT,YAAY,KACZ,SAAS,KACT,UAAU,KACV,UAAU,KACV,YAAY,KACZ,cAAc,KACd,UAAU,KACV,WAAW,KACX,UAAU,MACV,SAAS,KACT,WAAW,KACX,aAAa,KACb,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,cAAc,KACd,WAAW,KACX,UAAU,KACV,cAAc,KACd,iBAAiB,KACjB,eAAe,KACf,aAAa,KACb,eAAe,KACf,YAAY,KACZ,YAAY,KACZ,cAAc,KACd,UAAU,KACV,cAAc,KACd,WAAW,KACX,SAAS,KACT,WAAW,KACX,YAAY,KACZ,aAAa,KACb,aAAa,KACb,WAAW,KACX,YAAY,KACZ,UAAU,KACV,UAAU,KACV,aAAa,KACb,SAAS,MACT,YAAY,KACZ,aAAa,KACb,YAAY,KACZ,aAAa,KACb,cAAc,KACd,eAAe,KACf,cAAc,KACd,aAAa,KACb,qBAAqB,KACrB,mBAAmB,KACnB,cAAc,KACd,YAAY,KACZ,cAAc,KACd,YAAY,KACZ,kBAAkB,KAClB,iBAAiB,KACjB,mBAAmB,KACnB,uBAAuB,KACvB,uBAAuB,KACvB,wBAAwB,KACxB,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,SAAS,MACT,aAAa,MACb,UAAU,KACV,UAAU,MACV,SAAS,KACT,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,cAAc,KACd,aAAa,KACb,cAAc,KACd,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,WAAW,KACX,YAAY,KACZ,UAAU,MACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,UAAU,MACV,WAAW,KACX,cAAc,KACd,UAAU,KACV,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,YAAY,KACZ,cAAc,KACd,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,UAAU,MACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,WAAW,KACX,aAAa,KACb,UAAU,KACV,UAAU,KACV,WAAW,KACX,aAAa,KACb,SAAS,KACT,UAAU,KACV,eAAe,KACf,SAAS,MACT,UAAU,KACV,WAAW,KACX,eAAe,KACf,SAAS,KACT,SAAS,KACT,UAAU,KACV,UAAU,KACV,YAAY,KACZ,qBAAqB,KACrB,sBAAsB,KACtB,cAAc,KACd,cAAc,KACd,gBAAgB,KAChB,iBAAiB,KACjB,iBAAiB,KACjB,UAAU,KACV,cAAc,KACd,YAAY,KACZ,aAAa,KACb,WAAW,KACX,cAAc,KACd,WAAW,KACX,YAAY,KACZ,aAAa,KACb,WAAW,KACX,YAAY,KACZ,UAAU,KACV,YAAY,KACZ,gBAAgB,KAChB,eAAe,KACf,UAAU,KACV,aAAa,KACb,YAAY,KACZ,UAAU,MACV,YAAY,KACZ,SAAS,KACT,UAAU,KACV,YAAY,KACZ,WAAW,KACX,WAAW,KACX,UAAU,MACV,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,aAAa,KACb,aAAa,KACb,WAAW,KACX,WAAW,KACX,YAAY,KACZ,aAAa,KACb,SAAS,KACT,cAAc,KACd,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,UAAU,MACV,YAAY,KACZ,aAAa,KACb,iBAAiB,KACjB,iBAAiB,KACjB,cAAc,KACd,gBAAgB,KAChB,WAAW,KACX,YAAY,KACZ,oBAAoB,KACpB,qBAAqB,KACrB,WAAW,KACX,WAAW,KACX,cAAc,KACd,WAAW,KACX,YAAY,KACZ,UAAU,KACV,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,UAAU,KACV,WAAW,KACX,aAAa,KACb,WAAW,KACX,YAAY,KACZ,SAAS,KACT,QAAQ,KACR,aAAa,KACb,WAAW,KACX,aAAa,KACb,QAAQ,KACR,SAAS,KACT,WAAW,KACX,aAAa,KACb,YAAY,KACZ,SAAS,MACT,WAAW,KACX,WAAW,KACX,UAAU,KACV,aAAa,KACb,iBAAiB,KACjB,WAAW,KACX,SAAS,KACT,aAAa,KACb,WAAW,KACX,SAAS,KACT,WAAW,KACX,YAAY,KACZ,mBAAmB,KACnB,YAAY,KACZ,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,UAAU,MACV,SAAS,KACT,WAAW,KACX,cAAc,KACd,cAAc,KACd,aAAa,KACb,eAAe,KACf,oBAAoB,KACpB,eAAe,KACf,oBAAoB,KACpB,qBAAqB,KACrB,sBAAsB,KACtB,cAAc,KACd,YAAY,KACZ,YAAY,KACZ,UAAU,MACV,UAAU,KACV,UAAU,KACV,YAAY,KACZ,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,aAAa,KACb,UAAU,KACV,cAAc,KACd,WAAW,KACX,UAAU,KACV,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,UAAU,KACV,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,QAAQ,KACR,WAAW,KACX,SAAS,MACT,QAAQ,KACR,WAAW,KACX,YAAY,KACZ,SAAS,KACT,YAAY,KACZ,QAAQ,KACR,cAAc,KACd,SAAS,KACT,SAAS,KACT,YAAY,KACZ,WAAW,KACX,WAAW,KACX,cAAc,KACd,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,SAAS,KACT,UAAU,KACV,WAAW,KACX,UAAU,MACV,UAAU,KACV,YAAY,KACZ,WAAW,KACX,UAAU,KACV,aAAa,KACb,WAAW,KACX,YAAY,KACZ,aAAa,KACb,WAAW,KACX,gBAAgB,KAChB,iBAAiB,KACjB,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,aAAa,KACb,cAAc,KACd,WAAW,KACX,WAAW,KACX,UAAU,KACV,WAAW,KACX,UAAU,KACV,SAAS,KACT,QAAQ,KACR,SAAS,KACT,SAAS,KACT,UAAU,KACV,UAAU,KACV,UAAU,KACV,WAAW,KACX,iBAAiB,KACjB,kBAAkB,KAClB,mBAAmB,KACnB,SAAS,KACT,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,SAAS,MACT,WAAW,KACX,WAAW,MACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,UAAU,KACV,UAAU,MACV,YAAY,KACZ,UAAU,KACV,WAAW,KACX,cAAc,KACd,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,WAAW,KACX,UAAU,MACV,QAAQ,KACR,SAAS,KACT,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,SAAS,KACT,YAAY,KACZ,WAAW,KACX,SAAS,KACT,UAAU,KACV,QAAQ,KACR,SAAS,KACT,SAAS,KACT,UAAU,KACV,cAAc,KACd,SAAS,KACT,WAAW,KACX,YAAY,KACZ,aAAa,KACb,cAAc,KACd,UAAU,MACV,YAAY,KACZ,SAAS,MACT,QAAQ,KACR,SAAS,KACT,WAAW,KACX,UAAU,KACV,QAAQ,KACR,SAAS,KACT,SAAS,KACT,SAAS,KACT,SAAS,KACT,UAAU,KACV,cAAc,KACd,SAAS,KACT,UAAU,KACV,WAAW,KACX,WAAW,KACX,UAAU,MACV,WAAW,KACX,UAAU,KACV,UAAU,KACV,WAAW,KACX,WAAW,KACX,OAAO,KACP,QAAQ,KACR,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,aAAa,KACb,eAAe,KACf,YAAY,KACZ,YAAY,KACZ,eAAe,KACf,gBAAgB,KAChB,aAAa,KACb,YAAY,KACZ,eAAe,MACf,UAAU,MACV,UAAU,KACV,YAAY,KACZ,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,aAAa,KACb,WAAW,KACX,UAAU,KACV,WAAW,KACX,YAAY,KACZ,eAAe,KACf,YAAY,KACZ,YAAY,KACZ,SAAS,MACT,cAAc,KACd,cAAc,KACd,WAAW,KACX,YAAY,KACZ,mBAAmB,KACnB,oBAAoB,KACpB,UAAU,MACV,YAAY,KACZ,UAAU,MACV,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,QAAQ,KACR,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,UAAU,KACV,WAAW,KACX,SAAS,KACT,SAAS,MACT,WAAW,KACX,YAAY,KACZ,QAAQ,KACR,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,cAAc,KACd,cAAc,KACd,WAAW,KACX,UAAU,KACV,WAAW,KACX,QAAQ,KACR,YAAY,KACZ,WAAW,KACX,cAAc,KACd,YAAY,KACZ,SAAS,KACT,YAAY,KACZ,cAAc,KACd,cAAc,KACd,cAAc,KACd,aAAa,KACb,UAAU,KACV,WAAW,KACX,UAAU,MACV,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,UAAU,MACV,UAAU,KACV,WAAW,KACX,aAAa,KACb,WAAW,KACX,YAAY,KACZ,WAAW,KACX,QAAQ,KACR,YAAY,KACZ,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,SAAS,KACT,SAAS,MACT,WAAW,KACX,UAAU,MACV,UAAU,MACV,YAAY,KACZ,WAAW,KACX,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,SAAS,MACT,YAAY,KACZ,UAAU,KACV,UAAU,KACV,UAAU,MACV,UAAU,MACV,WAAW,KACX,UAAU,KACV,YAAY,KACZ,WAAW,KACX,QAAQ,KACR,SAAS,KACT,UAAU,KACV,YAAY,KACZ,cAAc,KACd,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,aAAa,KACb,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,aAAa,KACb,YAAY,KACZ,SAAS,KACT,YAAY,KACZ,UAAU,KACV,WAAW,MACX,WAAW,KACX,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,aAAa,KACb,aAAa,KACb,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,UAAU,KACV,SAAS,KACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,aAAa,KACb,cAAc,KACd,UAAU,KACV,QAAQ,KACR,eAAe,KACf,mBAAmB,KACnB,qBAAqB,KACrB,mBAAmB,KACnB,oBAAoB,KACpB,oBAAoB,KACpB,qBAAqB,KACrB,uBAAuB,KACvB,yBAAyB,KACzB,oBAAoB,KACpB,SAAS,KACT,SAAS,KACT,UAAU,KACV,cAAc,KACd,SAAS,KACT,WAAW,KACX,YAAY,KACZ,aAAa,KACb,cAAc,KACd,UAAU,MACV,YAAY,KACZ,gBAAgB,KAChB,aAAa,KACb,eAAe,KACf,gBAAgB,KAChB,aAAa,KACb,aAAa,KACb,YAAY,KACZ,YAAY,KACZ,SAAS,MACT,QAAQ,KACR,SAAS,KACT,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,UAAU,KACV,QAAQ,KACR,WAAW,KACX,cAAc,KACd,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,gBAAgB,KAChB,SAAS,KACT,UAAU,KACV,cAAc,KACd,SAAS,KACT,UAAU,KACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,mBAAmB,KACnB,wBAAwB,KACxB,gBAAgB,KAChB,oBAAoB,KACpB,mBAAmB,KACnB,oBAAoB,KACpB,WAAW,KACX,UAAU,MACV,YAAY,KACZ,aAAa,KACb,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,aAAa,KACb,UAAU,KACV,UAAU,KACV,YAAY,KACZ,WAAW,KACX,cAAc,KACd,WAAW,KACX,YAAY,KACZ,SAAS,KACT,WAAW,KACX,YAAY,KACZ,UAAU,MACV,SAAS,KACT,UAAU,KACV,WAAW,KACX,WAAW,KACX,UAAU,KACV,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,OAAO,KACP,QAAQ,KACR,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,aAAa,KACb,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,cAAc,KACd,aAAa,KACb,eAAe,MACf,UAAU,MACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,UAAU,KACV,UAAU,KACV,aAAa,KACb,SAAS,KACT,YAAY,KACZ,gBAAgB,KAChB,gBAAgB,KAChB,cAAc,KACd,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,WAAW,KACX,mBAAmB,KACnB,SAAS,MACT,SAAS,KACT,UAAU,KACV,WAAW,KACX,SAAS,KACT,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,aAAa,KACb,UAAU,KACV,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,UAAU,MACV,QAAQ,KACR,UAAU,MACV,YAAY,KACZ,QAAQ,KACR,cAAc,KACd,WAAW,KACX,SAAS,MACT,SAAS,MACT,UAAU,MACV,gBAAgB,KAChB,qBAAqB,KACrB,SAAS,MACT,SAAS,MACT,UAAU,MACV,iBAAiB,KACjB,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,MACV,SAAS,KACT,UAAU,MACV,WAAW,MACX,WAAW,KACX,aAAa,KACb,WAAW,KACX,aAAa,KACb,cAAc,KACd,SAAS,KACT,UAAU,KACV,WAAW,MACX,YAAY,MACZ,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,cAAc,MACd,UAAU,KACV,SAAS,KACT,WAAW,KACX,QAAQ,KACR,WAAW,KACX,YAAY,KACZ,WAAW,KACX,aAAa,KACb,WAAW,MACX,YAAY,KACZ,YAAY,KACZ,WAAW,MACX,YAAY,KACZ,aAAa,KACb,SAAS,MACT,SAAS,MACT,SAAS,KACT,UAAU,KACV,WAAW,MACX,eAAe,MACf,UAAU,MACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,QAAQ,KACR,SAAS,KACT,UAAU,KACV,SAAS,KACT,UAAU,KACV,WAAW,KACX,SAAS,MACT,WAAW,KACX,UAAU,KACV,SAAS,KACT,gBAAgB,KAChB,qBAAqB,KACrB,UAAU,KACV,WAAW,MACX,eAAe,MACf,UAAU,MACV,WAAW,KACX,WAAW,KACX,SAAS,KACT,WAAW,KACX,YAAY,KACZ,UAAU,KACV,UAAU,MACV,QAAQ,KACR,SAAS,KACT,WAAW,KACX,YAAY,MACZ,cAAc,MACd,aAAa,KACb,aAAa,KACb,aAAa,KACb,WAAW,KACX,aAAa,KACb,aAAa,KACb,aAAa,KACb,UAAU,KACV,eAAe,KACf,YAAY,MACZ,WAAW,MACX,aAAa,KACb,SAAS,KACT,YAAY,KACZ,UAAU,MACV,WAAW,KACX,aAAa,MACb,WAAW,KACX,WAAW,KACX,YAAY,MACZ,YAAY,MACZ,iBAAiB,KACjB,WAAW,KACX,YAAY,KACZ,SAAS,KACT,YAAY,KACZ,UAAU,MACV,UAAU,MACV,eAAe,KACf,oBAAoB,KACpB,UAAU,KACV,WAAW,KACX,YAAY,KACZ,WAAW,KACX,WAAW,KACX,aAAa,KACb,aAAa,KACb,UAAU,KACV,WAAW,MACX,WAAW,KACX,aAAa,MACb,eAAe,KACf,gBAAgB,MAChB,WAAW,KACX,aAAa,MACb,UAAU,KACV,WAAW,MACX,WAAW,KACX,aAAa,MACb,eAAe,KACf,gBAAgB,MAChB,UAAU,KACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,mBAAmB,KACnB,qBAAqB,KACrB,oBAAoB,KACpB,sBAAsB,KACtB,QAAQ,KACR,SAAS,KACT,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,UAAU,MACV,YAAY,KACZ,UAAU,MACV,UAAU,MACV,aAAa,KACb,YAAY,KACZ,UAAU,MACV,UAAU,MACV,aAAa,MACb,YAAY,KACZ,aAAa,MACb,WAAW,MACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,aAAa,KACb,YAAY,KACZ,QAAQ,KACR,WAAW,KACX,YAAY,KACZ,UAAU,KACV,UAAU,KACV,UAAU,KACV,WAAW,KACX,SAAS,KACT,WAAW,KACX,YAAY,KACZ,UAAU,KACV,UAAU,KACV,YAAY,KACZ,WAAW,KACX,WAAW,KACX,SAAS,MACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,SAAS,KACT,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,WAAW,KACX,aAAa,KACb,WAAW,KACX,SAAS,KACT,WAAW,KACX,WAAW,KACX,aAAa,KACb,UAAU,KACV,YAAY,KACZ,UAAU,MACV,UAAU,KACV,WAAW,KACX,WAAW,KACX,QAAQ,KACR,WAAW,KACX,SAAS,KACT,WAAW,KACX,aAAa,KACb,SAAS,KACT,UAAU,KACV,SAAS,KACT,UAAU,KACV,YAAY,KACZ,UAAU,KACV,aAAa,KACb,SAAS,KACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,cAAc,KACd,SAAS,KACT,UAAU,KACV,WAAW,KACX,SAAS,KACT,SAAS,KACT,UAAU,KACV,cAAc,KACd,YAAY,KACZ,WAAW,KACX,UAAU,KACV,SAAS,KACT,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,aAAa,KACb,SAAS,MACT,SAAS,KACT,UAAU,KACV,YAAY,KACZ,WAAW,KACX,QAAQ,KACR,eAAe,KACf,SAAS,KACT,YAAY,KACZ,aAAa,KACb,YAAY,KACZ,UAAU,KACV,cAAc,KACd,WAAW,KACX,aAAa,KACb,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,WAAW,KACX,YAAY,KACZ,aAAa,KACb,aAAa,KACb,QAAQ,KACR,cAAc,KACd,UAAU,MACV,UAAU,KACV,WAAW,KACX,QAAQ,KACR,SAAS,KACT,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,gBAAgB,KAChB,iBAAiB,KACjB,YAAY,KACZ,iBAAiB,KACjB,cAAc,KACd,cAAc,KACd,aAAa,KACb,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,cAAc,KACd,cAAc,KACd,cAAc,KACd,UAAU,KACV,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,MACV,SAAS,KACT,YAAY,KACZ,SAAS,MACT,UAAU,KACV,UAAU,MACV,YAAY,KACZ,UAAU,MACV,iBAAiB,KACjB,aAAa,KACb,WAAW,KACX,aAAa,KACb,SAAS,KACT,UAAU,KACV,WAAW,KACX,UAAU,KACV,YAAY,KACZ,WAAW,KACX,UAAU,KACV,UAAU,MACV,YAAY,KACZ,WAAW,KACX,cAAc,KACd,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,UAAU,KACV,YAAY,KACZ,WAAW,KACX,aAAa,KACb,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,aAAa,KACb,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,eAAe,KACf,WAAW,KACX,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,aAAa,KACb,aAAa,KACb,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,UAAU,KACV,SAAS,KACT,UAAU,KACV,aAAa,KACb,WAAW,KACX,YAAY,KACZ,UAAU,KACV,UAAU,KACV,aAAa,KACb,cAAc,KACd,WAAW,KACX,UAAU,KACV,QAAQ,KACR,SAAS,KACT,YAAY,KACZ,YAAY,KACZ,SAAS,MACT,WAAW,KACX,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,gBAAgB,KAChB,oBAAoB,KACpB,sBAAsB,KACtB,oBAAoB,KACpB,qBAAqB,KACrB,uBAAuB,KACvB,sBAAsB,KACtB,qBAAqB,KACrB,qBAAqB,KACrB,UAAU,KACV,kBAAkB,KAClB,WAAW,KACX,WAAW,KACX,SAAS,KACT,YAAY,KACZ,gBAAgB,KAChB,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,WAAW,KACX,UAAU,MACV,YAAY,KACZ,aAAa,KACb,UAAU,KACV,YAAY,KACZ,cAAc,KACd,WAAW,KACX,YAAY,KACZ,UAAU,MACV,SAAS,KACT,UAAU,KACV,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,cAAc,KACd,aAAa,KACb,QAAQ,KACR,YAAY,KACZ,WAAW,KACX,QAAQ,KACR,SAAS,KACT,UAAU,KACV,YAAY,KACZ,WAAW,KACX,SAAS,KACT,YAAY,KACZ,WAAW,KACX,UAAU,KACV,WAAW,KACX,YAAY,KACZ,cAAc,KACd,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,KACX,WAAW,KACX,WAAW,KACX,YAAY,KACZ,WAAW,KACX,aAAa,KACb,SAAS,KACT,UAAU,KACV,UAAU,KACV,YAAY,KACZ,cAAc,KACd,WAAW,KACX,UAAU,KACV,SAAS,MACT,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,cAAc,KACd,mBAAmB,KACnB,QAAQ,KACR,SAAS,KACT,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,YAAY,KACZ,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,WAAW,KACX,aAAa,KACb,aAAa,KACb,WAAW,KACX,mBAAmB,KACnB,YAAY,KACZ,cAAc,KACd,UAAU,KACV,WAAW,KACX,SAAS,KACT,UAAU,KACV,WAAW,MACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,YAAY,KACZ,UAAU,MACV,YAAY,KACZ,eAAe,KACf,UAAU,KACV,WAAW,KACX,YAAY,MACZ,WAAW,KACX,YAAY,MACZ,WAAW,KACX,YAAY,KACZ,cAAc,KACd,gBAAgB,KAChB,WAAW,KACX,YAAY,KACZ,cAAc,KACd,gBAAgB,KAChB,SAAS,KACT,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,WAAW,KACX,UAAU,MACV,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,WAAW,KACX,qBAAqB,KACrB,iBAAiB,KACjB,WAAW,KACX,SAAS,KACT,UAAU,KACV,YAAY,KACZ,UAAU,KACV,aAAa,KACb,aAAa,KACb,WAAW,KACX,WAAW,KACX,aAAa,KACb,aAAa,KACb,YAAY,KACZ,cAAc,KACd,eAAe,KACf,eAAe,KACf,gBAAgB,KAChB,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,gBAAgB,KAChB,iBAAiB,KACjB,YAAY,KACZ,iBAAiB,KACjB,cAAc,KACd,cAAc,KACd,aAAa,KACb,SAAS,KACT,UAAU,KACV,SAAS,KACT,UAAU,KACV,SAAS,KACT,UAAU,KACV,SAAS,KACT,UAAU,KACV,SAAS,KACT,UAAU,KACV,YAAY,KACZ,aAAa,KACb,UAAU,KACV,aAAa,KACb,aAAa,KACb,aAAa,KACb,aAAa,KACb,aAAa,KACb,WAAW,KACX,WAAW,KACX,aAAa,KACb,YAAY,KACZ,cAAc,KACd,eAAe,KACf,eAAe,KACf,gBAAgB,KAChB,YAAY,KACZ,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,WAAW,KACX,aAAa,KACb,YAAY,KACZ,UAAU,KACV,WAAW,KACX,YAAY,KACZ,SAAS,KACT,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,UAAU,KACV,YAAY,KACZ,SAAS,MACT,YAAY,KACZ,eAAe,KACf,WAAW,KACX,cAAc,KACd,YAAY,KACZ,iBAAiB,KACjB,cAAc,KACd,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,UAAU,KACV,WAAW,KACX,YAAY,KACZ,cAAc,KACd,YAAY,KACZ,UAAU,KACV,UAAU,KACV,SAAS,KACT,YAAY,KACZ,YAAY,KACZ,UAAU,MACV,aAAa,KACb,UAAU,KACV,YAAY,KACZ,WAAW,KACX,cAAc,KACd,kBAAkB,KAClB,kBAAkB,KAClB,oBAAoB,KACpB,eAAe,KACf,mBAAmB,KACnB,qBAAqB,KACrB,YAAY,KACZ,UAAU,KACV,cAAc,KACd,aAAa,KACb,WAAW,KACX,aAAa,KACb,cAAc,KACd,UAAU,MACV,UAAU,KACV,WAAW,KACX,YAAY,KACZ,WAAW,KACX,sBAAsB,KACtB,uBAAuB,KACvB,UAAU,KACV,UAAU,KACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,SAAS,MACT,WAAW,KACX,YAAY,KACZ,WAAW,KACX,WAAW,KACX,WAAW,KACX,YAAY,KACZ,cAAc,KACd,YAAY,KACZ,WAAW,KACX,WAAW,KACX,QAAQ,KACR,SAAS,KACT,WAAW,KACX,UAAU,MACV,aAAa,KACb,iBAAiB,KACjB,mBAAmB,KACnB,oBAAoB,KACpB,WAAW,KACX,UAAU,KACV,WAAW,KACX,aAAa,KACb,gBAAgB,KAChB,YAAY,KACZ,cAAc,KACd,YAAY,KACZ,WAAW,KACX,WAAW,KACX,UAAU,MACV,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,WAAW,KACX,SAAS,KACT,UAAU,KACV,aAAa,KACb,UAAU,KACV,UAAU,KACV,WAAW,KACX,WAAW,KACX,YAAY,KACZ,gBAAgB,KAChB,cAAc,KACd,gBAAgB,KAChB,YAAY,KACZ,WAAW,KACX,eAAe,KACf,UAAU,KACV,YAAY,KACZ,cAAc,KACd,kBAAkB,MAClB,mBAAmB,MACnB,kBAAkB,MAClB,mBAAmB,MACnB,cAAc,KACd,qBAAqB,KACrB,sBAAsB,KACtB,SAAS,KACT,WAAW,KACX,SAAS,KACT,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,UAAU,KACV,SAAS,MACT,WAAW,KACX,WAAW,MACX,WAAW,MACX,UAAU,MACV,WAAW,KACX,WAAW,KACX,UAAU,MACV,YAAY,MACZ,YAAY,MACZ,YAAY,MACZ,YAAY,MACZ,aAAa,KACb,WAAW,KACX,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,YAAY,KACZ,SAAS,MACT,UAAU,MACV,QAAQ,KACR,QAAQ,KACR,YAAY,KACZ,UAAU,MACV,UAAU,KACV,WAAW,KACX,UAAU,KACV,WAAW,KACX,SAAS,MACT,WAAW,KACX,WAAW,KACX,QAAQ,KACR,WAAW,KACX,WAAW,KACX,UAAU,KACV,UAAU,KACV,WAAW,KACX,UAAU,MACV,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,WAAW,KACX,UAAU,MACV,YAAY,KACZ,YAAY,KACZ,WAAW,KACX,UAAU,KACV,YAAY,KACZ,WAAW,KACX,YAAY,KACZ,UAAU,KACV,WAAW,KACX,SAAS,KACT,QAAQ,KACR,SAAS,KACT,SAAS,MACT,UAAU,KACV,UAAU,MACV,UAAU,MACV,UAAU,KACV,SAAS,KACT,UAAU,KACV,YAAY,KACZ,YAAY,KACZ,SAAS,KACT,UAAU,KACV,YAAY,KACZ,UAAU,KACV,SAAS,MACT,UAAU,KACV,aAAa,KACb,UAAU,MACV,UAAU,MACV,SAAS,KACT,UAAU,IAAA,GAEdC,YAAc,EACV,KAAK,WACL,KAAK,SACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,MAAM,SACN,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,SACL,KAAK,WACL,MAAM,UACN,KAAK,QACL,KAAK,WACL,MAAM,UACN,KAAK,aACL,KAAK,YACL,KAAK,UACL,KAAK,YACL,KAAK,UACL,KAAK,oBACL,KAAK,SACL,KAAK,aACL,KAAK,YACL,KAAK,UACL,MAAM,SACN,MAAM,UACN,KAAK,WACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,SACL,KAAK,QACL,KAAK,SACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,aACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,SACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,cACL,KAAK,YACL,KAAK,YACL,KAAK,gBACL,KAAK,YACL,KAAK,WACL,KAAK,2BACL,KAAK,UACL,KAAK,eACL,KAAK,YACL,KAAK,cACL,KAAK,WACL,MAAM,UACN,KAAK,SACL,KAAK,aACL,KAAK,cACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,aACL,KAAK,UACL,KAAK,mBACL,KAAK,YACL,KAAK,SACL,KAAK,WACL,KAAK,WACL,MAAM,SACN,KAAK,WACL,KAAK,SACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,aACL,KAAK,QACL,MAAM,UACN,KAAK,SACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,UACL,KAAK,SACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,eACL,KAAK,kBACL,KAAK,WACL,KAAK,eACL,KAAK,yBACL,KAAK,uBACL,KAAK,WACL,KAAK,uBACL,KAAK,wBACL,KAAK,sBACL,KAAK,wBACL,KAAK,SACL,KAAK,gBACL,MAAM,UACN,KAAK,YACL,KAAK,SACL,KAAK,SACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,UACL,MAAM,SACN,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,sBACL,KAAK,0BACL,KAAK,WACL,MAAM,UACN,KAAK,aACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,iBACL,KAAK,UACL,KAAK,SACL,KAAK,UACL,KAAK,WACL,KAAK,kBACL,KAAK,SACL,MAAM,SACN,KAAK,uBACL,KAAK,UACL,MAAM,UACN,KAAK,YACL,KAAK,UACL,KAAK,UACL,KAAK,QACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,UACL,MAAM,SACN,KAAK,SACL,MAAM,UACN,KAAK,SACL,KAAK,eACL,KAAK,UACL,KAAK,oBACL,KAAK,aACL,KAAK,SACL,KAAK,YACL,MAAM,UACN,KAAK,QACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,WACL,KAAK,mBACL,KAAK,YACL,KAAK,iBACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,UACL,KAAK,cACL,KAAK,YACL,KAAK,WACL,KAAK,QACL,KAAK,SACL,KAAK,SACL,KAAK,UACL,KAAK,QACL,KAAK,QACL,KAAK,WACL,MAAM,UACN,KAAK,UACL,KAAK,cACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,SACL,MAAM,SACN,MAAM,UACN,MAAM,UACN,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,SACL,MAAM,SACN,MAAM,UACN,MAAM,UACN,KAAK,UACL,KAAK,QACL,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,YACL,KAAK,sBACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,uBACL,KAAK,qBACL,KAAK,uBACL,KAAK,YACL,KAAK,oBACL,KAAK,qBACL,KAAK,WACL,KAAK,gBACL,KAAK,mBACL,KAAK,WACL,KAAK,qBACL,KAAK,oBACL,KAAK,sBACL,KAAK,qBACL,KAAK,mBACL,KAAK,qBACL,KAAK,WACL,KAAK,mBACL,KAAK,eACL,KAAK,UACL,KAAK,QACL,KAAK,cACL,KAAK,SACL,KAAK,UACL,MAAM,SACN,KAAK,QACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,MAAM,UACN,KAAK,aACL,KAAK,aACL,KAAK,SACL,KAAK,YACL,KAAK,QACL,KAAK,SACL,KAAK,SACL,KAAK,iBACL,KAAK,YACL,MAAM,SACN,KAAK,QACL,MAAM,UACN,KAAK,QACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,KAAK,oBACL,MAAM,aACN,MAAM,SACN,KAAK,aACL,KAAK,UACL,KAAK,cACL,KAAK,SACL,KAAK,YACL,KAAK,eACL,KAAK,WACL,KAAK,aACL,KAAK,QACL,MAAM,WACN,KAAK,aACL,KAAK,UACL,KAAK,UACL,MAAM,WACN,MAAM,UACN,KAAK,UACL,MAAM,UACN,KAAK,WACL,MAAM,WACN,MAAM,YACN,KAAK,mBACL,MAAM,wBACN,KAAK,qBACL,KAAK,SACL,KAAK,UACL,KAAK,UACL,MAAM,UACN,MAAM,UACN,KAAK,WACL,MAAM,6BACN,MAAM,uBACN,KAAK,WACL,MAAM,aACN,KAAK,YACL,KAAK,aACL,KAAK,oBACL,MAAM,yBACN,KAAK,sBACL,MAAM,qBACN,KAAK,aACL,MAAM,uBACN,KAAK,aACL,MAAM,WACN,KAAK,eACL,KAAK,WACL,MAAM,aACN,KAAK,YACL,MAAM,sBACN,MAAM,WACN,KAAK,eACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,aACL,KAAK,WACL,MAAM,UACN,KAAK,YACL,KAAK,QACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,YACL,MAAM,SACN,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,aACL,MAAM,UACN,KAAK,WACL,KAAK,WACL,KAAK,QACL,MAAM,UACN,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,eACL,KAAK,UACL,KAAK,qBACL,KAAK,UACL,KAAK,SACL,MAAM,SACN,KAAK,SACL,KAAK,QACL,KAAK,QACL,KAAK,YACL,KAAK,QACL,KAAK,UACL,KAAK,YACL,KAAK,iBACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,MAAM,UACN,KAAK,SACL,KAAM,UACN,MAAM,SACN,KAAK,eACL,MAAM,UACN,KAAK,cACL,KAAK,SACL,KAAK,YACL,KAAK,UACL,KAAK,uBACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,KAAK,cACL,KAAK,SACL,KAAK,WACL,KAAK,WACL,KAAK,SACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,wBACL,KAAK,sBACL,KAAK,wBACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,oBACL,KAAK,WACL,KAAK,sBACL,KAAK,qBACL,KAAK,uBACL,KAAK,sBACL,KAAK,oBACL,KAAK,sBACL,KAAK,oBACL,KAAK,oBACL,KAAK,WACL,KAAK,kBACL,KAAK,WACL,KAAK,aACL,KAAK,SACL,KAAK,iBACL,KAAK,YACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,QACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,MAAM,SACN,KAAK,aACL,KAAK,WACL,KAAK,YACL,MAAM,UACN,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,cACL,KAAK,gBACL,KAAK,cACL,KAAK,gBACL,KAAK,WACL,MAAM,UACN,KAAK,YACL,KAAK,YACL,KAAK,cACL,KAAK,UACL,KAAK,YACL,KAAK,iBACL,KAAK,aACL,KAAK,SACL,KAAK,YACL,KAAK,YACL,KAAK,cACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAM,SACN,KAAK,SACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,MAAM,SACN,KAAK,eACL,KAAK,WACL,MAAM,gBACN,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,MAAM,UACN,KAAK,UACL,MAAM,UACN,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,cACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,YACL,MAAM,SACN,KAAK,YACL,KAAK,WACLC,GAAK,YACL,KAAK,gBACL,KAAK,UACL,KAAK,sBACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,MAAM,UACN,KAAK,gBACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,cACL,KAAK,aACL,KAAK,aACL,KAAK,WACL,KAAK,aACL,KAAK,WACL,MAAM,UACN,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,SACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,uBACL,KAAK,YACL,KAAK,YACL,MAAM,SACN,MAAM,UACN,MAAM,UACN,KAAK,YACL,KAAK,WACL,KAAK,YACL,MAAM,SACN,MAAM,UACN,MAAM,UACN,MAAM,SACN,KAAK,QACL,MAAM,UACN,MAAM,UACN,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,MAAM,SACN,MAAM,UACN,MAAM,UACN,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,cACL,MAAM,UACN,KAAK,YACL,KAAK,YACL,KAAK,YACL,MAAM,SACN,KAAK,SACL,KAAK,WACL,KAAK,SACL,KAAK,WACL,MAAM,SACN,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,cACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,mBACL,KAAK,cACL,KAAK,cACL,KAAK,cACL,KAAK,cACL,KAAK,cACL,KAAK,cACL,KAAK,cACL,KAAK,cACL,KAAK,WACL,KAAK,aACL,KAAK,cACL,KAAK,YACL,KAAK,aACL,KAAK,WACL,MAAM,UACN,KAAK,SACL,KAAK,YACL,KAAK,cACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,MAAM,UACN,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,cACL,KAAK,cACL,KAAK,SACL,KAAK,YACL,KAAK,aACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,MAAM,SACN,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,MAAM,SACN,MAAM,aACN,KAAK,UACL,MAAM,UACN,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,MAAM,UACN,KAAK,WACL,MAAM,UACN,KAAK,WACL,KAAK,cACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,SACL,KAAK,YACL,KAAK,cACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,MAAM,UACN,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,aACL,KAAK,UACL,KAAK,aACL,KAAK,UACL,MAAM,SACN,KAAK,UACL,KAAK,eACL,KAAK,SACL,KAAK,SACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,QACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,cACL,KAAK,YACL,KAAK,aACL,KAAK,cACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,gBACL,KAAK,aACL,MAAM,UACN,KAAK,YACL,KAAK,WACL,KAAK,WACL,MAAM,UACN,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,aACL,KAAK,aACL,KAAK,iBACL,KAAK,iBACL,KAAK,oBACL,KAAK,aACL,KAAK,SACL,KAAK,cACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,MAAM,UACN,KAAK,qBACL,KAAK,aACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,SACL,KAAK,oBACL,KAAK,WACL,KAAK,SACL,KAAK,WACL,KAAK,aACL,KAAK,YACL,MAAM,SACN,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,cACL,KAAK,YACLC,GAAK,YACL,MAAM,UACN,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,cACL,KAAK,YACL,MAAM,UACN,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,kBACL,KAAK,aACL,KAAK,UACL,KAAK,cACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,aACL,KAAK,SACL,KAAK,UACL,KAAK,mBACL,MAAM,SACN,KAAK,QACL,KAAK,YACL,KAAK,gBACL,KAAK,YACL,KAAK,QACL,KAAK,cACL,KAAK,SACL,KAAK,iBACL,KAAK,YACL,KAAK,WACL,KAAK,gBACL,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,SACL,KAAK,UACL,KAAK,WACL,MAAM,UACN,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,aACL,KAAK,gBACL,KAAK,YACL,KAAK,aACL,KAAK,aACL,KAAK,cACL,KAAK,kBACL,KAAK,WACL,KAAK,UACL,KAAK,SACL,KAAK,SACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,SACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,MAAM,SACN,KAAK,WACLC,IAAM,WACN,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,MAAM,UACN,KAAK,eACL,KAAK,WACL,KAAK,cACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,MAAM,UACN,KAAK,gBACL,KAAK,YACL,KAAK,WACL,KAAK,eACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,aACL,KAAK,cACL,MAAM,UACN,KAAK,YACL,MAAM,SACN,KAAK,WACL,KAAK,UACL,KAAK,SACL,KAAK,SACL,KAAK,SACL,KAAK,WACL,KAAK,cACL,KAAK,UACL,KAAK,WACL,MAAM,UACN,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,aACL,KAAK,YACL,MAAM,UACN,KAAK,YACL,KAAK,aACL,KAAK,yBACL,KAAK,YACL,KAAK,WACL,KAAK,eACL,KAAK,UACL,KAAK,YACL,MAAM,SACN,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,MAAM,UACN,KAAK,YACL,MAAM,UACN,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,UACL,KAAK,WACL,MAAM,SACN,KAAK,YACL,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,cACL,KAAK,cACL,KAAK,cACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,MAAM,UACN,KAAK,UACL,KAAK,YACL,MAAM,UACN,KAAK,WACL,KAAK,aACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,SACL,MAAM,SACN,KAAK,WACL,MAAM,UACN,MAAM,UACN,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,cACL,KAAK,YACL,KAAK,SACL,MAAM,SACN,KAAK,YACL,KAAK,UACL,KAAK,UACL,MAAM,UACN,MAAM,UACN,KAAK,YACL,KAAK,WACL,KAAK,gBACL,KAAK,UACL,KAAK,YACL,KAAK,cACL,KAAK,YACL,KAAK,WACL,KAAK,gBACL,KAAK,WACL,KAAK,aACL,KAAK,YACL,KAAK,mBACL,KAAK,YACL,KAAK,aACL,KAAK,mBACL,KAAK,SACL,KAAK,YACL,KAAK,UACL,MAAM,WACN,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,aACL,KAAK,aACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,KAAK,UACL,KAAK,aACL,KAAK,cACL,KAAK,UACL,KAAK,SACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,aACL,KAAK,cACL,MAAM,UACN,KAAK,YACL,KAAK,WACL,KAAK,YACL,MAAM,SACN,KAAK,SACL,KAAK,YACL,KAAK,WACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,gBACL,KAAK,WACL,KAAK,cACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,MAAM,UACN,KAAK,YACL,KAAK,aACL,KAAK,YACL,KAAK,aACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,KAAK,WACL,KAAK,YACL,MAAM,UACN,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,aACL,KAAK,YACL,KAAK,kBACL,KAAK,cACL,KAAK,aACL,MAAM,UACN,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,aACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,KAAK,WACL,MAAM,SACN,KAAK,SACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,aACL,KAAK,UACL,KAAK,YACL,MAAM,UACN,MAAM,UACN,KAAK,QACL,KAAK,WACL,MAAM,SACN,MAAM,SACN,KAAK,WACL,KAAK,WACL,MAAM,SACN,MAAM,SACN,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,MAAM,UACN,MAAM,UACN,MAAM,WACN,KAAK,WACL,KAAK,aACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,MAAM,cACN,KAAK,UACL,KAAK,SACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,MAAM,WACN,KAAK,UACL,MAAM,SACN,KAAK,qBACL,KAAK,WACL,KAAK,SACL,KAAK,UACL,KAAK,UACL,MAAM,WACN,KAAK,gBACL,KAAK,UACL,MAAM,UACN,KAAK,SACL,MAAM,YACN,MAAM,cACN,KAAK,aACL,KAAK,aACL,KAAK,aACL,KAAK,aACL,MAAM,YACN,MAAM,WACN,KAAK,aACL,KAAK,iBACL,MAAM,YACN,MAAM,YACN,MAAM,UACN,KAAK,UACL,MAAM,gBACN,KAAK,UACL,MAAM,gBACN,KAAK,YACL,KAAK,QACL,KAAK,SACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,MAAM,UACN,KAAK,YACL,MAAM,UACN,MAAM,UACN,KAAK,aACL,KAAK,YACL,MAAM,UACN,MAAM,UACN,MAAM,aACN,KAAK,YACL,MAAM,aACN,MAAM,WACN,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,YACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,MAAM,SACN,KAAK,UACL,KAAK,YACL,KAAK,SACL,KAAK,WACL,KAAK,WACL,KAAK,aACL,KAAK,SACL,KAAK,WACL,KAAK,WACL,KAAK,aACL,KAAK,UACL,MAAM,UACN,KAAK,UACL,KAAK,WACL,KAAK,SACL,KAAK,SACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,UACL,KAAK,aACL,KAAK,SACL,KAAK,YACL,KAAK,UACL,KAAK,YACL,KAAK,cACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,aACL,MAAM,SACN,KAAK,SACL,KAAK,YACL,KAAK,WACL,KAAK,QACL,KAAK,WACL,KAAK,aACL,KAAK,UACL,KAAK,cACL,KAAK,aACL,KAAK,YACL,KAAK,WACL,KAAK,aACL,KAAK,aACL,KAAK,cACL,MAAM,UACN,KAAK,WACL,KAAK,SACL,KAAK,gBACL,KAAK,WACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,cACL,KAAK,cACL,KAAK,cACL,KAAK,YACL,MAAM,UACN,KAAK,SACL,KAAK,YACL,MAAM,SACN,MAAM,UACN,KAAK,YACL,MAAM,UACN,KAAK,aACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,MAAM,UACN,KAAK,YACL,KAAK,cACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,YACL,KAAK,aACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,aACL,KAAK,oBACL,KAAK,qBACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK,aACL,KAAK,aACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,KAAK,UACL,KAAK,aACL,KAAK,UACL,KAAK,UACL,KAAK,YACL,MAAM,SACN,KAAK,YACL,KAAK,SACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,SACL,KAAK,gBACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,MAAM,UACN,KAAK,YACL,KAAK,aACL,KAAK,UACL,KAAK,YACL,KAAK,cACL,KAAK,YACL,MAAM,UACN,KAAK,YACL,KAAK,mBACL,KAAK,cACL,KAAK,aACL,KAAK,QACL,KAAK,YACL,KAAK,SACL,KAAK,gBACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,cACL,KAAK,iBACL,KAAK,cACL,KAAK,cACL,KAAK,SACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,MAAM,SACN,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,SACL,KAAK,WACL,KAAK,cACL,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,UACL,KAAK,WACL,KAAK,WACL,KAAK,aACL,KAAK,aACL,KAAK,YACL,KAAK,cACL,KAAK,YACL,KAAK,SACL,KAAK,UACL,MAAM,WACN,KAAK,YACL,KAAK,SACL,KAAK,UACL,KAAK,YACL,MAAM,UACN,KAAK,eACL,MAAM,YACN,MAAM,YACN,MAAM,UACN,KAAK,UACL,KAAK,YACL,KAAK,eACL,KAAK,YACL,KAAK,aACL,KAAK,aACL,KAAK,gBACL,KAAK,eACL,KAAK,aACL,KAAK,aACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,UACL,KAAK,eACL,KAAK,YACL,KAAK,aACL,KAAK,aACL,KAAK,aACL,KAAK,aACL,KAAK,aACL,KAAK,aACL,KAAK,gBACL,KAAK,eACL,KAAK,aACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,SACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,KAAK,YACL,MAAM,SACN,KAAK,WACL,KAAK,cACL,KAAK,WACL,KAAK,WACL,KAAK,cACL,KAAK,YACL,KAAK,YACL,KAAK,YACL,MAAM,UACN,KAAK,aACL,KAAK,YACL,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,cACL,KAAK,aACL,KAAK,WACL,KAAK,aACL,KAAK,cACL,MAAM,UACN,KAAK,UACL,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,KAAK,WACL,KAAK,SACL,KAAK,YACL,KAAK,YACL,MAAM,SACN,KAAK,YACL,KAAK,WACL,KAAK,cACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,KAAK,WACL,MAAM,UACN,KAAK,aACL,KAAK,WACL,KAAK,cACL,KAAK,YACL,KAAK,WACL,KAAK,WACL,MAAM,UACN,KAAK,WACL,KAAK,YACL,KAAK,UACL,KAAK,aACL,KAAK,UACL,KAAK,WACL,KAAK,YACL,MAAM,YACN,MAAM,YACN,MAAM,YACN,MAAM,YACN,KAAK,SACL,KAAK,YACL,KAAK,WACL,KAAK,YACL,MAAM,SACN,MAAM,UACN,MAAM,UACN,KAAK,aACL,KAAK,WACL,KAAK,YACL,KAAK,YACL,KAAK,QACL,MAAM,SACN,MAAM,UACN,MAAM,UACN,MAAM,SACN,KAAK,QACL,KAAK,UACL,MAAM,UACN,MAAM,UACN,KAAK,YACL,KAAK,UACL,KAAK,WACL,KAAK,SACL,KAAK,SACL,MAAM,SACN,KAAK,UACL,MAAM,UACN,MAAM,UACN,KAAK,UACL,KAAK,UACL,KAAK,YACL,KAAK,YACL,KAAK,SACL,KAAK,UACL,KAAK,UACL,MAAM,SACN,KAAK,UACL,KAAK,aACL,MAAM,UACN,MAAM,UACN,KAAK,SACL,KAAK,SAAA,EAAA,EAAA;;;;;;;;;ACpyIJC,YAAAC,oBAA4C,EACrD,GAAG,OACH,KAAK,MACL,KAAK,MACL,KAAK,KACL,KAAK,MACL,KAAK,MACL,KAAK,MACL,KAAK,MACL,KAAK,KACL,KAAK,MACL,KAAK,KACL,KAAK,MACL,KAAK,KACL,KAAK,KACL,KAAK,MACL,KAAK,MACL,KAAK,MACL,KAAK,MACL,KAAK,MACL,KAAK,MACL,KAAK,MACL,KAAK,KACL,KAAK,MACL,KAAK,KACL,KAAK,MACL,KAAK,KACL,KAAK,KACL,KAAK,IAAA;;;;;;;;;AC5BIC,YAAAC,gBACTC,OAAOD,iBACP,SAAUE,iBAAAA;AACN,aAAOD,OAAOE,aACVC,KAAKC,OAAOH,kBAAkB,SAAW,IAAA,IAAS,QAChDA,kBAAkB,SAAW,OAAS,KAAA;IAEhD;AAESH,YAAAO,eAAeL,OAAOM,UAAUC,cACvC,SAAUC,OAAeC,UAAAA;AACrB,aAAOD,MAAMD,YAAYE,QAAAA;IAC7B,IACA,SAAUD,OAAeC,UAAAA;AACrB,cAAQD,MAAME,WAAWD,QAAAA,IAAY,SAAU,OAAQD,MAAME,WAAWD,WAAW,CAAA,IAAK,QAAS;IACrG;AAEOX,YAAAa,oBAAoB;AACpBb,YAAAc,kBAAkB;;;;;;;;;;;;;;;;;;;;AClB/B,QAAAC,qBAAAC;AACA,QAAAC,wBAAAD;AACA,QAAAE,oBAAAF;AAEA,QAAMG,qBAAkBC,SAAAA,SAAA,CAAA,GACjBL,mBAAAM,eAAAA,GAAe,EAClBC,KAAKP,mBAAAM,gBAAgBE,MAAAA,CAAAA;AAGzB,aAASC,mBAAmBC,WAAmBC,aAAqBC,eAAAA;AAChED,kBAAYE,YAAY;AACxB,UAAIC,eAAeH,YAAYI,KAAKL,SAAAA;AACpC,UAAIM;AACJ,UAAIF,cAAc;AACdE,wBAAgB;AAChB,YAAIC,mBAAmB;AACvB,WAAG;AACC,cAAIA,qBAAqBH,aAAaI,OAAO;AACzCF,6BAAiBN,UAAUS,UAAUF,kBAAkBH,aAAaI,KAAAA;UAAAA;AAExE,cAAME,eAAeN,aAAa,CAAA;AAClCE,2BAAiBJ,cAAcQ,YAAAA;AAC/BH,6BAAmBH,aAAaI,QAAQE,aAAaC;QAAAA,SAC/CP,eAAeH,YAAYI,KAAKL,SAAAA;AAE1C,YAAIO,qBAAqBP,UAAUW,QAAQ;AACvCL,2BAAiBN,UAAUS,UAAUF,gBAAAA;QAAAA;MAAAA,OAEtC;AACHD,wBAAgBN;MAAAA;AAEpB,aAAOM;IACX;AAqBA,QAAMM,gBAA4C,EAC9CC,cAAc,YACdC,UAAU,8IACVC,mBAAmB,uKACnBC,uBAAuB,kKACvBC,WAAW,qMAAA;AAGf,QAAMC,uBAAsC,EACxCC,MAAM,gBACNC,OAAO,OACPC,SAAS,UAAA;AAIb,aAAgBC,OACZC,MACAC,IAAAA;AAAAA,UAAAC,KAAAD,OAAAA,SAAAN,uBAAAM,IAACE,KAAAD,GAAAN,MAAAA,OAAAO,OAAAA,SAAA,iBAAAA,IAAuBC,KAAAF,GAAAJ,SAAAA,UAAAM,OAAAA,SAAA,YAAAA,IAAqBC,KAAAH,GAAAL,OAAAA,QAAAQ,OAAAA,SAAA,QAAAA;AAE7C,UAAA,CAAKL,MAAM;AACP,eAAO;MAAA;AAGX,UAAMM,eAAejB,cAAcO,IAAAA;AACnC,UAAMW,aAAapC,mBAAmB0B,KAAAA,EAAOW;AAC7C,UAAMC,QAAQX,YAAY;AAE1B,aAAOtB,mBAAmBwB,MAAMM,cAAc,SAACI,OAAAA;AAC3C,YAAIC,SAASJ,WAAWG,KAAAA;AACxB,YAAA,CAAKC,QAAQ;AACT,cAAMC,OAAOF,MAAMtB,SAAS,IAAIlB,kBAAA2C,aAAaH,OAAO,CAAA,IAAMA,MAAMI,WAAW,CAAA;AAC3EH,oBAAUF,QAAQ,QAAQG,KAAKG,SAAS,EAAA,IAAM,OAAOH,QAAQ;QAAA;AAEjE,eAAOD;MACX,CAAA;IACJ;AApBAK,YAAAjB,SAAAA;AAsBA,QAAMkB,uBAAsC,EACxCC,OAAO,QACPrB,OAAO,MAAA;AAGX,QAAMsB,SAAS;AACf,QAAMC,YAAY;AAElB,QAAMC,oBAAgF,EAClFC,KAAK,EACDH,QACAC,WACAG,MAAMxD,mBAAAyD,YAAYF,IAAAA,GAEtBG,OAAO,EACHN,QACAC,WACAG,MAAMxD,mBAAAyD,YAAYC,MAAAA,GAEtBlD,OAAO,EACH4C,QACAC,WACAG,MAAMxD,mBAAAyD,YAAYjD,MAAAA,EAAAA;AAI1B,QAAMmD,gBAAatD,SAAAA,SAAA,CAAA,GACZiD,iBAAAA,GAAiB,EACpB/C,KAAK+C,kBAAkB9C,MAAAA,CAAAA;AAG3B,QAAMoD,eAAeC,OAAOD;AAC5B,QAAME,kBAAkBF,aAAa,KAAA;AAErC,QAAMG,6BAA4C,EAC9CjC,OAAO,MAAA;AAGX,aAASkC,iBACLC,QACAzB,YACA0B,aACAC,UAAAA;AAEA,UAAIC,eAAeH;AACnB,UAAMI,uBAAuBJ,OAAOA,OAAO5C,SAAS,CAAA;AACpD,UAAI6C,eAAeG,yBAAyB,KAAK;AAC7CD,uBAAeH;MAAAA,WACRE,YAAYE,yBAAyB,KAAK;AACjDD,uBAAeH;MAAAA,OACZ;AACH,YAAMK,0BAA0B9B,WAAWyB,MAAAA;AAC3C,YAAIK,yBAAyB;AACzBF,yBAAeE;QAAAA,WACRL,OAAO,CAAA,MAAO,OAAOA,OAAO,CAAA,MAAO,KAAK;AAC/C,cAAMM,mBAAmBN,OAAO,CAAA;AAChC,cAAMO,aACFD,oBAAoB,OAAOA,oBAAoB,MACzCE,SAASR,OAAOS,OAAO,CAAA,GAAI,EAAA,IAC3BD,SAASR,OAAOS,OAAO,CAAA,CAAA;AAEjCN,yBACII,cAAc,UACRV,kBACAU,aAAa,QACbrE,kBAAAwE,cAAcH,UAAAA,IACdZ,aAAa1D,sBAAA0E,kBAAkBJ,UAAAA,KAAeA,UAAAA;QAAAA;MAAAA;AAGhE,aAAOJ;IACX;AAGA,aAAgBS,aACZZ,QACA/B,IAAAA;AAAAA,UAACC,MAAAD,OAAAA,SAAA6B,6BAAA7B,IAAAJ,OAAAA,QAAAK,OAAAA,SAAA,QAAAA;AAED,UAAA,CAAK8B,QAAQ;AACT,eAAO;MAAA;AAEX,aAAOD,iBAAiBC,QAAQ7D,mBAAmB0B,KAAAA,EAAOgD,UAAU,OAAO,KAAA;IAC/E;AARA7B,YAAA4B,eAAAA;AAWA,aAAgBE,OACZ9C,MACAC,IAAAA;AAAAA,UAAAC,KAAAD,OAAAA,SAAAgB,uBAAAhB,IAACE,KAAAD,GAAAL,OAAAA,QAAAM,OAAAA,SAAA,QAAAA,IAAeC,KAAAF,GAAAgB,OAAAA,QAAAd,OAAAA,SAAAP,UAAA,QAAA,WAAA,SAAAO;AAEhB,UAAA,CAAKJ,MAAM;AACP,eAAO;MAAA;AAGX,UAAM+C,eAAerB,cAAc7B,KAAAA,EAAOqB,KAAAA;AAC1C,UAAMX,aAAapC,mBAAmB0B,KAAAA,EAAOgD;AAC7C,UAAMZ,cAAcf,UAAU;AAC9B,UAAMgB,WAAWhB,UAAU;AAE3B,aAAO1C,mBAAmBwB,MAAM+C,cAAc,SAACf,QAAAA;AAC3C,eAAAD,iBAAiBC,QAAQzB,YAAY0B,aAAaC,QAAAA;MAAlD,CAAA;IAER;AAhBAlB,YAAA8B,SAAAA;;;", "names": ["exports", "bodyRegExps", "xml", "html4", "html5", "namedReferences", "entities", "characters", "_", "$", "fj", "exports", "numericUnicodeMap", "exports", "fromCodePoint", "String", "astralCodePoint", "fromCharCode", "Math", "floor", "getCodePoint", "prototype", "codePointAt", "input", "position", "charCodeAt", "highSurrogateFrom", "highSurrogateTo", "named_references_1", "require", "numeric_unicode_map_1", "surrogate_pairs_1", "allNamedReferences", "__assign", "namedReferences", "all", "html5", "replaceUsingRegExp", "macroText", "macroRegExp", "macroReplacer", "lastIndex", "replaceMatch", "exec", "replaceResult", "replaceLastIndex", "index", "substring", "replaceInput", "length", "encodeRegExps", "specialChars", "non<PERSON><PERSON><PERSON>", "nonAsciiPrintable", "nonAsciiPrintableOnly", "extensive", "defaultEncodeOptions", "mode", "level", "numeric", "encode", "text", "_a", "_b", "_c", "_d", "_e", "encodeRegExp", "references", "characters", "isHex", "input", "result", "code", "getCodePoint", "charCodeAt", "toString", "exports", "defaultDecodeOptions", "scope", "strict", "attribute", "baseDecodeRegExps", "xml", "body", "bodyRegExps", "html4", "decodeRegExps", "fromCharCode", "String", "outOfBoundsChar", "defaultDecodeEntityOptions", "getDecodedEntity", "entity", "isAttribute", "isStrict", "decodeResult", "decodeEntityLastChar", "decodeResultByReference", "decodeSecondChar", "decodeCode", "parseInt", "substr", "fromCodePoint", "numericUnicodeMap", "decodeEntity", "entities", "decode", "decodeRegExp"]}