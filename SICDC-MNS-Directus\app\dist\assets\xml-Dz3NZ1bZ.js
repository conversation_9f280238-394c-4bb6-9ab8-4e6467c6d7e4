import{g as s}from"./index-C0qcAVKU.js";import{a as l}from"./index.DUmRo3Ep.entry.js";function i(t,n){for(var o=0;o<n.length;o++){const e=n[o];if(typeof e!="string"&&!Array.isArray(e)){for(const r in e)if(r!=="default"&&!(r in t)){const a=Object.getOwnPropertyDescriptor(e,r);a&&Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:()=>e[r]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}var f=l();const c=s(f),u=i({__proto__:null,default:c},[f]);export{u as x};
