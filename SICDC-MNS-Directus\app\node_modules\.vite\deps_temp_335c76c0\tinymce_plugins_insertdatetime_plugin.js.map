{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/insertdatetime/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const option = name => editor => editor.options.get(name);\n    const register$2 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('insertdatetime_dateformat', {\n        processor: 'string',\n        default: editor.translate('%Y-%m-%d')\n      });\n      registerOption('insertdatetime_timeformat', {\n        processor: 'string',\n        default: editor.translate('%H:%M:%S')\n      });\n      registerOption('insertdatetime_formats', {\n        processor: 'string[]',\n        default: [\n          '%H:%M:%S',\n          '%Y-%m-%d',\n          '%I:%M:%S %p',\n          '%D'\n        ]\n      });\n      registerOption('insertdatetime_element', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const getDateFormat = option('insertdatetime_dateformat');\n    const getTimeFormat = option('insertdatetime_timeformat');\n    const getFormats = option('insertdatetime_formats');\n    const shouldInsertTimeElement = option('insertdatetime_element');\n    const getDefaultDateTime = editor => {\n      const formats = getFormats(editor);\n      return formats.length > 0 ? formats[0] : getTimeFormat(editor);\n    };\n\n    const daysShort = 'Sun Mon Tue Wed Thu Fri Sat Sun'.split(' ');\n    const daysLong = 'Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday'.split(' ');\n    const monthsShort = 'Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec'.split(' ');\n    const monthsLong = 'January February March April May June July August September October November December'.split(' ');\n    const addZeros = (value, len) => {\n      value = '' + value;\n      if (value.length < len) {\n        for (let i = 0; i < len - value.length; i++) {\n          value = '0' + value;\n        }\n      }\n      return value;\n    };\n    const getDateTime = (editor, fmt, date = new Date()) => {\n      fmt = fmt.replace('%D', '%m/%d/%Y');\n      fmt = fmt.replace('%r', '%I:%M:%S %p');\n      fmt = fmt.replace('%Y', '' + date.getFullYear());\n      fmt = fmt.replace('%y', '' + date.getYear());\n      fmt = fmt.replace('%m', addZeros(date.getMonth() + 1, 2));\n      fmt = fmt.replace('%d', addZeros(date.getDate(), 2));\n      fmt = fmt.replace('%H', '' + addZeros(date.getHours(), 2));\n      fmt = fmt.replace('%M', '' + addZeros(date.getMinutes(), 2));\n      fmt = fmt.replace('%S', '' + addZeros(date.getSeconds(), 2));\n      fmt = fmt.replace('%I', '' + ((date.getHours() + 11) % 12 + 1));\n      fmt = fmt.replace('%p', '' + (date.getHours() < 12 ? 'AM' : 'PM'));\n      fmt = fmt.replace('%B', '' + editor.translate(monthsLong[date.getMonth()]));\n      fmt = fmt.replace('%b', '' + editor.translate(monthsShort[date.getMonth()]));\n      fmt = fmt.replace('%A', '' + editor.translate(daysLong[date.getDay()]));\n      fmt = fmt.replace('%a', '' + editor.translate(daysShort[date.getDay()]));\n      fmt = fmt.replace('%%', '%');\n      return fmt;\n    };\n    const updateElement = (editor, timeElm, computerTime, userTime) => {\n      const newTimeElm = editor.dom.create('time', { datetime: computerTime }, userTime);\n      editor.dom.replace(newTimeElm, timeElm);\n      editor.selection.select(newTimeElm, true);\n      editor.selection.collapse(false);\n    };\n    const insertDateTime = (editor, format) => {\n      if (shouldInsertTimeElement(editor)) {\n        const userTime = getDateTime(editor, format);\n        let computerTime;\n        if (/%[HMSIp]/.test(format)) {\n          computerTime = getDateTime(editor, '%Y-%m-%dT%H:%M');\n        } else {\n          computerTime = getDateTime(editor, '%Y-%m-%d');\n        }\n        const timeElm = editor.dom.getParent(editor.selection.getStart(), 'time');\n        if (timeElm) {\n          updateElement(editor, timeElm, computerTime, userTime);\n        } else {\n          editor.insertContent('<time datetime=\"' + computerTime + '\">' + userTime + '</time>');\n        }\n      } else {\n        editor.insertContent(getDateTime(editor, format));\n      }\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mceInsertDate', (_ui, value) => {\n        insertDateTime(editor, value !== null && value !== void 0 ? value : getDateFormat(editor));\n      });\n      editor.addCommand('mceInsertTime', (_ui, value) => {\n        insertDateTime(editor, value !== null && value !== void 0 ? value : getTimeFormat(editor));\n      });\n    };\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const onSetupEditable = editor => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const register = editor => {\n      const formats = getFormats(editor);\n      const defaultFormat = Cell(getDefaultDateTime(editor));\n      const insertDateTime = format => editor.execCommand('mceInsertDate', false, format);\n      editor.ui.registry.addSplitButton('insertdatetime', {\n        icon: 'insert-time',\n        tooltip: 'Insert date/time',\n        select: value => value === defaultFormat.get(),\n        fetch: done => {\n          done(global.map(formats, format => ({\n            type: 'choiceitem',\n            text: getDateTime(editor, format),\n            value: format\n          })));\n        },\n        onAction: _api => {\n          insertDateTime(defaultFormat.get());\n        },\n        onItemAction: (_api, value) => {\n          defaultFormat.set(value);\n          insertDateTime(value);\n        },\n        onSetup: onSetupEditable(editor)\n      });\n      const makeMenuItemHandler = format => () => {\n        defaultFormat.set(format);\n        insertDateTime(format);\n      };\n      editor.ui.registry.addNestedMenuItem('insertdatetime', {\n        icon: 'insert-time',\n        text: 'Date/time',\n        getSubmenuItems: () => global.map(formats, format => ({\n          type: 'menuitem',\n          text: getDateTime(editor, format),\n          onAction: makeMenuItemHandler(format)\n        })),\n        onSetup: onSetupEditable(editor)\n      });\n    };\n\n    var Plugin = () => {\n      global$1.add('insertdatetime', editor => {\n        register$2(editor);\n        register$1(editor);\n        register(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,QAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,QAAM,aAAa,YAAU;AAC3B,UAAM,iBAAiB,OAAO,QAAQ;AACtC,mBAAe,6BAA6B;AAAA,MAC1C,WAAW;AAAA,MACX,SAAS,OAAO,UAAU,UAAU;AAAA,IACtC,CAAC;AACD,mBAAe,6BAA6B;AAAA,MAC1C,WAAW;AAAA,MACX,SAAS,OAAO,UAAU,UAAU;AAAA,IACtC,CAAC;AACD,mBAAe,0BAA0B;AAAA,MACvC,WAAW;AAAA,MACX,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,mBAAe,0BAA0B;AAAA,MACvC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,OAAO,2BAA2B;AACxD,QAAM,gBAAgB,OAAO,2BAA2B;AACxD,QAAM,aAAa,OAAO,wBAAwB;AAClD,QAAM,0BAA0B,OAAO,wBAAwB;AAC/D,QAAM,qBAAqB,YAAU;AACnC,UAAM,UAAU,WAAW,MAAM;AACjC,WAAO,QAAQ,SAAS,IAAI,QAAQ,CAAC,IAAI,cAAc,MAAM;AAAA,EAC/D;AAEA,QAAM,YAAY,kCAAkC,MAAM,GAAG;AAC7D,QAAM,WAAW,kEAAkE,MAAM,GAAG;AAC5F,QAAM,cAAc,kDAAkD,MAAM,GAAG;AAC/E,QAAM,aAAa,wFAAwF,MAAM,GAAG;AACpH,QAAM,WAAW,CAAC,OAAO,QAAQ;AAC/B,YAAQ,KAAK;AACb,QAAI,MAAM,SAAS,KAAK;AACtB,eAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,gBAAQ,MAAM;AAAA,MAChB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAc,CAAC,QAAQ,KAAK,OAAO,oBAAI,KAAK,MAAM;AACtD,UAAM,IAAI,QAAQ,MAAM,UAAU;AAClC,UAAM,IAAI,QAAQ,MAAM,aAAa;AACrC,UAAM,IAAI,QAAQ,MAAM,KAAK,KAAK,YAAY,CAAC;AAC/C,UAAM,IAAI,QAAQ,MAAM,KAAK,KAAK,QAAQ,CAAC;AAC3C,UAAM,IAAI,QAAQ,MAAM,SAAS,KAAK,SAAS,IAAI,GAAG,CAAC,CAAC;AACxD,UAAM,IAAI,QAAQ,MAAM,SAAS,KAAK,QAAQ,GAAG,CAAC,CAAC;AACnD,UAAM,IAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,SAAS,GAAG,CAAC,CAAC;AACzD,UAAM,IAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,WAAW,GAAG,CAAC,CAAC;AAC3D,UAAM,IAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,WAAW,GAAG,CAAC,CAAC;AAC3D,UAAM,IAAI,QAAQ,MAAM,OAAO,KAAK,SAAS,IAAI,MAAM,KAAK,EAAE;AAC9D,UAAM,IAAI,QAAQ,MAAY,KAAK,SAAS,IAAI,KAAK,OAAO,IAAK;AACjE,UAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC;AAC1E,UAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC;AAC3E,UAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,SAAS,KAAK,OAAO,CAAC,CAAC,CAAC;AACtE,UAAM,IAAI,QAAQ,MAAM,KAAK,OAAO,UAAU,UAAU,KAAK,OAAO,CAAC,CAAC,CAAC;AACvE,UAAM,IAAI,QAAQ,MAAM,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,QAAM,gBAAgB,CAAC,QAAQ,SAAS,cAAc,aAAa;AACjE,UAAM,aAAa,OAAO,IAAI,OAAO,QAAQ,EAAE,UAAU,aAAa,GAAG,QAAQ;AACjF,WAAO,IAAI,QAAQ,YAAY,OAAO;AACtC,WAAO,UAAU,OAAO,YAAY,IAAI;AACxC,WAAO,UAAU,SAAS,KAAK;AAAA,EACjC;AACA,QAAM,iBAAiB,CAAC,QAAQ,WAAW;AACzC,QAAI,wBAAwB,MAAM,GAAG;AACnC,YAAM,WAAW,YAAY,QAAQ,MAAM;AAC3C,UAAI;AACJ,UAAI,WAAW,KAAK,MAAM,GAAG;AAC3B,uBAAe,YAAY,QAAQ,gBAAgB;AAAA,MACrD,OAAO;AACL,uBAAe,YAAY,QAAQ,UAAU;AAAA,MAC/C;AACA,YAAM,UAAU,OAAO,IAAI,UAAU,OAAO,UAAU,SAAS,GAAG,MAAM;AACxE,UAAI,SAAS;AACX,sBAAc,QAAQ,SAAS,cAAc,QAAQ;AAAA,MACvD,OAAO;AACL,eAAO,cAAc,qBAAqB,eAAe,OAAO,WAAW,SAAS;AAAA,MACtF;AAAA,IACF,OAAO;AACL,aAAO,cAAc,YAAY,QAAQ,MAAM,CAAC;AAAA,IAClD;AAAA,EACF;AAEA,QAAM,aAAa,YAAU;AAC3B,WAAO,WAAW,iBAAiB,CAAC,KAAK,UAAU;AACjD,qBAAe,QAAQ,UAAU,QAAQ,UAAU,SAAS,QAAQ,cAAc,MAAM,CAAC;AAAA,IAC3F,CAAC;AACD,WAAO,WAAW,iBAAiB,CAAC,KAAK,UAAU;AACjD,qBAAe,QAAQ,UAAU,QAAQ,UAAU,SAAS,QAAQ,cAAc,MAAM,CAAC;AAAA,IAC3F,CAAC;AAAA,EACH;AAEA,QAAM,OAAO,aAAW;AACtB,QAAI,QAAQ;AACZ,UAAM,MAAM,MAAM;AAChB,aAAO;AAAA,IACT;AACA,UAAM,MAAM,OAAK;AACf,cAAQ;AAAA,IACV;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE5D,QAAM,kBAAkB,YAAU,SAAO;AACvC,UAAM,cAAc,MAAM;AACxB,UAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,IAC9C;AACA,WAAO,GAAG,cAAc,WAAW;AACnC,gBAAY;AACZ,WAAO,MAAM;AACX,aAAO,IAAI,cAAc,WAAW;AAAA,IACtC;AAAA,EACF;AACA,QAAM,WAAW,YAAU;AACzB,UAAM,UAAU,WAAW,MAAM;AACjC,UAAM,gBAAgB,KAAK,mBAAmB,MAAM,CAAC;AACrD,UAAMA,kBAAiB,YAAU,OAAO,YAAY,iBAAiB,OAAO,MAAM;AAClF,WAAO,GAAG,SAAS,eAAe,kBAAkB;AAAA,MAClD,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ,WAAS,UAAU,cAAc,IAAI;AAAA,MAC7C,OAAO,UAAQ;AACb,aAAK,OAAO,IAAI,SAAS,aAAW;AAAA,UAClC,MAAM;AAAA,UACN,MAAM,YAAY,QAAQ,MAAM;AAAA,UAChC,OAAO;AAAA,QACT,EAAE,CAAC;AAAA,MACL;AAAA,MACA,UAAU,UAAQ;AAChB,QAAAA,gBAAe,cAAc,IAAI,CAAC;AAAA,MACpC;AAAA,MACA,cAAc,CAAC,MAAM,UAAU;AAC7B,sBAAc,IAAI,KAAK;AACvB,QAAAA,gBAAe,KAAK;AAAA,MACtB;AAAA,MACA,SAAS,gBAAgB,MAAM;AAAA,IACjC,CAAC;AACD,UAAM,sBAAsB,YAAU,MAAM;AAC1C,oBAAc,IAAI,MAAM;AACxB,MAAAA,gBAAe,MAAM;AAAA,IACvB;AACA,WAAO,GAAG,SAAS,kBAAkB,kBAAkB;AAAA,MACrD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,iBAAiB,MAAM,OAAO,IAAI,SAAS,aAAW;AAAA,QACpD,MAAM;AAAA,QACN,MAAM,YAAY,QAAQ,MAAM;AAAA,QAChC,UAAU,oBAAoB,MAAM;AAAA,MACtC,EAAE;AAAA,MACF,SAAS,gBAAgB,MAAM;AAAA,IACjC,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,MAAM;AACjB,aAAS,IAAI,kBAAkB,YAAU;AACvC,iBAAW,MAAM;AACjB,iBAAW,MAAM;AACjB,eAAS,MAAM;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,SAAO;AAEX,GAAG;", "names": ["insertDateTime"]}