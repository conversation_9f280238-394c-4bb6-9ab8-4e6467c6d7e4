{"version": 3, "sources": ["../../../../node_modules/.pnpm/wellknown@0.5.0/node_modules/wellknown/index.js"], "sourcesContent": ["/*eslint-disable no-cond-assign */\nmodule.exports = parse;\nmodule.exports.parse = parse;\nmodule.exports.stringify = stringify;\n\nvar numberRegexp = /[-+]?([0-9]*\\.[0-9]+|[0-9]+)([eE][-+]?[0-9]+)?/;\n// Matches sequences like '100 100' or '100 100 100'.\nvar tuples = new RegExp('^' + numberRegexp.source + '(\\\\s' + numberRegexp.source + '){1,}');\n\n/*\n * Parse WKT and return GeoJSON.\n *\n * @param {string} _ A WKT geometry\n * @return {?Object} A GeoJSON geometry object\n */\nfunction parse (input) {\n  var parts = input.split(';');\n  var _ = parts.pop();\n  var srid = (parts.shift() || '').split('=').pop();\n\n  var i = 0;\n\n  function $ (re) {\n    var match = _.substring(i).match(re);\n    if (!match) return null;\n    else {\n      i += match[0].length;\n      return match[0];\n    }\n  }\n\n  function crs (obj) {\n    if (obj && srid.match(/\\d+/)) {\n      obj.crs = {\n        type: 'name',\n        properties: {\n          name: 'urn:ogc:def:crs:EPSG::' + srid\n        }\n      };\n    }\n\n    return obj;\n  }\n\n  function white () { $(/^\\s*/); }\n\n  function multicoords () {\n    white();\n    var depth = 0;\n    var rings = [];\n    var stack = [rings];\n    var pointer = rings;\n    var elem;\n\n    while (elem =\n           $(/^(\\()/) ||\n             $(/^(\\))/) ||\n               $(/^(,)/) ||\n                 $(tuples)) {\n      if (elem === '(') {\n        stack.push(pointer);\n        pointer = [];\n        stack[stack.length - 1].push(pointer);\n        depth++;\n      } else if (elem === ')') {\n        // For the case: Polygon(), ...\n        if (pointer.length === 0) return null;\n\n        pointer = stack.pop();\n        // the stack was empty, input was malformed\n        if (!pointer) return null;\n        depth--;\n        if (depth === 0) break;\n      } else if (elem === ',') {\n        pointer = [];\n        stack[stack.length - 1].push(pointer);\n      } else if (!elem.split(/\\s/g).some(isNaN)) {\n        Array.prototype.push.apply(pointer, elem.split(/\\s/g).map(parseFloat));\n      } else {\n        return null;\n      }\n      white();\n    }\n\n    if (depth !== 0) return null;\n\n    return rings;\n  }\n\n  function coords () {\n    var list = [];\n    var item;\n    var pt;\n    while (pt =\n           $(tuples) ||\n             $(/^(,)/)) {\n      if (pt === ',') {\n        list.push(item);\n        item = [];\n      } else if (!pt.split(/\\s/g).some(isNaN)) {\n        if (!item) item = [];\n        Array.prototype.push.apply(item, pt.split(/\\s/g).map(parseFloat));\n      }\n      white();\n    }\n\n    if (item) list.push(item);\n    else return null;\n\n    return list.length ? list : null;\n  }\n\n  function point () {\n    if (!$(/^(point(\\sz)?)/i)) return null;\n    white();\n    if (!$(/^(\\()/)) return null;\n    var c = coords();\n    if (!c) return null;\n    white();\n    if (!$(/^(\\))/)) return null;\n    return {\n      type: 'Point',\n      coordinates: c[0]\n    };\n  }\n\n  function multipoint () {\n    if (!$(/^(multipoint)/i)) return null;\n    white();\n    var newCoordsFormat = _\n      .substring(_.indexOf('(') + 1, _.length - 1)\n      .replace(/\\(/g, '')\n      .replace(/\\)/g, '');\n    _ = 'MULTIPOINT (' + newCoordsFormat + ')';\n    var c = multicoords();\n    if (!c) return null;\n    white();\n    return {\n      type: 'MultiPoint',\n      coordinates: c\n    };\n  }\n\n  function multilinestring () {\n    if (!$(/^(multilinestring)/i)) return null;\n    white();\n    var c = multicoords();\n    if (!c) return null;\n    white();\n    return {\n      type: 'MultiLineString',\n      coordinates: c\n    };\n  }\n\n  function linestring () {\n    if (!$(/^(linestring(\\sz)?)/i)) return null;\n    white();\n    if (!$(/^(\\()/)) return null;\n    var c = coords();\n    if (!c) return null;\n    if (!$(/^(\\))/)) return null;\n    return {\n      type: 'LineString',\n      coordinates: c\n    };\n  }\n\n  function polygon () {\n    if (!$(/^(polygon(\\sz)?)/i)) return null;\n    white();\n    var c = multicoords();\n    if (!c) return null;\n    return {\n      type: 'Polygon',\n      coordinates: c\n    };\n  }\n\n  function multipolygon () {\n    if (!$(/^(multipolygon)/i)) return null;\n    white();\n    var c = multicoords();\n    if (!c) return null;\n    return {\n      type: 'MultiPolygon',\n      coordinates: c\n    };\n  }\n\n  function geometrycollection () {\n    var geometries = [];\n    var geometry;\n\n    if (!$(/^(geometrycollection)/i)) return null;\n    white();\n\n    if (!$(/^(\\()/)) return null;\n    while (geometry = root()) {\n      geometries.push(geometry);\n      white();\n      $(/^(,)/);\n      white();\n    }\n    if (!$(/^(\\))/)) return null;\n\n    return {\n      type: 'GeometryCollection',\n      geometries: geometries\n    };\n  }\n\n  function root () {\n    return point() ||\n      linestring() ||\n      polygon() ||\n      multipoint() ||\n      multilinestring() ||\n      multipolygon() ||\n      geometrycollection();\n  }\n\n  return crs(root());\n}\n\n/**\n * Stringifies a GeoJSON object into WKT\n */\nfunction stringify (gj) {\n  if (gj.type === 'Feature') {\n    gj = gj.geometry;\n  }\n\n  function pairWKT (c) {\n    return c.join(' ');\n  }\n\n  function ringWKT (r) {\n    return r.map(pairWKT).join(', ');\n  }\n\n  function ringsWKT (r) {\n    return r.map(ringWKT).map(wrapParens).join(', ');\n  }\n\n  function multiRingsWKT (r) {\n    return r.map(ringsWKT).map(wrapParens).join(', ');\n  }\n\n  function wrapParens (s) { return '(' + s + ')'; }\n\n  switch (gj.type) {\n    case 'Point':\n      return 'POINT (' + pairWKT(gj.coordinates) + ')';\n    case 'LineString':\n      return 'LINESTRING (' + ringWKT(gj.coordinates) + ')';\n    case 'Polygon':\n      return 'POLYGON (' + ringsWKT(gj.coordinates) + ')';\n    case 'MultiPoint':\n      return 'MULTIPOINT (' + ringWKT(gj.coordinates) + ')';\n    case 'MultiPolygon':\n      return 'MULTIPOLYGON (' + multiRingsWKT(gj.coordinates) + ')';\n    case 'MultiLineString':\n      return 'MULTILINESTRING (' + ringsWKT(gj.coordinates) + ')';\n    case 'GeometryCollection':\n      return 'GEOMETRYCOLLECTION (' + gj.geometries.map(stringify).join(', ') + ')';\n    default:\n      throw new Error('stringify requires a valid GeoJSON Feature or geometry object as input');\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AACA,WAAO,UAAU;AACjB,WAAO,QAAQ,QAAQ;AACvB,WAAO,QAAQ,YAAY;AAE3B,QAAI,eAAe;AAEnB,QAAI,SAAS,IAAI,OAAO,MAAM,aAAa,SAAS,SAAS,aAAa,SAAS,OAAO;AAQ1F,aAAS,MAAO,OAAO;AACrB,UAAI,QAAQ,MAAM,MAAM,GAAG;AAC3B,UAAI,IAAI,MAAM,IAAI;AAClB,UAAI,QAAQ,MAAM,MAAM,KAAK,IAAI,MAAM,GAAG,EAAE,IAAI;AAEhD,UAAI,IAAI;AAER,eAAS,EAAG,IAAI;AACd,YAAI,QAAQ,EAAE,UAAU,CAAC,EAAE,MAAM,EAAE;AACnC,YAAI,CAAC;AAAO,iBAAO;AAAA,aACd;AACH,eAAK,MAAM,CAAC,EAAE;AACd,iBAAO,MAAM,CAAC;AAAA,QAChB;AAAA,MACF;AAEA,eAAS,IAAK,KAAK;AACjB,YAAI,OAAO,KAAK,MAAM,KAAK,GAAG;AAC5B,cAAI,MAAM;AAAA,YACR,MAAM;AAAA,YACN,YAAY;AAAA,cACV,MAAM,2BAA2B;AAAA,YACnC;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,QAAS;AAAE,UAAE,MAAM;AAAA,MAAG;AAE/B,eAAS,cAAe;AACtB,cAAM;AACN,YAAI,QAAQ;AACZ,YAAI,QAAQ,CAAC;AACb,YAAI,QAAQ,CAAC,KAAK;AAClB,YAAI,UAAU;AACd,YAAI;AAEJ,eAAO,OACA,EAAE,OAAO,KACP,EAAE,OAAO,KACP,EAAE,MAAM,KACN,EAAE,MAAM,GAAG;AACtB,cAAI,SAAS,KAAK;AAChB,kBAAM,KAAK,OAAO;AAClB,sBAAU,CAAC;AACX,kBAAM,MAAM,SAAS,CAAC,EAAE,KAAK,OAAO;AACpC;AAAA,UACF,WAAW,SAAS,KAAK;AAEvB,gBAAI,QAAQ,WAAW;AAAG,qBAAO;AAEjC,sBAAU,MAAM,IAAI;AAEpB,gBAAI,CAAC;AAAS,qBAAO;AACrB;AACA,gBAAI,UAAU;AAAG;AAAA,UACnB,WAAW,SAAS,KAAK;AACvB,sBAAU,CAAC;AACX,kBAAM,MAAM,SAAS,CAAC,EAAE,KAAK,OAAO;AAAA,UACtC,WAAW,CAAC,KAAK,MAAM,KAAK,EAAE,KAAK,KAAK,GAAG;AACzC,kBAAM,UAAU,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,EAAE,IAAI,UAAU,CAAC;AAAA,UACvE,OAAO;AACL,mBAAO;AAAA,UACT;AACA,gBAAM;AAAA,QACR;AAEA,YAAI,UAAU;AAAG,iBAAO;AAExB,eAAO;AAAA,MACT;AAEA,eAAS,SAAU;AACjB,YAAI,OAAO,CAAC;AACZ,YAAI;AACJ,YAAI;AACJ,eAAO,KACA,EAAE,MAAM,KACN,EAAE,MAAM,GAAG;AAClB,cAAI,OAAO,KAAK;AACd,iBAAK,KAAK,IAAI;AACd,mBAAO,CAAC;AAAA,UACV,WAAW,CAAC,GAAG,MAAM,KAAK,EAAE,KAAK,KAAK,GAAG;AACvC,gBAAI,CAAC;AAAM,qBAAO,CAAC;AACnB,kBAAM,UAAU,KAAK,MAAM,MAAM,GAAG,MAAM,KAAK,EAAE,IAAI,UAAU,CAAC;AAAA,UAClE;AACA,gBAAM;AAAA,QACR;AAEA,YAAI;AAAM,eAAK,KAAK,IAAI;AAAA;AACnB,iBAAO;AAEZ,eAAO,KAAK,SAAS,OAAO;AAAA,MAC9B;AAEA,eAAS,QAAS;AAChB,YAAI,CAAC,EAAE,iBAAiB;AAAG,iBAAO;AAClC,cAAM;AACN,YAAI,CAAC,EAAE,OAAO;AAAG,iBAAO;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,CAAC;AAAG,iBAAO;AACf,cAAM;AACN,YAAI,CAAC,EAAE,OAAO;AAAG,iBAAO;AACxB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,aAAa,EAAE,CAAC;AAAA,QAClB;AAAA,MACF;AAEA,eAAS,aAAc;AACrB,YAAI,CAAC,EAAE,gBAAgB;AAAG,iBAAO;AACjC,cAAM;AACN,YAAI,kBAAkB,EACnB,UAAU,EAAE,QAAQ,GAAG,IAAI,GAAG,EAAE,SAAS,CAAC,EAC1C,QAAQ,OAAO,EAAE,EACjB,QAAQ,OAAO,EAAE;AACpB,YAAI,iBAAiB,kBAAkB;AACvC,YAAI,IAAI,YAAY;AACpB,YAAI,CAAC;AAAG,iBAAO;AACf,cAAM;AACN,eAAO;AAAA,UACL,MAAM;AAAA,UACN,aAAa;AAAA,QACf;AAAA,MACF;AAEA,eAAS,kBAAmB;AAC1B,YAAI,CAAC,EAAE,qBAAqB;AAAG,iBAAO;AACtC,cAAM;AACN,YAAI,IAAI,YAAY;AACpB,YAAI,CAAC;AAAG,iBAAO;AACf,cAAM;AACN,eAAO;AAAA,UACL,MAAM;AAAA,UACN,aAAa;AAAA,QACf;AAAA,MACF;AAEA,eAAS,aAAc;AACrB,YAAI,CAAC,EAAE,sBAAsB;AAAG,iBAAO;AACvC,cAAM;AACN,YAAI,CAAC,EAAE,OAAO;AAAG,iBAAO;AACxB,YAAI,IAAI,OAAO;AACf,YAAI,CAAC;AAAG,iBAAO;AACf,YAAI,CAAC,EAAE,OAAO;AAAG,iBAAO;AACxB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,aAAa;AAAA,QACf;AAAA,MACF;AAEA,eAAS,UAAW;AAClB,YAAI,CAAC,EAAE,mBAAmB;AAAG,iBAAO;AACpC,cAAM;AACN,YAAI,IAAI,YAAY;AACpB,YAAI,CAAC;AAAG,iBAAO;AACf,eAAO;AAAA,UACL,MAAM;AAAA,UACN,aAAa;AAAA,QACf;AAAA,MACF;AAEA,eAAS,eAAgB;AACvB,YAAI,CAAC,EAAE,kBAAkB;AAAG,iBAAO;AACnC,cAAM;AACN,YAAI,IAAI,YAAY;AACpB,YAAI,CAAC;AAAG,iBAAO;AACf,eAAO;AAAA,UACL,MAAM;AAAA,UACN,aAAa;AAAA,QACf;AAAA,MACF;AAEA,eAAS,qBAAsB;AAC7B,YAAI,aAAa,CAAC;AAClB,YAAI;AAEJ,YAAI,CAAC,EAAE,wBAAwB;AAAG,iBAAO;AACzC,cAAM;AAEN,YAAI,CAAC,EAAE,OAAO;AAAG,iBAAO;AACxB,eAAO,WAAW,KAAK,GAAG;AACxB,qBAAW,KAAK,QAAQ;AACxB,gBAAM;AACN,YAAE,MAAM;AACR,gBAAM;AAAA,QACR;AACA,YAAI,CAAC,EAAE,OAAO;AAAG,iBAAO;AAExB,eAAO;AAAA,UACL,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAEA,eAAS,OAAQ;AACf,eAAO,MAAM,KACX,WAAW,KACX,QAAQ,KACR,WAAW,KACX,gBAAgB,KAChB,aAAa,KACb,mBAAmB;AAAA,MACvB;AAEA,aAAO,IAAI,KAAK,CAAC;AAAA,IACnB;AAKA,aAAS,UAAW,IAAI;AACtB,UAAI,GAAG,SAAS,WAAW;AACzB,aAAK,GAAG;AAAA,MACV;AAEA,eAAS,QAAS,GAAG;AACnB,eAAO,EAAE,KAAK,GAAG;AAAA,MACnB;AAEA,eAAS,QAAS,GAAG;AACnB,eAAO,EAAE,IAAI,OAAO,EAAE,KAAK,IAAI;AAAA,MACjC;AAEA,eAAS,SAAU,GAAG;AACpB,eAAO,EAAE,IAAI,OAAO,EAAE,IAAI,UAAU,EAAE,KAAK,IAAI;AAAA,MACjD;AAEA,eAAS,cAAe,GAAG;AACzB,eAAO,EAAE,IAAI,QAAQ,EAAE,IAAI,UAAU,EAAE,KAAK,IAAI;AAAA,MAClD;AAEA,eAAS,WAAY,GAAG;AAAE,eAAO,MAAM,IAAI;AAAA,MAAK;AAEhD,cAAQ,GAAG,MAAM;AAAA,QACf,KAAK;AACH,iBAAO,YAAY,QAAQ,GAAG,WAAW,IAAI;AAAA,QAC/C,KAAK;AACH,iBAAO,iBAAiB,QAAQ,GAAG,WAAW,IAAI;AAAA,QACpD,KAAK;AACH,iBAAO,cAAc,SAAS,GAAG,WAAW,IAAI;AAAA,QAClD,KAAK;AACH,iBAAO,iBAAiB,QAAQ,GAAG,WAAW,IAAI;AAAA,QACpD,KAAK;AACH,iBAAO,mBAAmB,cAAc,GAAG,WAAW,IAAI;AAAA,QAC5D,KAAK;AACH,iBAAO,sBAAsB,SAAS,GAAG,WAAW,IAAI;AAAA,QAC1D,KAAK;AACH,iBAAO,yBAAyB,GAAG,WAAW,IAAI,SAAS,EAAE,KAAK,IAAI,IAAI;AAAA,QAC5E;AACE,gBAAM,IAAI,MAAM,wEAAwE;AAAA,MAC5F;AAAA,IACF;AAAA;AAAA;", "names": []}