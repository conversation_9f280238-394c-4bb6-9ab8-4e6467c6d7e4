{"version": 3, "sources": ["../../../../node_modules/.pnpm/geojson@0.5.0/node_modules/geojson/geojson.js"], "sourcesContent": ["(function(GeoJSON) {\n  GeoJSON.version = '0.5.0';\n\n  // Allow user to specify default parameters\n  GeoJSON.defaults = {\n    doThrows: {\n      invalidGeometry: false\n    }\n  };\n\n  function InvalidGeometryError() {\n    var args = 1 <= arguments.length ? [].slice.call(arguments, 0) : [];\n    var item = args.shift();\n    var params = args.shift();\n\n    Error.apply(this, args);\n    this.message = this.message || \"Invalid Geometry: \" + 'item: ' + JSON.stringify(item) + ', params: ' + JSON.stringify(params);\n  }\n\n  InvalidGeometryError.prototype = Error;\n\n\n  GeoJSON.errors = {\n    InvalidGeometryError: InvalidGeometryError\n  };\n\n  //exposing so this can be overriden maybe by geojson-validation or the like\n  GeoJSON.isGeometryValid = function(geometry){\n    if(!geometry || !Object.keys(geometry).length)\n      return false;\n\n    return !!geometry.type && !!geometry.coordinates && Array.isArray(geometry.coordinates) && !!geometry.coordinates.length;\n  };\n\n  // The one and only public function.\n  // Converts an array of objects into a GeoJSON feature collection\n  GeoJSON.parse = function(objects, params, callback) {\n    var geojson,\n        settings = applyDefaults(params, this.defaults),\n        propFunc;\n\n    geomAttrs.length = 0; // Reset the list of geometry fields\n    setGeom(settings);\n    propFunc = getPropFunction(settings);\n\n    if (Array.isArray(objects)) {\n      geojson = {\"type\": \"FeatureCollection\", \"features\": []};\n      objects.forEach(function(item){\n        geojson.features.push(getFeature({item:item, params: settings, propFunc:propFunc}));\n      });\n      addOptionals(geojson, settings);\n    } else {\n      geojson = getFeature({item:objects, params: settings, propFunc:propFunc});\n      addOptionals(geojson, settings);\n    }\n\n    if (callback && typeof callback === 'function') {\n      callback(geojson);\n    } else {\n      return geojson;\n    }\n  };\n\n  // Helper functions\n  var geoms = ['Point', 'MultiPoint', 'LineString', 'MultiLineString', 'Polygon', 'MultiPolygon', 'GeoJSON'],\n      geomAttrs = [];\n\n  // Adds default settings to user-specified params\n  // Does not overwrite any settings--only adds defaults\n  // the the user did not specify\n  function applyDefaults(params, defaults) {\n    var settings = params || {};\n\n    for(var setting in defaults) {\n      if(defaults.hasOwnProperty(setting) && !settings[setting]) {\n        settings[setting] = defaults[setting];\n      }\n    }\n\n    return settings;\n  }\n\n  // Adds the optional GeoJSON properties crs and bbox\n  // if they have been specified\n  function addOptionals(geojson, settings){\n    if(settings.crs && checkCRS(settings.crs)) {\n      if(settings.isPostgres)\n        geojson.geometry.crs = settings.crs;\n      else\n        geojson.crs = settings.crs;\n    }\n    if (settings.bbox) {\n      geojson.bbox = settings.bbox;\n    }\n    if (settings.extraGlobal) {\n      geojson.properties = {};\n      for (var key in settings.extraGlobal) {\n        geojson.properties[key] = settings.extraGlobal[key];\n      }\n    }\n  }\n\n  // Verify that the structure of CRS object is valid\n  function checkCRS(crs) {\n    if (crs.type === 'name') {\n        if (crs.properties && crs.properties.name) {\n            return true;\n        } else {\n            throw new Error('Invalid CRS. Properties must contain \"name\" key');\n        }\n    } else if (crs.type === 'link') {\n        if (crs.properties && crs.properties.href && crs.properties.type) {\n            return true;\n        } else {\n            throw new Error('Invalid CRS. Properties must contain \"href\" and \"type\" key');\n        }\n    } else {\n        throw new Error('Invald CRS. Type attribute must be \"name\" or \"link\"');\n    }\n  }\n\n  // Moves the user-specified geometry parameters\n  // under the `geom` key in param for easier access\n  function setGeom(params) {\n    params.geom = {};\n\n    for(var param in params) {\n      if(params.hasOwnProperty(param) && geoms.indexOf(param) !== -1){\n        params.geom[param] = params[param];\n        delete params[param];\n      }\n    }\n\n    setGeomAttrList(params.geom);\n  }\n\n  // Adds fields which contain geometry data\n  // to geomAttrs. This list is used when adding\n  // properties to the features so that no geometry\n  // fields are added the properties key\n  function setGeomAttrList(params) {\n    for(var param in params) {\n      if(params.hasOwnProperty(param)) {\n        if(typeof params[param] === 'string') {\n          geomAttrs.push(params[param]);\n        } else if (typeof params[param] === 'object') { // Array of coordinates for Point\n          geomAttrs.push(params[param][0]);\n          geomAttrs.push(params[param][1]);\n        }\n      }\n    }\n\n    if(geomAttrs.length === 0) { throw new Error('No geometry attributes specified'); }\n  }\n\n  // Creates a feature object to be added\n  // to the GeoJSON features array\n  function getFeature(args) {\n    var item = args.item,\n      params = args.params,\n      propFunc = args.propFunc;\n\n    var feature = { \"type\": \"Feature\" };\n\n    feature.geometry = buildGeom(item, params);\n    feature.properties = propFunc.call(item);\n\n    return feature;\n  }\n\n  function isNested(val){\n    return (/^.+\\..+$/.test(val));\n  }\n\n  // Assembles the `geometry` property\n  // for the feature output\n  function buildGeom(item, params) {\n    var geom = {},\n        attr;\n\n    for(var gtype in params.geom) {\n      var val = params.geom[gtype];\n\n      // Geometry parameter specified as: {Point: 'coords'}\n      if(typeof val === 'string' && item.hasOwnProperty(val)) {\n        if(gtype === 'GeoJSON') {\n          geom = item[val];\n        } else {\n          geom.type = gtype;\n          geom.coordinates = item[val];\n        }\n      }\n\n      /* Handle things like:\n      Polygon: {\n        northeast: ['lat', 'lng'],\n        southwest: ['lat', 'lng']\n      }\n      */\n      else if(typeof val === 'object' && !Array.isArray(val)) {\n        /*jshint loopfunc: true */\n        var points = Object.keys(val).map(function(key){\n          var order = val[key];\n          var newItem = item[key];\n          return buildGeom(newItem, {geom:{ Point: order}});\n        });\n        geom.type = gtype;\n        /*jshint loopfunc: true */\n        geom.coordinates = [].concat(points.map(function(p){\n          return p.coordinates;\n        }));\n      }\n\n      // Geometry parameter specified as: {Point: ['lat', 'lng']}\n      else if(Array.isArray(val) && item.hasOwnProperty(val[0]) && item.hasOwnProperty(val[1])){\n        geom.type = gtype;\n        geom.coordinates = [Number(item[val[1]]), Number(item[val[0]])];\n      }\n\n      // Geometry parameter specified as: {Point: ['container.lat', 'container.lng']}\n      else if(Array.isArray(val) && isNested(val[0]) && isNested(val[1])){\n        var coordinates = [];\n        for (var i = 0; i < val.length; i++) {\t// i.e. 0 and 1\n          var paths = val[i].split('.');\n          var itemClone = item;\n          for (var j = 0; j < paths.length; j++) {\n            if (!itemClone.hasOwnProperty(paths[j])) {\n              return false;\n            }\n            itemClone = itemClone[paths[j]];\t// Iterate deeper into the object\n          }\n          coordinates[i] = itemClone;\n        }\n        geom.type = gtype;\n        geom.coordinates = [Number(coordinates[1]), Number(coordinates[0])];\n      }\n    }\n\n    if(params.doThrows && params.doThrows.invalidGeometry && !GeoJSON.isGeometryValid(geom)){\n      throw new InvalidGeometryError(item, params);\n    }\n\n    return geom;\n  }\n\n  // Returns the function to be used to\n  // build the properties object for each feature\n  function getPropFunction(params) {\n    var func;\n\n    if(!params.exclude && !params.include) {\n      func = function(properties) {\n        for(var attr in this) {\n          if(this.hasOwnProperty(attr) && (geomAttrs.indexOf(attr) === -1)) {\n            properties[attr] = this[attr];\n          }\n        }\n      };\n    } else if(params.include) {\n      func = function(properties) {\n        params.include.forEach(function(attr){\n          properties[attr] = this[attr];\n        }, this);\n      };\n    } else if(params.exclude) {\n      func = function(properties) {\n        for(var attr in this) {\n          if(this.hasOwnProperty(attr) && (geomAttrs.indexOf(attr) === -1) && (params.exclude.indexOf(attr) === -1)) {\n            properties[attr] = this[attr];\n          }\n        }\n      };\n    }\n\n    return function() {\n      var properties = {};\n\n      func.call(this, properties);\n\n      if(params.extra) { addExtra(properties, params.extra); }\n      return properties;\n    };\n  }\n\n  // Adds data contained in the `extra`\n  // parameter if it has been specified\n  function addExtra(properties, extra) {\n    for(var key in extra){\n      if(extra.hasOwnProperty(key)) {\n        properties[key] = extra[key];\n      }\n    }\n\n    return properties;\n  }\n\n}(typeof module == 'object' ? module.exports : window.GeoJSON = {}));\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,SAAS;AACjB,cAAQ,UAAU;AAGlB,cAAQ,WAAW;AAAA,QACjB,UAAU;AAAA,UACR,iBAAiB;AAAA,QACnB;AAAA,MACF;AAEA,eAAS,uBAAuB;AAC9B,YAAI,OAAO,KAAK,UAAU,SAAS,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC,IAAI,CAAC;AAClE,YAAI,OAAO,KAAK,MAAM;AACtB,YAAI,SAAS,KAAK,MAAM;AAExB,cAAM,MAAM,MAAM,IAAI;AACtB,aAAK,UAAU,KAAK,WAAW,6BAAkC,KAAK,UAAU,IAAI,IAAI,eAAe,KAAK,UAAU,MAAM;AAAA,MAC9H;AAEA,2BAAqB,YAAY;AAGjC,cAAQ,SAAS;AAAA,QACf;AAAA,MACF;AAGA,cAAQ,kBAAkB,SAAS,UAAS;AAC1C,YAAG,CAAC,YAAY,CAAC,OAAO,KAAK,QAAQ,EAAE;AACrC,iBAAO;AAET,eAAO,CAAC,CAAC,SAAS,QAAQ,CAAC,CAAC,SAAS,eAAe,MAAM,QAAQ,SAAS,WAAW,KAAK,CAAC,CAAC,SAAS,YAAY;AAAA,MACpH;AAIA,cAAQ,QAAQ,SAAS,SAAS,QAAQ,UAAU;AAClD,YAAI,SACA,WAAW,cAAc,QAAQ,KAAK,QAAQ,GAC9C;AAEJ,kBAAU,SAAS;AACnB,gBAAQ,QAAQ;AAChB,mBAAW,gBAAgB,QAAQ;AAEnC,YAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,oBAAU,EAAC,QAAQ,qBAAqB,YAAY,CAAC,EAAC;AACtD,kBAAQ,QAAQ,SAAS,MAAK;AAC5B,oBAAQ,SAAS,KAAK,WAAW,EAAC,MAAW,QAAQ,UAAU,SAAiB,CAAC,CAAC;AAAA,UACpF,CAAC;AACD,uBAAa,SAAS,QAAQ;AAAA,QAChC,OAAO;AACL,oBAAU,WAAW,EAAC,MAAK,SAAS,QAAQ,UAAU,SAAiB,CAAC;AACxE,uBAAa,SAAS,QAAQ;AAAA,QAChC;AAEA,YAAI,YAAY,OAAO,aAAa,YAAY;AAC9C,mBAAS,OAAO;AAAA,QAClB,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,UAAI,QAAQ,CAAC,SAAS,cAAc,cAAc,mBAAmB,WAAW,gBAAgB,SAAS,GACrG,YAAY,CAAC;AAKjB,eAAS,cAAc,QAAQ,UAAU;AACvC,YAAI,WAAW,UAAU,CAAC;AAE1B,iBAAQ,WAAW,UAAU;AAC3B,cAAG,SAAS,eAAe,OAAO,KAAK,CAAC,SAAS,OAAO,GAAG;AACzD,qBAAS,OAAO,IAAI,SAAS,OAAO;AAAA,UACtC;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAIA,eAAS,aAAa,SAAS,UAAS;AACtC,YAAG,SAAS,OAAO,SAAS,SAAS,GAAG,GAAG;AACzC,cAAG,SAAS;AACV,oBAAQ,SAAS,MAAM,SAAS;AAAA;AAEhC,oBAAQ,MAAM,SAAS;AAAA,QAC3B;AACA,YAAI,SAAS,MAAM;AACjB,kBAAQ,OAAO,SAAS;AAAA,QAC1B;AACA,YAAI,SAAS,aAAa;AACxB,kBAAQ,aAAa,CAAC;AACtB,mBAAS,OAAO,SAAS,aAAa;AACpC,oBAAQ,WAAW,GAAG,IAAI,SAAS,YAAY,GAAG;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAGA,eAAS,SAAS,KAAK;AACrB,YAAI,IAAI,SAAS,QAAQ;AACrB,cAAI,IAAI,cAAc,IAAI,WAAW,MAAM;AACvC,mBAAO;AAAA,UACX,OAAO;AACH,kBAAM,IAAI,MAAM,iDAAiD;AAAA,UACrE;AAAA,QACJ,WAAW,IAAI,SAAS,QAAQ;AAC5B,cAAI,IAAI,cAAc,IAAI,WAAW,QAAQ,IAAI,WAAW,MAAM;AAC9D,mBAAO;AAAA,UACX,OAAO;AACH,kBAAM,IAAI,MAAM,4DAA4D;AAAA,UAChF;AAAA,QACJ,OAAO;AACH,gBAAM,IAAI,MAAM,qDAAqD;AAAA,QACzE;AAAA,MACF;AAIA,eAAS,QAAQ,QAAQ;AACvB,eAAO,OAAO,CAAC;AAEf,iBAAQ,SAAS,QAAQ;AACvB,cAAG,OAAO,eAAe,KAAK,KAAK,MAAM,QAAQ,KAAK,MAAM,IAAG;AAC7D,mBAAO,KAAK,KAAK,IAAI,OAAO,KAAK;AACjC,mBAAO,OAAO,KAAK;AAAA,UACrB;AAAA,QACF;AAEA,wBAAgB,OAAO,IAAI;AAAA,MAC7B;AAMA,eAAS,gBAAgB,QAAQ;AAC/B,iBAAQ,SAAS,QAAQ;AACvB,cAAG,OAAO,eAAe,KAAK,GAAG;AAC/B,gBAAG,OAAO,OAAO,KAAK,MAAM,UAAU;AACpC,wBAAU,KAAK,OAAO,KAAK,CAAC;AAAA,YAC9B,WAAW,OAAO,OAAO,KAAK,MAAM,UAAU;AAC5C,wBAAU,KAAK,OAAO,KAAK,EAAE,CAAC,CAAC;AAC/B,wBAAU,KAAK,OAAO,KAAK,EAAE,CAAC,CAAC;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AAEA,YAAG,UAAU,WAAW,GAAG;AAAE,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QAAG;AAAA,MACpF;AAIA,eAAS,WAAW,MAAM;AACxB,YAAI,OAAO,KAAK,MACd,SAAS,KAAK,QACd,WAAW,KAAK;AAElB,YAAI,UAAU,EAAE,QAAQ,UAAU;AAElC,gBAAQ,WAAW,UAAU,MAAM,MAAM;AACzC,gBAAQ,aAAa,SAAS,KAAK,IAAI;AAEvC,eAAO;AAAA,MACT;AAEA,eAAS,SAAS,KAAI;AACpB,eAAQ,WAAW,KAAK,GAAG;AAAA,MAC7B;AAIA,eAAS,UAAU,MAAM,QAAQ;AAC/B,YAAI,OAAO,CAAC,GACR;AAEJ,iBAAQ,SAAS,OAAO,MAAM;AAC5B,cAAI,MAAM,OAAO,KAAK,KAAK;AAG3B,cAAG,OAAO,QAAQ,YAAY,KAAK,eAAe,GAAG,GAAG;AACtD,gBAAG,UAAU,WAAW;AACtB,qBAAO,KAAK,GAAG;AAAA,YACjB,OAAO;AACL,mBAAK,OAAO;AACZ,mBAAK,cAAc,KAAK,GAAG;AAAA,YAC7B;AAAA,UACF,WAQQ,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG,GAAG;AAEtD,gBAAI,SAAS,OAAO,KAAK,GAAG,EAAE,IAAI,SAAS,KAAI;AAC7C,kBAAI,QAAQ,IAAI,GAAG;AACnB,kBAAI,UAAU,KAAK,GAAG;AACtB,qBAAO,UAAU,SAAS,EAAC,MAAK,EAAE,OAAO,MAAK,EAAC,CAAC;AAAA,YAClD,CAAC;AACD,iBAAK,OAAO;AAEZ,iBAAK,cAAc,CAAC,EAAE,OAAO,OAAO,IAAI,SAAS,GAAE;AACjD,qBAAO,EAAE;AAAA,YACX,CAAC,CAAC;AAAA,UACJ,WAGQ,MAAM,QAAQ,GAAG,KAAK,KAAK,eAAe,IAAI,CAAC,CAAC,KAAK,KAAK,eAAe,IAAI,CAAC,CAAC,GAAE;AACvF,iBAAK,OAAO;AACZ,iBAAK,cAAc,CAAC,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;AAAA,UAChE,WAGQ,MAAM,QAAQ,GAAG,KAAK,SAAS,IAAI,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,GAAE;AACjE,gBAAI,cAAc,CAAC;AACnB,qBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,kBAAI,QAAQ,IAAI,CAAC,EAAE,MAAM,GAAG;AAC5B,kBAAI,YAAY;AAChB,uBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAI,CAAC,UAAU,eAAe,MAAM,CAAC,CAAC,GAAG;AACvC,yBAAO;AAAA,gBACT;AACA,4BAAY,UAAU,MAAM,CAAC,CAAC;AAAA,cAChC;AACA,0BAAY,CAAC,IAAI;AAAA,YACnB;AACA,iBAAK,OAAO;AACZ,iBAAK,cAAc,CAAC,OAAO,YAAY,CAAC,CAAC,GAAG,OAAO,YAAY,CAAC,CAAC,CAAC;AAAA,UACpE;AAAA,QACF;AAEA,YAAG,OAAO,YAAY,OAAO,SAAS,mBAAmB,CAAC,QAAQ,gBAAgB,IAAI,GAAE;AACtF,gBAAM,IAAI,qBAAqB,MAAM,MAAM;AAAA,QAC7C;AAEA,eAAO;AAAA,MACT;AAIA,eAAS,gBAAgB,QAAQ;AAC/B,YAAI;AAEJ,YAAG,CAAC,OAAO,WAAW,CAAC,OAAO,SAAS;AACrC,iBAAO,SAAS,YAAY;AAC1B,qBAAQ,QAAQ,MAAM;AACpB,kBAAG,KAAK,eAAe,IAAI,KAAM,UAAU,QAAQ,IAAI,MAAM,IAAK;AAChE,2BAAW,IAAI,IAAI,KAAK,IAAI;AAAA,cAC9B;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAU,OAAO,SAAS;AACxB,iBAAO,SAAS,YAAY;AAC1B,mBAAO,QAAQ,QAAQ,SAAS,MAAK;AACnC,yBAAW,IAAI,IAAI,KAAK,IAAI;AAAA,YAC9B,GAAG,IAAI;AAAA,UACT;AAAA,QACF,WAAU,OAAO,SAAS;AACxB,iBAAO,SAAS,YAAY;AAC1B,qBAAQ,QAAQ,MAAM;AACpB,kBAAG,KAAK,eAAe,IAAI,KAAM,UAAU,QAAQ,IAAI,MAAM,MAAQ,OAAO,QAAQ,QAAQ,IAAI,MAAM,IAAK;AACzG,2BAAW,IAAI,IAAI,KAAK,IAAI;AAAA,cAC9B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,eAAO,WAAW;AAChB,cAAI,aAAa,CAAC;AAElB,eAAK,KAAK,MAAM,UAAU;AAE1B,cAAG,OAAO,OAAO;AAAE,qBAAS,YAAY,OAAO,KAAK;AAAA,UAAG;AACvD,iBAAO;AAAA,QACT;AAAA,MACF;AAIA,eAAS,SAAS,YAAY,OAAO;AACnC,iBAAQ,OAAO,OAAM;AACnB,cAAG,MAAM,eAAe,GAAG,GAAG;AAC5B,uBAAW,GAAG,IAAI,MAAM,GAAG;AAAA,UAC7B;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IAEF,GAAE,OAAO,UAAU,WAAW,OAAO,UAAU,OAAO,UAAU,CAAC,CAAC;AAAA;AAAA;", "names": []}