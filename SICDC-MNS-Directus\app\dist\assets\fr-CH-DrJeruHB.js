import{f as a,l as o,m}from"./match-Bpy-C_Mf.js";import{c as t}from"./index.DUmRo3Ep.entry.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";const i={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd.MM.y"},r={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},s={full:"{{date}} 'à' {{time}}",long:"{{date}} 'à' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},l={date:t({formats:i,defaultWidth:"full"}),time:t({formats:r,defaultWidth:"full"}),dateTime:t({formats:s,defaultWidth:"full"})},d={lastWeek:"eeee 'la semaine dernière à' p",yesterday:"'hier à' p",today:"'aujourd’hui à' p",tomorrow:"'demain à' p'",nextWeek:"eeee 'la semaine prochaine à' p",other:"P"},n=(e,f,c,p)=>d[e],F={code:"fr-CH",formatDistance:a,formatLong:l,formatRelative:n,localize:o,match:m,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{F as default,F as frCH};
