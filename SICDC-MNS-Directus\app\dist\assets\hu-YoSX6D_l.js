import{c as o,b as n,i as v,j as s}from"./index.DUmRo3Ep.entry.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";const p={about:"kör<PERSON>lbelül",over:"több mint",almost:"majdnem",lessthan:"kevesebb mint"},z={xseconds:" másodperc",halfaminute:"fél perc",xminutes:" perc",xhours:" óra",xdays:" nap",xweeks:" hét",xmonths:" hónap",xyears:" év"},b={xseconds:{"-1":" másodperccel ezelőtt",1:" másodperc múlva",0:" másodperce"},halfaminute:{"-1":"fél perccel ezelőtt",1:"fél perc múlva",0:"fél perce"},xminutes:{"-1":" perccel ezelőtt",1:" perc múlva",0:" perce"},xhours:{"-1":" órával ezelőtt",1:" óra múlva",0:" órája"},xdays:{"-1":" nappal ezelőtt",1:" nap múlva",0:" napja"},xweeks:{"-1":" héttel ezelőtt",1:" hét múlva",0:" hete"},xmonths:{"-1":" hónappal ezelőtt",1:" hónap múlva",0:" hónapja"},xyears:{"-1":" évvel ezelőtt",1:" év múlva",0:" éve"}},g=(e,a,t)=>{const r=e.match(/about|over|almost|lessthan/i),u=r?e.replace(r[0],""):e,c=(t==null?void 0:t.addSuffix)===!0,i=u.toLowerCase(),h=(t==null?void 0:t.comparison)||0,l=c?b[i][h]:z[i];let d=i==="halfaminute"?l:a+l;if(r){const f=r[0].toLowerCase();d=p[f]+" "+d}return d},y={full:"y. MMMM d., EEEE",long:"y. MMMM d.",medium:"y. MMM d.",short:"y. MM. dd."},j={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},w={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},P={date:o({formats:y,defaultWidth:"full"}),time:o({formats:j,defaultWidth:"full"}),dateTime:o({formats:w,defaultWidth:"full"})},k=["vasárnap","hétfőn","kedden","szerdán","csütörtökön","pénteken","szombaton"];function m(e){return a=>{const t=k[a.getDay()];return`${e?"":"'múlt' "}'${t}' p'-kor'`}}const I={lastWeek:m(!1),yesterday:"'tegnap' p'-kor'",today:"'ma' p'-kor'",tomorrow:"'holnap' p'-kor'",nextWeek:m(!0),other:"P"},M=(e,a)=>{const t=I[e];return typeof t=="function"?t(a):t},W={narrow:["ie.","isz."],abbreviated:["i. e.","i. sz."],wide:["Krisztus előtt","időszámításunk szerint"]},x={narrow:["1.","2.","3.","4."],abbreviated:["1. n.év","2. n.év","3. n.év","4. n.év"],wide:["1. negyedév","2. negyedév","3. negyedév","4. negyedév"]},V={narrow:["I.","II.","III.","IV."],abbreviated:["I. n.év","II. n.év","III. n.év","IV. n.év"],wide:["I. negyedév","II. negyedév","III. negyedév","IV. negyedév"]},S={narrow:["J","F","M","Á","M","J","J","A","Sz","O","N","D"],abbreviated:["jan.","febr.","márc.","ápr.","máj.","jún.","júl.","aug.","szept.","okt.","nov.","dec."],wide:["január","február","március","április","május","június","július","augusztus","szeptember","október","november","december"]},F={narrow:["V","H","K","Sz","Cs","P","Sz"],short:["V","H","K","Sze","Cs","P","Szo"],abbreviated:["V","H","K","Sze","Cs","P","Szo"],wide:["vasárnap","hétfő","kedd","szerda","csütörtök","péntek","szombat"]},C={narrow:{am:"de.",pm:"du.",midnight:"éjfél",noon:"dél",morning:"reggel",afternoon:"du.",evening:"este",night:"éjjel"},abbreviated:{am:"de.",pm:"du.",midnight:"éjfél",noon:"dél",morning:"reggel",afternoon:"du.",evening:"este",night:"éjjel"},wide:{am:"de.",pm:"du.",midnight:"éjfél",noon:"dél",morning:"reggel",afternoon:"délután",evening:"este",night:"éjjel"}},D=(e,a)=>Number(e)+".",H={ordinalNumber:D,era:n({values:W,defaultWidth:"wide"}),quarter:n({values:x,defaultWidth:"wide",argumentCallback:e=>e-1,formattingValues:V,defaultFormattingWidth:"wide"}),month:n({values:S,defaultWidth:"wide"}),day:n({values:F,defaultWidth:"wide"}),dayPeriod:n({values:C,defaultWidth:"wide"})},L=/^(\d+)\.?/i,E=/\d+/i,N={narrow:/^(ie\.|isz\.)/i,abbreviated:/^(i\.\s?e\.?|b?\s?c\s?e|i\.\s?sz\.?)/i,wide:/^(Krisztus előtt|időszámításunk előtt|időszámításunk szerint|i\. sz\.)/i},K={narrow:[/ie/i,/isz/i],abbreviated:[/^(i\.?\s?e\.?|b\s?ce)/i,/^(i\.?\s?sz\.?|c\s?e)/i],any:[/előtt/i,/(szerint|i. sz.)/i]},O={narrow:/^[1234]\.?/i,abbreviated:/^[1234]?\.?\s?n\.év/i,wide:/^([1234]|I|II|III|IV)?\.?\s?negyedév/i},$={any:[/1|I$/i,/2|II$/i,/3|III/i,/4|IV/i]},q={narrow:/^[jfmaásond]|sz/i,abbreviated:/^(jan\.?|febr\.?|márc\.?|ápr\.?|máj\.?|jún\.?|júl\.?|aug\.?|szept\.?|okt\.?|nov\.?|dec\.?)/i,wide:/^(január|február|március|április|május|június|július|augusztus|szeptember|október|november|december)/i},J={narrow:[/^j/i,/^f/i,/^m/i,/^a|á/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s|sz/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^már/i,/^áp/i,/^máj/i,/^jún/i,/^júl/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Q={narrow:/^([vhkpc]|sz|cs|sz)/i,short:/^([vhkp]|sze|cs|szo)/i,abbreviated:/^([vhkp]|sze|cs|szo)/i,wide:/^(vasárnap|hétfő|kedd|szerda|csütörtök|péntek|szombat)/i},R={narrow:[/^v/i,/^h/i,/^k/i,/^sz/i,/^c/i,/^p/i,/^sz/i],any:[/^v/i,/^h/i,/^k/i,/^sze/i,/^c/i,/^p/i,/^szo/i]},T={any:/^((de|du)\.?|éjfél|délután|dél|reggel|este|éjjel)/i},A={any:{am:/^de\.?/i,pm:/^du\.?/i,midnight:/^éjf/i,noon:/^dé/i,morning:/reg/i,afternoon:/^délu\.?/i,evening:/es/i,night:/éjj/i}},_={ordinalNumber:v({matchPattern:L,parsePattern:E,valueCallback:e=>parseInt(e,10)}),era:s({matchPatterns:N,defaultMatchWidth:"wide",parsePatterns:K,defaultParseWidth:"any"}),quarter:s({matchPatterns:O,defaultMatchWidth:"wide",parsePatterns:$,defaultParseWidth:"any",valueCallback:e=>e+1}),month:s({matchPatterns:q,defaultMatchWidth:"wide",parsePatterns:J,defaultParseWidth:"any"}),day:s({matchPatterns:Q,defaultMatchWidth:"wide",parsePatterns:R,defaultParseWidth:"any"}),dayPeriod:s({matchPatterns:T,defaultMatchWidth:"any",parsePatterns:A,defaultParseWidth:"any"})},te={code:"hu",formatDistance:g,formatLong:P,formatRelative:M,localize:H,match:_,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{te as default,te as hu};
