import{g as s}from"./index-C0qcAVKU.js";import{r as u}from"./ruby-lmODm0S9.js";function i(t,a){for(var o=0;o<a.length;o++){const r=a[o];if(typeof r!="string"&&!Array.isArray(r)){for(const e in r)if(e!=="default"&&!(e in t)){const n=Object.getOwnPropertyDescriptor(r,e);n&&Object.defineProperty(t,e,n.get?n:{enumerable:!0,get:()=>r[e]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}var f=u();const c=s(f),b=i({__proto__:null,default:c},[f]);export{b as r};
