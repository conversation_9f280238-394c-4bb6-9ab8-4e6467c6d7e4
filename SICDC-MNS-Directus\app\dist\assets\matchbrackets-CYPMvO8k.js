import{g as n}from"./index-C0qcAVKU.js";import{r as f}from"./index.DUmRo3Ep.entry.js";function i(r,c){for(var o=0;o<c.length;o++){const e=c[o];if(typeof e!="string"&&!Array.isArray(e)){for(const t in e)if(t!=="default"&&!(t in r)){const a=Object.getOwnPropertyDescriptor(e,t);a&&Object.defineProperty(r,t,a.get?a:{enumerable:!0,get:()=>e[t]})}}}return Object.freeze(Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}))}var s=f();const m=n(s),b=i({__proto__:null,default:m},[s]);export{b as m};
