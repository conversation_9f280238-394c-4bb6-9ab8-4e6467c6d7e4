{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/preview/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const option = name => editor => editor.options.get(name);\n    const getContentStyle = option('content_style');\n    const shouldUseContentCssCors = option('content_css_cors');\n    const getBodyClass = option('body_class');\n    const getBodyId = option('body_id');\n\n    const getPreviewHtml = editor => {\n      var _a;\n      let headHtml = '';\n      const encode = editor.dom.encode;\n      const contentStyle = (_a = getContentStyle(editor)) !== null && _a !== void 0 ? _a : '';\n      headHtml += '<base href=\"' + encode(editor.documentBaseURI.getURI()) + '\">';\n      const cors = shouldUseContentCssCors(editor) ? ' crossorigin=\"anonymous\"' : '';\n      global.each(editor.contentCSS, url => {\n        headHtml += '<link type=\"text/css\" rel=\"stylesheet\" href=\"' + encode(editor.documentBaseURI.toAbsolute(url)) + '\"' + cors + '>';\n      });\n      if (contentStyle) {\n        headHtml += '<style type=\"text/css\">' + contentStyle + '</style>';\n      }\n      const bodyId = getBodyId(editor);\n      const bodyClass = getBodyClass(editor);\n      const isMetaKeyPressed = global$1.os.isMacOS() || global$1.os.isiOS() ? 'e.metaKey' : 'e.ctrlKey && !e.altKey';\n      const preventClicksOnLinksScript = '<script>' + 'document.addEventListener && document.addEventListener(\"click\", function(e) {' + 'for (var elm = e.target; elm; elm = elm.parentNode) {' + 'if (elm.nodeName === \"A\" && !(' + isMetaKeyPressed + ')) {' + 'e.preventDefault();' + '}' + '}' + '}, false);' + '</script> ';\n      const directionality = editor.getBody().dir;\n      const dirAttr = directionality ? ' dir=\"' + encode(directionality) + '\"' : '';\n      const previewHtml = '<!DOCTYPE html>' + '<html>' + '<head>' + headHtml + '</head>' + '<body id=\"' + encode(bodyId) + '\" class=\"mce-content-body ' + encode(bodyClass) + '\"' + dirAttr + '>' + editor.getContent() + preventClicksOnLinksScript + '</body>' + '</html>';\n      return previewHtml;\n    };\n\n    const open = editor => {\n      const content = getPreviewHtml(editor);\n      const dataApi = editor.windowManager.open({\n        title: 'Preview',\n        size: 'large',\n        body: {\n          type: 'panel',\n          items: [{\n              name: 'preview',\n              type: 'iframe',\n              sandboxed: true,\n              transparent: false\n            }]\n        },\n        buttons: [{\n            type: 'cancel',\n            name: 'close',\n            text: 'Close',\n            primary: true\n          }],\n        initialData: { preview: content }\n      });\n      dataApi.focus('close');\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mcePreview', () => {\n        open(editor);\n      });\n    };\n\n    const register = editor => {\n      const onAction = () => editor.execCommand('mcePreview');\n      editor.ui.registry.addButton('preview', {\n        icon: 'preview',\n        tooltip: 'Preview',\n        onAction\n      });\n      editor.ui.registry.addMenuItem('preview', {\n        icon: 'preview',\n        text: 'Preview',\n        onAction\n      });\n    };\n\n    var Plugin = () => {\n      global$2.add('preview', editor => {\n        register$1(editor);\n        register(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAEvD,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE5D,QAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,QAAM,kBAAkB,OAAO,eAAe;AAC9C,QAAM,0BAA0B,OAAO,kBAAkB;AACzD,QAAM,eAAe,OAAO,YAAY;AACxC,QAAM,YAAY,OAAO,SAAS;AAElC,QAAM,iBAAiB,YAAU;AAC/B,QAAI;AACJ,QAAI,WAAW;AACf,UAAM,SAAS,OAAO,IAAI;AAC1B,UAAM,gBAAgB,KAAK,gBAAgB,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK;AACrF,gBAAY,iBAAiB,OAAO,OAAO,gBAAgB,OAAO,CAAC,IAAI;AACvE,UAAM,OAAO,wBAAwB,MAAM,IAAI,6BAA6B;AAC5E,WAAO,KAAK,OAAO,YAAY,SAAO;AACpC,kBAAY,kDAAkD,OAAO,OAAO,gBAAgB,WAAW,GAAG,CAAC,IAAI,MAAM,OAAO;AAAA,IAC9H,CAAC;AACD,QAAI,cAAc;AAChB,kBAAY,4BAA4B,eAAe;AAAA,IACzD;AACA,UAAM,SAAS,UAAU,MAAM;AAC/B,UAAM,YAAY,aAAa,MAAM;AACrC,UAAM,mBAAmB,SAAS,GAAG,QAAQ,KAAK,SAAS,GAAG,MAAM,IAAI,cAAc;AACtF,UAAM,6BAA6B,6KAA4L,mBAAmB;AAClP,UAAM,iBAAiB,OAAO,QAAQ,EAAE;AACxC,UAAM,UAAU,iBAAiB,WAAW,OAAO,cAAc,IAAI,MAAM;AAC3E,UAAM,cAAc,gCAA0C,WAAW,sBAA2B,OAAO,MAAM,IAAI,+BAA+B,OAAO,SAAS,IAAI,MAAM,UAAU,MAAM,OAAO,WAAW,IAAI,6BAA6B;AACjP,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,YAAU;AACrB,UAAM,UAAU,eAAe,MAAM;AACrC,UAAM,UAAU,OAAO,cAAc,KAAK;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,OAAO,CAAC;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,WAAW;AAAA,UACX,aAAa;AAAA,QACf,CAAC;AAAA,MACL;AAAA,MACA,SAAS,CAAC;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,MACH,aAAa,EAAE,SAAS,QAAQ;AAAA,IAClC,CAAC;AACD,YAAQ,MAAM,OAAO;AAAA,EACvB;AAEA,QAAM,aAAa,YAAU;AAC3B,WAAO,WAAW,cAAc,MAAM;AACpC,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,YAAU;AACzB,UAAM,WAAW,MAAM,OAAO,YAAY,YAAY;AACtD,WAAO,GAAG,SAAS,UAAU,WAAW;AAAA,MACtC,MAAM;AAAA,MACN,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AACD,WAAO,GAAG,SAAS,YAAY,WAAW;AAAA,MACxC,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,MAAM;AACjB,aAAS,IAAI,WAAW,YAAU;AAChC,iBAAW,MAAM;AACjB,eAAS,MAAM;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,SAAO;AAEX,GAAG;", "names": []}