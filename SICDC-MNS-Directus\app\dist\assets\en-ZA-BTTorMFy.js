import{c as t,f as m,d as a,l as o,m as e}from"./index.DUmRo3Ep.entry.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";const s={full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"yyyy/MM/dd"},i={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},d={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},l={date:t({formats:s,defaultWidth:"full"}),time:t({formats:i,defaultWidth:"full"}),dateTime:t({formats:d,defaultWidth:"full"})},p={code:"en-ZA",formatDistance:m,formatLong:l,formatRelative:a,localize:o,match:e,options:{weekStartsOn:0,firstWeekContainsDate:1}};export{p as default,p as enZA};
