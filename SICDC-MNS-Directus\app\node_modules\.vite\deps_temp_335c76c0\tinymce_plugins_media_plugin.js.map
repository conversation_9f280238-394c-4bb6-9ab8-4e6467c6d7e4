{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/media/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    var global$6 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType = type => value => typeOf(value) === type;\n    const isString = isType('string');\n    const isObject = isType('object');\n    const isArray = isType('array');\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const nativePush = Array.prototype.push;\n    const each$1 = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const flatten = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    const keys = Object.keys;\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n    const get$1 = (obj, key) => {\n      return has(obj, key) ? Optional.from(obj[key]) : Optional.none();\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n\n    const option = name => editor => editor.options.get(name);\n    const register$2 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('audio_template_callback', { processor: 'function' });\n      registerOption('video_template_callback', { processor: 'function' });\n      registerOption('iframe_template_callback', { processor: 'function' });\n      registerOption('media_live_embeds', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('media_filter_html', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('media_url_resolver', { processor: 'function' });\n      registerOption('media_alt_source', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('media_poster', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('media_dimensions', {\n        processor: 'boolean',\n        default: true\n      });\n    };\n    const getAudioTemplateCallback = option('audio_template_callback');\n    const getVideoTemplateCallback = option('video_template_callback');\n    const getIframeTemplateCallback = option('iframe_template_callback');\n    const hasLiveEmbeds = option('media_live_embeds');\n    const shouldFilterHtml = option('media_filter_html');\n    const getUrlResolver = option('media_url_resolver');\n    const hasAltSource = option('media_alt_source');\n    const hasPoster = option('media_poster');\n    const hasDimensions = option('media_dimensions');\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.html.DomParser');\n\n    const DOM$1 = global$4.DOM;\n    const trimPx = value => value.replace(/px$/, '');\n    const getEphoxEmbedData = node => {\n      const style = node.attr('style');\n      const styles = style ? DOM$1.parseStyle(style) : {};\n      return {\n        type: 'ephox-embed-iri',\n        source: node.attr('data-ephox-embed-iri'),\n        altsource: '',\n        poster: '',\n        width: get$1(styles, 'max-width').map(trimPx).getOr(''),\n        height: get$1(styles, 'max-height').map(trimPx).getOr('')\n      };\n    };\n    const htmlToData = (html, schema) => {\n      let data = {};\n      const parser = global$3({\n        validate: false,\n        forced_root_block: false\n      }, schema);\n      const rootNode = parser.parse(html);\n      for (let node = rootNode; node; node = node.walk()) {\n        if (node.type === 1) {\n          const name = node.name;\n          if (node.attr('data-ephox-embed-iri')) {\n            data = getEphoxEmbedData(node);\n            break;\n          } else {\n            if (!data.source && name === 'param') {\n              data.source = node.attr('movie');\n            }\n            if (name === 'iframe' || name === 'object' || name === 'embed' || name === 'video' || name === 'audio') {\n              if (!data.type) {\n                data.type = name;\n              }\n              data = global$5.extend(node.attributes.map, data);\n            }\n            if (name === 'source') {\n              if (!data.source) {\n                data.source = node.attr('src');\n              } else if (!data.altsource) {\n                data.altsource = node.attr('src');\n              }\n            }\n            if (name === 'img' && !data.poster) {\n              data.poster = node.attr('src');\n            }\n          }\n        }\n      }\n      data.source = data.source || data.src || '';\n      data.altsource = data.altsource || '';\n      data.poster = data.poster || '';\n      return data;\n    };\n\n    const guess = url => {\n      var _a;\n      const mimes = {\n        mp3: 'audio/mpeg',\n        m4a: 'audio/x-m4a',\n        wav: 'audio/wav',\n        mp4: 'video/mp4',\n        webm: 'video/webm',\n        ogg: 'video/ogg',\n        swf: 'application/x-shockwave-flash'\n      };\n      const fileEnd = (_a = url.toLowerCase().split('.').pop()) !== null && _a !== void 0 ? _a : '';\n      return get$1(mimes, fileEnd).getOr('');\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.html.Node');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.html.Serializer');\n\n    const Parser = (schema, settings = {}) => global$3({\n      forced_root_block: false,\n      validate: false,\n      allow_conditional_comments: true,\n      ...settings\n    }, schema);\n\n    const DOM = global$4.DOM;\n    const addPx = value => /^[0-9.]+$/.test(value) ? value + 'px' : value;\n    const updateEphoxEmbed = (data, node) => {\n      const style = node.attr('style');\n      const styleMap = style ? DOM.parseStyle(style) : {};\n      if (isNonNullable(data.width)) {\n        styleMap['max-width'] = addPx(data.width);\n      }\n      if (isNonNullable(data.height)) {\n        styleMap['max-height'] = addPx(data.height);\n      }\n      node.attr('style', DOM.serializeStyle(styleMap));\n    };\n    const sources = [\n      'source',\n      'altsource'\n    ];\n    const updateHtml = (html, data, updateAll, schema) => {\n      let numSources = 0;\n      let sourceCount = 0;\n      const parser = Parser(schema);\n      parser.addNodeFilter('source', nodes => numSources = nodes.length);\n      const rootNode = parser.parse(html);\n      for (let node = rootNode; node; node = node.walk()) {\n        if (node.type === 1) {\n          const name = node.name;\n          if (node.attr('data-ephox-embed-iri')) {\n            updateEphoxEmbed(data, node);\n            break;\n          } else {\n            switch (name) {\n            case 'video':\n            case 'object':\n            case 'embed':\n            case 'img':\n            case 'iframe':\n              if (data.height !== undefined && data.width !== undefined) {\n                node.attr('width', data.width);\n                node.attr('height', data.height);\n              }\n              break;\n            }\n            if (updateAll) {\n              switch (name) {\n              case 'video':\n                node.attr('poster', data.poster);\n                node.attr('src', null);\n                for (let index = numSources; index < 2; index++) {\n                  if (data[sources[index]]) {\n                    const source = new global$2('source', 1);\n                    source.attr('src', data[sources[index]]);\n                    source.attr('type', data[sources[index] + 'mime'] || null);\n                    node.append(source);\n                  }\n                }\n                break;\n              case 'iframe':\n                node.attr('src', data.source);\n                break;\n              case 'object':\n                const hasImage = node.getAll('img').length > 0;\n                if (data.poster && !hasImage) {\n                  node.attr('src', data.poster);\n                  const img = new global$2('img', 1);\n                  img.attr('src', data.poster);\n                  img.attr('width', data.width);\n                  img.attr('height', data.height);\n                  node.append(img);\n                }\n                break;\n              case 'source':\n                if (sourceCount < 2) {\n                  node.attr('src', data[sources[sourceCount]]);\n                  node.attr('type', data[sources[sourceCount] + 'mime'] || null);\n                  if (!data[sources[sourceCount]]) {\n                    node.remove();\n                    continue;\n                  }\n                }\n                sourceCount++;\n                break;\n              case 'img':\n                if (!data.poster) {\n                  node.remove();\n                }\n                break;\n              }\n            }\n          }\n        }\n      }\n      return global$1({}, schema).serialize(rootNode);\n    };\n\n    const urlPatterns = [\n      {\n        regex: /youtu\\.be\\/([\\w\\-_\\?&=.]+)/i,\n        type: 'iframe',\n        w: 560,\n        h: 314,\n        url: 'www.youtube.com/embed/$1',\n        allowFullscreen: true\n      },\n      {\n        regex: /youtube\\.com(.+)v=([^&]+)(&([a-z0-9&=\\-_]+))?/i,\n        type: 'iframe',\n        w: 560,\n        h: 314,\n        url: 'www.youtube.com/embed/$2?$4',\n        allowFullscreen: true\n      },\n      {\n        regex: /youtube.com\\/embed\\/([a-z0-9\\?&=\\-_]+)/i,\n        type: 'iframe',\n        w: 560,\n        h: 314,\n        url: 'www.youtube.com/embed/$1',\n        allowFullscreen: true\n      },\n      {\n        regex: /vimeo\\.com\\/([0-9]+)\\?h=(\\w+)/,\n        type: 'iframe',\n        w: 425,\n        h: 350,\n        url: 'player.vimeo.com/video/$1?h=$2&title=0&byline=0&portrait=0&color=8dc7dc',\n        allowFullscreen: true\n      },\n      {\n        regex: /vimeo\\.com\\/(.*)\\/([0-9]+)\\?h=(\\w+)/,\n        type: 'iframe',\n        w: 425,\n        h: 350,\n        url: 'player.vimeo.com/video/$2?h=$3&title=0&amp;byline=0',\n        allowFullscreen: true\n      },\n      {\n        regex: /vimeo\\.com\\/([0-9]+)/,\n        type: 'iframe',\n        w: 425,\n        h: 350,\n        url: 'player.vimeo.com/video/$1?title=0&byline=0&portrait=0&color=8dc7dc',\n        allowFullscreen: true\n      },\n      {\n        regex: /vimeo\\.com\\/(.*)\\/([0-9]+)/,\n        type: 'iframe',\n        w: 425,\n        h: 350,\n        url: 'player.vimeo.com/video/$2?title=0&amp;byline=0',\n        allowFullscreen: true\n      },\n      {\n        regex: /maps\\.google\\.([a-z]{2,3})\\/maps\\/(.+)msid=(.+)/,\n        type: 'iframe',\n        w: 425,\n        h: 350,\n        url: 'maps.google.com/maps/ms?msid=$2&output=embed\"',\n        allowFullscreen: false\n      },\n      {\n        regex: /dailymotion\\.com\\/video\\/([^_]+)/,\n        type: 'iframe',\n        w: 480,\n        h: 270,\n        url: 'www.dailymotion.com/embed/video/$1',\n        allowFullscreen: true\n      },\n      {\n        regex: /dai\\.ly\\/([^_]+)/,\n        type: 'iframe',\n        w: 480,\n        h: 270,\n        url: 'www.dailymotion.com/embed/video/$1',\n        allowFullscreen: true\n      }\n    ];\n    const getProtocol = url => {\n      const protocolMatches = url.match(/^(https?:\\/\\/|www\\.)(.+)$/i);\n      if (protocolMatches && protocolMatches.length > 1) {\n        return protocolMatches[1] === 'www.' ? 'https://' : protocolMatches[1];\n      } else {\n        return 'https://';\n      }\n    };\n    const getUrl = (pattern, url) => {\n      const protocol = getProtocol(url);\n      const match = pattern.regex.exec(url);\n      let newUrl = protocol + pattern.url;\n      if (isNonNullable(match)) {\n        for (let i = 0; i < match.length; i++) {\n          newUrl = newUrl.replace('$' + i, () => match[i] ? match[i] : '');\n        }\n      }\n      return newUrl.replace(/\\?$/, '');\n    };\n    const matchPattern = url => {\n      const patterns = urlPatterns.filter(pattern => pattern.regex.test(url));\n      if (patterns.length > 0) {\n        return global$5.extend({}, patterns[0], { url: getUrl(patterns[0], url) });\n      } else {\n        return null;\n      }\n    };\n\n    const getIframeHtml = (data, iframeTemplateCallback) => {\n      if (iframeTemplateCallback) {\n        return iframeTemplateCallback(data);\n      } else {\n        const allowFullscreen = data.allowfullscreen ? ' allowFullscreen=\"1\"' : '';\n        return '<iframe src=\"' + data.source + '\" width=\"' + data.width + '\" height=\"' + data.height + '\"' + allowFullscreen + '></iframe>';\n      }\n    };\n    const getFlashHtml = data => {\n      let html = '<object data=\"' + data.source + '\" width=\"' + data.width + '\" height=\"' + data.height + '\" type=\"application/x-shockwave-flash\">';\n      if (data.poster) {\n        html += '<img src=\"' + data.poster + '\" width=\"' + data.width + '\" height=\"' + data.height + '\" />';\n      }\n      html += '</object>';\n      return html;\n    };\n    const getAudioHtml = (data, audioTemplateCallback) => {\n      if (audioTemplateCallback) {\n        return audioTemplateCallback(data);\n      } else {\n        return '<audio controls=\"controls\" src=\"' + data.source + '\">' + (data.altsource ? '\\n<source src=\"' + data.altsource + '\"' + (data.altsourcemime ? ' type=\"' + data.altsourcemime + '\"' : '') + ' />\\n' : '') + '</audio>';\n      }\n    };\n    const getVideoHtml = (data, videoTemplateCallback) => {\n      if (videoTemplateCallback) {\n        return videoTemplateCallback(data);\n      } else {\n        return '<video width=\"' + data.width + '\" height=\"' + data.height + '\"' + (data.poster ? ' poster=\"' + data.poster + '\"' : '') + ' controls=\"controls\">\\n' + '<source src=\"' + data.source + '\"' + (data.sourcemime ? ' type=\"' + data.sourcemime + '\"' : '') + ' />\\n' + (data.altsource ? '<source src=\"' + data.altsource + '\"' + (data.altsourcemime ? ' type=\"' + data.altsourcemime + '\"' : '') + ' />\\n' : '') + '</video>';\n      }\n    };\n    const dataToHtml = (editor, dataIn) => {\n      var _a;\n      const data = global$5.extend({}, dataIn);\n      if (!data.source) {\n        global$5.extend(data, htmlToData((_a = data.embed) !== null && _a !== void 0 ? _a : '', editor.schema));\n        if (!data.source) {\n          return '';\n        }\n      }\n      if (!data.altsource) {\n        data.altsource = '';\n      }\n      if (!data.poster) {\n        data.poster = '';\n      }\n      data.source = editor.convertURL(data.source, 'source');\n      data.altsource = editor.convertURL(data.altsource, 'source');\n      data.sourcemime = guess(data.source);\n      data.altsourcemime = guess(data.altsource);\n      data.poster = editor.convertURL(data.poster, 'poster');\n      const pattern = matchPattern(data.source);\n      if (pattern) {\n        data.source = pattern.url;\n        data.type = pattern.type;\n        data.allowfullscreen = pattern.allowFullscreen;\n        data.width = data.width || String(pattern.w);\n        data.height = data.height || String(pattern.h);\n      }\n      if (data.embed) {\n        return updateHtml(data.embed, data, true, editor.schema);\n      } else {\n        const audioTemplateCallback = getAudioTemplateCallback(editor);\n        const videoTemplateCallback = getVideoTemplateCallback(editor);\n        const iframeTemplateCallback = getIframeTemplateCallback(editor);\n        data.width = data.width || '300';\n        data.height = data.height || '150';\n        global$5.each(data, (value, key) => {\n          data[key] = editor.dom.encode('' + value);\n        });\n        if (data.type === 'iframe') {\n          return getIframeHtml(data, iframeTemplateCallback);\n        } else if (data.sourcemime === 'application/x-shockwave-flash') {\n          return getFlashHtml(data);\n        } else if (data.sourcemime.indexOf('audio') !== -1) {\n          return getAudioHtml(data, audioTemplateCallback);\n        } else {\n          return getVideoHtml(data, videoTemplateCallback);\n        }\n      }\n    };\n\n    const isMediaElement = element => element.hasAttribute('data-mce-object') || element.hasAttribute('data-ephox-embed-iri');\n    const setup$2 = editor => {\n      editor.on('mousedown', e => {\n        const previewObj = editor.dom.getParent(e.target, '.mce-preview-object');\n        if (previewObj && editor.dom.getAttrib(previewObj, 'data-mce-selected') === '2') {\n          e.stopImmediatePropagation();\n        }\n      });\n      editor.on('click keyup touchend', () => {\n        const selectedNode = editor.selection.getNode();\n        if (selectedNode && editor.dom.hasClass(selectedNode, 'mce-preview-object')) {\n          if (editor.dom.getAttrib(selectedNode, 'data-mce-selected')) {\n            selectedNode.setAttribute('data-mce-selected', '2');\n          }\n        }\n      });\n      editor.on('ObjectResized', e => {\n        const target = e.target;\n        if (target.getAttribute('data-mce-object')) {\n          let html = target.getAttribute('data-mce-html');\n          if (html) {\n            html = unescape(html);\n            target.setAttribute('data-mce-html', escape(updateHtml(html, {\n              width: String(e.width),\n              height: String(e.height)\n            }, false, editor.schema)));\n          }\n        }\n      });\n    };\n\n    const cache = {};\n    const embedPromise = (data, dataToHtml, handler) => {\n      return new Promise((res, rej) => {\n        const wrappedResolve = response => {\n          if (response.html) {\n            cache[data.source] = response;\n          }\n          return res({\n            url: data.source,\n            html: response.html ? response.html : dataToHtml(data)\n          });\n        };\n        if (cache[data.source]) {\n          wrappedResolve(cache[data.source]);\n        } else {\n          handler({ url: data.source }).then(wrappedResolve).catch(rej);\n        }\n      });\n    };\n    const defaultPromise = (data, dataToHtml) => Promise.resolve({\n      html: dataToHtml(data),\n      url: data.source\n    });\n    const loadedData = editor => data => dataToHtml(editor, data);\n    const getEmbedHtml = (editor, data) => {\n      const embedHandler = getUrlResolver(editor);\n      return embedHandler ? embedPromise(data, loadedData(editor), embedHandler) : defaultPromise(data, loadedData(editor));\n    };\n    const isCached = url => has(cache, url);\n\n    const extractMeta = (sourceInput, data) => get$1(data, sourceInput).bind(mainData => get$1(mainData, 'meta'));\n    const getValue = (data, metaData, sourceInput) => prop => {\n      const getFromData = () => get$1(data, prop);\n      const getFromMetaData = () => get$1(metaData, prop);\n      const getNonEmptyValue = c => get$1(c, 'value').bind(v => v.length > 0 ? Optional.some(v) : Optional.none());\n      const getFromValueFirst = () => getFromData().bind(child => isObject(child) ? getNonEmptyValue(child).orThunk(getFromMetaData) : getFromMetaData().orThunk(() => Optional.from(child)));\n      const getFromMetaFirst = () => getFromMetaData().orThunk(() => getFromData().bind(child => isObject(child) ? getNonEmptyValue(child) : Optional.from(child)));\n      return { [prop]: (prop === sourceInput ? getFromValueFirst() : getFromMetaFirst()).getOr('') };\n    };\n    const getDimensions = (data, metaData) => {\n      const dimensions = {};\n      get$1(data, 'dimensions').each(dims => {\n        each$1([\n          'width',\n          'height'\n        ], prop => {\n          get$1(metaData, prop).orThunk(() => get$1(dims, prop)).each(value => dimensions[prop] = value);\n        });\n      });\n      return dimensions;\n    };\n    const unwrap = (data, sourceInput) => {\n      const metaData = sourceInput && sourceInput !== 'dimensions' ? extractMeta(sourceInput, data).getOr({}) : {};\n      const get = getValue(data, metaData, sourceInput);\n      return {\n        ...get('source'),\n        ...get('altsource'),\n        ...get('poster'),\n        ...get('embed'),\n        ...getDimensions(data, metaData)\n      };\n    };\n    const wrap = data => {\n      const wrapped = {\n        ...data,\n        source: { value: get$1(data, 'source').getOr('') },\n        altsource: { value: get$1(data, 'altsource').getOr('') },\n        poster: { value: get$1(data, 'poster').getOr('') }\n      };\n      each$1([\n        'width',\n        'height'\n      ], prop => {\n        get$1(data, prop).each(value => {\n          const dimensions = wrapped.dimensions || {};\n          dimensions[prop] = value;\n          wrapped.dimensions = dimensions;\n        });\n      });\n      return wrapped;\n    };\n    const handleError = editor => error => {\n      const errorMessage = error && error.msg ? 'Media embed handler error: ' + error.msg : 'Media embed handler threw unknown error.';\n      editor.notificationManager.open({\n        type: 'error',\n        text: errorMessage\n      });\n    };\n    const getEditorData = editor => {\n      const element = editor.selection.getNode();\n      const snippet = isMediaElement(element) ? editor.serializer.serialize(element, { selection: true }) : '';\n      const data = htmlToData(snippet, editor.schema);\n      const getDimensionsOfElement = () => {\n        if (isEmbedIframe(data.source, data.type)) {\n          const rect = editor.dom.getRect(element);\n          return {\n            width: rect.w.toString().replace(/px$/, ''),\n            height: rect.h.toString().replace(/px$/, '')\n          };\n        } else {\n          return {};\n        }\n      };\n      const dimensions = getDimensionsOfElement();\n      return {\n        embed: snippet,\n        ...data,\n        ...dimensions\n      };\n    };\n    const addEmbedHtml = (api, editor) => response => {\n      if (isString(response.url) && response.url.trim().length > 0) {\n        const html = response.html;\n        const snippetData = htmlToData(html, editor.schema);\n        const nuData = {\n          ...snippetData,\n          source: response.url,\n          embed: html\n        };\n        api.setData(wrap(nuData));\n      }\n    };\n    const selectPlaceholder = (editor, beforeObjects) => {\n      const afterObjects = editor.dom.select('*[data-mce-object]');\n      for (let i = 0; i < beforeObjects.length; i++) {\n        for (let y = afterObjects.length - 1; y >= 0; y--) {\n          if (beforeObjects[i] === afterObjects[y]) {\n            afterObjects.splice(y, 1);\n          }\n        }\n      }\n      editor.selection.select(afterObjects[0]);\n    };\n    const handleInsert = (editor, html) => {\n      const beforeObjects = editor.dom.select('*[data-mce-object]');\n      editor.insertContent(html);\n      selectPlaceholder(editor, beforeObjects);\n      editor.nodeChanged();\n    };\n    const isEmbedIframe = (url, mediaDataType) => isNonNullable(mediaDataType) && mediaDataType === 'ephox-embed-iri' && isNonNullable(matchPattern(url));\n    const shouldInsertAsNewIframe = (prevData, newData) => {\n      const hasDimensionsChanged = (prevData, newData) => prevData.width !== newData.width || prevData.height !== newData.height;\n      return hasDimensionsChanged(prevData, newData) && isEmbedIframe(newData.source, prevData.type);\n    };\n    const submitForm = (prevData, newData, editor) => {\n      var _a;\n      newData.embed = shouldInsertAsNewIframe(prevData, newData) && hasDimensions(editor) ? dataToHtml(editor, {\n        ...newData,\n        embed: ''\n      }) : updateHtml((_a = newData.embed) !== null && _a !== void 0 ? _a : '', newData, false, editor.schema);\n      if (newData.embed && (prevData.source === newData.source || isCached(newData.source))) {\n        handleInsert(editor, newData.embed);\n      } else {\n        getEmbedHtml(editor, newData).then(response => {\n          handleInsert(editor, response.html);\n        }).catch(handleError(editor));\n      }\n    };\n    const showDialog = editor => {\n      const editorData = getEditorData(editor);\n      const currentData = Cell(editorData);\n      const initialData = wrap(editorData);\n      const handleSource = (prevData, api) => {\n        const serviceData = unwrap(api.getData(), 'source');\n        if (prevData.source !== serviceData.source) {\n          addEmbedHtml(win, editor)({\n            url: serviceData.source,\n            html: ''\n          });\n          getEmbedHtml(editor, serviceData).then(addEmbedHtml(win, editor)).catch(handleError(editor));\n        }\n      };\n      const handleEmbed = api => {\n        var _a;\n        const data = unwrap(api.getData());\n        const dataFromEmbed = htmlToData((_a = data.embed) !== null && _a !== void 0 ? _a : '', editor.schema);\n        api.setData(wrap(dataFromEmbed));\n      };\n      const handleUpdate = (api, sourceInput, prevData) => {\n        const dialogData = unwrap(api.getData(), sourceInput);\n        const data = shouldInsertAsNewIframe(prevData, dialogData) && hasDimensions(editor) ? {\n          ...dialogData,\n          embed: ''\n        } : dialogData;\n        const embed = dataToHtml(editor, data);\n        api.setData(wrap({\n          ...data,\n          embed\n        }));\n      };\n      const mediaInput = [{\n          name: 'source',\n          type: 'urlinput',\n          filetype: 'media',\n          label: 'Source',\n          picker_text: 'Browse files'\n        }];\n      const sizeInput = !hasDimensions(editor) ? [] : [{\n          type: 'sizeinput',\n          name: 'dimensions',\n          label: 'Constrain proportions',\n          constrain: true\n        }];\n      const generalTab = {\n        title: 'General',\n        name: 'general',\n        items: flatten([\n          mediaInput,\n          sizeInput\n        ])\n      };\n      const embedTextarea = {\n        type: 'textarea',\n        name: 'embed',\n        label: 'Paste your embed code below:'\n      };\n      const embedTab = {\n        title: 'Embed',\n        items: [embedTextarea]\n      };\n      const advancedFormItems = [];\n      if (hasAltSource(editor)) {\n        advancedFormItems.push({\n          name: 'altsource',\n          type: 'urlinput',\n          filetype: 'media',\n          label: 'Alternative source URL'\n        });\n      }\n      if (hasPoster(editor)) {\n        advancedFormItems.push({\n          name: 'poster',\n          type: 'urlinput',\n          filetype: 'image',\n          label: 'Media poster (Image URL)'\n        });\n      }\n      const advancedTab = {\n        title: 'Advanced',\n        name: 'advanced',\n        items: advancedFormItems\n      };\n      const tabs = [\n        generalTab,\n        embedTab\n      ];\n      if (advancedFormItems.length > 0) {\n        tabs.push(advancedTab);\n      }\n      const body = {\n        type: 'tabpanel',\n        tabs\n      };\n      const win = editor.windowManager.open({\n        title: 'Insert/Edit Media',\n        size: 'normal',\n        body,\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        onSubmit: api => {\n          const serviceData = unwrap(api.getData());\n          submitForm(currentData.get(), serviceData, editor);\n          api.close();\n        },\n        onChange: (api, detail) => {\n          switch (detail.name) {\n          case 'source':\n            handleSource(currentData.get(), api);\n            break;\n          case 'embed':\n            handleEmbed(api);\n            break;\n          case 'dimensions':\n          case 'altsource':\n          case 'poster':\n            handleUpdate(api, detail.name, currentData.get());\n            break;\n          }\n          currentData.set(unwrap(api.getData()));\n        },\n        initialData\n      });\n    };\n\n    const get = editor => {\n      const showDialog$1 = () => {\n        showDialog(editor);\n      };\n      return { showDialog: showDialog$1 };\n    };\n\n    const register$1 = editor => {\n      const showDialog$1 = () => {\n        showDialog(editor);\n      };\n      editor.addCommand('mceMedia', showDialog$1);\n    };\n\n    const checkRange = (str, substr, start) => substr === '' || str.length >= substr.length && str.substr(start, start + substr.length) === substr;\n    const startsWith = (str, prefix) => {\n      return checkRange(str, prefix, 0);\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const isLiveEmbedNode = node => {\n      const name = node.name;\n      return name === 'iframe' || name === 'video' || name === 'audio';\n    };\n    const getDimension = (node, styles, dimension, defaultValue = null) => {\n      const value = node.attr(dimension);\n      if (isNonNullable(value)) {\n        return value;\n      } else if (!has(styles, dimension)) {\n        return defaultValue;\n      } else {\n        return null;\n      }\n    };\n    const setDimensions = (node, previewNode, styles) => {\n      const useDefaults = previewNode.name === 'img' || node.name === 'video';\n      const defaultWidth = useDefaults ? '300' : null;\n      const fallbackHeight = node.name === 'audio' ? '30' : '150';\n      const defaultHeight = useDefaults ? fallbackHeight : null;\n      previewNode.attr({\n        width: getDimension(node, styles, 'width', defaultWidth),\n        height: getDimension(node, styles, 'height', defaultHeight)\n      });\n    };\n    const appendNodeContent = (editor, nodeName, previewNode, html) => {\n      const newNode = Parser(editor.schema).parse(html, { context: nodeName });\n      while (newNode.firstChild) {\n        previewNode.append(newNode.firstChild);\n      }\n    };\n    const createPlaceholderNode = (editor, node) => {\n      const name = node.name;\n      const placeHolder = new global$2('img', 1);\n      retainAttributesAndInnerHtml(editor, node, placeHolder);\n      setDimensions(node, placeHolder, {});\n      placeHolder.attr({\n        'style': node.attr('style'),\n        'src': global.transparentSrc,\n        'data-mce-object': name,\n        'class': 'mce-object mce-object-' + name\n      });\n      return placeHolder;\n    };\n    const createPreviewNode = (editor, node) => {\n      var _a;\n      const name = node.name;\n      const previewWrapper = new global$2('span', 1);\n      previewWrapper.attr({\n        'contentEditable': 'false',\n        'style': node.attr('style'),\n        'data-mce-object': name,\n        'class': 'mce-preview-object mce-object-' + name\n      });\n      retainAttributesAndInnerHtml(editor, node, previewWrapper);\n      const styles = editor.dom.parseStyle((_a = node.attr('style')) !== null && _a !== void 0 ? _a : '');\n      const previewNode = new global$2(name, 1);\n      setDimensions(node, previewNode, styles);\n      previewNode.attr({\n        src: node.attr('src'),\n        style: node.attr('style'),\n        class: node.attr('class')\n      });\n      if (name === 'iframe') {\n        previewNode.attr({\n          allowfullscreen: node.attr('allowfullscreen'),\n          frameborder: '0',\n          sandbox: node.attr('sandbox')\n        });\n      } else {\n        const attrs = [\n          'controls',\n          'crossorigin',\n          'currentTime',\n          'loop',\n          'muted',\n          'poster',\n          'preload'\n        ];\n        each$1(attrs, attrName => {\n          previewNode.attr(attrName, node.attr(attrName));\n        });\n        const sanitizedHtml = previewWrapper.attr('data-mce-html');\n        if (isNonNullable(sanitizedHtml)) {\n          appendNodeContent(editor, name, previewNode, unescape(sanitizedHtml));\n        }\n      }\n      const shimNode = new global$2('span', 1);\n      shimNode.attr('class', 'mce-shim');\n      previewWrapper.append(previewNode);\n      previewWrapper.append(shimNode);\n      return previewWrapper;\n    };\n    const retainAttributesAndInnerHtml = (editor, sourceNode, targetNode) => {\n      var _a;\n      const attribs = (_a = sourceNode.attributes) !== null && _a !== void 0 ? _a : [];\n      let ai = attribs.length;\n      while (ai--) {\n        const attrName = attribs[ai].name;\n        let attrValue = attribs[ai].value;\n        if (attrName !== 'width' && attrName !== 'height' && attrName !== 'style' && !startsWith(attrName, 'data-mce-')) {\n          if (attrName === 'data' || attrName === 'src') {\n            attrValue = editor.convertURL(attrValue, attrName);\n          }\n          targetNode.attr('data-mce-p-' + attrName, attrValue);\n        }\n      }\n      const serializer = global$1({ inner: true }, editor.schema);\n      const tempNode = new global$2('div', 1);\n      each$1(sourceNode.children(), child => tempNode.append(child));\n      const innerHtml = serializer.serialize(tempNode);\n      if (innerHtml) {\n        targetNode.attr('data-mce-html', escape(innerHtml));\n        targetNode.empty();\n      }\n    };\n    const isPageEmbedWrapper = node => {\n      const nodeClass = node.attr('class');\n      return isString(nodeClass) && /\\btiny-pageembed\\b/.test(nodeClass);\n    };\n    const isWithinEmbedWrapper = node => {\n      let tempNode = node;\n      while (tempNode = tempNode.parent) {\n        if (tempNode.attr('data-ephox-embed-iri') || isPageEmbedWrapper(tempNode)) {\n          return true;\n        }\n      }\n      return false;\n    };\n    const placeHolderConverter = editor => nodes => {\n      let i = nodes.length;\n      let node;\n      while (i--) {\n        node = nodes[i];\n        if (!node.parent) {\n          continue;\n        }\n        if (node.parent.attr('data-mce-object')) {\n          continue;\n        }\n        if (isLiveEmbedNode(node) && hasLiveEmbeds(editor)) {\n          if (!isWithinEmbedWrapper(node)) {\n            node.replace(createPreviewNode(editor, node));\n          }\n        } else {\n          if (!isWithinEmbedWrapper(node)) {\n            node.replace(createPlaceholderNode(editor, node));\n          }\n        }\n      }\n    };\n\n    const parseAndSanitize = (editor, context, html) => {\n      const getEditorOption = editor.options.get;\n      const sanitize = getEditorOption('xss_sanitization');\n      const validate = shouldFilterHtml(editor);\n      return Parser(editor.schema, {\n        sanitize,\n        validate\n      }).parse(html, { context });\n    };\n\n    const setup$1 = editor => {\n      editor.on('PreInit', () => {\n        const {schema, serializer, parser} = editor;\n        const boolAttrs = schema.getBoolAttrs();\n        each$1('webkitallowfullscreen mozallowfullscreen'.split(' '), name => {\n          boolAttrs[name] = {};\n        });\n        each({ embed: ['wmode'] }, (attrs, name) => {\n          const rule = schema.getElementRule(name);\n          if (rule) {\n            each$1(attrs, attr => {\n              rule.attributes[attr] = {};\n              rule.attributesOrder.push(attr);\n            });\n          }\n        });\n        parser.addNodeFilter('iframe,video,audio,object,embed', placeHolderConverter(editor));\n        serializer.addAttributeFilter('data-mce-object', (nodes, name) => {\n          var _a;\n          let i = nodes.length;\n          while (i--) {\n            const node = nodes[i];\n            if (!node.parent) {\n              continue;\n            }\n            const realElmName = node.attr(name);\n            const realElm = new global$2(realElmName, 1);\n            if (realElmName !== 'audio') {\n              const className = node.attr('class');\n              if (className && className.indexOf('mce-preview-object') !== -1 && node.firstChild) {\n                realElm.attr({\n                  width: node.firstChild.attr('width'),\n                  height: node.firstChild.attr('height')\n                });\n              } else {\n                realElm.attr({\n                  width: node.attr('width'),\n                  height: node.attr('height')\n                });\n              }\n            }\n            realElm.attr({ style: node.attr('style') });\n            const attribs = (_a = node.attributes) !== null && _a !== void 0 ? _a : [];\n            let ai = attribs.length;\n            while (ai--) {\n              const attrName = attribs[ai].name;\n              if (attrName.indexOf('data-mce-p-') === 0) {\n                realElm.attr(attrName.substr(11), attribs[ai].value);\n              }\n            }\n            const innerHtml = node.attr('data-mce-html');\n            if (innerHtml) {\n              const fragment = parseAndSanitize(editor, realElmName, unescape(innerHtml));\n              each$1(fragment.children(), child => realElm.append(child));\n            }\n            node.replace(realElm);\n          }\n        });\n      });\n      editor.on('SetContent', () => {\n        const dom = editor.dom;\n        each$1(dom.select('span.mce-preview-object'), elm => {\n          if (dom.select('span.mce-shim', elm).length === 0) {\n            dom.add(elm, 'span', { class: 'mce-shim' });\n          }\n        });\n      });\n    };\n\n    const setup = editor => {\n      editor.on('ResolveName', e => {\n        let name;\n        if (e.target.nodeType === 1 && (name = e.target.getAttribute('data-mce-object'))) {\n          e.name = name;\n        }\n      });\n    };\n\n    const onSetupEditable = editor => api => {\n      const nodeChanged = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChanged);\n      nodeChanged();\n      return () => {\n        editor.off('NodeChange', nodeChanged);\n      };\n    };\n    const register = editor => {\n      const onAction = () => editor.execCommand('mceMedia');\n      editor.ui.registry.addToggleButton('media', {\n        tooltip: 'Insert/edit media',\n        icon: 'embed',\n        onAction,\n        onSetup: buttonApi => {\n          const selection = editor.selection;\n          buttonApi.setActive(isMediaElement(selection.getNode()));\n          const unbindSelectorChanged = selection.selectorChangedWithUnbind('img[data-mce-object],span[data-mce-object],div[data-ephox-embed-iri]', buttonApi.setActive).unbind;\n          const unbindEditable = onSetupEditable(editor)(buttonApi);\n          return () => {\n            unbindSelectorChanged();\n            unbindEditable();\n          };\n        }\n      });\n      editor.ui.registry.addMenuItem('media', {\n        icon: 'embed',\n        text: 'Media...',\n        onAction,\n        onSetup: onSetupEditable(editor)\n      });\n    };\n\n    var Plugin = () => {\n      global$6.add('media', editor => {\n        register$2(editor);\n        register$1(editor);\n        register(editor);\n        setup(editor);\n        setup$1(editor);\n        setup$2(editor);\n        return get(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,QAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,QAAI;AACJ,QAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,aAAO;AAAA,IACT,OAAO;AACL,eAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,IAC7F;AAAA,EACF;AACA,QAAM,SAAS,OAAK;AAClB,UAAM,IAAI,OAAO;AACjB,QAAI,MAAM,MAAM;AACd,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,SAAS,UAAQ,WAAS,OAAO,KAAK,MAAM;AAClD,QAAM,WAAW,OAAO,QAAQ;AAChC,QAAM,WAAW,OAAO,QAAQ;AAChC,QAAM,UAAU,OAAO,OAAO;AAC9B,QAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,QAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AAAA,EAExC,MAAM,SAAS;AAAA,IACb,YAAY,KAAK,OAAO;AACtB,WAAK,MAAM;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,IACA,OAAO,KAAK,OAAO;AACjB,aAAO,IAAI,SAAS,MAAM,KAAK;AAAA,IACjC;AAAA,IACA,OAAO,OAAO;AACZ,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,KAAK,QAAQ,QAAQ;AACnB,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AACP,aAAO,KAAK;AAAA,IACd;AAAA,IACA,SAAS;AACP,aAAO,CAAC,KAAK;AAAA,IACf;AAAA,IACA,IAAI,QAAQ;AACV,UAAI,KAAK,KAAK;AACZ,eAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,MACzC,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IACzC;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IAC1C;AAAA,IACA,OAAO,WAAW;AAChB,UAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,MAAM,aAAa;AACjB,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,GAAG,aAAa;AACd,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B;AAAA,IACA,WAAW,OAAO;AAChB,aAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,IACvC;AAAA,IACA,QAAQ,OAAO;AACb,aAAO,KAAK,MAAM,OAAO,MAAM;AAAA,IACjC;AAAA,IACA,SAAS,SAAS;AAChB,UAAI,CAAC,KAAK,KAAK;AACb,cAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,MAC9F,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,IACA,OAAO,KAAK,OAAO;AACjB,aAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,IACrE;AAAA,IACA,YAAY;AACV,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,iBAAiB;AACf,aAAO,KAAK;AAAA,IACd;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,IACA,UAAU;AACR,aAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,IACpC;AAAA,IACA,WAAW;AACT,aAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,IAC9C;AAAA,EACF;AACA,WAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,QAAM,aAAa,MAAM,UAAU;AACnC,QAAM,SAAS,CAAC,IAAI,MAAM;AACxB,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,UAAU,QAAM;AACpB,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,UAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG;AACnB,cAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,MAC7E;AACA,iBAAW,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,aAAW;AACtB,QAAI,QAAQ;AACZ,UAAMA,OAAM,MAAM;AAChB,aAAO;AAAA,IACT;AACA,UAAM,MAAM,OAAK;AACf,cAAQ;AAAA,IACV;AACA,WAAO;AAAA,MACL,KAAAA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO,OAAO;AACpB,QAAM,iBAAiB,OAAO;AAC9B,QAAM,OAAO,CAAC,KAAK,MAAM;AACvB,UAAM,QAAQ,KAAK,GAAG;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,IAAI,IAAI,CAAC;AACf,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,KAAK,QAAQ;AAC1B,WAAO,IAAI,KAAK,GAAG,IAAI,SAAS,KAAK,IAAI,GAAG,CAAC,IAAI,SAAS,KAAK;AAAA,EACjE;AACA,QAAM,MAAM,CAAC,KAAK,QAAQ,eAAe,KAAK,KAAK,GAAG;AAEtD,QAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,QAAM,aAAa,YAAU;AAC3B,UAAM,iBAAiB,OAAO,QAAQ;AACtC,mBAAe,2BAA2B,EAAE,WAAW,WAAW,CAAC;AACnE,mBAAe,2BAA2B,EAAE,WAAW,WAAW,CAAC;AACnE,mBAAe,4BAA4B,EAAE,WAAW,WAAW,CAAC;AACpE,mBAAe,qBAAqB;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,qBAAqB;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,sBAAsB,EAAE,WAAW,WAAW,CAAC;AAC9D,mBAAe,oBAAoB;AAAA,MACjC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,gBAAgB;AAAA,MAC7B,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AACD,mBAAe,oBAAoB;AAAA,MACjC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,QAAM,2BAA2B,OAAO,yBAAyB;AACjE,QAAM,2BAA2B,OAAO,yBAAyB;AACjE,QAAM,4BAA4B,OAAO,0BAA0B;AACnE,QAAM,gBAAgB,OAAO,mBAAmB;AAChD,QAAM,mBAAmB,OAAO,mBAAmB;AACnD,QAAM,iBAAiB,OAAO,oBAAoB;AAClD,QAAM,eAAe,OAAO,kBAAkB;AAC9C,QAAM,YAAY,OAAO,cAAc;AACvC,QAAM,gBAAgB,OAAO,kBAAkB;AAE/C,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE9D,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,sBAAsB;AAEhE,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,wBAAwB;AAElE,QAAM,QAAQ,SAAS;AACvB,QAAM,SAAS,WAAS,MAAM,QAAQ,OAAO,EAAE;AAC/C,QAAM,oBAAoB,UAAQ;AAChC,UAAM,QAAQ,KAAK,KAAK,OAAO;AAC/B,UAAM,SAAS,QAAQ,MAAM,WAAW,KAAK,IAAI,CAAC;AAClD,WAAO;AAAA,MACL,MAAM;AAAA,MACN,QAAQ,KAAK,KAAK,sBAAsB;AAAA,MACxC,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO,MAAM,QAAQ,WAAW,EAAE,IAAI,MAAM,EAAE,MAAM,EAAE;AAAA,MACtD,QAAQ,MAAM,QAAQ,YAAY,EAAE,IAAI,MAAM,EAAE,MAAM,EAAE;AAAA,IAC1D;AAAA,EACF;AACA,QAAM,aAAa,CAAC,MAAM,WAAW;AACnC,QAAI,OAAO,CAAC;AACZ,UAAM,SAAS,SAAS;AAAA,MACtB,UAAU;AAAA,MACV,mBAAmB;AAAA,IACrB,GAAG,MAAM;AACT,UAAM,WAAW,OAAO,MAAM,IAAI;AAClC,aAAS,OAAO,UAAU,MAAM,OAAO,KAAK,KAAK,GAAG;AAClD,UAAI,KAAK,SAAS,GAAG;AACnB,cAAM,OAAO,KAAK;AAClB,YAAI,KAAK,KAAK,sBAAsB,GAAG;AACrC,iBAAO,kBAAkB,IAAI;AAC7B;AAAA,QACF,OAAO;AACL,cAAI,CAAC,KAAK,UAAU,SAAS,SAAS;AACpC,iBAAK,SAAS,KAAK,KAAK,OAAO;AAAA,UACjC;AACA,cAAI,SAAS,YAAY,SAAS,YAAY,SAAS,WAAW,SAAS,WAAW,SAAS,SAAS;AACtG,gBAAI,CAAC,KAAK,MAAM;AACd,mBAAK,OAAO;AAAA,YACd;AACA,mBAAO,SAAS,OAAO,KAAK,WAAW,KAAK,IAAI;AAAA,UAClD;AACA,cAAI,SAAS,UAAU;AACrB,gBAAI,CAAC,KAAK,QAAQ;AAChB,mBAAK,SAAS,KAAK,KAAK,KAAK;AAAA,YAC/B,WAAW,CAAC,KAAK,WAAW;AAC1B,mBAAK,YAAY,KAAK,KAAK,KAAK;AAAA,YAClC;AAAA,UACF;AACA,cAAI,SAAS,SAAS,CAAC,KAAK,QAAQ;AAClC,iBAAK,SAAS,KAAK,KAAK,KAAK;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,SAAS,KAAK,UAAU,KAAK,OAAO;AACzC,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,SAAS,KAAK,UAAU;AAC7B,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,SAAO;AACnB,QAAI;AACJ,UAAM,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,UAAM,WAAW,KAAK,IAAI,YAAY,EAAE,MAAM,GAAG,EAAE,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK;AAC3F,WAAO,MAAM,OAAO,OAAO,EAAE,MAAM,EAAE;AAAA,EACvC;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,mBAAmB;AAE7D,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,yBAAyB;AAEnE,QAAM,SAAS,CAAC,QAAQ,WAAW,CAAC,MAAM,SAAS;AAAA,IACjD,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,4BAA4B;AAAA,IAC5B,GAAG;AAAA,EACL,GAAG,MAAM;AAET,QAAM,MAAM,SAAS;AACrB,QAAM,QAAQ,WAAS,YAAY,KAAK,KAAK,IAAI,QAAQ,OAAO;AAChE,QAAM,mBAAmB,CAAC,MAAM,SAAS;AACvC,UAAM,QAAQ,KAAK,KAAK,OAAO;AAC/B,UAAM,WAAW,QAAQ,IAAI,WAAW,KAAK,IAAI,CAAC;AAClD,QAAI,cAAc,KAAK,KAAK,GAAG;AAC7B,eAAS,WAAW,IAAI,MAAM,KAAK,KAAK;AAAA,IAC1C;AACA,QAAI,cAAc,KAAK,MAAM,GAAG;AAC9B,eAAS,YAAY,IAAI,MAAM,KAAK,MAAM;AAAA,IAC5C;AACA,SAAK,KAAK,SAAS,IAAI,eAAe,QAAQ,CAAC;AAAA,EACjD;AACA,QAAM,UAAU;AAAA,IACd;AAAA,IACA;AAAA,EACF;AACA,QAAM,aAAa,CAAC,MAAM,MAAM,WAAW,WAAW;AACpD,QAAI,aAAa;AACjB,QAAI,cAAc;AAClB,UAAM,SAAS,OAAO,MAAM;AAC5B,WAAO,cAAc,UAAU,WAAS,aAAa,MAAM,MAAM;AACjE,UAAM,WAAW,OAAO,MAAM,IAAI;AAClC,aAAS,OAAO,UAAU,MAAM,OAAO,KAAK,KAAK,GAAG;AAClD,UAAI,KAAK,SAAS,GAAG;AACnB,cAAM,OAAO,KAAK;AAClB,YAAI,KAAK,KAAK,sBAAsB,GAAG;AACrC,2BAAiB,MAAM,IAAI;AAC3B;AAAA,QACF,OAAO;AACL,kBAAQ,MAAM;AAAA,YACd,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACH,kBAAI,KAAK,WAAW,UAAa,KAAK,UAAU,QAAW;AACzD,qBAAK,KAAK,SAAS,KAAK,KAAK;AAC7B,qBAAK,KAAK,UAAU,KAAK,MAAM;AAAA,cACjC;AACA;AAAA,UACF;AACA,cAAI,WAAW;AACb,oBAAQ,MAAM;AAAA,cACd,KAAK;AACH,qBAAK,KAAK,UAAU,KAAK,MAAM;AAC/B,qBAAK,KAAK,OAAO,IAAI;AACrB,yBAAS,QAAQ,YAAY,QAAQ,GAAG,SAAS;AAC/C,sBAAI,KAAK,QAAQ,KAAK,CAAC,GAAG;AACxB,0BAAM,SAAS,IAAI,SAAS,UAAU,CAAC;AACvC,2BAAO,KAAK,OAAO,KAAK,QAAQ,KAAK,CAAC,CAAC;AACvC,2BAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,IAAI,MAAM,KAAK,IAAI;AACzD,yBAAK,OAAO,MAAM;AAAA,kBACpB;AAAA,gBACF;AACA;AAAA,cACF,KAAK;AACH,qBAAK,KAAK,OAAO,KAAK,MAAM;AAC5B;AAAA,cACF,KAAK;AACH,sBAAM,WAAW,KAAK,OAAO,KAAK,EAAE,SAAS;AAC7C,oBAAI,KAAK,UAAU,CAAC,UAAU;AAC5B,uBAAK,KAAK,OAAO,KAAK,MAAM;AAC5B,wBAAM,MAAM,IAAI,SAAS,OAAO,CAAC;AACjC,sBAAI,KAAK,OAAO,KAAK,MAAM;AAC3B,sBAAI,KAAK,SAAS,KAAK,KAAK;AAC5B,sBAAI,KAAK,UAAU,KAAK,MAAM;AAC9B,uBAAK,OAAO,GAAG;AAAA,gBACjB;AACA;AAAA,cACF,KAAK;AACH,oBAAI,cAAc,GAAG;AACnB,uBAAK,KAAK,OAAO,KAAK,QAAQ,WAAW,CAAC,CAAC;AAC3C,uBAAK,KAAK,QAAQ,KAAK,QAAQ,WAAW,IAAI,MAAM,KAAK,IAAI;AAC7D,sBAAI,CAAC,KAAK,QAAQ,WAAW,CAAC,GAAG;AAC/B,yBAAK,OAAO;AACZ;AAAA,kBACF;AAAA,gBACF;AACA;AACA;AAAA,cACF,KAAK;AACH,oBAAI,CAAC,KAAK,QAAQ;AAChB,uBAAK,OAAO;AAAA,gBACd;AACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,CAAC,GAAG,MAAM,EAAE,UAAU,QAAQ;AAAA,EAChD;AAEA,QAAM,cAAc;AAAA,IAClB;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,cAAc,SAAO;AACzB,UAAM,kBAAkB,IAAI,MAAM,4BAA4B;AAC9D,QAAI,mBAAmB,gBAAgB,SAAS,GAAG;AACjD,aAAO,gBAAgB,CAAC,MAAM,SAAS,aAAa,gBAAgB,CAAC;AAAA,IACvE,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,SAAS,CAAC,SAAS,QAAQ;AAC/B,UAAM,WAAW,YAAY,GAAG;AAChC,UAAM,QAAQ,QAAQ,MAAM,KAAK,GAAG;AACpC,QAAI,SAAS,WAAW,QAAQ;AAChC,QAAI,cAAc,KAAK,GAAG;AACxB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,iBAAS,OAAO,QAAQ,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE;AAAA,MACjE;AAAA,IACF;AACA,WAAO,OAAO,QAAQ,OAAO,EAAE;AAAA,EACjC;AACA,QAAM,eAAe,SAAO;AAC1B,UAAM,WAAW,YAAY,OAAO,aAAW,QAAQ,MAAM,KAAK,GAAG,CAAC;AACtE,QAAI,SAAS,SAAS,GAAG;AACvB,aAAO,SAAS,OAAO,CAAC,GAAG,SAAS,CAAC,GAAG,EAAE,KAAK,OAAO,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC;AAAA,IAC3E,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,gBAAgB,CAAC,MAAM,2BAA2B;AACtD,QAAI,wBAAwB;AAC1B,aAAO,uBAAuB,IAAI;AAAA,IACpC,OAAO;AACL,YAAM,kBAAkB,KAAK,kBAAkB,yBAAyB;AACxE,aAAO,kBAAkB,KAAK,SAAS,cAAc,KAAK,QAAQ,eAAe,KAAK,SAAS,MAAM,kBAAkB;AAAA,IACzH;AAAA,EACF;AACA,QAAM,eAAe,UAAQ;AAC3B,QAAI,OAAO,mBAAmB,KAAK,SAAS,cAAc,KAAK,QAAQ,eAAe,KAAK,SAAS;AACpG,QAAI,KAAK,QAAQ;AACf,cAAQ,eAAe,KAAK,SAAS,cAAc,KAAK,QAAQ,eAAe,KAAK,SAAS;AAAA,IAC/F;AACA,YAAQ;AACR,WAAO;AAAA,EACT;AACA,QAAM,eAAe,CAAC,MAAM,0BAA0B;AACpD,QAAI,uBAAuB;AACzB,aAAO,sBAAsB,IAAI;AAAA,IACnC,OAAO;AACL,aAAO,qCAAqC,KAAK,SAAS,QAAQ,KAAK,YAAY,oBAAoB,KAAK,YAAY,OAAO,KAAK,gBAAgB,YAAY,KAAK,gBAAgB,MAAM,MAAM,UAAU,MAAM;AAAA,IACnN;AAAA,EACF;AACA,QAAM,eAAe,CAAC,MAAM,0BAA0B;AACpD,QAAI,uBAAuB;AACzB,aAAO,sBAAsB,IAAI;AAAA,IACnC,OAAO;AACL,aAAO,mBAAmB,KAAK,QAAQ,eAAe,KAAK,SAAS,OAAO,KAAK,SAAS,cAAc,KAAK,SAAS,MAAM,MAAM,yCAA8C,KAAK,SAAS,OAAO,KAAK,aAAa,YAAY,KAAK,aAAa,MAAM,MAAM,WAAW,KAAK,YAAY,kBAAkB,KAAK,YAAY,OAAO,KAAK,gBAAgB,YAAY,KAAK,gBAAgB,MAAM,MAAM,UAAU,MAAM;AAAA,IAC1Z;AAAA,EACF;AACA,QAAM,aAAa,CAAC,QAAQ,WAAW;AACrC,QAAI;AACJ,UAAM,OAAO,SAAS,OAAO,CAAC,GAAG,MAAM;AACvC,QAAI,CAAC,KAAK,QAAQ;AAChB,eAAS,OAAO,MAAM,YAAY,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAK,IAAI,OAAO,MAAM,CAAC;AACtG,UAAI,CAAC,KAAK,QAAQ;AAChB,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS;AAAA,IAChB;AACA,SAAK,SAAS,OAAO,WAAW,KAAK,QAAQ,QAAQ;AACrD,SAAK,YAAY,OAAO,WAAW,KAAK,WAAW,QAAQ;AAC3D,SAAK,aAAa,MAAM,KAAK,MAAM;AACnC,SAAK,gBAAgB,MAAM,KAAK,SAAS;AACzC,SAAK,SAAS,OAAO,WAAW,KAAK,QAAQ,QAAQ;AACrD,UAAM,UAAU,aAAa,KAAK,MAAM;AACxC,QAAI,SAAS;AACX,WAAK,SAAS,QAAQ;AACtB,WAAK,OAAO,QAAQ;AACpB,WAAK,kBAAkB,QAAQ;AAC/B,WAAK,QAAQ,KAAK,SAAS,OAAO,QAAQ,CAAC;AAC3C,WAAK,SAAS,KAAK,UAAU,OAAO,QAAQ,CAAC;AAAA,IAC/C;AACA,QAAI,KAAK,OAAO;AACd,aAAO,WAAW,KAAK,OAAO,MAAM,MAAM,OAAO,MAAM;AAAA,IACzD,OAAO;AACL,YAAM,wBAAwB,yBAAyB,MAAM;AAC7D,YAAM,wBAAwB,yBAAyB,MAAM;AAC7D,YAAM,yBAAyB,0BAA0B,MAAM;AAC/D,WAAK,QAAQ,KAAK,SAAS;AAC3B,WAAK,SAAS,KAAK,UAAU;AAC7B,eAAS,KAAK,MAAM,CAAC,OAAO,QAAQ;AAClC,aAAK,GAAG,IAAI,OAAO,IAAI,OAAO,KAAK,KAAK;AAAA,MAC1C,CAAC;AACD,UAAI,KAAK,SAAS,UAAU;AAC1B,eAAO,cAAc,MAAM,sBAAsB;AAAA,MACnD,WAAW,KAAK,eAAe,iCAAiC;AAC9D,eAAO,aAAa,IAAI;AAAA,MAC1B,WAAW,KAAK,WAAW,QAAQ,OAAO,MAAM,IAAI;AAClD,eAAO,aAAa,MAAM,qBAAqB;AAAA,MACjD,OAAO;AACL,eAAO,aAAa,MAAM,qBAAqB;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,iBAAiB,aAAW,QAAQ,aAAa,iBAAiB,KAAK,QAAQ,aAAa,sBAAsB;AACxH,QAAM,UAAU,YAAU;AACxB,WAAO,GAAG,aAAa,OAAK;AAC1B,YAAM,aAAa,OAAO,IAAI,UAAU,EAAE,QAAQ,qBAAqB;AACvE,UAAI,cAAc,OAAO,IAAI,UAAU,YAAY,mBAAmB,MAAM,KAAK;AAC/E,UAAE,yBAAyB;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,WAAO,GAAG,wBAAwB,MAAM;AACtC,YAAM,eAAe,OAAO,UAAU,QAAQ;AAC9C,UAAI,gBAAgB,OAAO,IAAI,SAAS,cAAc,oBAAoB,GAAG;AAC3E,YAAI,OAAO,IAAI,UAAU,cAAc,mBAAmB,GAAG;AAC3D,uBAAa,aAAa,qBAAqB,GAAG;AAAA,QACpD;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,GAAG,iBAAiB,OAAK;AAC9B,YAAM,SAAS,EAAE;AACjB,UAAI,OAAO,aAAa,iBAAiB,GAAG;AAC1C,YAAI,OAAO,OAAO,aAAa,eAAe;AAC9C,YAAI,MAAM;AACR,iBAAO,SAAS,IAAI;AACpB,iBAAO,aAAa,iBAAiB,OAAO,WAAW,MAAM;AAAA,YAC3D,OAAO,OAAO,EAAE,KAAK;AAAA,YACrB,QAAQ,OAAO,EAAE,MAAM;AAAA,UACzB,GAAG,OAAO,OAAO,MAAM,CAAC,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,QAAQ,CAAC;AACf,QAAM,eAAe,CAAC,MAAMC,aAAY,YAAY;AAClD,WAAO,IAAI,QAAQ,CAAC,KAAK,QAAQ;AAC/B,YAAM,iBAAiB,cAAY;AACjC,YAAI,SAAS,MAAM;AACjB,gBAAM,KAAK,MAAM,IAAI;AAAA,QACvB;AACA,eAAO,IAAI;AAAA,UACT,KAAK,KAAK;AAAA,UACV,MAAM,SAAS,OAAO,SAAS,OAAOA,YAAW,IAAI;AAAA,QACvD,CAAC;AAAA,MACH;AACA,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,uBAAe,MAAM,KAAK,MAAM,CAAC;AAAA,MACnC,OAAO;AACL,gBAAQ,EAAE,KAAK,KAAK,OAAO,CAAC,EAAE,KAAK,cAAc,EAAE,MAAM,GAAG;AAAA,MAC9D;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,iBAAiB,CAAC,MAAMA,gBAAe,QAAQ,QAAQ;AAAA,IAC3D,MAAMA,YAAW,IAAI;AAAA,IACrB,KAAK,KAAK;AAAA,EACZ,CAAC;AACD,QAAM,aAAa,YAAU,UAAQ,WAAW,QAAQ,IAAI;AAC5D,QAAM,eAAe,CAAC,QAAQ,SAAS;AACrC,UAAM,eAAe,eAAe,MAAM;AAC1C,WAAO,eAAe,aAAa,MAAM,WAAW,MAAM,GAAG,YAAY,IAAI,eAAe,MAAM,WAAW,MAAM,CAAC;AAAA,EACtH;AACA,QAAM,WAAW,SAAO,IAAI,OAAO,GAAG;AAEtC,QAAM,cAAc,CAAC,aAAa,SAAS,MAAM,MAAM,WAAW,EAAE,KAAK,cAAY,MAAM,UAAU,MAAM,CAAC;AAC5G,QAAM,WAAW,CAAC,MAAM,UAAU,gBAAgB,UAAQ;AACxD,UAAM,cAAc,MAAM,MAAM,MAAM,IAAI;AAC1C,UAAM,kBAAkB,MAAM,MAAM,UAAU,IAAI;AAClD,UAAM,mBAAmB,OAAK,MAAM,GAAG,OAAO,EAAE,KAAK,OAAK,EAAE,SAAS,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK,CAAC;AAC3G,UAAM,oBAAoB,MAAM,YAAY,EAAE,KAAK,WAAS,SAAS,KAAK,IAAI,iBAAiB,KAAK,EAAE,QAAQ,eAAe,IAAI,gBAAgB,EAAE,QAAQ,MAAM,SAAS,KAAK,KAAK,CAAC,CAAC;AACtL,UAAM,mBAAmB,MAAM,gBAAgB,EAAE,QAAQ,MAAM,YAAY,EAAE,KAAK,WAAS,SAAS,KAAK,IAAI,iBAAiB,KAAK,IAAI,SAAS,KAAK,KAAK,CAAC,CAAC;AAC5J,WAAO,EAAE,CAAC,IAAI,IAAI,SAAS,cAAc,kBAAkB,IAAI,iBAAiB,GAAG,MAAM,EAAE,EAAE;AAAA,EAC/F;AACA,QAAM,gBAAgB,CAAC,MAAM,aAAa;AACxC,UAAM,aAAa,CAAC;AACpB,UAAM,MAAM,YAAY,EAAE,KAAK,UAAQ;AACrC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF,GAAG,UAAQ;AACT,cAAM,UAAU,IAAI,EAAE,QAAQ,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE,KAAK,WAAS,WAAW,IAAI,IAAI,KAAK;AAAA,MAC/F,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC,MAAM,gBAAgB;AACpC,UAAM,WAAW,eAAe,gBAAgB,eAAe,YAAY,aAAa,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;AAC3G,UAAMD,OAAM,SAAS,MAAM,UAAU,WAAW;AAChD,WAAO;AAAA,MACL,GAAGA,KAAI,QAAQ;AAAA,MACf,GAAGA,KAAI,WAAW;AAAA,MAClB,GAAGA,KAAI,QAAQ;AAAA,MACf,GAAGA,KAAI,OAAO;AAAA,MACd,GAAG,cAAc,MAAM,QAAQ;AAAA,IACjC;AAAA,EACF;AACA,QAAM,OAAO,UAAQ;AACnB,UAAM,UAAU;AAAA,MACd,GAAG;AAAA,MACH,QAAQ,EAAE,OAAO,MAAM,MAAM,QAAQ,EAAE,MAAM,EAAE,EAAE;AAAA,MACjD,WAAW,EAAE,OAAO,MAAM,MAAM,WAAW,EAAE,MAAM,EAAE,EAAE;AAAA,MACvD,QAAQ,EAAE,OAAO,MAAM,MAAM,QAAQ,EAAE,MAAM,EAAE,EAAE;AAAA,IACnD;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF,GAAG,UAAQ;AACT,YAAM,MAAM,IAAI,EAAE,KAAK,WAAS;AAC9B,cAAM,aAAa,QAAQ,cAAc,CAAC;AAC1C,mBAAW,IAAI,IAAI;AACnB,gBAAQ,aAAa;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,cAAc,YAAU,WAAS;AACrC,UAAM,eAAe,SAAS,MAAM,MAAM,gCAAgC,MAAM,MAAM;AACtF,WAAO,oBAAoB,KAAK;AAAA,MAC9B,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,YAAU;AAC9B,UAAM,UAAU,OAAO,UAAU,QAAQ;AACzC,UAAM,UAAU,eAAe,OAAO,IAAI,OAAO,WAAW,UAAU,SAAS,EAAE,WAAW,KAAK,CAAC,IAAI;AACtG,UAAM,OAAO,WAAW,SAAS,OAAO,MAAM;AAC9C,UAAM,yBAAyB,MAAM;AACnC,UAAI,cAAc,KAAK,QAAQ,KAAK,IAAI,GAAG;AACzC,cAAM,OAAO,OAAO,IAAI,QAAQ,OAAO;AACvC,eAAO;AAAA,UACL,OAAO,KAAK,EAAE,SAAS,EAAE,QAAQ,OAAO,EAAE;AAAA,UAC1C,QAAQ,KAAK,EAAE,SAAS,EAAE,QAAQ,OAAO,EAAE;AAAA,QAC7C;AAAA,MACF,OAAO;AACL,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,UAAM,aAAa,uBAAuB;AAC1C,WAAO;AAAA,MACL,OAAO;AAAA,MACP,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,eAAe,CAAC,KAAK,WAAW,cAAY;AAChD,QAAI,SAAS,SAAS,GAAG,KAAK,SAAS,IAAI,KAAK,EAAE,SAAS,GAAG;AAC5D,YAAM,OAAO,SAAS;AACtB,YAAM,cAAc,WAAW,MAAM,OAAO,MAAM;AAClD,YAAM,SAAS;AAAA,QACb,GAAG;AAAA,QACH,QAAQ,SAAS;AAAA,QACjB,OAAO;AAAA,MACT;AACA,UAAI,QAAQ,KAAK,MAAM,CAAC;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,oBAAoB,CAAC,QAAQ,kBAAkB;AACnD,UAAM,eAAe,OAAO,IAAI,OAAO,oBAAoB;AAC3D,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,eAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,YAAI,cAAc,CAAC,MAAM,aAAa,CAAC,GAAG;AACxC,uBAAa,OAAO,GAAG,CAAC;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU,OAAO,aAAa,CAAC,CAAC;AAAA,EACzC;AACA,QAAM,eAAe,CAAC,QAAQ,SAAS;AACrC,UAAM,gBAAgB,OAAO,IAAI,OAAO,oBAAoB;AAC5D,WAAO,cAAc,IAAI;AACzB,sBAAkB,QAAQ,aAAa;AACvC,WAAO,YAAY;AAAA,EACrB;AACA,QAAM,gBAAgB,CAAC,KAAK,kBAAkB,cAAc,aAAa,KAAK,kBAAkB,qBAAqB,cAAc,aAAa,GAAG,CAAC;AACpJ,QAAM,0BAA0B,CAAC,UAAU,YAAY;AACrD,UAAM,uBAAuB,CAACE,WAAUC,aAAYD,UAAS,UAAUC,SAAQ,SAASD,UAAS,WAAWC,SAAQ;AACpH,WAAO,qBAAqB,UAAU,OAAO,KAAK,cAAc,QAAQ,QAAQ,SAAS,IAAI;AAAA,EAC/F;AACA,QAAM,aAAa,CAAC,UAAU,SAAS,WAAW;AAChD,QAAI;AACJ,YAAQ,QAAQ,wBAAwB,UAAU,OAAO,KAAK,cAAc,MAAM,IAAI,WAAW,QAAQ;AAAA,MACvG,GAAG;AAAA,MACH,OAAO;AAAA,IACT,CAAC,IAAI,YAAY,KAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,KAAK,IAAI,SAAS,OAAO,OAAO,MAAM;AACvG,QAAI,QAAQ,UAAU,SAAS,WAAW,QAAQ,UAAU,SAAS,QAAQ,MAAM,IAAI;AACrF,mBAAa,QAAQ,QAAQ,KAAK;AAAA,IACpC,OAAO;AACL,mBAAa,QAAQ,OAAO,EAAE,KAAK,cAAY;AAC7C,qBAAa,QAAQ,SAAS,IAAI;AAAA,MACpC,CAAC,EAAE,MAAM,YAAY,MAAM,CAAC;AAAA,IAC9B;AAAA,EACF;AACA,QAAM,aAAa,YAAU;AAC3B,UAAM,aAAa,cAAc,MAAM;AACvC,UAAM,cAAc,KAAK,UAAU;AACnC,UAAM,cAAc,KAAK,UAAU;AACnC,UAAM,eAAe,CAAC,UAAU,QAAQ;AACtC,YAAM,cAAc,OAAO,IAAI,QAAQ,GAAG,QAAQ;AAClD,UAAI,SAAS,WAAW,YAAY,QAAQ;AAC1C,qBAAa,KAAK,MAAM,EAAE;AAAA,UACxB,KAAK,YAAY;AAAA,UACjB,MAAM;AAAA,QACR,CAAC;AACD,qBAAa,QAAQ,WAAW,EAAE,KAAK,aAAa,KAAK,MAAM,CAAC,EAAE,MAAM,YAAY,MAAM,CAAC;AAAA,MAC7F;AAAA,IACF;AACA,UAAM,cAAc,SAAO;AACzB,UAAI;AACJ,YAAM,OAAO,OAAO,IAAI,QAAQ,CAAC;AACjC,YAAM,gBAAgB,YAAY,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAK,IAAI,OAAO,MAAM;AACrG,UAAI,QAAQ,KAAK,aAAa,CAAC;AAAA,IACjC;AACA,UAAM,eAAe,CAAC,KAAK,aAAa,aAAa;AACnD,YAAM,aAAa,OAAO,IAAI,QAAQ,GAAG,WAAW;AACpD,YAAM,OAAO,wBAAwB,UAAU,UAAU,KAAK,cAAc,MAAM,IAAI;AAAA,QACpF,GAAG;AAAA,QACH,OAAO;AAAA,MACT,IAAI;AACJ,YAAM,QAAQ,WAAW,QAAQ,IAAI;AACrC,UAAI,QAAQ,KAAK;AAAA,QACf,GAAG;AAAA,QACH;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AACA,UAAM,aAAa,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,aAAa;AAAA,IACf,CAAC;AACH,UAAM,YAAY,CAAC,cAAc,MAAM,IAAI,CAAC,IAAI,CAAC;AAAA,MAC7C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,IACb,CAAC;AACH,UAAM,aAAa;AAAA,MACjB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,gBAAgB;AAAA,MACpB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AACA,UAAM,WAAW;AAAA,MACf,OAAO;AAAA,MACP,OAAO,CAAC,aAAa;AAAA,IACvB;AACA,UAAM,oBAAoB,CAAC;AAC3B,QAAI,aAAa,MAAM,GAAG;AACxB,wBAAkB,KAAK;AAAA,QACrB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,QAAI,UAAU,MAAM,GAAG;AACrB,wBAAkB,KAAK;AAAA,QACrB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,UAAM,cAAc;AAAA,MAClB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AACA,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,IACF;AACA,QAAI,kBAAkB,SAAS,GAAG;AAChC,WAAK,KAAK,WAAW;AAAA,IACvB;AACA,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AACA,UAAM,MAAM,OAAO,cAAc,KAAK;AAAA,MACpC,OAAO;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,UAAU,SAAO;AACf,cAAM,cAAc,OAAO,IAAI,QAAQ,CAAC;AACxC,mBAAW,YAAY,IAAI,GAAG,aAAa,MAAM;AACjD,YAAI,MAAM;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,KAAK,WAAW;AACzB,gBAAQ,OAAO,MAAM;AAAA,UACrB,KAAK;AACH,yBAAa,YAAY,IAAI,GAAG,GAAG;AACnC;AAAA,UACF,KAAK;AACH,wBAAY,GAAG;AACf;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,yBAAa,KAAK,OAAO,MAAM,YAAY,IAAI,CAAC;AAChD;AAAA,QACF;AACA,oBAAY,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC;AAAA,MACvC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,MAAM,YAAU;AACpB,UAAM,eAAe,MAAM;AACzB,iBAAW,MAAM;AAAA,IACnB;AACA,WAAO,EAAE,YAAY,aAAa;AAAA,EACpC;AAEA,QAAM,aAAa,YAAU;AAC3B,UAAM,eAAe,MAAM;AACzB,iBAAW,MAAM;AAAA,IACnB;AACA,WAAO,WAAW,YAAY,YAAY;AAAA,EAC5C;AAEA,QAAM,aAAa,CAAC,KAAK,QAAQ,UAAU,WAAW,MAAM,IAAI,UAAU,OAAO,UAAU,IAAI,OAAO,OAAO,QAAQ,OAAO,MAAM,MAAM;AACxI,QAAM,aAAa,CAAC,KAAK,WAAW;AAClC,WAAO,WAAW,KAAK,QAAQ,CAAC;AAAA,EAClC;AAEA,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAErD,QAAM,kBAAkB,UAAQ;AAC9B,UAAM,OAAO,KAAK;AAClB,WAAO,SAAS,YAAY,SAAS,WAAW,SAAS;AAAA,EAC3D;AACA,QAAM,eAAe,CAAC,MAAM,QAAQ,WAAW,eAAe,SAAS;AACrE,UAAM,QAAQ,KAAK,KAAK,SAAS;AACjC,QAAI,cAAc,KAAK,GAAG;AACxB,aAAO;AAAA,IACT,WAAW,CAAC,IAAI,QAAQ,SAAS,GAAG;AAClC,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,MAAM,aAAa,WAAW;AACnD,UAAM,cAAc,YAAY,SAAS,SAAS,KAAK,SAAS;AAChE,UAAM,eAAe,cAAc,QAAQ;AAC3C,UAAM,iBAAiB,KAAK,SAAS,UAAU,OAAO;AACtD,UAAM,gBAAgB,cAAc,iBAAiB;AACrD,gBAAY,KAAK;AAAA,MACf,OAAO,aAAa,MAAM,QAAQ,SAAS,YAAY;AAAA,MACvD,QAAQ,aAAa,MAAM,QAAQ,UAAU,aAAa;AAAA,IAC5D,CAAC;AAAA,EACH;AACA,QAAM,oBAAoB,CAAC,QAAQ,UAAU,aAAa,SAAS;AACjE,UAAM,UAAU,OAAO,OAAO,MAAM,EAAE,MAAM,MAAM,EAAE,SAAS,SAAS,CAAC;AACvE,WAAO,QAAQ,YAAY;AACzB,kBAAY,OAAO,QAAQ,UAAU;AAAA,IACvC;AAAA,EACF;AACA,QAAM,wBAAwB,CAAC,QAAQ,SAAS;AAC9C,UAAM,OAAO,KAAK;AAClB,UAAM,cAAc,IAAI,SAAS,OAAO,CAAC;AACzC,iCAA6B,QAAQ,MAAM,WAAW;AACtD,kBAAc,MAAM,aAAa,CAAC,CAAC;AACnC,gBAAY,KAAK;AAAA,MACf,SAAS,KAAK,KAAK,OAAO;AAAA,MAC1B,OAAO,OAAO;AAAA,MACd,mBAAmB;AAAA,MACnB,SAAS,2BAA2B;AAAA,IACtC,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,oBAAoB,CAAC,QAAQ,SAAS;AAC1C,QAAI;AACJ,UAAM,OAAO,KAAK;AAClB,UAAM,iBAAiB,IAAI,SAAS,QAAQ,CAAC;AAC7C,mBAAe,KAAK;AAAA,MAClB,mBAAmB;AAAA,MACnB,SAAS,KAAK,KAAK,OAAO;AAAA,MAC1B,mBAAmB;AAAA,MACnB,SAAS,mCAAmC;AAAA,IAC9C,CAAC;AACD,iCAA6B,QAAQ,MAAM,cAAc;AACzD,UAAM,SAAS,OAAO,IAAI,YAAY,KAAK,KAAK,KAAK,OAAO,OAAO,QAAQ,OAAO,SAAS,KAAK,EAAE;AAClG,UAAM,cAAc,IAAI,SAAS,MAAM,CAAC;AACxC,kBAAc,MAAM,aAAa,MAAM;AACvC,gBAAY,KAAK;AAAA,MACf,KAAK,KAAK,KAAK,KAAK;AAAA,MACpB,OAAO,KAAK,KAAK,OAAO;AAAA,MACxB,OAAO,KAAK,KAAK,OAAO;AAAA,IAC1B,CAAC;AACD,QAAI,SAAS,UAAU;AACrB,kBAAY,KAAK;AAAA,QACf,iBAAiB,KAAK,KAAK,iBAAiB;AAAA,QAC5C,aAAa;AAAA,QACb,SAAS,KAAK,KAAK,SAAS;AAAA,MAC9B,CAAC;AAAA,IACH,OAAO;AACL,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO,OAAO,cAAY;AACxB,oBAAY,KAAK,UAAU,KAAK,KAAK,QAAQ,CAAC;AAAA,MAChD,CAAC;AACD,YAAM,gBAAgB,eAAe,KAAK,eAAe;AACzD,UAAI,cAAc,aAAa,GAAG;AAChC,0BAAkB,QAAQ,MAAM,aAAa,SAAS,aAAa,CAAC;AAAA,MACtE;AAAA,IACF;AACA,UAAM,WAAW,IAAI,SAAS,QAAQ,CAAC;AACvC,aAAS,KAAK,SAAS,UAAU;AACjC,mBAAe,OAAO,WAAW;AACjC,mBAAe,OAAO,QAAQ;AAC9B,WAAO;AAAA,EACT;AACA,QAAM,+BAA+B,CAAC,QAAQ,YAAY,eAAe;AACvE,QAAI;AACJ,UAAM,WAAW,KAAK,WAAW,gBAAgB,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC/E,QAAI,KAAK,QAAQ;AACjB,WAAO,MAAM;AACX,YAAM,WAAW,QAAQ,EAAE,EAAE;AAC7B,UAAI,YAAY,QAAQ,EAAE,EAAE;AAC5B,UAAI,aAAa,WAAW,aAAa,YAAY,aAAa,WAAW,CAAC,WAAW,UAAU,WAAW,GAAG;AAC/G,YAAI,aAAa,UAAU,aAAa,OAAO;AAC7C,sBAAY,OAAO,WAAW,WAAW,QAAQ;AAAA,QACnD;AACA,mBAAW,KAAK,gBAAgB,UAAU,SAAS;AAAA,MACrD;AAAA,IACF;AACA,UAAM,aAAa,SAAS,EAAE,OAAO,KAAK,GAAG,OAAO,MAAM;AAC1D,UAAM,WAAW,IAAI,SAAS,OAAO,CAAC;AACtC,WAAO,WAAW,SAAS,GAAG,WAAS,SAAS,OAAO,KAAK,CAAC;AAC7D,UAAM,YAAY,WAAW,UAAU,QAAQ;AAC/C,QAAI,WAAW;AACb,iBAAW,KAAK,iBAAiB,OAAO,SAAS,CAAC;AAClD,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF;AACA,QAAM,qBAAqB,UAAQ;AACjC,UAAM,YAAY,KAAK,KAAK,OAAO;AACnC,WAAO,SAAS,SAAS,KAAK,qBAAqB,KAAK,SAAS;AAAA,EACnE;AACA,QAAM,uBAAuB,UAAQ;AACnC,QAAI,WAAW;AACf,WAAO,WAAW,SAAS,QAAQ;AACjC,UAAI,SAAS,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ,GAAG;AACzE,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,uBAAuB,YAAU,WAAS;AAC9C,QAAI,IAAI,MAAM;AACd,QAAI;AACJ,WAAO,KAAK;AACV,aAAO,MAAM,CAAC;AACd,UAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,MACF;AACA,UAAI,KAAK,OAAO,KAAK,iBAAiB,GAAG;AACvC;AAAA,MACF;AACA,UAAI,gBAAgB,IAAI,KAAK,cAAc,MAAM,GAAG;AAClD,YAAI,CAAC,qBAAqB,IAAI,GAAG;AAC/B,eAAK,QAAQ,kBAAkB,QAAQ,IAAI,CAAC;AAAA,QAC9C;AAAA,MACF,OAAO;AACL,YAAI,CAAC,qBAAqB,IAAI,GAAG;AAC/B,eAAK,QAAQ,sBAAsB,QAAQ,IAAI,CAAC;AAAA,QAClD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,mBAAmB,CAAC,QAAQ,SAAS,SAAS;AAClD,UAAM,kBAAkB,OAAO,QAAQ;AACvC,UAAM,WAAW,gBAAgB,kBAAkB;AACnD,UAAM,WAAW,iBAAiB,MAAM;AACxC,WAAO,OAAO,OAAO,QAAQ;AAAA,MAC3B;AAAA,MACA;AAAA,IACF,CAAC,EAAE,MAAM,MAAM,EAAE,QAAQ,CAAC;AAAA,EAC5B;AAEA,QAAM,UAAU,YAAU;AACxB,WAAO,GAAG,WAAW,MAAM;AACzB,YAAM,EAAC,QAAQ,YAAY,OAAM,IAAI;AACrC,YAAM,YAAY,OAAO,aAAa;AACtC,aAAO,2CAA2C,MAAM,GAAG,GAAG,UAAQ;AACpE,kBAAU,IAAI,IAAI,CAAC;AAAA,MACrB,CAAC;AACD,WAAK,EAAE,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,SAAS;AAC1C,cAAM,OAAO,OAAO,eAAe,IAAI;AACvC,YAAI,MAAM;AACR,iBAAO,OAAO,UAAQ;AACpB,iBAAK,WAAW,IAAI,IAAI,CAAC;AACzB,iBAAK,gBAAgB,KAAK,IAAI;AAAA,UAChC,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,cAAc,mCAAmC,qBAAqB,MAAM,CAAC;AACpF,iBAAW,mBAAmB,mBAAmB,CAAC,OAAO,SAAS;AAChE,YAAI;AACJ,YAAI,IAAI,MAAM;AACd,eAAO,KAAK;AACV,gBAAM,OAAO,MAAM,CAAC;AACpB,cAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,UACF;AACA,gBAAM,cAAc,KAAK,KAAK,IAAI;AAClC,gBAAM,UAAU,IAAI,SAAS,aAAa,CAAC;AAC3C,cAAI,gBAAgB,SAAS;AAC3B,kBAAM,YAAY,KAAK,KAAK,OAAO;AACnC,gBAAI,aAAa,UAAU,QAAQ,oBAAoB,MAAM,MAAM,KAAK,YAAY;AAClF,sBAAQ,KAAK;AAAA,gBACX,OAAO,KAAK,WAAW,KAAK,OAAO;AAAA,gBACnC,QAAQ,KAAK,WAAW,KAAK,QAAQ;AAAA,cACvC,CAAC;AAAA,YACH,OAAO;AACL,sBAAQ,KAAK;AAAA,gBACX,OAAO,KAAK,KAAK,OAAO;AAAA,gBACxB,QAAQ,KAAK,KAAK,QAAQ;AAAA,cAC5B,CAAC;AAAA,YACH;AAAA,UACF;AACA,kBAAQ,KAAK,EAAE,OAAO,KAAK,KAAK,OAAO,EAAE,CAAC;AAC1C,gBAAM,WAAW,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,KAAK,CAAC;AACzE,cAAI,KAAK,QAAQ;AACjB,iBAAO,MAAM;AACX,kBAAM,WAAW,QAAQ,EAAE,EAAE;AAC7B,gBAAI,SAAS,QAAQ,aAAa,MAAM,GAAG;AACzC,sBAAQ,KAAK,SAAS,OAAO,EAAE,GAAG,QAAQ,EAAE,EAAE,KAAK;AAAA,YACrD;AAAA,UACF;AACA,gBAAM,YAAY,KAAK,KAAK,eAAe;AAC3C,cAAI,WAAW;AACb,kBAAM,WAAW,iBAAiB,QAAQ,aAAa,SAAS,SAAS,CAAC;AAC1E,mBAAO,SAAS,SAAS,GAAG,WAAS,QAAQ,OAAO,KAAK,CAAC;AAAA,UAC5D;AACA,eAAK,QAAQ,OAAO;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,WAAO,GAAG,cAAc,MAAM;AAC5B,YAAM,MAAM,OAAO;AACnB,aAAO,IAAI,OAAO,yBAAyB,GAAG,SAAO;AACnD,YAAI,IAAI,OAAO,iBAAiB,GAAG,EAAE,WAAW,GAAG;AACjD,cAAI,IAAI,KAAK,QAAQ,EAAE,OAAO,WAAW,CAAC;AAAA,QAC5C;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,QAAM,QAAQ,YAAU;AACtB,WAAO,GAAG,eAAe,OAAK;AAC5B,UAAI;AACJ,UAAI,EAAE,OAAO,aAAa,MAAM,OAAO,EAAE,OAAO,aAAa,iBAAiB,IAAI;AAChF,UAAE,OAAO;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,kBAAkB,YAAU,SAAO;AACvC,UAAM,cAAc,MAAM;AACxB,UAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,IAC9C;AACA,WAAO,GAAG,cAAc,WAAW;AACnC,gBAAY;AACZ,WAAO,MAAM;AACX,aAAO,IAAI,cAAc,WAAW;AAAA,IACtC;AAAA,EACF;AACA,QAAM,WAAW,YAAU;AACzB,UAAM,WAAW,MAAM,OAAO,YAAY,UAAU;AACpD,WAAO,GAAG,SAAS,gBAAgB,SAAS;AAAA,MAC1C,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,MACA,SAAS,eAAa;AACpB,cAAM,YAAY,OAAO;AACzB,kBAAU,UAAU,eAAe,UAAU,QAAQ,CAAC,CAAC;AACvD,cAAM,wBAAwB,UAAU,0BAA0B,wEAAwE,UAAU,SAAS,EAAE;AAC/J,cAAM,iBAAiB,gBAAgB,MAAM,EAAE,SAAS;AACxD,eAAO,MAAM;AACX,gCAAsB;AACtB,yBAAe;AAAA,QACjB;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,GAAG,SAAS,YAAY,SAAS;AAAA,MACtC,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,MACA,SAAS,gBAAgB,MAAM;AAAA,IACjC,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,MAAM;AACjB,aAAS,IAAI,SAAS,YAAU;AAC9B,iBAAW,MAAM;AACjB,iBAAW,MAAM;AACjB,eAAS,MAAM;AACf,YAAM,MAAM;AACZ,cAAQ,MAAM;AACd,cAAQ,MAAM;AACd,aAAO,IAAI,MAAM;AAAA,IACnB,CAAC;AAAA,EACH;AAEA,SAAO;AAEX,GAAG;", "names": ["get", "dataToHtml", "prevData", "newData"]}