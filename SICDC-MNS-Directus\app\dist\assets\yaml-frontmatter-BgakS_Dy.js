import{g}from"./index-C0qcAVKU.js";import{e as v}from"./index.DUmRo3Ep.entry.js";import{a as b}from"./yaml-BrOl0OpS.js";function k(f,u){for(var n=0;n<u.length;n++){const a=u[n];if(typeof a!="string"&&!Array.isArray(a)){for(const r in a)if(r!=="default"&&!(r in f)){const o=Object.getOwnPropertyDescriptor(a,r);o&&Object.defineProperty(f,r,o.get?o:{enumerable:!0,get:()=>a[r]})}}}return Object.freeze(Object.defineProperty(f,Symbol.toStringTag,{value:"Module"}))}var S={exports:{}};(function(f,u){(function(n){n(v(),b)})(function(n){var a=0,r=1,o=2;n.defineMode("yaml-frontmatter",function(s,p){var l=n.getMode(s,"yaml"),m=n.getMode(s,p&&p.base||"gfm");function y(e){return e.state==r?{mode:l,state:e.yaml}:{mode:m,state:e.inner}}return{startState:function(){return{state:a,yaml:null,inner:n.startState(m)}},copyState:function(e){return{state:e.state,yaml:e.yaml&&n.copyState(l,e.yaml),inner:n.copyState(m,e.inner)}},token:function(e,t){if(t.state==a)return e.match("---",!1)?(t.state=r,t.yaml=n.startState(l),l.token(e,t.yaml)):(t.state=o,m.token(e,t.inner));if(t.state==r){var c=e.sol()&&e.match(/(---|\.\.\.)/,!1),i=l.token(e,t.yaml);return c&&(t.state=o,t.yaml=null),i}else return m.token(e,t.inner)},innerMode:y,indent:function(e,t,c){var i=y(e);return i.mode.indent?i.mode.indent(i.state,t,c):n.Pass},blankLine:function(e){var t=y(e);if(t.mode.blankLine)return t.mode.blankLine(t.state)}}})})})();var d=S.exports;const x=g(d),j=k({__proto__:null,default:x},[d]);export{j as y};
