{"version": 3, "sources": ["../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/ru/_lib/formatDistance.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/ru/_lib/formatLong.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/ru/_lib/formatRelative.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/ru/_lib/localize.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/ru/_lib/match.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/ru.mjs"], "sourcesContent": ["function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\n\nfunction buildLocalizeTokenFn(scheme) {\n  return (count, options) => {\n    if (options?.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return \"через \" + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + \" назад\";\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: \"меньше секунды\",\n      singularNominative: \"меньше {{count}} секунды\",\n      singularGenitive: \"меньше {{count}} секунд\",\n      pluralGenitive: \"меньше {{count}} секунд\",\n    },\n    future: {\n      one: \"меньше, чем через секунду\",\n      singularNominative: \"меньше, чем через {{count}} секунду\",\n      singularGenitive: \"меньше, чем через {{count}} секунды\",\n      pluralGenitive: \"меньше, чем через {{count}} секунд\",\n    },\n  }),\n\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} секунда\",\n      singularGenitive: \"{{count}} секунды\",\n      pluralGenitive: \"{{count}} секунд\",\n    },\n    past: {\n      singularNominative: \"{{count}} секунду назад\",\n      singularGenitive: \"{{count}} секунды назад\",\n      pluralGenitive: \"{{count}} секунд назад\",\n    },\n    future: {\n      singularNominative: \"через {{count}} секунду\",\n      singularGenitive: \"через {{count}} секунды\",\n      pluralGenitive: \"через {{count}} секунд\",\n    },\n  }),\n\n  halfAMinute: (_count, options) => {\n    if (options?.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return \"через полминуты\";\n      } else {\n        return \"полминуты назад\";\n      }\n    }\n\n    return \"полминуты\";\n  },\n\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: \"меньше минуты\",\n      singularNominative: \"меньше {{count}} минуты\",\n      singularGenitive: \"меньше {{count}} минут\",\n      pluralGenitive: \"меньше {{count}} минут\",\n    },\n    future: {\n      one: \"меньше, чем через минуту\",\n      singularNominative: \"меньше, чем через {{count}} минуту\",\n      singularGenitive: \"меньше, чем через {{count}} минуты\",\n      pluralGenitive: \"меньше, чем через {{count}} минут\",\n    },\n  }),\n\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} минута\",\n      singularGenitive: \"{{count}} минуты\",\n      pluralGenitive: \"{{count}} минут\",\n    },\n    past: {\n      singularNominative: \"{{count}} минуту назад\",\n      singularGenitive: \"{{count}} минуты назад\",\n      pluralGenitive: \"{{count}} минут назад\",\n    },\n    future: {\n      singularNominative: \"через {{count}} минуту\",\n      singularGenitive: \"через {{count}} минуты\",\n      pluralGenitive: \"через {{count}} минут\",\n    },\n  }),\n\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"около {{count}} часа\",\n      singularGenitive: \"около {{count}} часов\",\n      pluralGenitive: \"около {{count}} часов\",\n    },\n    future: {\n      singularNominative: \"приблизительно через {{count}} час\",\n      singularGenitive: \"приблизительно через {{count}} часа\",\n      pluralGenitive: \"приблизительно через {{count}} часов\",\n    },\n  }),\n\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} час\",\n      singularGenitive: \"{{count}} часа\",\n      pluralGenitive: \"{{count}} часов\",\n    },\n  }),\n\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} день\",\n      singularGenitive: \"{{count}} дня\",\n      pluralGenitive: \"{{count}} дней\",\n    },\n  }),\n\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"около {{count}} недели\",\n      singularGenitive: \"около {{count}} недель\",\n      pluralGenitive: \"около {{count}} недель\",\n    },\n    future: {\n      singularNominative: \"приблизительно через {{count}} неделю\",\n      singularGenitive: \"приблизительно через {{count}} недели\",\n      pluralGenitive: \"приблизительно через {{count}} недель\",\n    },\n  }),\n\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} неделя\",\n      singularGenitive: \"{{count}} недели\",\n      pluralGenitive: \"{{count}} недель\",\n    },\n  }),\n\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"около {{count}} месяца\",\n      singularGenitive: \"около {{count}} месяцев\",\n      pluralGenitive: \"около {{count}} месяцев\",\n    },\n    future: {\n      singularNominative: \"приблизительно через {{count}} месяц\",\n      singularGenitive: \"приблизительно через {{count}} месяца\",\n      pluralGenitive: \"приблизительно через {{count}} месяцев\",\n    },\n  }),\n\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} месяц\",\n      singularGenitive: \"{{count}} месяца\",\n      pluralGenitive: \"{{count}} месяцев\",\n    },\n  }),\n\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"около {{count}} года\",\n      singularGenitive: \"около {{count}} лет\",\n      pluralGenitive: \"около {{count}} лет\",\n    },\n    future: {\n      singularNominative: \"приблизительно через {{count}} год\",\n      singularGenitive: \"приблизительно через {{count}} года\",\n      pluralGenitive: \"приблизительно через {{count}} лет\",\n    },\n  }),\n\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} год\",\n      singularGenitive: \"{{count}} года\",\n      pluralGenitive: \"{{count}} лет\",\n    },\n  }),\n\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"больше {{count}} года\",\n      singularGenitive: \"больше {{count}} лет\",\n      pluralGenitive: \"больше {{count}} лет\",\n    },\n    future: {\n      singularNominative: \"больше, чем через {{count}} год\",\n      singularGenitive: \"больше, чем через {{count}} года\",\n      pluralGenitive: \"больше, чем через {{count}} лет\",\n    },\n  }),\n\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"почти {{count}} год\",\n      singularGenitive: \"почти {{count}} года\",\n      pluralGenitive: \"почти {{count}} лет\",\n    },\n    future: {\n      singularNominative: \"почти через {{count}} год\",\n      singularGenitive: \"почти через {{count}} года\",\n      pluralGenitive: \"почти через {{count}} лет\",\n    },\n  }),\n};\n\nexport const formatDistance = (token, count, options) => {\n  return formatDistanceLocale[token](count, options);\n};\n", "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.mjs\";\n\nconst dateFormats = {\n  full: \"EEEE, d MMMM y 'г.'\",\n  long: \"d MMMM y 'г.'\",\n  medium: \"d MMM y 'г.'\",\n  short: \"dd.MM.y\",\n};\n\nconst timeFormats = {\n  full: \"H:mm:ss zzzz\",\n  long: \"H:mm:ss z\",\n  medium: \"H:mm:ss\",\n  short: \"H:mm\",\n};\n\nconst dateTimeFormats = {\n  any: \"{{date}}, {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"any\",\n  }),\n};\n", "import { isSameWeek } from \"../../../isSameWeek.mjs\";\n\nconst accusativeWeekdays = [\n  \"воскресенье\",\n  \"понедельник\",\n  \"вторник\",\n  \"среду\",\n  \"четверг\",\n  \"пятницу\",\n  \"субботу\",\n];\n\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0:\n      return \"'в прошлое \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'в прошлый \" + weekday + \" в' p\";\n    case 3:\n    case 5:\n    case 6:\n      return \"'в прошлую \" + weekday + \" в' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  if (day === 2 /* Tue */) {\n    return \"'во \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\n\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0:\n      return \"'в следующее \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'в следующий \" + weekday + \" в' p\";\n    case 3:\n    case 5:\n    case 6:\n      return \"'в следующую \" + weekday + \" в' p\";\n  }\n}\n\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера в' p\",\n  today: \"'сегодня в' p\",\n  tomorrow: \"'завтра в' p\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n", "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"до н.э.\", \"н.э.\"],\n  abbreviated: [\"до н. э.\", \"н. э.\"],\n  wide: [\"до нашей эры\", \"нашей эры\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-й кв.\", \"2-й кв.\", \"3-й кв.\", \"4-й кв.\"],\n  wide: [\"1-й квартал\", \"2-й квартал\", \"3-й квартал\", \"4-й квартал\"],\n};\n\nconst monthValues = {\n  narrow: [\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"],\n  abbreviated: [\n    \"янв.\",\n    \"фев.\",\n    \"март\",\n    \"апр.\",\n    \"май\",\n    \"июнь\",\n    \"июль\",\n    \"авг.\",\n    \"сент.\",\n    \"окт.\",\n    \"нояб.\",\n    \"дек.\",\n  ],\n\n  wide: [\n    \"январь\",\n    \"февраль\",\n    \"март\",\n    \"апрель\",\n    \"май\",\n    \"июнь\",\n    \"июль\",\n    \"август\",\n    \"сентябрь\",\n    \"октябрь\",\n    \"ноябрь\",\n    \"декабрь\",\n  ],\n};\n\nconst formattingMonthValues = {\n  narrow: [\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"],\n  abbreviated: [\n    \"янв.\",\n    \"фев.\",\n    \"мар.\",\n    \"апр.\",\n    \"мая\",\n    \"июн.\",\n    \"июл.\",\n    \"авг.\",\n    \"сент.\",\n    \"окт.\",\n    \"нояб.\",\n    \"дек.\",\n  ],\n\n  wide: [\n    \"января\",\n    \"февраля\",\n    \"марта\",\n    \"апреля\",\n    \"мая\",\n    \"июня\",\n    \"июля\",\n    \"августа\",\n    \"сентября\",\n    \"октября\",\n    \"ноября\",\n    \"декабря\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"В\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"вс\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"вск\", \"пнд\", \"втр\", \"срд\", \"чтв\", \"птн\", \"суб\"],\n  wide: [\n    \"воскресенье\",\n    \"понедельник\",\n    \"вторник\",\n    \"среда\",\n    \"четверг\",\n    \"пятница\",\n    \"суббота\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утро\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ночь\",\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утро\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ночь\",\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полночь\",\n    noon: \"полдень\",\n    morning: \"утро\",\n    afternoon: \"день\",\n    evening: \"вечер\",\n    night: \"ночь\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утра\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночи\",\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утра\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночи\",\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полночь\",\n    noon: \"полдень\",\n    morning: \"утра\",\n    afternoon: \"дня\",\n    evening: \"вечера\",\n    night: \"ночи\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n\n  let suffix;\n  if (unit === \"date\") {\n    suffix = \"-е\";\n  } else if (unit === \"week\" || unit === \"minute\" || unit === \"second\") {\n    suffix = \"-я\";\n  } else {\n    suffix = \"-й\";\n  }\n\n  return number + suffix;\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n", "import { buildMatchFn } from \"../../_lib/buildMatchFn.mjs\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.mjs\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(е|я|й|ое|ье|ая|ья|ый|ой|ий|ый))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^((до )?н\\.?\\s?э\\.?)/i,\n  abbreviated: /^((до )?н\\.?\\s?э\\.?)/i,\n  wide: /^(до нашей эры|нашей эры|наша эра)/i,\n};\nconst parseEraPatterns = {\n  any: [/^д/i, /^н/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?[ыои]?й?)? кв.?/i,\n  wide: /^[1234](-?[ыои]?й?)? квартал/i,\n};\n\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^[яфмаисонд]/i,\n  abbreviated:\n    /^(янв|фев|март?|апр|ма[йя]|июн[ья]?|июл[ья]?|авг|сент?|окт|нояб?|дек)\\.?/i,\n  wide: /^(январ[ья]|феврал[ья]|марта?|апрел[ья]|ма[йя]|июн[ья]|июл[ья]|августа?|сентябр[ья]|октябр[ья]|октябр[ья]|ноябр[ья]|декабр[ья])/i,\n};\n\nconst parseMonthPatterns = {\n  narrow: [\n    /^я/i,\n    /^ф/i,\n    /^м/i,\n    /^а/i,\n    /^м/i,\n    /^и/i,\n    /^и/i,\n    /^а/i,\n    /^с/i,\n    /^о/i,\n    /^н/i,\n    /^я/i,\n  ],\n\n  any: [\n    /^я/i,\n    /^ф/i,\n    /^мар/i,\n    /^ап/i,\n    /^ма[йя]/i,\n    /^июн/i,\n    /^июл/i,\n    /^ав/i,\n    /^с/i,\n    /^о/i,\n    /^н/i,\n    /^д/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[впсч]/i,\n  short: /^(вс|во|пн|по|вт|ср|чт|че|пт|пя|сб|су)\\.?/i,\n  abbreviated: /^(вск|вос|пнд|пон|втр|вто|срд|сре|чтв|чет|птн|пят|суб).?/i,\n  wide: /^(воскресень[ея]|понедельника?|вторника?|сред[аы]|четверга?|пятниц[аы]|суббот[аы])/i,\n};\n\nconst parseDayPatterns = {\n  narrow: [/^в/i, /^п/i, /^в/i, /^с/i, /^ч/i, /^п/i, /^с/i],\n  any: [/^в[ос]/i, /^п[он]/i, /^в/i, /^ср/i, /^ч/i, /^п[ят]/i, /^с[уб]/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow: /^([дп]п|полн\\.?|полд\\.?|утр[оа]|день|дня|веч\\.?|ноч[ьи])/i,\n  abbreviated: /^([дп]п|полн\\.?|полд\\.?|утр[оа]|день|дня|веч\\.?|ноч[ьи])/i,\n  wide: /^([дп]п|полночь|полдень|утр[оа]|день|дня|вечера?|ноч[ьи])/i,\n};\n\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^дп/i,\n    pm: /^пп/i,\n    midnight: /^полн/i,\n    noon: /^полд/i,\n    morning: /^у/i,\n    afternoon: /^д[ен]/i,\n    evening: /^в/i,\n    night: /^н/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n", "import { formatDistance } from \"./ru/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./ru/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./ru/_lib/formatRelative.mjs\";\nimport { localize } from \"./ru/_lib/localize.mjs\";\nimport { match } from \"./ru/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Russian locale.\n * @language Russian\n * @iso-639-2 rus\n * <AUTHOR> [@kossnocorp](https://github.com/kossnocorp)\n * <AUTHOR> [@leshakoss](https://github.com/leshakoss)\n */\nexport const ru = {\n  code: \"ru\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default ru;\n"], "mappings": ";;;;;;;;;;;;AAAA,SAAS,WAAW,QAAQ,OAAO;AAEjC,MAAI,OAAO,QAAQ,UAAa,UAAU,GAAG;AAC3C,WAAO,OAAO;AAAA,EAChB;AAEA,QAAM,QAAQ,QAAQ;AACtB,QAAM,SAAS,QAAQ;AAGvB,MAAI,UAAU,KAAK,WAAW,IAAI;AAChC,WAAO,OAAO,mBAAmB,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAGrE,WAAW,SAAS,KAAK,SAAS,MAAM,SAAS,MAAM,SAAS,KAAK;AACnE,WAAO,OAAO,iBAAiB,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAGnE,OAAO;AACL,WAAO,OAAO,eAAe,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EACjE;AACF;AAEA,SAAS,qBAAqB,QAAQ;AACpC,SAAO,CAAC,OAAO,YAAY;AACzB,QAAI,mCAAS,WAAW;AACtB,UAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,YAAI,OAAO,QAAQ;AACjB,iBAAO,WAAW,OAAO,QAAQ,KAAK;AAAA,QACxC,OAAO;AACL,iBAAO,WAAW,WAAW,OAAO,SAAS,KAAK;AAAA,QACpD;AAAA,MACF,OAAO;AACL,YAAI,OAAO,MAAM;AACf,iBAAO,WAAW,OAAO,MAAM,KAAK;AAAA,QACtC,OAAO;AACL,iBAAO,WAAW,OAAO,SAAS,KAAK,IAAI;AAAA,QAC7C;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,WAAW,OAAO,SAAS,KAAK;AAAA,IACzC;AAAA,EACF;AACF;AAEA,IAAM,uBAAuB;AAAA,EAC3B,kBAAkB,qBAAqB;AAAA,IACrC,SAAS;AAAA,MACP,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,UAAU,qBAAqB;AAAA,IAC7B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,aAAa,CAAC,QAAQ,YAAY;AAChC,QAAI,mCAAS,WAAW;AACtB,UAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,kBAAkB,qBAAqB;AAAA,IACrC,SAAS;AAAA,MACP,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,UAAU,qBAAqB;AAAA,IAC7B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,OAAO,qBAAqB;AAAA,IAC1B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,cAAc,qBAAqB;AAAA,IACjC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,SAAS,qBAAqB;AAAA,IAC5B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,YAAY,qBAAqB;AAAA,IAC/B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,cAAc,qBAAqB;AAAA,IACjC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AAEO,IAAM,iBAAiB,CAAC,OAAO,OAAO,YAAY;AACvD,SAAO,qBAAqB,KAAK,EAAE,OAAO,OAAO;AACnD;;;ACnPA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEA,IAAM,kBAAkB;AAAA,EACtB,KAAK;AACP;AAEO,IAAM,aAAa;AAAA,EACxB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;;;ACjCA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,SAAS,KAAK;AACrB,QAAM,UAAU,mBAAmB,GAAG;AAEtC,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,aAAO,gBAAgB,UAAU;AAAA,IACnC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,gBAAgB,UAAU;AAAA,IACnC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,gBAAgB,UAAU;AAAA,EACrC;AACF;AAEA,SAAS,SAAS,KAAK;AACrB,QAAM,UAAU,mBAAmB,GAAG;AAEtC,MAAI,QAAQ,GAAa;AACvB,WAAO,SAAS,UAAU;AAAA,EAC5B,OAAO;AACL,WAAO,QAAQ,UAAU;AAAA,EAC3B;AACF;AAEA,SAAS,SAAS,KAAK;AACrB,QAAM,UAAU,mBAAmB,GAAG;AAEtC,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,aAAO,kBAAkB,UAAU;AAAA,IACrC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,kBAAkB,UAAU;AAAA,IACrC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,kBAAkB,UAAU;AAAA,EACvC;AACF;AAEA,IAAM,uBAAuB;AAAA,EAC3B,UAAU,CAAC,MAAM,UAAU,YAAY;AACrC,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,WAAW,MAAM,UAAU,OAAO,GAAG;AACvC,aAAO,SAAS,GAAG;AAAA,IACrB,OAAO;AACL,aAAO,SAAS,GAAG;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU,CAAC,MAAM,UAAU,YAAY;AACrC,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,WAAW,MAAM,UAAU,OAAO,GAAG;AACvC,aAAO,SAAS,GAAG;AAAA,IACrB,OAAO;AACL,aAAO,SAAS,GAAG;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACT;AAEO,IAAM,iBAAiB,CAAC,OAAO,MAAM,UAAU,YAAY;AAChE,QAAM,SAAS,qBAAqB,KAAK;AAEzC,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,OAAO,MAAM,UAAU,OAAO;AAAA,EACvC;AAEA,SAAO;AACT;;;ACrFA,IAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,WAAW,MAAM;AAAA,EAC1B,aAAa,CAAC,YAAY,OAAO;AAAA,EACjC,MAAM,CAAC,gBAAgB,WAAW;AACpC;AAEA,IAAM,gBAAgB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,WAAW,WAAW,WAAW,SAAS;AAAA,EACxD,MAAM,CAAC,eAAe,eAAe,eAAe,aAAa;AACnE;AAEA,IAAM,cAAc;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,wBAAwB;AAAA,EAC5B,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEA,IAAM,4BAA4B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEA,IAAM,gBAAgB,CAAC,aAAa,YAAY;AAC9C,QAAM,SAAS,OAAO,WAAW;AACjC,QAAM,OAAO,mCAAS;AAEtB,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,aAAS;AAAA,EACX,WAAW,SAAS,UAAU,SAAS,YAAY,SAAS,UAAU;AACpE,aAAS;AAAA,EACX,OAAO;AACL,aAAS;AAAA,EACX;AAEA,SAAO,SAAS;AAClB;AAEO,IAAM,WAAW;AAAA,EACtB;AAAA,EAEA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,CAAC,YAAY,UAAU;AAAA,EAC3C,CAAC;AAAA,EAED,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AAAA,EAED,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;;;AC9MA,IAAM,4BAA4B;AAClC,IAAM,4BAA4B;AAElC,IAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAM,mBAAmB;AAAA,EACvB,KAAK,CAAC,OAAO,KAAK;AACpB;AAEA,IAAM,uBAAuB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AAEA,IAAM,uBAAuB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AAEA,IAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,EACR,aACE;AAAA,EACF,MAAM;AACR;AAEA,IAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,KAAK;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AAEA,IAAM,mBAAmB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,WAAW,WAAW,OAAO,QAAQ,OAAO,WAAW,SAAS;AACxE;AAEA,IAAM,yBAAyB;AAAA,EAC7B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AAEA,IAAM,yBAAyB;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEO,IAAM,QAAQ;AAAA,EACnB,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,CAAC,UAAU,SAAS,OAAO,EAAE;AAAA,EAC9C,CAAC;AAAA,EAED,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,CAAC,UAAU,QAAQ;AAAA,EACpC,CAAC;AAAA,EAED,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;;;AC3HO,IAAM,KAAK;AAAA,EAChB,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AACF;AAGA,IAAO,aAAQ;", "names": []}