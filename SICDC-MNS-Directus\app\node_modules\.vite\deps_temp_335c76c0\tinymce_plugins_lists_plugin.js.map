{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/lists/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    var global$7 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const isString = isType$1('string');\n    const isObject = isType$1('object');\n    const isArray = isType$1('array');\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const noop = () => {\n    };\n    const compose1 = (fbc, fab) => a => fbc(fab(a));\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const tripleEquals = (a, b) => {\n      return a === b;\n    };\n    function curry(fn, ...initialArgs) {\n      return (...restArgs) => {\n        const all = initialArgs.concat(restArgs);\n        return fn.apply(null, all);\n      };\n    }\n    const not = f => t => !f(t);\n    const never = constant(false);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const nativeSlice = Array.prototype.slice;\n    const nativeIndexOf = Array.prototype.indexOf;\n    const nativePush = Array.prototype.push;\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains$1 = (xs, x) => rawIndexOf(xs, x) > -1;\n    const exists = (xs, pred) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return true;\n        }\n      }\n      return false;\n    };\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each$1 = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const filter$1 = (xs, pred) => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    const groupBy = (xs, f) => {\n      if (xs.length === 0) {\n        return [];\n      } else {\n        let wasType = f(xs[0]);\n        const r = [];\n        let group = [];\n        for (let i = 0, len = xs.length; i < len; i++) {\n          const x = xs[i];\n          const type = f(x);\n          if (type !== wasType) {\n            r.push(group);\n            group = [];\n          }\n          wasType = type;\n          group.push(x);\n        }\n        if (group.length !== 0) {\n          r.push(group);\n        }\n        return r;\n      }\n    };\n    const foldl = (xs, f, acc) => {\n      each$1(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    const findUntil = (xs, pred, until) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const find = (xs, pred) => {\n      return findUntil(xs, pred, never);\n    };\n    const flatten = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    const bind = (xs, f) => flatten(map(xs, f));\n    const reverse = xs => {\n      const r = nativeSlice.call(xs, 0);\n      r.reverse();\n      return r;\n    };\n    const get$1 = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = xs => get$1(xs, 0);\n    const last = xs => get$1(xs, xs.length - 1);\n    const unique = (xs, comparator) => {\n      const r = [];\n      const isDuplicated = isFunction(comparator) ? x => exists(r, i => comparator(i, x)) : x => contains$1(r, x);\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (!isDuplicated(x)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n\n    const is$2 = (lhs, rhs, comparator = tripleEquals) => lhs.exists(left => comparator(left, rhs));\n    const equals = (lhs, rhs, comparator = tripleEquals) => lift2(lhs, rhs, comparator).getOr(lhs.isNone() && rhs.isNone());\n    const lift2 = (oa, ob, f) => oa.isSome() && ob.isSome() ? Optional.some(f(oa.getOrDie(), ob.getOrDie())) : Optional.none();\n\n    const COMMENT = 8;\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom$1(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom$1(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom$1(node);\n    };\n    const fromDom$1 = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom$1);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom: fromDom$1,\n      fromPoint\n    };\n\n    const is$1 = (element, selector) => {\n      const dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        const elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n\n    const eq = (e1, e2) => e1.dom === e2.dom;\n    const contains = (e1, e2) => {\n      const d1 = e1.dom;\n      const d2 = e2.dom;\n      return d1 === d2 ? false : d1.contains(d2);\n    };\n    const is = is$1;\n\n    const Global = typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const path = (parts, scope) => {\n      let o = scope !== undefined && scope !== null ? scope : Global;\n      for (let i = 0; i < parts.length && o !== undefined && o !== null; ++i) {\n        o = o[parts[i]];\n      }\n      return o;\n    };\n    const resolve = (p, scope) => {\n      const parts = p.split('.');\n      return path(parts, scope);\n    };\n\n    const unsafe = (name, scope) => {\n      return resolve(name, scope);\n    };\n    const getOrDie = (name, scope) => {\n      const actual = unsafe(name, scope);\n      if (actual === undefined || actual === null) {\n        throw new Error(name + ' not available on this browser');\n      }\n      return actual;\n    };\n\n    const getPrototypeOf = Object.getPrototypeOf;\n    const sandHTMLElement = scope => {\n      return getOrDie('HTMLElement', scope);\n    };\n    const isPrototypeOf = x => {\n      const scope = resolve('ownerDocument.defaultView', x);\n      return isObject(x) && (sandHTMLElement(scope).prototype.isPrototypeOf(x) || /^HTML\\w*Element$/.test(getPrototypeOf(x).constructor.name));\n    };\n\n    const name = element => {\n      const r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n    const type = element => element.dom.nodeType;\n    const isType = t => element => type(element) === t;\n    const isComment = element => type(element) === COMMENT || name(element) === '#comment';\n    const isHTMLElement = element => isElement$1(element) && isPrototypeOf(element.dom);\n    const isElement$1 = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocument = isType(DOCUMENT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n    const isTag = tag => e => isElement$1(e) && name(e) === tag;\n\n    const owner = element => SugarElement.fromDom(element.dom.ownerDocument);\n    const documentOrOwner = dos => isDocument(dos) ? dos : owner(dos);\n    const parent = element => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const parentElement = element => Optional.from(element.dom.parentElement).map(SugarElement.fromDom);\n    const nextSibling = element => Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);\n    const children = element => map(element.dom.childNodes, SugarElement.fromDom);\n    const child = (element, index) => {\n      const cs = element.dom.childNodes;\n      return Optional.from(cs[index]).map(SugarElement.fromDom);\n    };\n    const firstChild = element => child(element, 0);\n    const lastChild = element => child(element, element.dom.childNodes.length - 1);\n\n    const isShadowRoot = dos => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    const getRootNode = supported ? e => SugarElement.fromDom(e.dom.getRootNode()) : documentOrOwner;\n    const getShadowRoot = e => {\n      const r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    const getShadowHost = e => SugarElement.fromDom(e.dom.host);\n\n    const inBody = element => {\n      const dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      const doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n\n    var ClosestOrAncestor = (is, ancestor, scope, a, isRoot) => {\n      if (is(scope, a)) {\n        return Optional.some(scope);\n      } else if (isFunction(isRoot) && isRoot(scope)) {\n        return Optional.none();\n      } else {\n        return ancestor(scope, a, isRoot);\n      }\n    };\n\n    const ancestor$3 = (scope, predicate, isRoot) => {\n      let element = scope.dom;\n      const stop = isFunction(isRoot) ? isRoot : never;\n      while (element.parentNode) {\n        element = element.parentNode;\n        const el = SugarElement.fromDom(element);\n        if (predicate(el)) {\n          return Optional.some(el);\n        } else if (stop(el)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const closest$2 = (scope, predicate, isRoot) => {\n      const is = (s, test) => test(s);\n      return ClosestOrAncestor(is, ancestor$3, scope, predicate, isRoot);\n    };\n\n    const ancestor$2 = (scope, selector, isRoot) => ancestor$3(scope, e => is$1(e, selector), isRoot);\n    const closest$1 = (scope, selector, isRoot) => {\n      const is = (element, selector) => is$1(element, selector);\n      return ClosestOrAncestor(is, ancestor$2, scope, selector, isRoot);\n    };\n\n    const closest = target => closest$1(target, '[contenteditable]');\n    const isEditable = (element, assumeEditable = false) => {\n      if (inBody(element)) {\n        return element.dom.isContentEditable;\n      } else {\n        return closest(element).fold(constant(assumeEditable), editable => getRaw(editable) === 'true');\n      }\n    };\n    const getRaw = element => element.dom.contentEditable;\n\n    const before$1 = (marker, element) => {\n      const parent$1 = parent(marker);\n      parent$1.each(v => {\n        v.dom.insertBefore(element.dom, marker.dom);\n      });\n    };\n    const after = (marker, element) => {\n      const sibling = nextSibling(marker);\n      sibling.fold(() => {\n        const parent$1 = parent(marker);\n        parent$1.each(v => {\n          append$1(v, element);\n        });\n      }, v => {\n        before$1(v, element);\n      });\n    };\n    const prepend = (parent, element) => {\n      const firstChild$1 = firstChild(parent);\n      firstChild$1.fold(() => {\n        append$1(parent, element);\n      }, v => {\n        parent.dom.insertBefore(element.dom, v.dom);\n      });\n    };\n    const append$1 = (parent, element) => {\n      parent.dom.appendChild(element.dom);\n    };\n\n    const before = (marker, elements) => {\n      each$1(elements, x => {\n        before$1(marker, x);\n      });\n    };\n    const append = (parent, elements) => {\n      each$1(elements, x => {\n        append$1(parent, x);\n      });\n    };\n\n    const empty = element => {\n      element.dom.textContent = '';\n      each$1(children(element), rogue => {\n        remove(rogue);\n      });\n    };\n    const remove = element => {\n      const dom = element.dom;\n      if (dom.parentNode !== null) {\n        dom.parentNode.removeChild(dom);\n      }\n    };\n\n    var global$6 = tinymce.util.Tools.resolve('tinymce.dom.RangeUtils');\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    const fromDom = nodes => map(nodes, SugarElement.fromDom);\n\n    const keys = Object.keys;\n    const each = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n    const objAcc = r => (x, i) => {\n      r[i] = x;\n    };\n    const internalFilter = (obj, pred, onTrue, onFalse) => {\n      each(obj, (x, i) => {\n        (pred(x, i) ? onTrue : onFalse)(x, i);\n      });\n    };\n    const filter = (obj, pred) => {\n      const t = {};\n      internalFilter(obj, pred, objAcc(t), noop);\n      return t;\n    };\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const setAll = (element, attrs) => {\n      const dom = element.dom;\n      each(attrs, (v, k) => {\n        rawSet(dom, k, v);\n      });\n    };\n    const clone$1 = element => foldl(element.dom.attributes, (acc, attr) => {\n      acc[attr.name] = attr.value;\n      return acc;\n    }, {});\n\n    const clone = (original, isDeep) => SugarElement.fromDom(original.dom.cloneNode(isDeep));\n    const deep = original => clone(original, true);\n    const shallowAs = (original, tag) => {\n      const nu = SugarElement.fromTag(tag);\n      const attributes = clone$1(original);\n      setAll(nu, attributes);\n      return nu;\n    };\n    const mutate = (original, tag) => {\n      const nu = shallowAs(original, tag);\n      after(original, nu);\n      const children$1 = children(original);\n      append(nu, children$1);\n      remove(original);\n      return nu;\n    };\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const matchNodeName = name => node => isNonNullable(node) && node.nodeName.toLowerCase() === name;\n    const matchNodeNames = regex => node => isNonNullable(node) && regex.test(node.nodeName);\n    const isTextNode$1 = node => isNonNullable(node) && node.nodeType === 3;\n    const isElement = node => isNonNullable(node) && node.nodeType === 1;\n    const isListNode = matchNodeNames(/^(OL|UL|DL)$/);\n    const isOlUlNode = matchNodeNames(/^(OL|UL)$/);\n    const isOlNode = matchNodeName('ol');\n    const isListItemNode = matchNodeNames(/^(LI|DT|DD)$/);\n    const isDlItemNode = matchNodeNames(/^(DT|DD)$/);\n    const isTableCellNode = matchNodeNames(/^(TH|TD)$/);\n    const isBr = matchNodeName('br');\n    const isFirstChild = node => {\n      var _a;\n      return ((_a = node.parentNode) === null || _a === void 0 ? void 0 : _a.firstChild) === node;\n    };\n    const isTextBlock = (editor, node) => isNonNullable(node) && node.nodeName in editor.schema.getTextBlockElements();\n    const isBlock = (node, blockElements) => isNonNullable(node) && node.nodeName in blockElements;\n    const isVoid = (editor, node) => isNonNullable(node) && node.nodeName in editor.schema.getVoidElements();\n    const isBogusBr = (dom, node) => {\n      if (!isBr(node)) {\n        return false;\n      }\n      return dom.isBlock(node.nextSibling) && !isBr(node.previousSibling);\n    };\n    const isEmpty$2 = (dom, elm, keepBookmarks) => {\n      const empty = dom.isEmpty(elm);\n      if (keepBookmarks && dom.select('span[data-mce-type=bookmark]', elm).length > 0) {\n        return false;\n      }\n      return empty;\n    };\n    const isChildOfBody = (dom, elm) => dom.isChildOf(elm, dom.getRoot());\n\n    const option = name => editor => editor.options.get(name);\n    const register$3 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('lists_indent_on_tab', {\n        processor: 'boolean',\n        default: true\n      });\n    };\n    const shouldIndentOnTab = option('lists_indent_on_tab');\n    const getForcedRootBlock = option('forced_root_block');\n    const getForcedRootBlockAttrs = option('forced_root_block_attrs');\n\n    const createTextBlock = (editor, contentNode, attrs = {}) => {\n      const dom = editor.dom;\n      const blockElements = editor.schema.getBlockElements();\n      const fragment = dom.createFragment();\n      const blockName = getForcedRootBlock(editor);\n      const blockAttrs = getForcedRootBlockAttrs(editor);\n      let node;\n      let textBlock;\n      let hasContentNode = false;\n      textBlock = dom.create(blockName, {\n        ...blockAttrs,\n        ...attrs.style ? { style: attrs.style } : {}\n      });\n      if (!isBlock(contentNode.firstChild, blockElements)) {\n        fragment.appendChild(textBlock);\n      }\n      while (node = contentNode.firstChild) {\n        const nodeName = node.nodeName;\n        if (!hasContentNode && (nodeName !== 'SPAN' || node.getAttribute('data-mce-type') !== 'bookmark')) {\n          hasContentNode = true;\n        }\n        if (isBlock(node, blockElements)) {\n          fragment.appendChild(node);\n          textBlock = null;\n        } else {\n          if (!textBlock) {\n            textBlock = dom.create(blockName, blockAttrs);\n            fragment.appendChild(textBlock);\n          }\n          textBlock.appendChild(node);\n        }\n      }\n      if (!hasContentNode && textBlock) {\n        textBlock.appendChild(dom.create('br', { 'data-mce-bogus': '1' }));\n      }\n      return fragment;\n    };\n\n    const DOM$2 = global$3.DOM;\n    const splitList = (editor, list, li) => {\n      const removeAndKeepBookmarks = targetNode => {\n        const parent = targetNode.parentNode;\n        if (parent) {\n          global$2.each(bookmarks, node => {\n            parent.insertBefore(node, li.parentNode);\n          });\n        }\n        DOM$2.remove(targetNode);\n      };\n      const bookmarks = DOM$2.select('span[data-mce-type=\"bookmark\"]', list);\n      const newBlock = createTextBlock(editor, li);\n      const tmpRng = DOM$2.createRng();\n      tmpRng.setStartAfter(li);\n      tmpRng.setEndAfter(list);\n      const fragment = tmpRng.extractContents();\n      for (let node = fragment.firstChild; node; node = node.firstChild) {\n        if (node.nodeName === 'LI' && editor.dom.isEmpty(node)) {\n          DOM$2.remove(node);\n          break;\n        }\n      }\n      if (!editor.dom.isEmpty(fragment)) {\n        DOM$2.insertAfter(fragment, list);\n      }\n      DOM$2.insertAfter(newBlock, list);\n      const parent = li.parentElement;\n      if (parent && isEmpty$2(editor.dom, parent)) {\n        removeAndKeepBookmarks(parent);\n      }\n      DOM$2.remove(li);\n      if (isEmpty$2(editor.dom, list)) {\n        DOM$2.remove(list);\n      }\n    };\n\n    const isDescriptionDetail = isTag('dd');\n    const isDescriptionTerm = isTag('dt');\n    const outdentDlItem = (editor, item) => {\n      if (isDescriptionDetail(item)) {\n        mutate(item, 'dt');\n      } else if (isDescriptionTerm(item)) {\n        parentElement(item).each(dl => splitList(editor, dl.dom, item.dom));\n      }\n    };\n    const indentDlItem = item => {\n      if (isDescriptionTerm(item)) {\n        mutate(item, 'dd');\n      }\n    };\n    const dlIndentation = (editor, indentation, dlItems) => {\n      if (indentation === 'Indent') {\n        each$1(dlItems, indentDlItem);\n      } else {\n        each$1(dlItems, item => outdentDlItem(editor, item));\n      }\n    };\n\n    const getNormalizedPoint = (container, offset) => {\n      if (isTextNode$1(container)) {\n        return {\n          container,\n          offset\n        };\n      }\n      const node = global$6.getNode(container, offset);\n      if (isTextNode$1(node)) {\n        return {\n          container: node,\n          offset: offset >= container.childNodes.length ? node.data.length : 0\n        };\n      } else if (node.previousSibling && isTextNode$1(node.previousSibling)) {\n        return {\n          container: node.previousSibling,\n          offset: node.previousSibling.data.length\n        };\n      } else if (node.nextSibling && isTextNode$1(node.nextSibling)) {\n        return {\n          container: node.nextSibling,\n          offset: 0\n        };\n      }\n      return {\n        container,\n        offset\n      };\n    };\n    const normalizeRange = rng => {\n      const outRng = rng.cloneRange();\n      const rangeStart = getNormalizedPoint(rng.startContainer, rng.startOffset);\n      outRng.setStart(rangeStart.container, rangeStart.offset);\n      const rangeEnd = getNormalizedPoint(rng.endContainer, rng.endOffset);\n      outRng.setEnd(rangeEnd.container, rangeEnd.offset);\n      return outRng;\n    };\n\n    const listNames = [\n      'OL',\n      'UL',\n      'DL'\n    ];\n    const listSelector = listNames.join(',');\n    const getParentList = (editor, node) => {\n      const selectionStart = node || editor.selection.getStart(true);\n      return editor.dom.getParent(selectionStart, listSelector, getClosestListHost(editor, selectionStart));\n    };\n    const isParentListSelected = (parentList, selectedBlocks) => isNonNullable(parentList) && selectedBlocks.length === 1 && selectedBlocks[0] === parentList;\n    const findSubLists = parentList => filter$1(parentList.querySelectorAll(listSelector), isListNode);\n    const getSelectedSubLists = editor => {\n      const parentList = getParentList(editor);\n      const selectedBlocks = editor.selection.getSelectedBlocks();\n      if (isParentListSelected(parentList, selectedBlocks)) {\n        return findSubLists(parentList);\n      } else {\n        return filter$1(selectedBlocks, elm => {\n          return isListNode(elm) && parentList !== elm;\n        });\n      }\n    };\n    const findParentListItemsNodes = (editor, elms) => {\n      const listItemsElms = global$2.map(elms, elm => {\n        const parentLi = editor.dom.getParent(elm, 'li,dd,dt', getClosestListHost(editor, elm));\n        return parentLi ? parentLi : elm;\n      });\n      return unique(listItemsElms);\n    };\n    const getSelectedListItems = editor => {\n      const selectedBlocks = editor.selection.getSelectedBlocks();\n      return filter$1(findParentListItemsNodes(editor, selectedBlocks), isListItemNode);\n    };\n    const getSelectedDlItems = editor => filter$1(getSelectedListItems(editor), isDlItemNode);\n    const getClosestEditingHost = (editor, elm) => {\n      const parentTableCell = editor.dom.getParents(elm, 'TD,TH');\n      return parentTableCell.length > 0 ? parentTableCell[0] : editor.getBody();\n    };\n    const isListHost = (schema, node) => !isListNode(node) && !isListItemNode(node) && exists(listNames, listName => schema.isValidChild(node.nodeName, listName));\n    const getClosestListHost = (editor, elm) => {\n      const parentBlocks = editor.dom.getParents(elm, editor.dom.isBlock);\n      const isNotForcedRootBlock = elm => elm.nodeName.toLowerCase() !== getForcedRootBlock(editor);\n      const parentBlock = find(parentBlocks, elm => isNotForcedRootBlock(elm) && isListHost(editor.schema, elm));\n      return parentBlock.getOr(editor.getBody());\n    };\n    const isListInsideAnLiWithFirstAndLastNotListElement = list => parent(list).exists(parent => isListItemNode(parent.dom) && firstChild(parent).exists(firstChild => !isListNode(firstChild.dom)) && lastChild(parent).exists(lastChild => !isListNode(lastChild.dom)));\n    const findLastParentListNode = (editor, elm) => {\n      const parentLists = editor.dom.getParents(elm, 'ol,ul', getClosestListHost(editor, elm));\n      return last(parentLists);\n    };\n    const getSelectedLists = editor => {\n      const firstList = findLastParentListNode(editor, editor.selection.getStart());\n      const subsequentLists = filter$1(editor.selection.getSelectedBlocks(), isOlUlNode);\n      return firstList.toArray().concat(subsequentLists);\n    };\n    const getParentLists = editor => {\n      const elm = editor.selection.getStart();\n      return editor.dom.getParents(elm, 'ol,ul', getClosestListHost(editor, elm));\n    };\n    const getSelectedListRoots = editor => {\n      const selectedLists = getSelectedLists(editor);\n      const parentLists = getParentLists(editor);\n      return find(parentLists, p => isListInsideAnLiWithFirstAndLastNotListElement(SugarElement.fromDom(p))).fold(() => getUniqueListRoots(editor, selectedLists), l => [l]);\n    };\n    const getUniqueListRoots = (editor, lists) => {\n      const listRoots = map(lists, list => findLastParentListNode(editor, list).getOr(list));\n      return unique(listRoots);\n    };\n\n    const isCustomList = list => /\\btox\\-/.test(list.className);\n    const inList = (parents, listName) => findUntil(parents, isListNode, isTableCellNode).exists(list => list.nodeName === listName && !isCustomList(list));\n    const isWithinNonEditable = (editor, element) => element !== null && !editor.dom.isEditable(element);\n    const selectionIsWithinNonEditableList = editor => {\n      const parentList = getParentList(editor);\n      return isWithinNonEditable(editor, parentList);\n    };\n    const isWithinNonEditableList = (editor, element) => {\n      const parentList = editor.dom.getParent(element, 'ol,ul,dl');\n      return isWithinNonEditable(editor, parentList);\n    };\n    const setNodeChangeHandler = (editor, nodeChangeHandler) => {\n      const initialNode = editor.selection.getNode();\n      nodeChangeHandler({\n        parents: editor.dom.getParents(initialNode),\n        element: initialNode\n      });\n      editor.on('NodeChange', nodeChangeHandler);\n      return () => editor.off('NodeChange', nodeChangeHandler);\n    };\n\n    const fromElements = (elements, scope) => {\n      const doc = scope || document;\n      const fragment = doc.createDocumentFragment();\n      each$1(elements, element => {\n        fragment.appendChild(element.dom);\n      });\n      return SugarElement.fromDom(fragment);\n    };\n\n    const fireListEvent = (editor, action, element) => editor.dispatch('ListMutation', {\n      action,\n      element\n    });\n\n    const blank = r => s => s.replace(r, '');\n    const trim = blank(/^\\s+|\\s+$/g);\n    const isNotEmpty = s => s.length > 0;\n    const isEmpty$1 = s => !isNotEmpty(s);\n\n    const isSupported = dom => dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    const internalSet = (dom, property, value) => {\n      if (!isString(value)) {\n        console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n        throw new Error('CSS value must be a string: ' + value);\n      }\n      if (isSupported(dom)) {\n        dom.style.setProperty(property, value);\n      }\n    };\n    const set = (element, property, value) => {\n      const dom = element.dom;\n      internalSet(dom, property, value);\n    };\n\n    const isList = el => is(el, 'OL,UL');\n    const isListItem = el => is(el, 'LI');\n    const hasFirstChildList = el => firstChild(el).exists(isList);\n    const hasLastChildList = el => lastChild(el).exists(isList);\n\n    const isEntryList = entry => 'listAttributes' in entry;\n    const isEntryComment = entry => 'isComment' in entry;\n    const isEntryFragment = entry => 'isFragment' in entry;\n    const isIndented = entry => entry.depth > 0;\n    const isSelected = entry => entry.isSelected;\n    const cloneItemContent = li => {\n      const children$1 = children(li);\n      const content = hasLastChildList(li) ? children$1.slice(0, -1) : children$1;\n      return map(content, deep);\n    };\n    const createEntry = (li, depth, isSelected) => parent(li).filter(isElement$1).map(list => ({\n      depth,\n      dirty: false,\n      isSelected,\n      content: cloneItemContent(li),\n      itemAttributes: clone$1(li),\n      listAttributes: clone$1(list),\n      listType: name(list),\n      isInPreviousLi: false\n    }));\n\n    const joinSegment = (parent, child) => {\n      append$1(parent.item, child.list);\n    };\n    const joinSegments = segments => {\n      for (let i = 1; i < segments.length; i++) {\n        joinSegment(segments[i - 1], segments[i]);\n      }\n    };\n    const appendSegments = (head$1, tail) => {\n      lift2(last(head$1), head(tail), joinSegment);\n    };\n    const createSegment = (scope, listType) => {\n      const segment = {\n        list: SugarElement.fromTag(listType, scope),\n        item: SugarElement.fromTag('li', scope)\n      };\n      append$1(segment.list, segment.item);\n      return segment;\n    };\n    const createSegments = (scope, entry, size) => {\n      const segments = [];\n      for (let i = 0; i < size; i++) {\n        segments.push(createSegment(scope, isEntryList(entry) ? entry.listType : entry.parentListType));\n      }\n      return segments;\n    };\n    const populateSegments = (segments, entry) => {\n      for (let i = 0; i < segments.length - 1; i++) {\n        set(segments[i].item, 'list-style-type', 'none');\n      }\n      last(segments).each(segment => {\n        if (isEntryList(entry)) {\n          setAll(segment.list, entry.listAttributes);\n          setAll(segment.item, entry.itemAttributes);\n        }\n        append(segment.item, entry.content);\n      });\n    };\n    const normalizeSegment = (segment, entry) => {\n      if (name(segment.list) !== entry.listType) {\n        segment.list = mutate(segment.list, entry.listType);\n      }\n      setAll(segment.list, entry.listAttributes);\n    };\n    const createItem = (scope, attr, content) => {\n      const item = SugarElement.fromTag('li', scope);\n      setAll(item, attr);\n      append(item, content);\n      return item;\n    };\n    const appendItem = (segment, item) => {\n      append$1(segment.list, item);\n      segment.item = item;\n    };\n    const writeShallow = (scope, cast, entry) => {\n      const newCast = cast.slice(0, entry.depth);\n      last(newCast).each(segment => {\n        if (isEntryList(entry)) {\n          const item = createItem(scope, entry.itemAttributes, entry.content);\n          appendItem(segment, item);\n          normalizeSegment(segment, entry);\n        } else if (isEntryFragment(entry)) {\n          append(segment.item, entry.content);\n        } else {\n          const item = SugarElement.fromHtml(`<!--${ entry.content }-->`);\n          append$1(segment.list, item);\n        }\n      });\n      return newCast;\n    };\n    const writeDeep = (scope, cast, entry) => {\n      const segments = createSegments(scope, entry, entry.depth - cast.length);\n      joinSegments(segments);\n      populateSegments(segments, entry);\n      appendSegments(cast, segments);\n      return cast.concat(segments);\n    };\n    const composeList = (scope, entries) => {\n      let firstCommentEntryOpt = Optional.none();\n      const cast = foldl(entries, (cast, entry, i) => {\n        if (!isEntryComment(entry)) {\n          return entry.depth > cast.length ? writeDeep(scope, cast, entry) : writeShallow(scope, cast, entry);\n        } else {\n          if (i === 0) {\n            firstCommentEntryOpt = Optional.some(entry);\n            return cast;\n          }\n          return writeShallow(scope, cast, entry);\n        }\n      }, []);\n      firstCommentEntryOpt.each(firstCommentEntry => {\n        const item = SugarElement.fromHtml(`<!--${ firstCommentEntry.content }-->`);\n        head(cast).each(fistCast => {\n          prepend(fistCast.list, item);\n        });\n      });\n      return head(cast).map(segment => segment.list);\n    };\n\n    const indentEntry = (indentation, entry) => {\n      switch (indentation) {\n      case 'Indent':\n        entry.depth++;\n        break;\n      case 'Outdent':\n        entry.depth--;\n        break;\n      case 'Flatten':\n        entry.depth = 0;\n      }\n      entry.dirty = true;\n    };\n\n    const cloneListProperties = (target, source) => {\n      if (isEntryList(target) && isEntryList(source)) {\n        target.listType = source.listType;\n        target.listAttributes = { ...source.listAttributes };\n      }\n    };\n    const cleanListProperties = entry => {\n      entry.listAttributes = filter(entry.listAttributes, (_value, key) => key !== 'start');\n    };\n    const closestSiblingEntry = (entries, start) => {\n      const depth = entries[start].depth;\n      const matches = entry => entry.depth === depth && !entry.dirty;\n      const until = entry => entry.depth < depth;\n      return findUntil(reverse(entries.slice(0, start)), matches, until).orThunk(() => findUntil(entries.slice(start + 1), matches, until));\n    };\n    const normalizeEntries = entries => {\n      each$1(entries, (entry, i) => {\n        closestSiblingEntry(entries, i).fold(() => {\n          if (entry.dirty && isEntryList(entry)) {\n            cleanListProperties(entry);\n          }\n        }, matchingEntry => cloneListProperties(entry, matchingEntry));\n      });\n      return entries;\n    };\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    const parseSingleItem = (depth, itemSelection, selectionState, item) => {\n      var _a;\n      if (isComment(item)) {\n        return [{\n            depth: depth + 1,\n            content: (_a = item.dom.nodeValue) !== null && _a !== void 0 ? _a : '',\n            dirty: false,\n            isSelected: false,\n            isComment: true\n          }];\n      }\n      itemSelection.each(selection => {\n        if (eq(selection.start, item)) {\n          selectionState.set(true);\n        }\n      });\n      const currentItemEntry = createEntry(item, depth, selectionState.get());\n      itemSelection.each(selection => {\n        if (eq(selection.end, item)) {\n          selectionState.set(false);\n        }\n      });\n      const childListEntries = lastChild(item).filter(isList).map(list => parseList(depth, itemSelection, selectionState, list)).getOr([]);\n      return currentItemEntry.toArray().concat(childListEntries);\n    };\n    const parseItem = (depth, itemSelection, selectionState, item) => firstChild(item).filter(isList).fold(() => parseSingleItem(depth, itemSelection, selectionState, item), list => {\n      const parsedSiblings = foldl(children(item), (acc, liChild, i) => {\n        if (i === 0) {\n          return acc;\n        } else {\n          if (isListItem(liChild)) {\n            return acc.concat(parseSingleItem(depth, itemSelection, selectionState, liChild));\n          } else {\n            const fragment = {\n              isFragment: true,\n              depth,\n              content: [liChild],\n              isSelected: false,\n              dirty: false,\n              parentListType: name(list)\n            };\n            return acc.concat(fragment);\n          }\n        }\n      }, []);\n      return parseList(depth, itemSelection, selectionState, list).concat(parsedSiblings);\n    });\n    const parseList = (depth, itemSelection, selectionState, list) => bind(children(list), element => {\n      const parser = isList(element) ? parseList : parseItem;\n      const newDepth = depth + 1;\n      return parser(newDepth, itemSelection, selectionState, element);\n    });\n    const parseLists = (lists, itemSelection) => {\n      const selectionState = Cell(false);\n      const initialDepth = 0;\n      return map(lists, list => ({\n        sourceList: list,\n        entries: parseList(initialDepth, itemSelection, selectionState, list)\n      }));\n    };\n\n    const outdentedComposer = (editor, entries) => {\n      const normalizedEntries = normalizeEntries(entries);\n      return map(normalizedEntries, entry => {\n        const content = !isEntryComment(entry) ? fromElements(entry.content) : fromElements([SugarElement.fromHtml(`<!--${ entry.content }-->`)]);\n        const listItemAttrs = isEntryList(entry) ? entry.itemAttributes : {};\n        return SugarElement.fromDom(createTextBlock(editor, content.dom, listItemAttrs));\n      });\n    };\n    const indentedComposer = (editor, entries) => {\n      const normalizedEntries = normalizeEntries(entries);\n      return composeList(editor.contentDocument, normalizedEntries).toArray();\n    };\n    const composeEntries = (editor, entries) => bind(groupBy(entries, isIndented), entries => {\n      const groupIsIndented = head(entries).exists(isIndented);\n      return groupIsIndented ? indentedComposer(editor, entries) : outdentedComposer(editor, entries);\n    });\n    const indentSelectedEntries = (entries, indentation) => {\n      each$1(filter$1(entries, isSelected), entry => indentEntry(indentation, entry));\n    };\n    const getItemSelection = editor => {\n      const selectedListItems = map(getSelectedListItems(editor), SugarElement.fromDom);\n      return lift2(find(selectedListItems, not(hasFirstChildList)), find(reverse(selectedListItems), not(hasFirstChildList)), (start, end) => ({\n        start,\n        end\n      }));\n    };\n    const listIndentation = (editor, lists, indentation) => {\n      const entrySets = parseLists(lists, getItemSelection(editor));\n      each$1(entrySets, entrySet => {\n        indentSelectedEntries(entrySet.entries, indentation);\n        const composedLists = composeEntries(editor, entrySet.entries);\n        each$1(composedLists, composedList => {\n          fireListEvent(editor, indentation === 'Indent' ? 'IndentList' : 'OutdentList', composedList.dom);\n        });\n        before(entrySet.sourceList, composedLists);\n        remove(entrySet.sourceList);\n      });\n    };\n\n    const selectionIndentation = (editor, indentation) => {\n      const lists = fromDom(getSelectedListRoots(editor));\n      const dlItems = fromDom(getSelectedDlItems(editor));\n      let isHandled = false;\n      if (lists.length || dlItems.length) {\n        const bookmark = editor.selection.getBookmark();\n        listIndentation(editor, lists, indentation);\n        dlIndentation(editor, indentation, dlItems);\n        editor.selection.moveToBookmark(bookmark);\n        editor.selection.setRng(normalizeRange(editor.selection.getRng()));\n        editor.nodeChanged();\n        isHandled = true;\n      }\n      return isHandled;\n    };\n    const handleIndentation = (editor, indentation) => !selectionIsWithinNonEditableList(editor) && selectionIndentation(editor, indentation);\n    const indentListSelection = editor => handleIndentation(editor, 'Indent');\n    const outdentListSelection = editor => handleIndentation(editor, 'Outdent');\n    const flattenListSelection = editor => handleIndentation(editor, 'Flatten');\n\n    const zeroWidth = '\\uFEFF';\n    const isZwsp = char => char === zeroWidth;\n\n    const ancestor$1 = (scope, predicate, isRoot) => ancestor$3(scope, predicate, isRoot).isSome();\n\n    const ancestor = (element, target) => ancestor$1(element, curry(eq, target));\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.BookmarkManager');\n\n    const DOM$1 = global$3.DOM;\n    const createBookmark = rng => {\n      const bookmark = {};\n      const setupEndPoint = start => {\n        let container = rng[start ? 'startContainer' : 'endContainer'];\n        let offset = rng[start ? 'startOffset' : 'endOffset'];\n        if (isElement(container)) {\n          const offsetNode = DOM$1.create('span', { 'data-mce-type': 'bookmark' });\n          if (container.hasChildNodes()) {\n            offset = Math.min(offset, container.childNodes.length - 1);\n            if (start) {\n              container.insertBefore(offsetNode, container.childNodes[offset]);\n            } else {\n              DOM$1.insertAfter(offsetNode, container.childNodes[offset]);\n            }\n          } else {\n            container.appendChild(offsetNode);\n          }\n          container = offsetNode;\n          offset = 0;\n        }\n        bookmark[start ? 'startContainer' : 'endContainer'] = container;\n        bookmark[start ? 'startOffset' : 'endOffset'] = offset;\n      };\n      setupEndPoint(true);\n      if (!rng.collapsed) {\n        setupEndPoint();\n      }\n      return bookmark;\n    };\n    const resolveBookmark = bookmark => {\n      const restoreEndPoint = start => {\n        const nodeIndex = container => {\n          var _a;\n          let node = (_a = container.parentNode) === null || _a === void 0 ? void 0 : _a.firstChild;\n          let idx = 0;\n          while (node) {\n            if (node === container) {\n              return idx;\n            }\n            if (!isElement(node) || node.getAttribute('data-mce-type') !== 'bookmark') {\n              idx++;\n            }\n            node = node.nextSibling;\n          }\n          return -1;\n        };\n        let container = bookmark[start ? 'startContainer' : 'endContainer'];\n        let offset = bookmark[start ? 'startOffset' : 'endOffset'];\n        if (!container) {\n          return;\n        }\n        if (isElement(container) && container.parentNode) {\n          const node = container;\n          offset = nodeIndex(container);\n          container = container.parentNode;\n          DOM$1.remove(node);\n          if (!container.hasChildNodes() && DOM$1.isBlock(container)) {\n            container.appendChild(DOM$1.create('br'));\n          }\n        }\n        bookmark[start ? 'startContainer' : 'endContainer'] = container;\n        bookmark[start ? 'startOffset' : 'endOffset'] = offset;\n      };\n      restoreEndPoint(true);\n      restoreEndPoint();\n      const rng = DOM$1.createRng();\n      rng.setStart(bookmark.startContainer, bookmark.startOffset);\n      if (bookmark.endContainer) {\n        rng.setEnd(bookmark.endContainer, bookmark.endOffset);\n      }\n      return normalizeRange(rng);\n    };\n\n    const listToggleActionFromListName = listName => {\n      switch (listName) {\n      case 'UL':\n        return 'ToggleUlList';\n      case 'OL':\n        return 'ToggleOlList';\n      case 'DL':\n        return 'ToggleDLList';\n      }\n    };\n\n    const updateListStyle = (dom, el, detail) => {\n      const type = detail['list-style-type'] ? detail['list-style-type'] : null;\n      dom.setStyle(el, 'list-style-type', type);\n    };\n    const setAttribs = (elm, attrs) => {\n      global$2.each(attrs, (value, key) => {\n        elm.setAttribute(key, value);\n      });\n    };\n    const updateListAttrs = (dom, el, detail) => {\n      setAttribs(el, detail['list-attributes']);\n      global$2.each(dom.select('li', el), li => {\n        setAttribs(li, detail['list-item-attributes']);\n      });\n    };\n    const updateListWithDetails = (dom, el, detail) => {\n      updateListStyle(dom, el, detail);\n      updateListAttrs(dom, el, detail);\n    };\n    const removeStyles = (dom, element, styles) => {\n      global$2.each(styles, style => dom.setStyle(element, style, ''));\n    };\n    const isInline = (editor, node) => isNonNullable(node) && !isBlock(node, editor.schema.getBlockElements());\n    const getEndPointNode = (editor, rng, start, root) => {\n      let container = rng[start ? 'startContainer' : 'endContainer'];\n      const offset = rng[start ? 'startOffset' : 'endOffset'];\n      if (isElement(container)) {\n        container = container.childNodes[Math.min(offset, container.childNodes.length - 1)] || container;\n      }\n      if (!start && isBr(container.nextSibling)) {\n        container = container.nextSibling;\n      }\n      const findBlockAncestor = node => {\n        while (!editor.dom.isBlock(node) && node.parentNode && root !== node) {\n          node = node.parentNode;\n        }\n        return node;\n      };\n      const findBetterContainer = (container, forward) => {\n        var _a;\n        const walker = new global$5(container, findBlockAncestor(container));\n        const dir = forward ? 'next' : 'prev';\n        let node;\n        while (node = walker[dir]()) {\n          if (!(isVoid(editor, node) || isZwsp(node.textContent) || ((_a = node.textContent) === null || _a === void 0 ? void 0 : _a.length) === 0)) {\n            return Optional.some(node);\n          }\n        }\n        return Optional.none();\n      };\n      if (start && isTextNode$1(container)) {\n        if (isZwsp(container.textContent)) {\n          container = findBetterContainer(container, false).getOr(container);\n        } else {\n          if (container.parentNode !== null && isInline(editor, container.parentNode)) {\n            container = container.parentNode;\n          }\n          while (container.previousSibling !== null && (isInline(editor, container.previousSibling) || isTextNode$1(container.previousSibling))) {\n            container = container.previousSibling;\n          }\n        }\n      }\n      if (!start && isTextNode$1(container)) {\n        if (isZwsp(container.textContent)) {\n          container = findBetterContainer(container, true).getOr(container);\n        } else {\n          if (container.parentNode !== null && isInline(editor, container.parentNode)) {\n            container = container.parentNode;\n          }\n          while (container.nextSibling !== null && (isInline(editor, container.nextSibling) || isTextNode$1(container.nextSibling))) {\n            container = container.nextSibling;\n          }\n        }\n      }\n      while (container.parentNode !== root) {\n        const parent = container.parentNode;\n        if (isTextBlock(editor, container)) {\n          return container;\n        }\n        if (/^(TD|TH)$/.test(parent.nodeName)) {\n          return container;\n        }\n        container = parent;\n      }\n      return container;\n    };\n    const getSelectedTextBlocks = (editor, rng, root) => {\n      const textBlocks = [];\n      const dom = editor.dom;\n      const startNode = getEndPointNode(editor, rng, true, root);\n      const endNode = getEndPointNode(editor, rng, false, root);\n      let block;\n      const siblings = [];\n      for (let node = startNode; node; node = node.nextSibling) {\n        siblings.push(node);\n        if (node === endNode) {\n          break;\n        }\n      }\n      global$2.each(siblings, node => {\n        var _a;\n        if (isTextBlock(editor, node)) {\n          textBlocks.push(node);\n          block = null;\n          return;\n        }\n        if (dom.isBlock(node) || isBr(node)) {\n          if (isBr(node)) {\n            dom.remove(node);\n          }\n          block = null;\n          return;\n        }\n        const nextSibling = node.nextSibling;\n        if (global$1.isBookmarkNode(node)) {\n          if (isListNode(nextSibling) || isTextBlock(editor, nextSibling) || !nextSibling && node.parentNode === root) {\n            block = null;\n            return;\n          }\n        }\n        if (!block) {\n          block = dom.create('p');\n          (_a = node.parentNode) === null || _a === void 0 ? void 0 : _a.insertBefore(block, node);\n          textBlocks.push(block);\n        }\n        block.appendChild(node);\n      });\n      return textBlocks;\n    };\n    const hasCompatibleStyle = (dom, sib, detail) => {\n      const sibStyle = dom.getStyle(sib, 'list-style-type');\n      let detailStyle = detail ? detail['list-style-type'] : '';\n      detailStyle = detailStyle === null ? '' : detailStyle;\n      return sibStyle === detailStyle;\n    };\n    const getRootSearchStart = (editor, range) => {\n      const start = editor.selection.getStart(true);\n      const startPoint = getEndPointNode(editor, range, true, editor.getBody());\n      if (ancestor(SugarElement.fromDom(startPoint), SugarElement.fromDom(range.commonAncestorContainer))) {\n        return range.commonAncestorContainer;\n      } else {\n        return start;\n      }\n    };\n    const applyList = (editor, listName, detail) => {\n      const rng = editor.selection.getRng();\n      let listItemName = 'LI';\n      const root = getClosestListHost(editor, getRootSearchStart(editor, rng));\n      const dom = editor.dom;\n      if (dom.getContentEditable(editor.selection.getNode()) === 'false') {\n        return;\n      }\n      listName = listName.toUpperCase();\n      if (listName === 'DL') {\n        listItemName = 'DT';\n      }\n      const bookmark = createBookmark(rng);\n      const selectedTextBlocks = filter$1(getSelectedTextBlocks(editor, rng, root), editor.dom.isEditable);\n      global$2.each(selectedTextBlocks, block => {\n        let listBlock;\n        const sibling = block.previousSibling;\n        const parent = block.parentNode;\n        if (!isListItemNode(parent)) {\n          if (sibling && isListNode(sibling) && sibling.nodeName === listName && hasCompatibleStyle(dom, sibling, detail)) {\n            listBlock = sibling;\n            block = dom.rename(block, listItemName);\n            sibling.appendChild(block);\n          } else {\n            listBlock = dom.create(listName);\n            parent.insertBefore(listBlock, block);\n            listBlock.appendChild(block);\n            block = dom.rename(block, listItemName);\n          }\n          removeStyles(dom, block, [\n            'margin',\n            'margin-right',\n            'margin-bottom',\n            'margin-left',\n            'margin-top',\n            'padding',\n            'padding-right',\n            'padding-bottom',\n            'padding-left',\n            'padding-top'\n          ]);\n          updateListWithDetails(dom, listBlock, detail);\n          mergeWithAdjacentLists(editor.dom, listBlock);\n        }\n      });\n      editor.selection.setRng(resolveBookmark(bookmark));\n    };\n    const isValidLists = (list1, list2) => {\n      return isListNode(list1) && list1.nodeName === (list2 === null || list2 === void 0 ? void 0 : list2.nodeName);\n    };\n    const hasSameListStyle = (dom, list1, list2) => {\n      const targetStyle = dom.getStyle(list1, 'list-style-type', true);\n      const style = dom.getStyle(list2, 'list-style-type', true);\n      return targetStyle === style;\n    };\n    const hasSameClasses = (elm1, elm2) => {\n      return elm1.className === elm2.className;\n    };\n    const shouldMerge = (dom, list1, list2) => {\n      return isValidLists(list1, list2) && hasSameListStyle(dom, list1, list2) && hasSameClasses(list1, list2);\n    };\n    const mergeWithAdjacentLists = (dom, listBlock) => {\n      let node;\n      let sibling = listBlock.nextSibling;\n      if (shouldMerge(dom, listBlock, sibling)) {\n        const liSibling = sibling;\n        while (node = liSibling.firstChild) {\n          listBlock.appendChild(node);\n        }\n        dom.remove(liSibling);\n      }\n      sibling = listBlock.previousSibling;\n      if (shouldMerge(dom, listBlock, sibling)) {\n        const liSibling = sibling;\n        while (node = liSibling.lastChild) {\n          listBlock.insertBefore(node, listBlock.firstChild);\n        }\n        dom.remove(liSibling);\n      }\n    };\n    const updateList$1 = (editor, list, listName, detail) => {\n      if (list.nodeName !== listName) {\n        const newList = editor.dom.rename(list, listName);\n        updateListWithDetails(editor.dom, newList, detail);\n        fireListEvent(editor, listToggleActionFromListName(listName), newList);\n      } else {\n        updateListWithDetails(editor.dom, list, detail);\n        fireListEvent(editor, listToggleActionFromListName(listName), list);\n      }\n    };\n    const updateCustomList = (editor, list, listName, detail) => {\n      list.classList.forEach((cls, _, classList) => {\n        if (cls.startsWith('tox-')) {\n          classList.remove(cls);\n          if (classList.length === 0) {\n            list.removeAttribute('class');\n          }\n        }\n      });\n      if (list.nodeName !== listName) {\n        const newList = editor.dom.rename(list, listName);\n        updateListWithDetails(editor.dom, newList, detail);\n        fireListEvent(editor, listToggleActionFromListName(listName), newList);\n      } else {\n        updateListWithDetails(editor.dom, list, detail);\n        fireListEvent(editor, listToggleActionFromListName(listName), list);\n      }\n    };\n    const toggleMultipleLists = (editor, parentList, lists, listName, detail) => {\n      const parentIsList = isListNode(parentList);\n      if (parentIsList && parentList.nodeName === listName && !hasListStyleDetail(detail) && !isCustomList(parentList)) {\n        flattenListSelection(editor);\n      } else {\n        applyList(editor, listName, detail);\n        const bookmark = createBookmark(editor.selection.getRng());\n        const allLists = parentIsList ? [\n          parentList,\n          ...lists\n        ] : lists;\n        const updateFunction = parentIsList && isCustomList(parentList) ? updateCustomList : updateList$1;\n        global$2.each(allLists, elm => {\n          updateFunction(editor, elm, listName, detail);\n        });\n        editor.selection.setRng(resolveBookmark(bookmark));\n      }\n    };\n    const hasListStyleDetail = detail => {\n      return 'list-style-type' in detail;\n    };\n    const toggleSingleList = (editor, parentList, listName, detail) => {\n      if (parentList === editor.getBody()) {\n        return;\n      }\n      if (parentList) {\n        if (parentList.nodeName === listName && !hasListStyleDetail(detail) && !isCustomList(parentList)) {\n          flattenListSelection(editor);\n        } else {\n          const bookmark = createBookmark(editor.selection.getRng());\n          if (isCustomList(parentList)) {\n            parentList.classList.forEach((cls, _, classList) => {\n              if (cls.startsWith('tox-')) {\n                classList.remove(cls);\n                if (classList.length === 0) {\n                  parentList.removeAttribute('class');\n                }\n              }\n            });\n          }\n          updateListWithDetails(editor.dom, parentList, detail);\n          const newList = editor.dom.rename(parentList, listName);\n          mergeWithAdjacentLists(editor.dom, newList);\n          editor.selection.setRng(resolveBookmark(bookmark));\n          applyList(editor, listName, detail);\n          fireListEvent(editor, listToggleActionFromListName(listName), newList);\n        }\n      } else {\n        applyList(editor, listName, detail);\n        fireListEvent(editor, listToggleActionFromListName(listName), parentList);\n      }\n    };\n    const toggleList = (editor, listName, _detail) => {\n      const parentList = getParentList(editor);\n      if (isWithinNonEditableList(editor, parentList)) {\n        return;\n      }\n      const selectedSubLists = getSelectedSubLists(editor);\n      const detail = isObject(_detail) ? _detail : {};\n      if (selectedSubLists.length > 0) {\n        toggleMultipleLists(editor, parentList, selectedSubLists, listName, detail);\n      } else {\n        toggleSingleList(editor, parentList, listName, detail);\n      }\n    };\n\n    const DOM = global$3.DOM;\n    const normalizeList = (dom, list) => {\n      const parentNode = list.parentElement;\n      if (parentNode && parentNode.nodeName === 'LI' && parentNode.firstChild === list) {\n        const sibling = parentNode.previousSibling;\n        if (sibling && sibling.nodeName === 'LI') {\n          sibling.appendChild(list);\n          if (isEmpty$2(dom, parentNode)) {\n            DOM.remove(parentNode);\n          }\n        } else {\n          DOM.setStyle(parentNode, 'listStyleType', 'none');\n        }\n      }\n      if (isListNode(parentNode)) {\n        const sibling = parentNode.previousSibling;\n        if (sibling && sibling.nodeName === 'LI') {\n          sibling.appendChild(list);\n        }\n      }\n    };\n    const normalizeLists = (dom, element) => {\n      const lists = global$2.grep(dom.select('ol,ul', element));\n      global$2.each(lists, list => {\n        normalizeList(dom, list);\n      });\n    };\n\n    const findNextCaretContainer = (editor, rng, isForward, root) => {\n      let node = rng.startContainer;\n      const offset = rng.startOffset;\n      if (isTextNode$1(node) && (isForward ? offset < node.data.length : offset > 0)) {\n        return node;\n      }\n      const nonEmptyBlocks = editor.schema.getNonEmptyElements();\n      if (isElement(node)) {\n        node = global$6.getNode(node, offset);\n      }\n      const walker = new global$5(node, root);\n      if (isForward) {\n        if (isBogusBr(editor.dom, node)) {\n          walker.next();\n        }\n      }\n      const walkFn = isForward ? walker.next.bind(walker) : walker.prev2.bind(walker);\n      while (node = walkFn()) {\n        if (node.nodeName === 'LI' && !node.hasChildNodes()) {\n          return node;\n        }\n        if (nonEmptyBlocks[node.nodeName]) {\n          return node;\n        }\n        if (isTextNode$1(node) && node.data.length > 0) {\n          return node;\n        }\n      }\n      return null;\n    };\n    const hasOnlyOneBlockChild = (dom, elm) => {\n      const childNodes = elm.childNodes;\n      return childNodes.length === 1 && !isListNode(childNodes[0]) && dom.isBlock(childNodes[0]);\n    };\n    const isUnwrappable = node => Optional.from(node).map(SugarElement.fromDom).filter(isHTMLElement).exists(el => isEditable(el) && !contains$1(['details'], name(el)));\n    const unwrapSingleBlockChild = (dom, elm) => {\n      if (hasOnlyOneBlockChild(dom, elm) && isUnwrappable(elm.firstChild)) {\n        dom.remove(elm.firstChild, true);\n      }\n    };\n    const moveChildren = (dom, fromElm, toElm) => {\n      let node;\n      const targetElm = hasOnlyOneBlockChild(dom, toElm) ? toElm.firstChild : toElm;\n      unwrapSingleBlockChild(dom, fromElm);\n      if (!isEmpty$2(dom, fromElm, true)) {\n        while (node = fromElm.firstChild) {\n          targetElm.appendChild(node);\n        }\n      }\n    };\n    const mergeLiElements = (dom, fromElm, toElm) => {\n      let listNode;\n      const ul = fromElm.parentNode;\n      if (!isChildOfBody(dom, fromElm) || !isChildOfBody(dom, toElm)) {\n        return;\n      }\n      if (isListNode(toElm.lastChild)) {\n        listNode = toElm.lastChild;\n      }\n      if (ul === toElm.lastChild) {\n        if (isBr(ul.previousSibling)) {\n          dom.remove(ul.previousSibling);\n        }\n      }\n      const node = toElm.lastChild;\n      if (node && isBr(node) && fromElm.hasChildNodes()) {\n        dom.remove(node);\n      }\n      if (isEmpty$2(dom, toElm, true)) {\n        empty(SugarElement.fromDom(toElm));\n      }\n      moveChildren(dom, fromElm, toElm);\n      if (listNode) {\n        toElm.appendChild(listNode);\n      }\n      const contains$1 = contains(SugarElement.fromDom(toElm), SugarElement.fromDom(fromElm));\n      const nestedLists = contains$1 ? dom.getParents(fromElm, isListNode, toElm) : [];\n      dom.remove(fromElm);\n      each$1(nestedLists, list => {\n        if (isEmpty$2(dom, list) && list !== dom.getRoot()) {\n          dom.remove(list);\n        }\n      });\n    };\n    const mergeIntoEmptyLi = (editor, fromLi, toLi) => {\n      empty(SugarElement.fromDom(toLi));\n      mergeLiElements(editor.dom, fromLi, toLi);\n      editor.selection.setCursorLocation(toLi, 0);\n    };\n    const mergeForward = (editor, rng, fromLi, toLi) => {\n      const dom = editor.dom;\n      if (dom.isEmpty(toLi)) {\n        mergeIntoEmptyLi(editor, fromLi, toLi);\n      } else {\n        const bookmark = createBookmark(rng);\n        mergeLiElements(dom, fromLi, toLi);\n        editor.selection.setRng(resolveBookmark(bookmark));\n      }\n    };\n    const mergeBackward = (editor, rng, fromLi, toLi) => {\n      const bookmark = createBookmark(rng);\n      mergeLiElements(editor.dom, fromLi, toLi);\n      const resolvedBookmark = resolveBookmark(bookmark);\n      editor.selection.setRng(resolvedBookmark);\n    };\n    const backspaceDeleteFromListToListCaret = (editor, isForward) => {\n      const dom = editor.dom, selection = editor.selection;\n      const selectionStartElm = selection.getStart();\n      const root = getClosestEditingHost(editor, selectionStartElm);\n      const li = dom.getParent(selection.getStart(), 'LI', root);\n      if (li) {\n        const ul = li.parentElement;\n        if (ul === editor.getBody() && isEmpty$2(dom, ul)) {\n          return true;\n        }\n        const rng = normalizeRange(selection.getRng());\n        const otherLi = dom.getParent(findNextCaretContainer(editor, rng, isForward, root), 'LI', root);\n        const willMergeParentIntoChild = otherLi && (isForward ? dom.isChildOf(li, otherLi) : dom.isChildOf(otherLi, li));\n        if (otherLi && otherLi !== li && !willMergeParentIntoChild) {\n          editor.undoManager.transact(() => {\n            if (isForward) {\n              mergeForward(editor, rng, otherLi, li);\n            } else {\n              if (isFirstChild(li)) {\n                outdentListSelection(editor);\n              } else {\n                mergeBackward(editor, rng, li, otherLi);\n              }\n            }\n          });\n          return true;\n        } else if (willMergeParentIntoChild && !isForward && otherLi !== li) {\n          editor.undoManager.transact(() => {\n            if (rng.commonAncestorContainer.parentElement) {\n              const bookmark = createBookmark(rng);\n              const oldParentElRef = rng.commonAncestorContainer.parentElement;\n              moveChildren(dom, rng.commonAncestorContainer.parentElement, otherLi);\n              oldParentElRef.remove();\n              const resolvedBookmark = resolveBookmark(bookmark);\n              editor.selection.setRng(resolvedBookmark);\n            }\n          });\n          return true;\n        } else if (!otherLi) {\n          if (!isForward && rng.startOffset === 0 && rng.endOffset === 0) {\n            editor.undoManager.transact(() => {\n              flattenListSelection(editor);\n            });\n            return true;\n          }\n        }\n      }\n      return false;\n    };\n    const removeBlock = (dom, block, root) => {\n      const parentBlock = dom.getParent(block.parentNode, dom.isBlock, root);\n      dom.remove(block);\n      if (parentBlock && dom.isEmpty(parentBlock)) {\n        dom.remove(parentBlock);\n      }\n    };\n    const backspaceDeleteIntoListCaret = (editor, isForward) => {\n      const dom = editor.dom;\n      const selectionStartElm = editor.selection.getStart();\n      const root = getClosestEditingHost(editor, selectionStartElm);\n      const block = dom.getParent(selectionStartElm, dom.isBlock, root);\n      if (block && dom.isEmpty(block, undefined, { checkRootAsContent: true })) {\n        const rng = normalizeRange(editor.selection.getRng());\n        const otherLi = dom.getParent(findNextCaretContainer(editor, rng, isForward, root), 'LI', root);\n        if (otherLi) {\n          const findValidElement = element => contains$1([\n            'td',\n            'th',\n            'caption'\n          ], name(element));\n          const findRoot = node => node.dom === root;\n          const otherLiCell = closest$2(SugarElement.fromDom(otherLi), findValidElement, findRoot);\n          const caretCell = closest$2(SugarElement.fromDom(rng.startContainer), findValidElement, findRoot);\n          if (!equals(otherLiCell, caretCell, eq)) {\n            return false;\n          }\n          editor.undoManager.transact(() => {\n            const parentNode = otherLi.parentNode;\n            removeBlock(dom, block, root);\n            mergeWithAdjacentLists(dom, parentNode);\n            editor.selection.select(otherLi, true);\n            editor.selection.collapse(isForward);\n          });\n          return true;\n        }\n      }\n      return false;\n    };\n    const backspaceDeleteCaret = (editor, isForward) => {\n      return backspaceDeleteFromListToListCaret(editor, isForward) || backspaceDeleteIntoListCaret(editor, isForward);\n    };\n    const hasListSelection = editor => {\n      const selectionStartElm = editor.selection.getStart();\n      const root = getClosestEditingHost(editor, selectionStartElm);\n      const startListParent = editor.dom.getParent(selectionStartElm, 'LI,DT,DD', root);\n      return startListParent || getSelectedListItems(editor).length > 0;\n    };\n    const backspaceDeleteRange = editor => {\n      if (hasListSelection(editor)) {\n        editor.undoManager.transact(() => {\n          editor.execCommand('Delete');\n          normalizeLists(editor.dom, editor.getBody());\n        });\n        return true;\n      }\n      return false;\n    };\n    const backspaceDelete = (editor, isForward) => {\n      const selection = editor.selection;\n      return !isWithinNonEditableList(editor, selection.getNode()) && (selection.isCollapsed() ? backspaceDeleteCaret(editor, isForward) : backspaceDeleteRange(editor));\n    };\n    const setup$2 = editor => {\n      editor.on('ExecCommand', e => {\n        const cmd = e.command.toLowerCase();\n        if ((cmd === 'delete' || cmd === 'forwarddelete') && hasListSelection(editor)) {\n          normalizeLists(editor.dom, editor.getBody());\n        }\n      });\n      editor.on('keydown', e => {\n        if (e.keyCode === global$4.BACKSPACE) {\n          if (backspaceDelete(editor, false)) {\n            e.preventDefault();\n          }\n        } else if (e.keyCode === global$4.DELETE) {\n          if (backspaceDelete(editor, true)) {\n            e.preventDefault();\n          }\n        }\n      });\n    };\n\n    const get = editor => ({\n      backspaceDelete: isForward => {\n        backspaceDelete(editor, isForward);\n      }\n    });\n\n    const updateList = (editor, update) => {\n      const parentList = getParentList(editor);\n      if (parentList === null || isWithinNonEditableList(editor, parentList)) {\n        return;\n      }\n      editor.undoManager.transact(() => {\n        if (isObject(update.styles)) {\n          editor.dom.setStyles(parentList, update.styles);\n        }\n        if (isObject(update.attrs)) {\n          each(update.attrs, (v, k) => editor.dom.setAttrib(parentList, k, v));\n        }\n      });\n    };\n\n    const parseAlphabeticBase26 = str => {\n      const chars = reverse(trim(str).split(''));\n      const values = map(chars, (char, i) => {\n        const charValue = char.toUpperCase().charCodeAt(0) - 'A'.charCodeAt(0) + 1;\n        return Math.pow(26, i) * charValue;\n      });\n      return foldl(values, (sum, v) => sum + v, 0);\n    };\n    const composeAlphabeticBase26 = value => {\n      value--;\n      if (value < 0) {\n        return '';\n      } else {\n        const remainder = value % 26;\n        const quotient = Math.floor(value / 26);\n        const rest = composeAlphabeticBase26(quotient);\n        const char = String.fromCharCode('A'.charCodeAt(0) + remainder);\n        return rest + char;\n      }\n    };\n    const isUppercase = str => /^[A-Z]+$/.test(str);\n    const isLowercase = str => /^[a-z]+$/.test(str);\n    const isNumeric = str => /^[0-9]+$/.test(str);\n    const deduceListType = start => {\n      if (isNumeric(start)) {\n        return 2;\n      } else if (isUppercase(start)) {\n        return 0;\n      } else if (isLowercase(start)) {\n        return 1;\n      } else if (isEmpty$1(start)) {\n        return 3;\n      } else {\n        return 4;\n      }\n    };\n    const parseStartValue = start => {\n      switch (deduceListType(start)) {\n      case 2:\n        return Optional.some({\n          listStyleType: Optional.none(),\n          start\n        });\n      case 0:\n        return Optional.some({\n          listStyleType: Optional.some('upper-alpha'),\n          start: parseAlphabeticBase26(start).toString()\n        });\n      case 1:\n        return Optional.some({\n          listStyleType: Optional.some('lower-alpha'),\n          start: parseAlphabeticBase26(start).toString()\n        });\n      case 3:\n        return Optional.some({\n          listStyleType: Optional.none(),\n          start: ''\n        });\n      case 4:\n        return Optional.none();\n      }\n    };\n    const parseDetail = detail => {\n      const start = parseInt(detail.start, 10);\n      if (is$2(detail.listStyleType, 'upper-alpha')) {\n        return composeAlphabeticBase26(start);\n      } else if (is$2(detail.listStyleType, 'lower-alpha')) {\n        return composeAlphabeticBase26(start).toLowerCase();\n      } else {\n        return detail.start;\n      }\n    };\n\n    const open = editor => {\n      const currentList = getParentList(editor);\n      if (!isOlNode(currentList) || isWithinNonEditableList(editor, currentList)) {\n        return;\n      }\n      editor.windowManager.open({\n        title: 'List Properties',\n        body: {\n          type: 'panel',\n          items: [{\n              type: 'input',\n              name: 'start',\n              label: 'Start list at number',\n              inputMode: 'numeric'\n            }]\n        },\n        initialData: {\n          start: parseDetail({\n            start: editor.dom.getAttrib(currentList, 'start', '1'),\n            listStyleType: Optional.from(editor.dom.getStyle(currentList, 'list-style-type'))\n          })\n        },\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        onSubmit: api => {\n          const data = api.getData();\n          parseStartValue(data.start).each(detail => {\n            editor.execCommand('mceListUpdate', false, {\n              attrs: { start: detail.start === '1' ? '' : detail.start },\n              styles: { 'list-style-type': detail.listStyleType.getOr('') }\n            });\n          });\n          api.close();\n        }\n      });\n    };\n\n    const queryListCommandState = (editor, listName) => () => {\n      const parentList = getParentList(editor);\n      return isNonNullable(parentList) && parentList.nodeName === listName;\n    };\n    const registerDialog = editor => {\n      editor.addCommand('mceListProps', () => {\n        open(editor);\n      });\n    };\n    const register$2 = editor => {\n      editor.on('BeforeExecCommand', e => {\n        const cmd = e.command.toLowerCase();\n        if (cmd === 'indent') {\n          indentListSelection(editor);\n        } else if (cmd === 'outdent') {\n          outdentListSelection(editor);\n        }\n      });\n      editor.addCommand('InsertUnorderedList', (ui, detail) => {\n        toggleList(editor, 'UL', detail);\n      });\n      editor.addCommand('InsertOrderedList', (ui, detail) => {\n        toggleList(editor, 'OL', detail);\n      });\n      editor.addCommand('InsertDefinitionList', (ui, detail) => {\n        toggleList(editor, 'DL', detail);\n      });\n      editor.addCommand('RemoveList', () => {\n        flattenListSelection(editor);\n      });\n      registerDialog(editor);\n      editor.addCommand('mceListUpdate', (ui, detail) => {\n        if (isObject(detail)) {\n          updateList(editor, detail);\n        }\n      });\n      editor.addQueryStateHandler('InsertUnorderedList', queryListCommandState(editor, 'UL'));\n      editor.addQueryStateHandler('InsertOrderedList', queryListCommandState(editor, 'OL'));\n      editor.addQueryStateHandler('InsertDefinitionList', queryListCommandState(editor, 'DL'));\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.html.Node');\n\n    const isTextNode = node => node.type === 3;\n    const isEmpty = nodeBuffer => nodeBuffer.length === 0;\n    const wrapInvalidChildren = list => {\n      const insertListItem = (buffer, refNode) => {\n        const li = global.create('li');\n        each$1(buffer, node => li.append(node));\n        if (refNode) {\n          list.insert(li, refNode, true);\n        } else {\n          list.append(li);\n        }\n      };\n      const reducer = (buffer, node) => {\n        if (isTextNode(node)) {\n          return [\n            ...buffer,\n            node\n          ];\n        } else if (!isEmpty(buffer) && !isTextNode(node)) {\n          insertListItem(buffer, node);\n          return [];\n        } else {\n          return buffer;\n        }\n      };\n      const restBuffer = foldl(list.children(), reducer, []);\n      if (!isEmpty(restBuffer)) {\n        insertListItem(restBuffer);\n      }\n    };\n    const setup$1 = editor => {\n      editor.on('PreInit', () => {\n        const {parser} = editor;\n        parser.addNodeFilter('ul,ol', nodes => each$1(nodes, wrapInvalidChildren));\n      });\n    };\n\n    const setupTabKey = editor => {\n      editor.on('keydown', e => {\n        if (e.keyCode !== global$4.TAB || global$4.metaKeyPressed(e)) {\n          return;\n        }\n        editor.undoManager.transact(() => {\n          if (e.shiftKey ? outdentListSelection(editor) : indentListSelection(editor)) {\n            e.preventDefault();\n          }\n        });\n      });\n    };\n    const setup = editor => {\n      if (shouldIndentOnTab(editor)) {\n        setupTabKey(editor);\n      }\n      setup$2(editor);\n    };\n\n    const setupToggleButtonHandler = (editor, listName) => api => {\n      const toggleButtonHandler = e => {\n        api.setActive(inList(e.parents, listName));\n        api.setEnabled(!isWithinNonEditableList(editor, e.element) && editor.selection.isEditable());\n      };\n      api.setEnabled(editor.selection.isEditable());\n      return setNodeChangeHandler(editor, toggleButtonHandler);\n    };\n    const register$1 = editor => {\n      const exec = command => () => editor.execCommand(command);\n      if (!editor.hasPlugin('advlist')) {\n        editor.ui.registry.addToggleButton('numlist', {\n          icon: 'ordered-list',\n          active: false,\n          tooltip: 'Numbered list',\n          onAction: exec('InsertOrderedList'),\n          onSetup: setupToggleButtonHandler(editor, 'OL')\n        });\n        editor.ui.registry.addToggleButton('bullist', {\n          icon: 'unordered-list',\n          active: false,\n          tooltip: 'Bullet list',\n          onAction: exec('InsertUnorderedList'),\n          onSetup: setupToggleButtonHandler(editor, 'UL')\n        });\n      }\n    };\n\n    const setupMenuButtonHandler = (editor, listName) => api => {\n      const menuButtonHandler = e => api.setEnabled(inList(e.parents, listName) && !isWithinNonEditableList(editor, e.element));\n      return setNodeChangeHandler(editor, menuButtonHandler);\n    };\n    const register = editor => {\n      const listProperties = {\n        text: 'List properties...',\n        icon: 'ordered-list',\n        onAction: () => editor.execCommand('mceListProps'),\n        onSetup: setupMenuButtonHandler(editor, 'OL')\n      };\n      editor.ui.registry.addMenuItem('listprops', listProperties);\n      editor.ui.registry.addContextMenu('lists', {\n        update: node => {\n          const parentList = getParentList(editor, node);\n          return isOlNode(parentList) ? ['listprops'] : [];\n        }\n      });\n    };\n\n    var Plugin = () => {\n      global$7.add('lists', editor => {\n        register$3(editor);\n        setup$1(editor);\n        if (!editor.hasPlugin('rtc', true)) {\n          setup(editor);\n          register$2(editor);\n        } else {\n          registerDialog(editor);\n        }\n        register$1(editor);\n        register(editor);\n        return get(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,QAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,QAAI;AACJ,QAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,aAAO;AAAA,IACT,OAAO;AACL,eAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,IAC7F;AAAA,EACF;AACA,QAAM,SAAS,OAAK;AAClB,UAAM,IAAI,OAAO;AACjB,QAAI,MAAM,MAAM;AACd,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,WAAW,CAAAA,UAAQ,WAAS,OAAO,KAAK,MAAMA;AACpD,QAAM,eAAe,CAAAA,UAAQ,WAAS,OAAO,UAAUA;AACvD,QAAM,WAAW,SAAS,QAAQ;AAClC,QAAM,WAAW,SAAS,QAAQ;AAClC,QAAM,UAAU,SAAS,OAAO;AAChC,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,QAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,QAAM,aAAa,aAAa,UAAU;AAC1C,QAAM,WAAW,aAAa,QAAQ;AAEtC,QAAM,OAAO,MAAM;AAAA,EACnB;AACA,QAAM,WAAW,CAAC,KAAK,QAAQ,OAAK,IAAI,IAAI,CAAC,CAAC;AAC9C,QAAM,WAAW,WAAS;AACxB,WAAO,MAAM;AACX,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,eAAe,CAAC,GAAG,MAAM;AAC7B,WAAO,MAAM;AAAA,EACf;AACA,WAAS,MAAM,OAAO,aAAa;AACjC,WAAO,IAAI,aAAa;AACtB,YAAM,MAAM,YAAY,OAAO,QAAQ;AACvC,aAAO,GAAG,MAAM,MAAM,GAAG;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,MAAM,OAAK,OAAK,CAAC,EAAE,CAAC;AAC1B,QAAM,QAAQ,SAAS,KAAK;AAAA,EAE5B,MAAM,SAAS;AAAA,IACb,YAAY,KAAK,OAAO;AACtB,WAAK,MAAM;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,IACA,OAAO,KAAK,OAAO;AACjB,aAAO,IAAI,SAAS,MAAM,KAAK;AAAA,IACjC;AAAA,IACA,OAAO,OAAO;AACZ,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,KAAK,QAAQ,QAAQ;AACnB,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AACP,aAAO,KAAK;AAAA,IACd;AAAA,IACA,SAAS;AACP,aAAO,CAAC,KAAK;AAAA,IACf;AAAA,IACA,IAAI,QAAQ;AACV,UAAI,KAAK,KAAK;AACZ,eAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,MACzC,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IACzC;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IAC1C;AAAA,IACA,OAAO,WAAW;AAChB,UAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,MAAM,aAAa;AACjB,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,GAAG,aAAa;AACd,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B;AAAA,IACA,WAAW,OAAO;AAChB,aAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,IACvC;AAAA,IACA,QAAQ,OAAO;AACb,aAAO,KAAK,MAAM,OAAO,MAAM;AAAA,IACjC;AAAA,IACA,SAAS,SAAS;AAChB,UAAI,CAAC,KAAK,KAAK;AACb,cAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,MAC9F,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,IACA,OAAO,KAAK,OAAO;AACjB,aAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,IACrE;AAAA,IACA,YAAY;AACV,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,iBAAiB;AACf,aAAO,KAAK;AAAA,IACd;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,IACA,UAAU;AACR,aAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,IACpC;AAAA,IACA,WAAW;AACT,aAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,IAC9C;AAAA,EACF;AACA,WAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,QAAM,cAAc,MAAM,UAAU;AACpC,QAAM,gBAAgB,MAAM,UAAU;AACtC,QAAM,aAAa,MAAM,UAAU;AACnC,QAAM,aAAa,CAAC,IAAI,MAAM,cAAc,KAAK,IAAI,CAAC;AACtD,QAAM,aAAa,CAAC,IAAI,MAAM,WAAW,IAAI,CAAC,IAAI;AAClD,QAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,KAAK,GAAG,CAAC,GAAG;AACd,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,MAAM,CAAC,IAAI,MAAM;AACrB,UAAM,MAAM,GAAG;AACf,UAAM,IAAI,IAAI,MAAM,GAAG;AACvB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,IACf;AACA,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC,IAAI,MAAM;AACxB,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,WAAW,CAAC,IAAI,SAAS;AAC7B,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,KAAK,GAAG,CAAC,GAAG;AACd,UAAE,KAAK,CAAC;AAAA,MACV;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,UAAU,CAAC,IAAI,MAAM;AACzB,QAAI,GAAG,WAAW,GAAG;AACnB,aAAO,CAAC;AAAA,IACV,OAAO;AACL,UAAI,UAAU,EAAE,GAAG,CAAC,CAAC;AACrB,YAAM,IAAI,CAAC;AACX,UAAI,QAAQ,CAAC;AACb,eAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,cAAM,IAAI,GAAG,CAAC;AACd,cAAMA,QAAO,EAAE,CAAC;AAChB,YAAIA,UAAS,SAAS;AACpB,YAAE,KAAK,KAAK;AACZ,kBAAQ,CAAC;AAAA,QACX;AACA,kBAAUA;AACV,cAAM,KAAK,CAAC;AAAA,MACd;AACA,UAAI,MAAM,WAAW,GAAG;AACtB,UAAE,KAAK,KAAK;AAAA,MACd;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,WAAO,IAAI,CAAC,GAAG,MAAM;AACnB,YAAM,EAAE,KAAK,GAAG,CAAC;AAAA,IACnB,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,YAAY,CAAC,IAAI,MAAM,UAAU;AACrC,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,KAAK,GAAG,CAAC,GAAG;AACd,eAAO,SAAS,KAAK,CAAC;AAAA,MACxB,WAAW,MAAM,GAAG,CAAC,GAAG;AACtB;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,QAAM,OAAO,CAAC,IAAI,SAAS;AACzB,WAAO,UAAU,IAAI,MAAM,KAAK;AAAA,EAClC;AACA,QAAM,UAAU,QAAM;AACpB,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,UAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG;AACnB,cAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,MAC7E;AACA,iBAAW,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AACA,QAAM,OAAO,CAAC,IAAI,MAAM,QAAQ,IAAI,IAAI,CAAC,CAAC;AAC1C,QAAM,UAAU,QAAM;AACpB,UAAM,IAAI,YAAY,KAAK,IAAI,CAAC;AAChC,MAAE,QAAQ;AACV,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,CAAC,CAAC,IAAI,SAAS,KAAK;AACxF,QAAM,OAAO,QAAM,MAAM,IAAI,CAAC;AAC9B,QAAM,OAAO,QAAM,MAAM,IAAI,GAAG,SAAS,CAAC;AAC1C,QAAM,SAAS,CAAC,IAAI,eAAe;AACjC,UAAM,IAAI,CAAC;AACX,UAAM,eAAe,WAAW,UAAU,IAAI,OAAK,OAAO,GAAG,OAAK,WAAW,GAAG,CAAC,CAAC,IAAI,OAAK,WAAW,GAAG,CAAC;AAC1G,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,CAAC,aAAa,CAAC,GAAG;AACpB,UAAE,KAAK,CAAC;AAAA,MACV;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,CAAC,KAAK,KAAK,aAAa,iBAAiB,IAAI,OAAO,UAAQ,WAAW,MAAM,GAAG,CAAC;AAC9F,QAAM,SAAS,CAAC,KAAK,KAAK,aAAa,iBAAiB,MAAM,KAAK,KAAK,UAAU,EAAE,MAAM,IAAI,OAAO,KAAK,IAAI,OAAO,CAAC;AACtH,QAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI,SAAS,KAAK,EAAE,GAAG,SAAS,GAAG,GAAG,SAAS,CAAC,CAAC,IAAI,SAAS,KAAK;AAEzH,QAAM,UAAU;AAChB,QAAM,WAAW;AACjB,QAAM,oBAAoB;AAC1B,QAAM,UAAU;AAChB,QAAM,OAAO;AAEb,QAAM,WAAW,CAAC,MAAM,UAAU;AAChC,UAAM,MAAM,SAAS;AACrB,UAAM,MAAM,IAAI,cAAc,KAAK;AACnC,QAAI,YAAY;AAChB,QAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,YAAM,UAAU;AAChB,cAAQ,MAAM,SAAS,IAAI;AAC3B,YAAM,IAAI,MAAM,OAAO;AAAA,IACzB;AACA,WAAO,UAAU,IAAI,WAAW,CAAC,CAAC;AAAA,EACpC;AACA,QAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,UAAM,MAAM,SAAS;AACrB,UAAM,OAAO,IAAI,cAAc,GAAG;AAClC,WAAO,UAAU,IAAI;AAAA,EACvB;AACA,QAAM,WAAW,CAAC,MAAM,UAAU;AAChC,UAAM,MAAM,SAAS;AACrB,UAAM,OAAO,IAAI,eAAe,IAAI;AACpC,WAAO,UAAU,IAAI;AAAA,EACvB;AACA,QAAM,YAAY,UAAQ;AACxB,QAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,WAAO,EAAE,KAAK,KAAK;AAAA,EACrB;AACA,QAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,SAAS;AAClG,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,EACF;AAEA,QAAM,OAAO,CAAC,SAAS,aAAa;AAClC,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI,aAAa,SAAS;AAC5B,aAAO;AAAA,IACT,OAAO;AACL,YAAM,OAAO;AACb,UAAI,KAAK,YAAY,QAAW;AAC9B,eAAO,KAAK,QAAQ,QAAQ;AAAA,MAC9B,WAAW,KAAK,sBAAsB,QAAW;AAC/C,eAAO,KAAK,kBAAkB,QAAQ;AAAA,MACxC,WAAW,KAAK,0BAA0B,QAAW;AACnD,eAAO,KAAK,sBAAsB,QAAQ;AAAA,MAC5C,WAAW,KAAK,uBAAuB,QAAW;AAChD,eAAO,KAAK,mBAAmB,QAAQ;AAAA,MACzC,OAAO;AACL,cAAM,IAAI,MAAM,gCAAgC;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,KAAK,CAAC,IAAI,OAAO,GAAG,QAAQ,GAAG;AACrC,QAAM,WAAW,CAAC,IAAI,OAAO;AAC3B,UAAM,KAAK,GAAG;AACd,UAAM,KAAK,GAAG;AACd,WAAO,OAAO,KAAK,QAAQ,GAAG,SAAS,EAAE;AAAA,EAC3C;AACA,QAAM,KAAK;AAEX,QAAM,SAAS,OAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAEjF,QAAM,OAAO,CAAC,OAAO,UAAU;AAC7B,QAAI,IAAI,UAAU,UAAa,UAAU,OAAO,QAAQ;AACxD,aAAS,IAAI,GAAG,IAAI,MAAM,UAAU,MAAM,UAAa,MAAM,MAAM,EAAE,GAAG;AACtE,UAAI,EAAE,MAAM,CAAC,CAAC;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,QAAM,UAAU,CAAC,GAAG,UAAU;AAC5B,UAAM,QAAQ,EAAE,MAAM,GAAG;AACzB,WAAO,KAAK,OAAO,KAAK;AAAA,EAC1B;AAEA,QAAM,SAAS,CAACC,OAAM,UAAU;AAC9B,WAAO,QAAQA,OAAM,KAAK;AAAA,EAC5B;AACA,QAAM,WAAW,CAACA,OAAM,UAAU;AAChC,UAAM,SAAS,OAAOA,OAAM,KAAK;AACjC,QAAI,WAAW,UAAa,WAAW,MAAM;AAC3C,YAAM,IAAI,MAAMA,QAAO,gCAAgC;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AAEA,QAAM,iBAAiB,OAAO;AAC9B,QAAM,kBAAkB,WAAS;AAC/B,WAAO,SAAS,eAAe,KAAK;AAAA,EACtC;AACA,QAAM,gBAAgB,OAAK;AACzB,UAAM,QAAQ,QAAQ,6BAA6B,CAAC;AACpD,WAAO,SAAS,CAAC,MAAM,gBAAgB,KAAK,EAAE,UAAU,cAAc,CAAC,KAAK,mBAAmB,KAAK,eAAe,CAAC,EAAE,YAAY,IAAI;AAAA,EACxI;AAEA,QAAM,OAAO,aAAW;AACtB,UAAM,IAAI,QAAQ,IAAI;AACtB,WAAO,EAAE,YAAY;AAAA,EACvB;AACA,QAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,QAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,QAAM,YAAY,aAAW,KAAK,OAAO,MAAM,WAAW,KAAK,OAAO,MAAM;AAC5E,QAAM,gBAAgB,aAAW,YAAY,OAAO,KAAK,cAAc,QAAQ,GAAG;AAClF,QAAM,cAAc,OAAO,OAAO;AAClC,QAAM,SAAS,OAAO,IAAI;AAC1B,QAAM,aAAa,OAAO,QAAQ;AAClC,QAAM,qBAAqB,OAAO,iBAAiB;AACnD,QAAM,QAAQ,SAAO,OAAK,YAAY,CAAC,KAAK,KAAK,CAAC,MAAM;AAExD,QAAM,QAAQ,aAAW,aAAa,QAAQ,QAAQ,IAAI,aAAa;AACvE,QAAM,kBAAkB,SAAO,WAAW,GAAG,IAAI,MAAM,MAAM,GAAG;AAChE,QAAM,SAAS,aAAW,SAAS,KAAK,QAAQ,IAAI,UAAU,EAAE,IAAI,aAAa,OAAO;AACxF,QAAM,gBAAgB,aAAW,SAAS,KAAK,QAAQ,IAAI,aAAa,EAAE,IAAI,aAAa,OAAO;AAClG,QAAM,cAAc,aAAW,SAAS,KAAK,QAAQ,IAAI,WAAW,EAAE,IAAI,aAAa,OAAO;AAC9F,QAAM,WAAW,aAAW,IAAI,QAAQ,IAAI,YAAY,aAAa,OAAO;AAC5E,QAAM,QAAQ,CAAC,SAAS,UAAU;AAChC,UAAM,KAAK,QAAQ,IAAI;AACvB,WAAO,SAAS,KAAK,GAAG,KAAK,CAAC,EAAE,IAAI,aAAa,OAAO;AAAA,EAC1D;AACA,QAAM,aAAa,aAAW,MAAM,SAAS,CAAC;AAC9C,QAAM,YAAY,aAAW,MAAM,SAAS,QAAQ,IAAI,WAAW,SAAS,CAAC;AAE7E,QAAM,eAAe,SAAO,mBAAmB,GAAG,KAAK,cAAc,IAAI,IAAI,IAAI;AACjF,QAAM,YAAY,WAAW,QAAQ,UAAU,YAAY,KAAK,WAAW,KAAK,UAAU,WAAW;AACrG,QAAM,cAAc,YAAY,OAAK,aAAa,QAAQ,EAAE,IAAI,YAAY,CAAC,IAAI;AACjF,QAAM,gBAAgB,OAAK;AACzB,UAAM,IAAI,YAAY,CAAC;AACvB,WAAO,aAAa,CAAC,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAAA,EAC5D;AACA,QAAM,gBAAgB,OAAK,aAAa,QAAQ,EAAE,IAAI,IAAI;AAE1D,QAAM,SAAS,aAAW;AACxB,UAAM,MAAM,OAAO,OAAO,IAAI,QAAQ,IAAI,aAAa,QAAQ;AAC/D,QAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,aAAO;AAAA,IACT;AACA,UAAM,MAAM,IAAI;AAChB,WAAO,cAAc,aAAa,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG,SAAS,QAAQ,aAAa,CAAC;AAAA,EACpH;AAEA,MAAI,oBAAoB,CAACC,KAAIC,WAAU,OAAO,GAAG,WAAW;AAC1D,QAAID,IAAG,OAAO,CAAC,GAAG;AAChB,aAAO,SAAS,KAAK,KAAK;AAAA,IAC5B,WAAW,WAAW,MAAM,KAAK,OAAO,KAAK,GAAG;AAC9C,aAAO,SAAS,KAAK;AAAA,IACvB,OAAO;AACL,aAAOC,UAAS,OAAO,GAAG,MAAM;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,aAAa,CAAC,OAAO,WAAW,WAAW;AAC/C,QAAI,UAAU,MAAM;AACpB,UAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,WAAO,QAAQ,YAAY;AACzB,gBAAU,QAAQ;AAClB,YAAM,KAAK,aAAa,QAAQ,OAAO;AACvC,UAAI,UAAU,EAAE,GAAG;AACjB,eAAO,SAAS,KAAK,EAAE;AAAA,MACzB,WAAW,KAAK,EAAE,GAAG;AACnB;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,QAAM,YAAY,CAAC,OAAO,WAAW,WAAW;AAC9C,UAAMD,MAAK,CAAC,GAAG,SAAS,KAAK,CAAC;AAC9B,WAAO,kBAAkBA,KAAI,YAAY,OAAO,WAAW,MAAM;AAAA,EACnE;AAEA,QAAM,aAAa,CAAC,OAAO,UAAU,WAAW,WAAW,OAAO,OAAK,KAAK,GAAG,QAAQ,GAAG,MAAM;AAChG,QAAM,YAAY,CAAC,OAAO,UAAU,WAAW;AAC7C,UAAMA,MAAK,CAAC,SAASE,cAAa,KAAK,SAASA,SAAQ;AACxD,WAAO,kBAAkBF,KAAI,YAAY,OAAO,UAAU,MAAM;AAAA,EAClE;AAEA,QAAM,UAAU,YAAU,UAAU,QAAQ,mBAAmB;AAC/D,QAAM,aAAa,CAAC,SAAS,iBAAiB,UAAU;AACtD,QAAI,OAAO,OAAO,GAAG;AACnB,aAAO,QAAQ,IAAI;AAAA,IACrB,OAAO;AACL,aAAO,QAAQ,OAAO,EAAE,KAAK,SAAS,cAAc,GAAG,cAAY,OAAO,QAAQ,MAAM,MAAM;AAAA,IAChG;AAAA,EACF;AACA,QAAM,SAAS,aAAW,QAAQ,IAAI;AAEtC,QAAM,WAAW,CAAC,QAAQ,YAAY;AACpC,UAAM,WAAW,OAAO,MAAM;AAC9B,aAAS,KAAK,OAAK;AACjB,QAAE,IAAI,aAAa,QAAQ,KAAK,OAAO,GAAG;AAAA,IAC5C,CAAC;AAAA,EACH;AACA,QAAM,QAAQ,CAAC,QAAQ,YAAY;AACjC,UAAM,UAAU,YAAY,MAAM;AAClC,YAAQ,KAAK,MAAM;AACjB,YAAM,WAAW,OAAO,MAAM;AAC9B,eAAS,KAAK,OAAK;AACjB,iBAAS,GAAG,OAAO;AAAA,MACrB,CAAC;AAAA,IACH,GAAG,OAAK;AACN,eAAS,GAAG,OAAO;AAAA,IACrB,CAAC;AAAA,EACH;AACA,QAAM,UAAU,CAACG,SAAQ,YAAY;AACnC,UAAM,eAAe,WAAWA,OAAM;AACtC,iBAAa,KAAK,MAAM;AACtB,eAASA,SAAQ,OAAO;AAAA,IAC1B,GAAG,OAAK;AACN,MAAAA,QAAO,IAAI,aAAa,QAAQ,KAAK,EAAE,GAAG;AAAA,IAC5C,CAAC;AAAA,EACH;AACA,QAAM,WAAW,CAACA,SAAQ,YAAY;AACpC,IAAAA,QAAO,IAAI,YAAY,QAAQ,GAAG;AAAA,EACpC;AAEA,QAAM,SAAS,CAAC,QAAQ,aAAa;AACnC,WAAO,UAAU,OAAK;AACpB,eAAS,QAAQ,CAAC;AAAA,IACpB,CAAC;AAAA,EACH;AACA,QAAM,SAAS,CAACA,SAAQ,aAAa;AACnC,WAAO,UAAU,OAAK;AACpB,eAASA,SAAQ,CAAC;AAAA,IACpB,CAAC;AAAA,EACH;AAEA,QAAM,QAAQ,aAAW;AACvB,YAAQ,IAAI,cAAc;AAC1B,WAAO,SAAS,OAAO,GAAG,WAAS;AACjC,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AACA,QAAM,SAAS,aAAW;AACxB,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI,eAAe,MAAM;AAC3B,UAAI,WAAW,YAAY,GAAG;AAAA,IAChC;AAAA,EACF;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,wBAAwB;AAElE,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,wBAAwB;AAElE,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,iBAAiB;AAE3D,QAAM,UAAU,WAAS,IAAI,OAAO,aAAa,OAAO;AAExD,QAAM,OAAO,OAAO;AACpB,QAAM,OAAO,CAAC,KAAK,MAAM;AACvB,UAAM,QAAQ,KAAK,GAAG;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,IAAI,IAAI,CAAC;AACf,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,SAAS,OAAK,CAAC,GAAG,MAAM;AAC5B,MAAE,CAAC,IAAI;AAAA,EACT;AACA,QAAM,iBAAiB,CAAC,KAAK,MAAM,QAAQ,YAAY;AACrD,SAAK,KAAK,CAAC,GAAG,MAAM;AAClB,OAAC,KAAK,GAAG,CAAC,IAAI,SAAS,SAAS,GAAG,CAAC;AAAA,IACtC,CAAC;AAAA,EACH;AACA,QAAM,SAAS,CAAC,KAAK,SAAS;AAC5B,UAAM,IAAI,CAAC;AACX,mBAAe,KAAK,MAAM,OAAO,CAAC,GAAG,IAAI;AACzC,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,CAAC,KAAK,KAAK,UAAU;AAClC,QAAI,SAAS,KAAK,KAAK,UAAU,KAAK,KAAK,SAAS,KAAK,GAAG;AAC1D,UAAI,aAAa,KAAK,QAAQ,EAAE;AAAA,IAClC,OAAO;AACL,cAAQ,MAAM,uCAAuC,KAAK,aAAa,OAAO,eAAe,GAAG;AAChG,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AAAA,EACF;AACA,QAAM,SAAS,CAAC,SAAS,UAAU;AACjC,UAAM,MAAM,QAAQ;AACpB,SAAK,OAAO,CAAC,GAAG,MAAM;AACpB,aAAO,KAAK,GAAG,CAAC;AAAA,IAClB,CAAC;AAAA,EACH;AACA,QAAM,UAAU,aAAW,MAAM,QAAQ,IAAI,YAAY,CAAC,KAAK,SAAS;AACtE,QAAI,KAAK,IAAI,IAAI,KAAK;AACtB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,QAAM,QAAQ,CAAC,UAAU,WAAW,aAAa,QAAQ,SAAS,IAAI,UAAU,MAAM,CAAC;AACvF,QAAM,OAAO,cAAY,MAAM,UAAU,IAAI;AAC7C,QAAM,YAAY,CAAC,UAAU,QAAQ;AACnC,UAAM,KAAK,aAAa,QAAQ,GAAG;AACnC,UAAM,aAAa,QAAQ,QAAQ;AACnC,WAAO,IAAI,UAAU;AACrB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC,UAAU,QAAQ;AAChC,UAAM,KAAK,UAAU,UAAU,GAAG;AAClC,UAAM,UAAU,EAAE;AAClB,UAAM,aAAa,SAAS,QAAQ;AACpC,WAAO,IAAI,UAAU;AACrB,WAAO,QAAQ;AACf,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,sBAAsB;AAEhE,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE9D,QAAM,gBAAgB,CAAAJ,UAAQ,UAAQ,cAAc,IAAI,KAAK,KAAK,SAAS,YAAY,MAAMA;AAC7F,QAAM,iBAAiB,WAAS,UAAQ,cAAc,IAAI,KAAK,MAAM,KAAK,KAAK,QAAQ;AACvF,QAAM,eAAe,UAAQ,cAAc,IAAI,KAAK,KAAK,aAAa;AACtE,QAAM,YAAY,UAAQ,cAAc,IAAI,KAAK,KAAK,aAAa;AACnE,QAAM,aAAa,eAAe,cAAc;AAChD,QAAM,aAAa,eAAe,WAAW;AAC7C,QAAM,WAAW,cAAc,IAAI;AACnC,QAAM,iBAAiB,eAAe,cAAc;AACpD,QAAM,eAAe,eAAe,WAAW;AAC/C,QAAM,kBAAkB,eAAe,WAAW;AAClD,QAAM,OAAO,cAAc,IAAI;AAC/B,QAAM,eAAe,UAAQ;AAC3B,QAAI;AACJ,aAAS,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB;AAAA,EACzF;AACA,QAAM,cAAc,CAAC,QAAQ,SAAS,cAAc,IAAI,KAAK,KAAK,YAAY,OAAO,OAAO,qBAAqB;AACjH,QAAM,UAAU,CAAC,MAAM,kBAAkB,cAAc,IAAI,KAAK,KAAK,YAAY;AACjF,QAAM,SAAS,CAAC,QAAQ,SAAS,cAAc,IAAI,KAAK,KAAK,YAAY,OAAO,OAAO,gBAAgB;AACvG,QAAM,YAAY,CAAC,KAAK,SAAS;AAC/B,QAAI,CAAC,KAAK,IAAI,GAAG;AACf,aAAO;AAAA,IACT;AACA,WAAO,IAAI,QAAQ,KAAK,WAAW,KAAK,CAAC,KAAK,KAAK,eAAe;AAAA,EACpE;AACA,QAAM,YAAY,CAAC,KAAK,KAAK,kBAAkB;AAC7C,UAAMK,SAAQ,IAAI,QAAQ,GAAG;AAC7B,QAAI,iBAAiB,IAAI,OAAO,gCAAgC,GAAG,EAAE,SAAS,GAAG;AAC/E,aAAO;AAAA,IACT;AACA,WAAOA;AAAA,EACT;AACA,QAAM,gBAAgB,CAAC,KAAK,QAAQ,IAAI,UAAU,KAAK,IAAI,QAAQ,CAAC;AAEpE,QAAM,SAAS,CAAAL,UAAQ,YAAU,OAAO,QAAQ,IAAIA,KAAI;AACxD,QAAM,aAAa,YAAU;AAC3B,UAAM,iBAAiB,OAAO,QAAQ;AACtC,mBAAe,uBAAuB;AAAA,MACpC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,QAAM,oBAAoB,OAAO,qBAAqB;AACtD,QAAM,qBAAqB,OAAO,mBAAmB;AACrD,QAAM,0BAA0B,OAAO,yBAAyB;AAEhE,QAAM,kBAAkB,CAAC,QAAQ,aAAa,QAAQ,CAAC,MAAM;AAC3D,UAAM,MAAM,OAAO;AACnB,UAAM,gBAAgB,OAAO,OAAO,iBAAiB;AACrD,UAAM,WAAW,IAAI,eAAe;AACpC,UAAM,YAAY,mBAAmB,MAAM;AAC3C,UAAM,aAAa,wBAAwB,MAAM;AACjD,QAAI;AACJ,QAAI;AACJ,QAAI,iBAAiB;AACrB,gBAAY,IAAI,OAAO,WAAW;AAAA,MAChC,GAAG;AAAA,MACH,GAAG,MAAM,QAAQ,EAAE,OAAO,MAAM,MAAM,IAAI,CAAC;AAAA,IAC7C,CAAC;AACD,QAAI,CAAC,QAAQ,YAAY,YAAY,aAAa,GAAG;AACnD,eAAS,YAAY,SAAS;AAAA,IAChC;AACA,WAAO,OAAO,YAAY,YAAY;AACpC,YAAM,WAAW,KAAK;AACtB,UAAI,CAAC,mBAAmB,aAAa,UAAU,KAAK,aAAa,eAAe,MAAM,aAAa;AACjG,yBAAiB;AAAA,MACnB;AACA,UAAI,QAAQ,MAAM,aAAa,GAAG;AAChC,iBAAS,YAAY,IAAI;AACzB,oBAAY;AAAA,MACd,OAAO;AACL,YAAI,CAAC,WAAW;AACd,sBAAY,IAAI,OAAO,WAAW,UAAU;AAC5C,mBAAS,YAAY,SAAS;AAAA,QAChC;AACA,kBAAU,YAAY,IAAI;AAAA,MAC5B;AAAA,IACF;AACA,QAAI,CAAC,kBAAkB,WAAW;AAChC,gBAAU,YAAY,IAAI,OAAO,MAAM,EAAE,kBAAkB,IAAI,CAAC,CAAC;AAAA,IACnE;AACA,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,SAAS;AACvB,QAAM,YAAY,CAAC,QAAQ,MAAM,OAAO;AACtC,UAAM,yBAAyB,gBAAc;AAC3C,YAAMI,UAAS,WAAW;AAC1B,UAAIA,SAAQ;AACV,iBAAS,KAAK,WAAW,UAAQ;AAC/B,UAAAA,QAAO,aAAa,MAAM,GAAG,UAAU;AAAA,QACzC,CAAC;AAAA,MACH;AACA,YAAM,OAAO,UAAU;AAAA,IACzB;AACA,UAAM,YAAY,MAAM,OAAO,kCAAkC,IAAI;AACrE,UAAM,WAAW,gBAAgB,QAAQ,EAAE;AAC3C,UAAM,SAAS,MAAM,UAAU;AAC/B,WAAO,cAAc,EAAE;AACvB,WAAO,YAAY,IAAI;AACvB,UAAM,WAAW,OAAO,gBAAgB;AACxC,aAAS,OAAO,SAAS,YAAY,MAAM,OAAO,KAAK,YAAY;AACjE,UAAI,KAAK,aAAa,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAG;AACtD,cAAM,OAAO,IAAI;AACjB;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,OAAO,IAAI,QAAQ,QAAQ,GAAG;AACjC,YAAM,YAAY,UAAU,IAAI;AAAA,IAClC;AACA,UAAM,YAAY,UAAU,IAAI;AAChC,UAAMA,UAAS,GAAG;AAClB,QAAIA,WAAU,UAAU,OAAO,KAAKA,OAAM,GAAG;AAC3C,6BAAuBA,OAAM;AAAA,IAC/B;AACA,UAAM,OAAO,EAAE;AACf,QAAI,UAAU,OAAO,KAAK,IAAI,GAAG;AAC/B,YAAM,OAAO,IAAI;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,sBAAsB,MAAM,IAAI;AACtC,QAAM,oBAAoB,MAAM,IAAI;AACpC,QAAM,gBAAgB,CAAC,QAAQ,SAAS;AACtC,QAAI,oBAAoB,IAAI,GAAG;AAC7B,aAAO,MAAM,IAAI;AAAA,IACnB,WAAW,kBAAkB,IAAI,GAAG;AAClC,oBAAc,IAAI,EAAE,KAAK,QAAM,UAAU,QAAQ,GAAG,KAAK,KAAK,GAAG,CAAC;AAAA,IACpE;AAAA,EACF;AACA,QAAM,eAAe,UAAQ;AAC3B,QAAI,kBAAkB,IAAI,GAAG;AAC3B,aAAO,MAAM,IAAI;AAAA,IACnB;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,QAAQ,aAAa,YAAY;AACtD,QAAI,gBAAgB,UAAU;AAC5B,aAAO,SAAS,YAAY;AAAA,IAC9B,OAAO;AACL,aAAO,SAAS,UAAQ,cAAc,QAAQ,IAAI,CAAC;AAAA,IACrD;AAAA,EACF;AAEA,QAAM,qBAAqB,CAAC,WAAW,WAAW;AAChD,QAAI,aAAa,SAAS,GAAG;AAC3B,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,UAAM,OAAO,SAAS,QAAQ,WAAW,MAAM;AAC/C,QAAI,aAAa,IAAI,GAAG;AACtB,aAAO;AAAA,QACL,WAAW;AAAA,QACX,QAAQ,UAAU,UAAU,WAAW,SAAS,KAAK,KAAK,SAAS;AAAA,MACrE;AAAA,IACF,WAAW,KAAK,mBAAmB,aAAa,KAAK,eAAe,GAAG;AACrE,aAAO;AAAA,QACL,WAAW,KAAK;AAAA,QAChB,QAAQ,KAAK,gBAAgB,KAAK;AAAA,MACpC;AAAA,IACF,WAAW,KAAK,eAAe,aAAa,KAAK,WAAW,GAAG;AAC7D,aAAO;AAAA,QACL,WAAW,KAAK;AAAA,QAChB,QAAQ;AAAA,MACV;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,SAAO;AAC5B,UAAM,SAAS,IAAI,WAAW;AAC9B,UAAM,aAAa,mBAAmB,IAAI,gBAAgB,IAAI,WAAW;AACzE,WAAO,SAAS,WAAW,WAAW,WAAW,MAAM;AACvD,UAAM,WAAW,mBAAmB,IAAI,cAAc,IAAI,SAAS;AACnE,WAAO,OAAO,SAAS,WAAW,SAAS,MAAM;AACjD,WAAO;AAAA,EACT;AAEA,QAAM,YAAY;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,UAAU,KAAK,GAAG;AACvC,QAAM,gBAAgB,CAAC,QAAQ,SAAS;AACtC,UAAM,iBAAiB,QAAQ,OAAO,UAAU,SAAS,IAAI;AAC7D,WAAO,OAAO,IAAI,UAAU,gBAAgB,cAAc,mBAAmB,QAAQ,cAAc,CAAC;AAAA,EACtG;AACA,QAAM,uBAAuB,CAAC,YAAY,mBAAmB,cAAc,UAAU,KAAK,eAAe,WAAW,KAAK,eAAe,CAAC,MAAM;AAC/I,QAAM,eAAe,gBAAc,SAAS,WAAW,iBAAiB,YAAY,GAAG,UAAU;AACjG,QAAM,sBAAsB,YAAU;AACpC,UAAM,aAAa,cAAc,MAAM;AACvC,UAAM,iBAAiB,OAAO,UAAU,kBAAkB;AAC1D,QAAI,qBAAqB,YAAY,cAAc,GAAG;AACpD,aAAO,aAAa,UAAU;AAAA,IAChC,OAAO;AACL,aAAO,SAAS,gBAAgB,SAAO;AACrC,eAAO,WAAW,GAAG,KAAK,eAAe;AAAA,MAC3C,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,2BAA2B,CAAC,QAAQ,SAAS;AACjD,UAAM,gBAAgB,SAAS,IAAI,MAAM,SAAO;AAC9C,YAAM,WAAW,OAAO,IAAI,UAAU,KAAK,YAAY,mBAAmB,QAAQ,GAAG,CAAC;AACtF,aAAO,WAAW,WAAW;AAAA,IAC/B,CAAC;AACD,WAAO,OAAO,aAAa;AAAA,EAC7B;AACA,QAAM,uBAAuB,YAAU;AACrC,UAAM,iBAAiB,OAAO,UAAU,kBAAkB;AAC1D,WAAO,SAAS,yBAAyB,QAAQ,cAAc,GAAG,cAAc;AAAA,EAClF;AACA,QAAM,qBAAqB,YAAU,SAAS,qBAAqB,MAAM,GAAG,YAAY;AACxF,QAAM,wBAAwB,CAAC,QAAQ,QAAQ;AAC7C,UAAM,kBAAkB,OAAO,IAAI,WAAW,KAAK,OAAO;AAC1D,WAAO,gBAAgB,SAAS,IAAI,gBAAgB,CAAC,IAAI,OAAO,QAAQ;AAAA,EAC1E;AACA,QAAM,aAAa,CAAC,QAAQ,SAAS,CAAC,WAAW,IAAI,KAAK,CAAC,eAAe,IAAI,KAAK,OAAO,WAAW,cAAY,OAAO,aAAa,KAAK,UAAU,QAAQ,CAAC;AAC7J,QAAM,qBAAqB,CAAC,QAAQ,QAAQ;AAC1C,UAAM,eAAe,OAAO,IAAI,WAAW,KAAK,OAAO,IAAI,OAAO;AAClE,UAAM,uBAAuB,CAAAE,SAAOA,KAAI,SAAS,YAAY,MAAM,mBAAmB,MAAM;AAC5F,UAAM,cAAc,KAAK,cAAc,CAAAA,SAAO,qBAAqBA,IAAG,KAAK,WAAW,OAAO,QAAQA,IAAG,CAAC;AACzG,WAAO,YAAY,MAAM,OAAO,QAAQ,CAAC;AAAA,EAC3C;AACA,QAAM,iDAAiD,UAAQ,OAAO,IAAI,EAAE,OAAO,CAAAF,YAAU,eAAeA,QAAO,GAAG,KAAK,WAAWA,OAAM,EAAE,OAAO,CAAAG,gBAAc,CAAC,WAAWA,YAAW,GAAG,CAAC,KAAK,UAAUH,OAAM,EAAE,OAAO,CAAAI,eAAa,CAAC,WAAWA,WAAU,GAAG,CAAC,CAAC;AACpQ,QAAM,yBAAyB,CAAC,QAAQ,QAAQ;AAC9C,UAAM,cAAc,OAAO,IAAI,WAAW,KAAK,SAAS,mBAAmB,QAAQ,GAAG,CAAC;AACvF,WAAO,KAAK,WAAW;AAAA,EACzB;AACA,QAAM,mBAAmB,YAAU;AACjC,UAAM,YAAY,uBAAuB,QAAQ,OAAO,UAAU,SAAS,CAAC;AAC5E,UAAM,kBAAkB,SAAS,OAAO,UAAU,kBAAkB,GAAG,UAAU;AACjF,WAAO,UAAU,QAAQ,EAAE,OAAO,eAAe;AAAA,EACnD;AACA,QAAM,iBAAiB,YAAU;AAC/B,UAAM,MAAM,OAAO,UAAU,SAAS;AACtC,WAAO,OAAO,IAAI,WAAW,KAAK,SAAS,mBAAmB,QAAQ,GAAG,CAAC;AAAA,EAC5E;AACA,QAAM,uBAAuB,YAAU;AACrC,UAAM,gBAAgB,iBAAiB,MAAM;AAC7C,UAAM,cAAc,eAAe,MAAM;AACzC,WAAO,KAAK,aAAa,OAAK,+CAA+C,aAAa,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,mBAAmB,QAAQ,aAAa,GAAG,OAAK,CAAC,CAAC,CAAC;AAAA,EACvK;AACA,QAAM,qBAAqB,CAAC,QAAQ,UAAU;AAC5C,UAAM,YAAY,IAAI,OAAO,UAAQ,uBAAuB,QAAQ,IAAI,EAAE,MAAM,IAAI,CAAC;AACrF,WAAO,OAAO,SAAS;AAAA,EACzB;AAEA,QAAM,eAAe,UAAQ,UAAU,KAAK,KAAK,SAAS;AAC1D,QAAM,SAAS,CAAC,SAAS,aAAa,UAAU,SAAS,YAAY,eAAe,EAAE,OAAO,UAAQ,KAAK,aAAa,YAAY,CAAC,aAAa,IAAI,CAAC;AACtJ,QAAM,sBAAsB,CAAC,QAAQ,YAAY,YAAY,QAAQ,CAAC,OAAO,IAAI,WAAW,OAAO;AACnG,QAAM,mCAAmC,YAAU;AACjD,UAAM,aAAa,cAAc,MAAM;AACvC,WAAO,oBAAoB,QAAQ,UAAU;AAAA,EAC/C;AACA,QAAM,0BAA0B,CAAC,QAAQ,YAAY;AACnD,UAAM,aAAa,OAAO,IAAI,UAAU,SAAS,UAAU;AAC3D,WAAO,oBAAoB,QAAQ,UAAU;AAAA,EAC/C;AACA,QAAM,uBAAuB,CAAC,QAAQ,sBAAsB;AAC1D,UAAM,cAAc,OAAO,UAAU,QAAQ;AAC7C,sBAAkB;AAAA,MAChB,SAAS,OAAO,IAAI,WAAW,WAAW;AAAA,MAC1C,SAAS;AAAA,IACX,CAAC;AACD,WAAO,GAAG,cAAc,iBAAiB;AACzC,WAAO,MAAM,OAAO,IAAI,cAAc,iBAAiB;AAAA,EACzD;AAEA,QAAM,eAAe,CAAC,UAAU,UAAU;AACxC,UAAM,MAAM,SAAS;AACrB,UAAM,WAAW,IAAI,uBAAuB;AAC5C,WAAO,UAAU,aAAW;AAC1B,eAAS,YAAY,QAAQ,GAAG;AAAA,IAClC,CAAC;AACD,WAAO,aAAa,QAAQ,QAAQ;AAAA,EACtC;AAEA,QAAM,gBAAgB,CAAC,QAAQ,QAAQ,YAAY,OAAO,SAAS,gBAAgB;AAAA,IACjF;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,QAAQ,OAAK,OAAK,EAAE,QAAQ,GAAG,EAAE;AACvC,QAAM,OAAO,MAAM,YAAY;AAC/B,QAAM,aAAa,OAAK,EAAE,SAAS;AACnC,QAAM,YAAY,OAAK,CAAC,WAAW,CAAC;AAEpC,QAAM,cAAc,SAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM,gBAAgB;AAE3F,QAAM,cAAc,CAAC,KAAK,UAAU,UAAU;AAC5C,QAAI,CAAC,SAAS,KAAK,GAAG;AACpB,cAAQ,MAAM,sCAAsC,UAAU,aAAa,OAAO,eAAe,GAAG;AACpG,YAAM,IAAI,MAAM,iCAAiC,KAAK;AAAA,IACxD;AACA,QAAI,YAAY,GAAG,GAAG;AACpB,UAAI,MAAM,YAAY,UAAU,KAAK;AAAA,IACvC;AAAA,EACF;AACA,QAAM,MAAM,CAAC,SAAS,UAAU,UAAU;AACxC,UAAM,MAAM,QAAQ;AACpB,gBAAY,KAAK,UAAU,KAAK;AAAA,EAClC;AAEA,QAAM,SAAS,QAAM,GAAG,IAAI,OAAO;AACnC,QAAM,aAAa,QAAM,GAAG,IAAI,IAAI;AACpC,QAAM,oBAAoB,QAAM,WAAW,EAAE,EAAE,OAAO,MAAM;AAC5D,QAAM,mBAAmB,QAAM,UAAU,EAAE,EAAE,OAAO,MAAM;AAE1D,QAAM,cAAc,WAAS,oBAAoB;AACjD,QAAM,iBAAiB,WAAS,eAAe;AAC/C,QAAM,kBAAkB,WAAS,gBAAgB;AACjD,QAAM,aAAa,WAAS,MAAM,QAAQ;AAC1C,QAAM,aAAa,WAAS,MAAM;AAClC,QAAM,mBAAmB,QAAM;AAC7B,UAAM,aAAa,SAAS,EAAE;AAC9B,UAAM,UAAU,iBAAiB,EAAE,IAAI,WAAW,MAAM,GAAG,EAAE,IAAI;AACjE,WAAO,IAAI,SAAS,IAAI;AAAA,EAC1B;AACA,QAAM,cAAc,CAAC,IAAI,OAAOC,gBAAe,OAAO,EAAE,EAAE,OAAO,WAAW,EAAE,IAAI,WAAS;AAAA,IACzF;AAAA,IACA,OAAO;AAAA,IACP,YAAAA;AAAA,IACA,SAAS,iBAAiB,EAAE;AAAA,IAC5B,gBAAgB,QAAQ,EAAE;AAAA,IAC1B,gBAAgB,QAAQ,IAAI;AAAA,IAC5B,UAAU,KAAK,IAAI;AAAA,IACnB,gBAAgB;AAAA,EAClB,EAAE;AAEF,QAAM,cAAc,CAACL,SAAQM,WAAU;AACrC,aAASN,QAAO,MAAMM,OAAM,IAAI;AAAA,EAClC;AACA,QAAM,eAAe,cAAY;AAC/B,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,kBAAY,SAAS,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,iBAAiB,CAAC,QAAQ,SAAS;AACvC,UAAM,KAAK,MAAM,GAAG,KAAK,IAAI,GAAG,WAAW;AAAA,EAC7C;AACA,QAAM,gBAAgB,CAAC,OAAO,aAAa;AACzC,UAAM,UAAU;AAAA,MACd,MAAM,aAAa,QAAQ,UAAU,KAAK;AAAA,MAC1C,MAAM,aAAa,QAAQ,MAAM,KAAK;AAAA,IACxC;AACA,aAAS,QAAQ,MAAM,QAAQ,IAAI;AACnC,WAAO;AAAA,EACT;AACA,QAAM,iBAAiB,CAAC,OAAO,OAAO,SAAS;AAC7C,UAAM,WAAW,CAAC;AAClB,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,eAAS,KAAK,cAAc,OAAO,YAAY,KAAK,IAAI,MAAM,WAAW,MAAM,cAAc,CAAC;AAAA,IAChG;AACA,WAAO;AAAA,EACT;AACA,QAAM,mBAAmB,CAAC,UAAU,UAAU;AAC5C,aAAS,IAAI,GAAG,IAAI,SAAS,SAAS,GAAG,KAAK;AAC5C,UAAI,SAAS,CAAC,EAAE,MAAM,mBAAmB,MAAM;AAAA,IACjD;AACA,SAAK,QAAQ,EAAE,KAAK,aAAW;AAC7B,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,QAAQ,MAAM,MAAM,cAAc;AACzC,eAAO,QAAQ,MAAM,MAAM,cAAc;AAAA,MAC3C;AACA,aAAO,QAAQ,MAAM,MAAM,OAAO;AAAA,IACpC,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,CAAC,SAAS,UAAU;AAC3C,QAAI,KAAK,QAAQ,IAAI,MAAM,MAAM,UAAU;AACzC,cAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM,QAAQ;AAAA,IACpD;AACA,WAAO,QAAQ,MAAM,MAAM,cAAc;AAAA,EAC3C;AACA,QAAM,aAAa,CAAC,OAAO,MAAM,YAAY;AAC3C,UAAM,OAAO,aAAa,QAAQ,MAAM,KAAK;AAC7C,WAAO,MAAM,IAAI;AACjB,WAAO,MAAM,OAAO;AACpB,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC,SAAS,SAAS;AACpC,aAAS,QAAQ,MAAM,IAAI;AAC3B,YAAQ,OAAO;AAAA,EACjB;AACA,QAAM,eAAe,CAAC,OAAO,MAAM,UAAU;AAC3C,UAAM,UAAU,KAAK,MAAM,GAAG,MAAM,KAAK;AACzC,SAAK,OAAO,EAAE,KAAK,aAAW;AAC5B,UAAI,YAAY,KAAK,GAAG;AACtB,cAAM,OAAO,WAAW,OAAO,MAAM,gBAAgB,MAAM,OAAO;AAClE,mBAAW,SAAS,IAAI;AACxB,yBAAiB,SAAS,KAAK;AAAA,MACjC,WAAW,gBAAgB,KAAK,GAAG;AACjC,eAAO,QAAQ,MAAM,MAAM,OAAO;AAAA,MACpC,OAAO;AACL,cAAM,OAAO,aAAa,SAAS,OAAQ,MAAM,OAAQ,KAAK;AAC9D,iBAAS,QAAQ,MAAM,IAAI;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,YAAY,CAAC,OAAO,MAAM,UAAU;AACxC,UAAM,WAAW,eAAe,OAAO,OAAO,MAAM,QAAQ,KAAK,MAAM;AACvE,iBAAa,QAAQ;AACrB,qBAAiB,UAAU,KAAK;AAChC,mBAAe,MAAM,QAAQ;AAC7B,WAAO,KAAK,OAAO,QAAQ;AAAA,EAC7B;AACA,QAAM,cAAc,CAAC,OAAO,YAAY;AACtC,QAAI,uBAAuB,SAAS,KAAK;AACzC,UAAM,OAAO,MAAM,SAAS,CAACC,OAAM,OAAO,MAAM;AAC9C,UAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,eAAO,MAAM,QAAQA,MAAK,SAAS,UAAU,OAAOA,OAAM,KAAK,IAAI,aAAa,OAAOA,OAAM,KAAK;AAAA,MACpG,OAAO;AACL,YAAI,MAAM,GAAG;AACX,iCAAuB,SAAS,KAAK,KAAK;AAC1C,iBAAOA;AAAA,QACT;AACA,eAAO,aAAa,OAAOA,OAAM,KAAK;AAAA,MACxC;AAAA,IACF,GAAG,CAAC,CAAC;AACL,yBAAqB,KAAK,uBAAqB;AAC7C,YAAM,OAAO,aAAa,SAAS,OAAQ,kBAAkB,OAAQ,KAAK;AAC1E,WAAK,IAAI,EAAE,KAAK,cAAY;AAC1B,gBAAQ,SAAS,MAAM,IAAI;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AACD,WAAO,KAAK,IAAI,EAAE,IAAI,aAAW,QAAQ,IAAI;AAAA,EAC/C;AAEA,QAAM,cAAc,CAAC,aAAa,UAAU;AAC1C,YAAQ,aAAa;AAAA,MACrB,KAAK;AACH,cAAM;AACN;AAAA,MACF,KAAK;AACH,cAAM;AACN;AAAA,MACF,KAAK;AACH,cAAM,QAAQ;AAAA,IAChB;AACA,UAAM,QAAQ;AAAA,EAChB;AAEA,QAAM,sBAAsB,CAAC,QAAQ,WAAW;AAC9C,QAAI,YAAY,MAAM,KAAK,YAAY,MAAM,GAAG;AAC9C,aAAO,WAAW,OAAO;AACzB,aAAO,iBAAiB,EAAE,GAAG,OAAO,eAAe;AAAA,IACrD;AAAA,EACF;AACA,QAAM,sBAAsB,WAAS;AACnC,UAAM,iBAAiB,OAAO,MAAM,gBAAgB,CAAC,QAAQ,QAAQ,QAAQ,OAAO;AAAA,EACtF;AACA,QAAM,sBAAsB,CAAC,SAAS,UAAU;AAC9C,UAAM,QAAQ,QAAQ,KAAK,EAAE;AAC7B,UAAM,UAAU,WAAS,MAAM,UAAU,SAAS,CAAC,MAAM;AACzD,UAAM,QAAQ,WAAS,MAAM,QAAQ;AACrC,WAAO,UAAU,QAAQ,QAAQ,MAAM,GAAG,KAAK,CAAC,GAAG,SAAS,KAAK,EAAE,QAAQ,MAAM,UAAU,QAAQ,MAAM,QAAQ,CAAC,GAAG,SAAS,KAAK,CAAC;AAAA,EACtI;AACA,QAAM,mBAAmB,aAAW;AAClC,WAAO,SAAS,CAAC,OAAO,MAAM;AAC5B,0BAAoB,SAAS,CAAC,EAAE,KAAK,MAAM;AACzC,YAAI,MAAM,SAAS,YAAY,KAAK,GAAG;AACrC,8BAAoB,KAAK;AAAA,QAC3B;AAAA,MACF,GAAG,mBAAiB,oBAAoB,OAAO,aAAa,CAAC;AAAA,IAC/D,CAAC;AACD,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,aAAW;AACtB,QAAI,QAAQ;AACZ,UAAMC,OAAM,MAAM;AAChB,aAAO;AAAA,IACT;AACA,UAAMC,OAAM,OAAK;AACf,cAAQ;AAAA,IACV;AACA,WAAO;AAAA,MACL,KAAAD;AAAA,MACA,KAAAC;AAAA,IACF;AAAA,EACF;AAEA,QAAM,kBAAkB,CAAC,OAAO,eAAe,gBAAgB,SAAS;AACtE,QAAI;AACJ,QAAI,UAAU,IAAI,GAAG;AACnB,aAAO,CAAC;AAAA,QACJ,OAAO,QAAQ;AAAA,QACf,UAAU,KAAK,KAAK,IAAI,eAAe,QAAQ,OAAO,SAAS,KAAK;AAAA,QACpE,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,WAAW;AAAA,MACb,CAAC;AAAA,IACL;AACA,kBAAc,KAAK,eAAa;AAC9B,UAAI,GAAG,UAAU,OAAO,IAAI,GAAG;AAC7B,uBAAe,IAAI,IAAI;AAAA,MACzB;AAAA,IACF,CAAC;AACD,UAAM,mBAAmB,YAAY,MAAM,OAAO,eAAe,IAAI,CAAC;AACtE,kBAAc,KAAK,eAAa;AAC9B,UAAI,GAAG,UAAU,KAAK,IAAI,GAAG;AAC3B,uBAAe,IAAI,KAAK;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,UAAM,mBAAmB,UAAU,IAAI,EAAE,OAAO,MAAM,EAAE,IAAI,UAAQ,UAAU,OAAO,eAAe,gBAAgB,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;AACnI,WAAO,iBAAiB,QAAQ,EAAE,OAAO,gBAAgB;AAAA,EAC3D;AACA,QAAM,YAAY,CAAC,OAAO,eAAe,gBAAgB,SAAS,WAAW,IAAI,EAAE,OAAO,MAAM,EAAE,KAAK,MAAM,gBAAgB,OAAO,eAAe,gBAAgB,IAAI,GAAG,UAAQ;AAChL,UAAM,iBAAiB,MAAM,SAAS,IAAI,GAAG,CAAC,KAAK,SAAS,MAAM;AAChE,UAAI,MAAM,GAAG;AACX,eAAO;AAAA,MACT,OAAO;AACL,YAAI,WAAW,OAAO,GAAG;AACvB,iBAAO,IAAI,OAAO,gBAAgB,OAAO,eAAe,gBAAgB,OAAO,CAAC;AAAA,QAClF,OAAO;AACL,gBAAM,WAAW;AAAA,YACf,YAAY;AAAA,YACZ;AAAA,YACA,SAAS,CAAC,OAAO;AAAA,YACjB,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,gBAAgB,KAAK,IAAI;AAAA,UAC3B;AACA,iBAAO,IAAI,OAAO,QAAQ;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,GAAG,CAAC,CAAC;AACL,WAAO,UAAU,OAAO,eAAe,gBAAgB,IAAI,EAAE,OAAO,cAAc;AAAA,EACpF,CAAC;AACD,QAAM,YAAY,CAAC,OAAO,eAAe,gBAAgB,SAAS,KAAK,SAAS,IAAI,GAAG,aAAW;AAChG,UAAM,SAAS,OAAO,OAAO,IAAI,YAAY;AAC7C,UAAM,WAAW,QAAQ;AACzB,WAAO,OAAO,UAAU,eAAe,gBAAgB,OAAO;AAAA,EAChE,CAAC;AACD,QAAM,aAAa,CAAC,OAAO,kBAAkB;AAC3C,UAAM,iBAAiB,KAAK,KAAK;AACjC,UAAM,eAAe;AACrB,WAAO,IAAI,OAAO,WAAS;AAAA,MACzB,YAAY;AAAA,MACZ,SAAS,UAAU,cAAc,eAAe,gBAAgB,IAAI;AAAA,IACtE,EAAE;AAAA,EACJ;AAEA,QAAM,oBAAoB,CAAC,QAAQ,YAAY;AAC7C,UAAM,oBAAoB,iBAAiB,OAAO;AAClD,WAAO,IAAI,mBAAmB,WAAS;AACrC,YAAM,UAAU,CAAC,eAAe,KAAK,IAAI,aAAa,MAAM,OAAO,IAAI,aAAa,CAAC,aAAa,SAAS,OAAQ,MAAM,OAAQ,KAAK,CAAC,CAAC;AACxI,YAAM,gBAAgB,YAAY,KAAK,IAAI,MAAM,iBAAiB,CAAC;AACnE,aAAO,aAAa,QAAQ,gBAAgB,QAAQ,QAAQ,KAAK,aAAa,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,CAAC,QAAQ,YAAY;AAC5C,UAAM,oBAAoB,iBAAiB,OAAO;AAClD,WAAO,YAAY,OAAO,iBAAiB,iBAAiB,EAAE,QAAQ;AAAA,EACxE;AACA,QAAM,iBAAiB,CAAC,QAAQ,YAAY,KAAK,QAAQ,SAAS,UAAU,GAAG,CAAAC,aAAW;AACxF,UAAM,kBAAkB,KAAKA,QAAO,EAAE,OAAO,UAAU;AACvD,WAAO,kBAAkB,iBAAiB,QAAQA,QAAO,IAAI,kBAAkB,QAAQA,QAAO;AAAA,EAChG,CAAC;AACD,QAAM,wBAAwB,CAAC,SAAS,gBAAgB;AACtD,WAAO,SAAS,SAAS,UAAU,GAAG,WAAS,YAAY,aAAa,KAAK,CAAC;AAAA,EAChF;AACA,QAAM,mBAAmB,YAAU;AACjC,UAAM,oBAAoB,IAAI,qBAAqB,MAAM,GAAG,aAAa,OAAO;AAChF,WAAO,MAAM,KAAK,mBAAmB,IAAI,iBAAiB,CAAC,GAAG,KAAK,QAAQ,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,GAAG,CAAC,OAAO,SAAS;AAAA,MACvI;AAAA,MACA;AAAA,IACF,EAAE;AAAA,EACJ;AACA,QAAM,kBAAkB,CAAC,QAAQ,OAAO,gBAAgB;AACtD,UAAM,YAAY,WAAW,OAAO,iBAAiB,MAAM,CAAC;AAC5D,WAAO,WAAW,cAAY;AAC5B,4BAAsB,SAAS,SAAS,WAAW;AACnD,YAAM,gBAAgB,eAAe,QAAQ,SAAS,OAAO;AAC7D,aAAO,eAAe,kBAAgB;AACpC,sBAAc,QAAQ,gBAAgB,WAAW,eAAe,eAAe,aAAa,GAAG;AAAA,MACjG,CAAC;AACD,aAAO,SAAS,YAAY,aAAa;AACzC,aAAO,SAAS,UAAU;AAAA,IAC5B,CAAC;AAAA,EACH;AAEA,QAAM,uBAAuB,CAAC,QAAQ,gBAAgB;AACpD,UAAM,QAAQ,QAAQ,qBAAqB,MAAM,CAAC;AAClD,UAAM,UAAU,QAAQ,mBAAmB,MAAM,CAAC;AAClD,QAAI,YAAY;AAChB,QAAI,MAAM,UAAU,QAAQ,QAAQ;AAClC,YAAM,WAAW,OAAO,UAAU,YAAY;AAC9C,sBAAgB,QAAQ,OAAO,WAAW;AAC1C,oBAAc,QAAQ,aAAa,OAAO;AAC1C,aAAO,UAAU,eAAe,QAAQ;AACxC,aAAO,UAAU,OAAO,eAAe,OAAO,UAAU,OAAO,CAAC,CAAC;AACjE,aAAO,YAAY;AACnB,kBAAY;AAAA,IACd;AACA,WAAO;AAAA,EACT;AACA,QAAM,oBAAoB,CAAC,QAAQ,gBAAgB,CAAC,iCAAiC,MAAM,KAAK,qBAAqB,QAAQ,WAAW;AACxI,QAAM,sBAAsB,YAAU,kBAAkB,QAAQ,QAAQ;AACxE,QAAM,uBAAuB,YAAU,kBAAkB,QAAQ,SAAS;AAC1E,QAAM,uBAAuB,YAAU,kBAAkB,QAAQ,SAAS;AAE1E,QAAM,YAAY;AAClB,QAAM,SAAS,UAAQ,SAAS;AAEhC,QAAM,aAAa,CAAC,OAAO,WAAW,WAAW,WAAW,OAAO,WAAW,MAAM,EAAE,OAAO;AAE7F,QAAM,WAAW,CAAC,SAAS,WAAW,WAAW,SAAS,MAAM,IAAI,MAAM,CAAC;AAE3E,MAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,6BAA6B;AAEvE,QAAM,QAAQ,SAAS;AACvB,QAAM,iBAAiB,SAAO;AAC5B,UAAM,WAAW,CAAC;AAClB,UAAM,gBAAgB,WAAS;AAC7B,UAAI,YAAY,IAAI,QAAQ,mBAAmB,cAAc;AAC7D,UAAI,SAAS,IAAI,QAAQ,gBAAgB,WAAW;AACpD,UAAI,UAAU,SAAS,GAAG;AACxB,cAAM,aAAa,MAAM,OAAO,QAAQ,EAAE,iBAAiB,WAAW,CAAC;AACvE,YAAI,UAAU,cAAc,GAAG;AAC7B,mBAAS,KAAK,IAAI,QAAQ,UAAU,WAAW,SAAS,CAAC;AACzD,cAAI,OAAO;AACT,sBAAU,aAAa,YAAY,UAAU,WAAW,MAAM,CAAC;AAAA,UACjE,OAAO;AACL,kBAAM,YAAY,YAAY,UAAU,WAAW,MAAM,CAAC;AAAA,UAC5D;AAAA,QACF,OAAO;AACL,oBAAU,YAAY,UAAU;AAAA,QAClC;AACA,oBAAY;AACZ,iBAAS;AAAA,MACX;AACA,eAAS,QAAQ,mBAAmB,cAAc,IAAI;AACtD,eAAS,QAAQ,gBAAgB,WAAW,IAAI;AAAA,IAClD;AACA,kBAAc,IAAI;AAClB,QAAI,CAAC,IAAI,WAAW;AAClB,oBAAc;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,cAAY;AAClC,UAAM,kBAAkB,WAAS;AAC/B,YAAM,YAAY,CAAAC,eAAa;AAC7B,YAAI;AACJ,YAAI,QAAQ,KAAKA,WAAU,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC/E,YAAI,MAAM;AACV,eAAO,MAAM;AACX,cAAI,SAASA,YAAW;AACtB,mBAAO;AAAA,UACT;AACA,cAAI,CAAC,UAAU,IAAI,KAAK,KAAK,aAAa,eAAe,MAAM,YAAY;AACzE;AAAA,UACF;AACA,iBAAO,KAAK;AAAA,QACd;AACA,eAAO;AAAA,MACT;AACA,UAAI,YAAY,SAAS,QAAQ,mBAAmB,cAAc;AAClE,UAAI,SAAS,SAAS,QAAQ,gBAAgB,WAAW;AACzD,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,UAAI,UAAU,SAAS,KAAK,UAAU,YAAY;AAChD,cAAM,OAAO;AACb,iBAAS,UAAU,SAAS;AAC5B,oBAAY,UAAU;AACtB,cAAM,OAAO,IAAI;AACjB,YAAI,CAAC,UAAU,cAAc,KAAK,MAAM,QAAQ,SAAS,GAAG;AAC1D,oBAAU,YAAY,MAAM,OAAO,IAAI,CAAC;AAAA,QAC1C;AAAA,MACF;AACA,eAAS,QAAQ,mBAAmB,cAAc,IAAI;AACtD,eAAS,QAAQ,gBAAgB,WAAW,IAAI;AAAA,IAClD;AACA,oBAAgB,IAAI;AACpB,oBAAgB;AAChB,UAAM,MAAM,MAAM,UAAU;AAC5B,QAAI,SAAS,SAAS,gBAAgB,SAAS,WAAW;AAC1D,QAAI,SAAS,cAAc;AACzB,UAAI,OAAO,SAAS,cAAc,SAAS,SAAS;AAAA,IACtD;AACA,WAAO,eAAe,GAAG;AAAA,EAC3B;AAEA,QAAM,+BAA+B,cAAY;AAC/C,YAAQ,UAAU;AAAA,MAClB,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,kBAAkB,CAAC,KAAK,IAAI,WAAW;AAC3C,UAAMhB,QAAO,OAAO,iBAAiB,IAAI,OAAO,iBAAiB,IAAI;AACrE,QAAI,SAAS,IAAI,mBAAmBA,KAAI;AAAA,EAC1C;AACA,QAAM,aAAa,CAAC,KAAK,UAAU;AACjC,aAAS,KAAK,OAAO,CAAC,OAAO,QAAQ;AACnC,UAAI,aAAa,KAAK,KAAK;AAAA,IAC7B,CAAC;AAAA,EACH;AACA,QAAM,kBAAkB,CAAC,KAAK,IAAI,WAAW;AAC3C,eAAW,IAAI,OAAO,iBAAiB,CAAC;AACxC,aAAS,KAAK,IAAI,OAAO,MAAM,EAAE,GAAG,QAAM;AACxC,iBAAW,IAAI,OAAO,sBAAsB,CAAC;AAAA,IAC/C,CAAC;AAAA,EACH;AACA,QAAM,wBAAwB,CAAC,KAAK,IAAI,WAAW;AACjD,oBAAgB,KAAK,IAAI,MAAM;AAC/B,oBAAgB,KAAK,IAAI,MAAM;AAAA,EACjC;AACA,QAAM,eAAe,CAAC,KAAK,SAAS,WAAW;AAC7C,aAAS,KAAK,QAAQ,WAAS,IAAI,SAAS,SAAS,OAAO,EAAE,CAAC;AAAA,EACjE;AACA,QAAM,WAAW,CAAC,QAAQ,SAAS,cAAc,IAAI,KAAK,CAAC,QAAQ,MAAM,OAAO,OAAO,iBAAiB,CAAC;AACzG,QAAM,kBAAkB,CAAC,QAAQ,KAAK,OAAO,SAAS;AACpD,QAAI,YAAY,IAAI,QAAQ,mBAAmB,cAAc;AAC7D,UAAM,SAAS,IAAI,QAAQ,gBAAgB,WAAW;AACtD,QAAI,UAAU,SAAS,GAAG;AACxB,kBAAY,UAAU,WAAW,KAAK,IAAI,QAAQ,UAAU,WAAW,SAAS,CAAC,CAAC,KAAK;AAAA,IACzF;AACA,QAAI,CAAC,SAAS,KAAK,UAAU,WAAW,GAAG;AACzC,kBAAY,UAAU;AAAA,IACxB;AACA,UAAM,oBAAoB,UAAQ;AAChC,aAAO,CAAC,OAAO,IAAI,QAAQ,IAAI,KAAK,KAAK,cAAc,SAAS,MAAM;AACpE,eAAO,KAAK;AAAA,MACd;AACA,aAAO;AAAA,IACT;AACA,UAAM,sBAAsB,CAACgB,YAAW,YAAY;AAClD,UAAI;AACJ,YAAM,SAAS,IAAI,SAASA,YAAW,kBAAkBA,UAAS,CAAC;AACnE,YAAM,MAAM,UAAU,SAAS;AAC/B,UAAI;AACJ,aAAO,OAAO,OAAO,GAAG,EAAE,GAAG;AAC3B,YAAI,EAAE,OAAO,QAAQ,IAAI,KAAK,OAAO,KAAK,WAAW,OAAO,KAAK,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,IAAI;AACzI,iBAAO,SAAS,KAAK,IAAI;AAAA,QAC3B;AAAA,MACF;AACA,aAAO,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,SAAS,aAAa,SAAS,GAAG;AACpC,UAAI,OAAO,UAAU,WAAW,GAAG;AACjC,oBAAY,oBAAoB,WAAW,KAAK,EAAE,MAAM,SAAS;AAAA,MACnE,OAAO;AACL,YAAI,UAAU,eAAe,QAAQ,SAAS,QAAQ,UAAU,UAAU,GAAG;AAC3E,sBAAY,UAAU;AAAA,QACxB;AACA,eAAO,UAAU,oBAAoB,SAAS,SAAS,QAAQ,UAAU,eAAe,KAAK,aAAa,UAAU,eAAe,IAAI;AACrI,sBAAY,UAAU;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,SAAS,aAAa,SAAS,GAAG;AACrC,UAAI,OAAO,UAAU,WAAW,GAAG;AACjC,oBAAY,oBAAoB,WAAW,IAAI,EAAE,MAAM,SAAS;AAAA,MAClE,OAAO;AACL,YAAI,UAAU,eAAe,QAAQ,SAAS,QAAQ,UAAU,UAAU,GAAG;AAC3E,sBAAY,UAAU;AAAA,QACxB;AACA,eAAO,UAAU,gBAAgB,SAAS,SAAS,QAAQ,UAAU,WAAW,KAAK,aAAa,UAAU,WAAW,IAAI;AACzH,sBAAY,UAAU;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU,eAAe,MAAM;AACpC,YAAMX,UAAS,UAAU;AACzB,UAAI,YAAY,QAAQ,SAAS,GAAG;AAClC,eAAO;AAAA,MACT;AACA,UAAI,YAAY,KAAKA,QAAO,QAAQ,GAAG;AACrC,eAAO;AAAA,MACT;AACA,kBAAYA;AAAA,IACd;AACA,WAAO;AAAA,EACT;AACA,QAAM,wBAAwB,CAAC,QAAQ,KAAK,SAAS;AACnD,UAAM,aAAa,CAAC;AACpB,UAAM,MAAM,OAAO;AACnB,UAAM,YAAY,gBAAgB,QAAQ,KAAK,MAAM,IAAI;AACzD,UAAM,UAAU,gBAAgB,QAAQ,KAAK,OAAO,IAAI;AACxD,QAAI;AACJ,UAAM,WAAW,CAAC;AAClB,aAAS,OAAO,WAAW,MAAM,OAAO,KAAK,aAAa;AACxD,eAAS,KAAK,IAAI;AAClB,UAAI,SAAS,SAAS;AACpB;AAAA,MACF;AAAA,IACF;AACA,aAAS,KAAK,UAAU,UAAQ;AAC9B,UAAI;AACJ,UAAI,YAAY,QAAQ,IAAI,GAAG;AAC7B,mBAAW,KAAK,IAAI;AACpB,gBAAQ;AACR;AAAA,MACF;AACA,UAAI,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,GAAG;AACnC,YAAI,KAAK,IAAI,GAAG;AACd,cAAI,OAAO,IAAI;AAAA,QACjB;AACA,gBAAQ;AACR;AAAA,MACF;AACA,YAAMY,eAAc,KAAK;AACzB,UAAI,SAAS,eAAe,IAAI,GAAG;AACjC,YAAI,WAAWA,YAAW,KAAK,YAAY,QAAQA,YAAW,KAAK,CAACA,gBAAe,KAAK,eAAe,MAAM;AAC3G,kBAAQ;AACR;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC,OAAO;AACV,gBAAQ,IAAI,OAAO,GAAG;AACtB,SAAC,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,OAAO,IAAI;AACvF,mBAAW,KAAK,KAAK;AAAA,MACvB;AACA,YAAM,YAAY,IAAI;AAAA,IACxB,CAAC;AACD,WAAO;AAAA,EACT;AACA,QAAM,qBAAqB,CAAC,KAAK,KAAK,WAAW;AAC/C,UAAM,WAAW,IAAI,SAAS,KAAK,iBAAiB;AACpD,QAAI,cAAc,SAAS,OAAO,iBAAiB,IAAI;AACvD,kBAAc,gBAAgB,OAAO,KAAK;AAC1C,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,qBAAqB,CAAC,QAAQ,UAAU;AAC5C,UAAM,QAAQ,OAAO,UAAU,SAAS,IAAI;AAC5C,UAAM,aAAa,gBAAgB,QAAQ,OAAO,MAAM,OAAO,QAAQ,CAAC;AACxE,QAAI,SAAS,aAAa,QAAQ,UAAU,GAAG,aAAa,QAAQ,MAAM,uBAAuB,CAAC,GAAG;AACnG,aAAO,MAAM;AAAA,IACf,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,YAAY,CAAC,QAAQ,UAAU,WAAW;AAC9C,UAAM,MAAM,OAAO,UAAU,OAAO;AACpC,QAAI,eAAe;AACnB,UAAM,OAAO,mBAAmB,QAAQ,mBAAmB,QAAQ,GAAG,CAAC;AACvE,UAAM,MAAM,OAAO;AACnB,QAAI,IAAI,mBAAmB,OAAO,UAAU,QAAQ,CAAC,MAAM,SAAS;AAClE;AAAA,IACF;AACA,eAAW,SAAS,YAAY;AAChC,QAAI,aAAa,MAAM;AACrB,qBAAe;AAAA,IACjB;AACA,UAAM,WAAW,eAAe,GAAG;AACnC,UAAM,qBAAqB,SAAS,sBAAsB,QAAQ,KAAK,IAAI,GAAG,OAAO,IAAI,UAAU;AACnG,aAAS,KAAK,oBAAoB,WAAS;AACzC,UAAI;AACJ,YAAM,UAAU,MAAM;AACtB,YAAMZ,UAAS,MAAM;AACrB,UAAI,CAAC,eAAeA,OAAM,GAAG;AAC3B,YAAI,WAAW,WAAW,OAAO,KAAK,QAAQ,aAAa,YAAY,mBAAmB,KAAK,SAAS,MAAM,GAAG;AAC/G,sBAAY;AACZ,kBAAQ,IAAI,OAAO,OAAO,YAAY;AACtC,kBAAQ,YAAY,KAAK;AAAA,QAC3B,OAAO;AACL,sBAAY,IAAI,OAAO,QAAQ;AAC/B,UAAAA,QAAO,aAAa,WAAW,KAAK;AACpC,oBAAU,YAAY,KAAK;AAC3B,kBAAQ,IAAI,OAAO,OAAO,YAAY;AAAA,QACxC;AACA,qBAAa,KAAK,OAAO;AAAA,UACvB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,8BAAsB,KAAK,WAAW,MAAM;AAC5C,+BAAuB,OAAO,KAAK,SAAS;AAAA,MAC9C;AAAA,IACF,CAAC;AACD,WAAO,UAAU,OAAO,gBAAgB,QAAQ,CAAC;AAAA,EACnD;AACA,QAAM,eAAe,CAAC,OAAO,UAAU;AACrC,WAAO,WAAW,KAAK,KAAK,MAAM,cAAc,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,EACtG;AACA,QAAM,mBAAmB,CAAC,KAAK,OAAO,UAAU;AAC9C,UAAM,cAAc,IAAI,SAAS,OAAO,mBAAmB,IAAI;AAC/D,UAAM,QAAQ,IAAI,SAAS,OAAO,mBAAmB,IAAI;AACzD,WAAO,gBAAgB;AAAA,EACzB;AACA,QAAM,iBAAiB,CAAC,MAAM,SAAS;AACrC,WAAO,KAAK,cAAc,KAAK;AAAA,EACjC;AACA,QAAM,cAAc,CAAC,KAAK,OAAO,UAAU;AACzC,WAAO,aAAa,OAAO,KAAK,KAAK,iBAAiB,KAAK,OAAO,KAAK,KAAK,eAAe,OAAO,KAAK;AAAA,EACzG;AACA,QAAM,yBAAyB,CAAC,KAAK,cAAc;AACjD,QAAI;AACJ,QAAI,UAAU,UAAU;AACxB,QAAI,YAAY,KAAK,WAAW,OAAO,GAAG;AACxC,YAAM,YAAY;AAClB,aAAO,OAAO,UAAU,YAAY;AAClC,kBAAU,YAAY,IAAI;AAAA,MAC5B;AACA,UAAI,OAAO,SAAS;AAAA,IACtB;AACA,cAAU,UAAU;AACpB,QAAI,YAAY,KAAK,WAAW,OAAO,GAAG;AACxC,YAAM,YAAY;AAClB,aAAO,OAAO,UAAU,WAAW;AACjC,kBAAU,aAAa,MAAM,UAAU,UAAU;AAAA,MACnD;AACA,UAAI,OAAO,SAAS;AAAA,IACtB;AAAA,EACF;AACA,QAAM,eAAe,CAAC,QAAQ,MAAM,UAAU,WAAW;AACvD,QAAI,KAAK,aAAa,UAAU;AAC9B,YAAM,UAAU,OAAO,IAAI,OAAO,MAAM,QAAQ;AAChD,4BAAsB,OAAO,KAAK,SAAS,MAAM;AACjD,oBAAc,QAAQ,6BAA6B,QAAQ,GAAG,OAAO;AAAA,IACvE,OAAO;AACL,4BAAsB,OAAO,KAAK,MAAM,MAAM;AAC9C,oBAAc,QAAQ,6BAA6B,QAAQ,GAAG,IAAI;AAAA,IACpE;AAAA,EACF;AACA,QAAM,mBAAmB,CAAC,QAAQ,MAAM,UAAU,WAAW;AAC3D,SAAK,UAAU,QAAQ,CAAC,KAAK,GAAG,cAAc;AAC5C,UAAI,IAAI,WAAW,MAAM,GAAG;AAC1B,kBAAU,OAAO,GAAG;AACpB,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,gBAAgB,OAAO;AAAA,QAC9B;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,KAAK,aAAa,UAAU;AAC9B,YAAM,UAAU,OAAO,IAAI,OAAO,MAAM,QAAQ;AAChD,4BAAsB,OAAO,KAAK,SAAS,MAAM;AACjD,oBAAc,QAAQ,6BAA6B,QAAQ,GAAG,OAAO;AAAA,IACvE,OAAO;AACL,4BAAsB,OAAO,KAAK,MAAM,MAAM;AAC9C,oBAAc,QAAQ,6BAA6B,QAAQ,GAAG,IAAI;AAAA,IACpE;AAAA,EACF;AACA,QAAM,sBAAsB,CAAC,QAAQ,YAAY,OAAO,UAAU,WAAW;AAC3E,UAAM,eAAe,WAAW,UAAU;AAC1C,QAAI,gBAAgB,WAAW,aAAa,YAAY,CAAC,mBAAmB,MAAM,KAAK,CAAC,aAAa,UAAU,GAAG;AAChH,2BAAqB,MAAM;AAAA,IAC7B,OAAO;AACL,gBAAU,QAAQ,UAAU,MAAM;AAClC,YAAM,WAAW,eAAe,OAAO,UAAU,OAAO,CAAC;AACzD,YAAM,WAAW,eAAe;AAAA,QAC9B;AAAA,QACA,GAAG;AAAA,MACL,IAAI;AACJ,YAAM,iBAAiB,gBAAgB,aAAa,UAAU,IAAI,mBAAmB;AACrF,eAAS,KAAK,UAAU,SAAO;AAC7B,uBAAe,QAAQ,KAAK,UAAU,MAAM;AAAA,MAC9C,CAAC;AACD,aAAO,UAAU,OAAO,gBAAgB,QAAQ,CAAC;AAAA,IACnD;AAAA,EACF;AACA,QAAM,qBAAqB,YAAU;AACnC,WAAO,qBAAqB;AAAA,EAC9B;AACA,QAAM,mBAAmB,CAAC,QAAQ,YAAY,UAAU,WAAW;AACjE,QAAI,eAAe,OAAO,QAAQ,GAAG;AACnC;AAAA,IACF;AACA,QAAI,YAAY;AACd,UAAI,WAAW,aAAa,YAAY,CAAC,mBAAmB,MAAM,KAAK,CAAC,aAAa,UAAU,GAAG;AAChG,6BAAqB,MAAM;AAAA,MAC7B,OAAO;AACL,cAAM,WAAW,eAAe,OAAO,UAAU,OAAO,CAAC;AACzD,YAAI,aAAa,UAAU,GAAG;AAC5B,qBAAW,UAAU,QAAQ,CAAC,KAAK,GAAG,cAAc;AAClD,gBAAI,IAAI,WAAW,MAAM,GAAG;AAC1B,wBAAU,OAAO,GAAG;AACpB,kBAAI,UAAU,WAAW,GAAG;AAC1B,2BAAW,gBAAgB,OAAO;AAAA,cACpC;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AACA,8BAAsB,OAAO,KAAK,YAAY,MAAM;AACpD,cAAM,UAAU,OAAO,IAAI,OAAO,YAAY,QAAQ;AACtD,+BAAuB,OAAO,KAAK,OAAO;AAC1C,eAAO,UAAU,OAAO,gBAAgB,QAAQ,CAAC;AACjD,kBAAU,QAAQ,UAAU,MAAM;AAClC,sBAAc,QAAQ,6BAA6B,QAAQ,GAAG,OAAO;AAAA,MACvE;AAAA,IACF,OAAO;AACL,gBAAU,QAAQ,UAAU,MAAM;AAClC,oBAAc,QAAQ,6BAA6B,QAAQ,GAAG,UAAU;AAAA,IAC1E;AAAA,EACF;AACA,QAAM,aAAa,CAAC,QAAQ,UAAU,YAAY;AAChD,UAAM,aAAa,cAAc,MAAM;AACvC,QAAI,wBAAwB,QAAQ,UAAU,GAAG;AAC/C;AAAA,IACF;AACA,UAAM,mBAAmB,oBAAoB,MAAM;AACnD,UAAM,SAAS,SAAS,OAAO,IAAI,UAAU,CAAC;AAC9C,QAAI,iBAAiB,SAAS,GAAG;AAC/B,0BAAoB,QAAQ,YAAY,kBAAkB,UAAU,MAAM;AAAA,IAC5E,OAAO;AACL,uBAAiB,QAAQ,YAAY,UAAU,MAAM;AAAA,IACvD;AAAA,EACF;AAEA,QAAM,MAAM,SAAS;AACrB,QAAM,gBAAgB,CAAC,KAAK,SAAS;AACnC,UAAM,aAAa,KAAK;AACxB,QAAI,cAAc,WAAW,aAAa,QAAQ,WAAW,eAAe,MAAM;AAChF,YAAM,UAAU,WAAW;AAC3B,UAAI,WAAW,QAAQ,aAAa,MAAM;AACxC,gBAAQ,YAAY,IAAI;AACxB,YAAI,UAAU,KAAK,UAAU,GAAG;AAC9B,cAAI,OAAO,UAAU;AAAA,QACvB;AAAA,MACF,OAAO;AACL,YAAI,SAAS,YAAY,iBAAiB,MAAM;AAAA,MAClD;AAAA,IACF;AACA,QAAI,WAAW,UAAU,GAAG;AAC1B,YAAM,UAAU,WAAW;AAC3B,UAAI,WAAW,QAAQ,aAAa,MAAM;AACxC,gBAAQ,YAAY,IAAI;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,CAAC,KAAK,YAAY;AACvC,UAAM,QAAQ,SAAS,KAAK,IAAI,OAAO,SAAS,OAAO,CAAC;AACxD,aAAS,KAAK,OAAO,UAAQ;AAC3B,oBAAc,KAAK,IAAI;AAAA,IACzB,CAAC;AAAA,EACH;AAEA,QAAM,yBAAyB,CAAC,QAAQ,KAAK,WAAW,SAAS;AAC/D,QAAI,OAAO,IAAI;AACf,UAAM,SAAS,IAAI;AACnB,QAAI,aAAa,IAAI,MAAM,YAAY,SAAS,KAAK,KAAK,SAAS,SAAS,IAAI;AAC9E,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,OAAO,OAAO,oBAAoB;AACzD,QAAI,UAAU,IAAI,GAAG;AACnB,aAAO,SAAS,QAAQ,MAAM,MAAM;AAAA,IACtC;AACA,UAAM,SAAS,IAAI,SAAS,MAAM,IAAI;AACtC,QAAI,WAAW;AACb,UAAI,UAAU,OAAO,KAAK,IAAI,GAAG;AAC/B,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AACA,UAAM,SAAS,YAAY,OAAO,KAAK,KAAK,MAAM,IAAI,OAAO,MAAM,KAAK,MAAM;AAC9E,WAAO,OAAO,OAAO,GAAG;AACtB,UAAI,KAAK,aAAa,QAAQ,CAAC,KAAK,cAAc,GAAG;AACnD,eAAO;AAAA,MACT;AACA,UAAI,eAAe,KAAK,QAAQ,GAAG;AACjC,eAAO;AAAA,MACT;AACA,UAAI,aAAa,IAAI,KAAK,KAAK,KAAK,SAAS,GAAG;AAC9C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,uBAAuB,CAAC,KAAK,QAAQ;AACzC,UAAM,aAAa,IAAI;AACvB,WAAO,WAAW,WAAW,KAAK,CAAC,WAAW,WAAW,CAAC,CAAC,KAAK,IAAI,QAAQ,WAAW,CAAC,CAAC;AAAA,EAC3F;AACA,QAAM,gBAAgB,UAAQ,SAAS,KAAK,IAAI,EAAE,IAAI,aAAa,OAAO,EAAE,OAAO,aAAa,EAAE,OAAO,QAAM,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,EAAE,CAAC,CAAC;AACnK,QAAM,yBAAyB,CAAC,KAAK,QAAQ;AAC3C,QAAI,qBAAqB,KAAK,GAAG,KAAK,cAAc,IAAI,UAAU,GAAG;AACnE,UAAI,OAAO,IAAI,YAAY,IAAI;AAAA,IACjC;AAAA,EACF;AACA,QAAM,eAAe,CAAC,KAAK,SAAS,UAAU;AAC5C,QAAI;AACJ,UAAM,YAAY,qBAAqB,KAAK,KAAK,IAAI,MAAM,aAAa;AACxE,2BAAuB,KAAK,OAAO;AACnC,QAAI,CAAC,UAAU,KAAK,SAAS,IAAI,GAAG;AAClC,aAAO,OAAO,QAAQ,YAAY;AAChC,kBAAU,YAAY,IAAI;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,QAAM,kBAAkB,CAAC,KAAK,SAAS,UAAU;AAC/C,QAAI;AACJ,UAAM,KAAK,QAAQ;AACnB,QAAI,CAAC,cAAc,KAAK,OAAO,KAAK,CAAC,cAAc,KAAK,KAAK,GAAG;AAC9D;AAAA,IACF;AACA,QAAI,WAAW,MAAM,SAAS,GAAG;AAC/B,iBAAW,MAAM;AAAA,IACnB;AACA,QAAI,OAAO,MAAM,WAAW;AAC1B,UAAI,KAAK,GAAG,eAAe,GAAG;AAC5B,YAAI,OAAO,GAAG,eAAe;AAAA,MAC/B;AAAA,IACF;AACA,UAAM,OAAO,MAAM;AACnB,QAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ,cAAc,GAAG;AACjD,UAAI,OAAO,IAAI;AAAA,IACjB;AACA,QAAI,UAAU,KAAK,OAAO,IAAI,GAAG;AAC/B,YAAM,aAAa,QAAQ,KAAK,CAAC;AAAA,IACnC;AACA,iBAAa,KAAK,SAAS,KAAK;AAChC,QAAI,UAAU;AACZ,YAAM,YAAY,QAAQ;AAAA,IAC5B;AACA,UAAMa,cAAa,SAAS,aAAa,QAAQ,KAAK,GAAG,aAAa,QAAQ,OAAO,CAAC;AACtF,UAAM,cAAcA,cAAa,IAAI,WAAW,SAAS,YAAY,KAAK,IAAI,CAAC;AAC/E,QAAI,OAAO,OAAO;AAClB,WAAO,aAAa,UAAQ;AAC1B,UAAI,UAAU,KAAK,IAAI,KAAK,SAAS,IAAI,QAAQ,GAAG;AAClD,YAAI,OAAO,IAAI;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,CAAC,QAAQ,QAAQ,SAAS;AACjD,UAAM,aAAa,QAAQ,IAAI,CAAC;AAChC,oBAAgB,OAAO,KAAK,QAAQ,IAAI;AACxC,WAAO,UAAU,kBAAkB,MAAM,CAAC;AAAA,EAC5C;AACA,QAAM,eAAe,CAAC,QAAQ,KAAK,QAAQ,SAAS;AAClD,UAAM,MAAM,OAAO;AACnB,QAAI,IAAI,QAAQ,IAAI,GAAG;AACrB,uBAAiB,QAAQ,QAAQ,IAAI;AAAA,IACvC,OAAO;AACL,YAAM,WAAW,eAAe,GAAG;AACnC,sBAAgB,KAAK,QAAQ,IAAI;AACjC,aAAO,UAAU,OAAO,gBAAgB,QAAQ,CAAC;AAAA,IACnD;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,QAAQ,KAAK,QAAQ,SAAS;AACnD,UAAM,WAAW,eAAe,GAAG;AACnC,oBAAgB,OAAO,KAAK,QAAQ,IAAI;AACxC,UAAM,mBAAmB,gBAAgB,QAAQ;AACjD,WAAO,UAAU,OAAO,gBAAgB;AAAA,EAC1C;AACA,QAAM,qCAAqC,CAAC,QAAQ,cAAc;AAChE,UAAM,MAAM,OAAO,KAAK,YAAY,OAAO;AAC3C,UAAM,oBAAoB,UAAU,SAAS;AAC7C,UAAM,OAAO,sBAAsB,QAAQ,iBAAiB;AAC5D,UAAM,KAAK,IAAI,UAAU,UAAU,SAAS,GAAG,MAAM,IAAI;AACzD,QAAI,IAAI;AACN,YAAM,KAAK,GAAG;AACd,UAAI,OAAO,OAAO,QAAQ,KAAK,UAAU,KAAK,EAAE,GAAG;AACjD,eAAO;AAAA,MACT;AACA,YAAM,MAAM,eAAe,UAAU,OAAO,CAAC;AAC7C,YAAM,UAAU,IAAI,UAAU,uBAAuB,QAAQ,KAAK,WAAW,IAAI,GAAG,MAAM,IAAI;AAC9F,YAAM,2BAA2B,YAAY,YAAY,IAAI,UAAU,IAAI,OAAO,IAAI,IAAI,UAAU,SAAS,EAAE;AAC/G,UAAI,WAAW,YAAY,MAAM,CAAC,0BAA0B;AAC1D,eAAO,YAAY,SAAS,MAAM;AAChC,cAAI,WAAW;AACb,yBAAa,QAAQ,KAAK,SAAS,EAAE;AAAA,UACvC,OAAO;AACL,gBAAI,aAAa,EAAE,GAAG;AACpB,mCAAqB,MAAM;AAAA,YAC7B,OAAO;AACL,4BAAc,QAAQ,KAAK,IAAI,OAAO;AAAA,YACxC;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT,WAAW,4BAA4B,CAAC,aAAa,YAAY,IAAI;AACnE,eAAO,YAAY,SAAS,MAAM;AAChC,cAAI,IAAI,wBAAwB,eAAe;AAC7C,kBAAM,WAAW,eAAe,GAAG;AACnC,kBAAM,iBAAiB,IAAI,wBAAwB;AACnD,yBAAa,KAAK,IAAI,wBAAwB,eAAe,OAAO;AACpE,2BAAe,OAAO;AACtB,kBAAM,mBAAmB,gBAAgB,QAAQ;AACjD,mBAAO,UAAU,OAAO,gBAAgB;AAAA,UAC1C;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT,WAAW,CAAC,SAAS;AACnB,YAAI,CAAC,aAAa,IAAI,gBAAgB,KAAK,IAAI,cAAc,GAAG;AAC9D,iBAAO,YAAY,SAAS,MAAM;AAChC,iCAAqB,MAAM;AAAA,UAC7B,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAc,CAAC,KAAK,OAAO,SAAS;AACxC,UAAM,cAAc,IAAI,UAAU,MAAM,YAAY,IAAI,SAAS,IAAI;AACrE,QAAI,OAAO,KAAK;AAChB,QAAI,eAAe,IAAI,QAAQ,WAAW,GAAG;AAC3C,UAAI,OAAO,WAAW;AAAA,IACxB;AAAA,EACF;AACA,QAAM,+BAA+B,CAAC,QAAQ,cAAc;AAC1D,UAAM,MAAM,OAAO;AACnB,UAAM,oBAAoB,OAAO,UAAU,SAAS;AACpD,UAAM,OAAO,sBAAsB,QAAQ,iBAAiB;AAC5D,UAAM,QAAQ,IAAI,UAAU,mBAAmB,IAAI,SAAS,IAAI;AAChE,QAAI,SAAS,IAAI,QAAQ,OAAO,QAAW,EAAE,oBAAoB,KAAK,CAAC,GAAG;AACxE,YAAM,MAAM,eAAe,OAAO,UAAU,OAAO,CAAC;AACpD,YAAM,UAAU,IAAI,UAAU,uBAAuB,QAAQ,KAAK,WAAW,IAAI,GAAG,MAAM,IAAI;AAC9F,UAAI,SAAS;AACX,cAAM,mBAAmB,aAAW,WAAW;AAAA,UAC7C;AAAA,UACA;AAAA,UACA;AAAA,QACF,GAAG,KAAK,OAAO,CAAC;AAChB,cAAM,WAAW,UAAQ,KAAK,QAAQ;AACtC,cAAM,cAAc,UAAU,aAAa,QAAQ,OAAO,GAAG,kBAAkB,QAAQ;AACvF,cAAM,YAAY,UAAU,aAAa,QAAQ,IAAI,cAAc,GAAG,kBAAkB,QAAQ;AAChG,YAAI,CAAC,OAAO,aAAa,WAAW,EAAE,GAAG;AACvC,iBAAO;AAAA,QACT;AACA,eAAO,YAAY,SAAS,MAAM;AAChC,gBAAM,aAAa,QAAQ;AAC3B,sBAAY,KAAK,OAAO,IAAI;AAC5B,iCAAuB,KAAK,UAAU;AACtC,iBAAO,UAAU,OAAO,SAAS,IAAI;AACrC,iBAAO,UAAU,SAAS,SAAS;AAAA,QACrC,CAAC;AACD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,uBAAuB,CAAC,QAAQ,cAAc;AAClD,WAAO,mCAAmC,QAAQ,SAAS,KAAK,6BAA6B,QAAQ,SAAS;AAAA,EAChH;AACA,QAAM,mBAAmB,YAAU;AACjC,UAAM,oBAAoB,OAAO,UAAU,SAAS;AACpD,UAAM,OAAO,sBAAsB,QAAQ,iBAAiB;AAC5D,UAAM,kBAAkB,OAAO,IAAI,UAAU,mBAAmB,YAAY,IAAI;AAChF,WAAO,mBAAmB,qBAAqB,MAAM,EAAE,SAAS;AAAA,EAClE;AACA,QAAM,uBAAuB,YAAU;AACrC,QAAI,iBAAiB,MAAM,GAAG;AAC5B,aAAO,YAAY,SAAS,MAAM;AAChC,eAAO,YAAY,QAAQ;AAC3B,uBAAe,OAAO,KAAK,OAAO,QAAQ,CAAC;AAAA,MAC7C,CAAC;AACD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,CAAC,QAAQ,cAAc;AAC7C,UAAM,YAAY,OAAO;AACzB,WAAO,CAAC,wBAAwB,QAAQ,UAAU,QAAQ,CAAC,MAAM,UAAU,YAAY,IAAI,qBAAqB,QAAQ,SAAS,IAAI,qBAAqB,MAAM;AAAA,EAClK;AACA,QAAM,UAAU,YAAU;AACxB,WAAO,GAAG,eAAe,OAAK;AAC5B,YAAM,MAAM,EAAE,QAAQ,YAAY;AAClC,WAAK,QAAQ,YAAY,QAAQ,oBAAoB,iBAAiB,MAAM,GAAG;AAC7E,uBAAe,OAAO,KAAK,OAAO,QAAQ,CAAC;AAAA,MAC7C;AAAA,IACF,CAAC;AACD,WAAO,GAAG,WAAW,OAAK;AACxB,UAAI,EAAE,YAAY,SAAS,WAAW;AACpC,YAAI,gBAAgB,QAAQ,KAAK,GAAG;AAClC,YAAE,eAAe;AAAA,QACnB;AAAA,MACF,WAAW,EAAE,YAAY,SAAS,QAAQ;AACxC,YAAI,gBAAgB,QAAQ,IAAI,GAAG;AACjC,YAAE,eAAe;AAAA,QACnB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,MAAM,aAAW;AAAA,IACrB,iBAAiB,eAAa;AAC5B,sBAAgB,QAAQ,SAAS;AAAA,IACnC;AAAA,EACF;AAEA,QAAM,aAAa,CAAC,QAAQ,WAAW;AACrC,UAAM,aAAa,cAAc,MAAM;AACvC,QAAI,eAAe,QAAQ,wBAAwB,QAAQ,UAAU,GAAG;AACtE;AAAA,IACF;AACA,WAAO,YAAY,SAAS,MAAM;AAChC,UAAI,SAAS,OAAO,MAAM,GAAG;AAC3B,eAAO,IAAI,UAAU,YAAY,OAAO,MAAM;AAAA,MAChD;AACA,UAAI,SAAS,OAAO,KAAK,GAAG;AAC1B,aAAK,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,IAAI,UAAU,YAAY,GAAG,CAAC,CAAC;AAAA,MACrE;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,wBAAwB,SAAO;AACnC,UAAM,QAAQ,QAAQ,KAAK,GAAG,EAAE,MAAM,EAAE,CAAC;AACzC,UAAM,SAAS,IAAI,OAAO,CAAC,MAAM,MAAM;AACrC,YAAM,YAAY,KAAK,YAAY,EAAE,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI;AACzE,aAAO,KAAK,IAAI,IAAI,CAAC,IAAI;AAAA,IAC3B,CAAC;AACD,WAAO,MAAM,QAAQ,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC;AAAA,EAC7C;AACA,QAAM,0BAA0B,WAAS;AACvC;AACA,QAAI,QAAQ,GAAG;AACb,aAAO;AAAA,IACT,OAAO;AACL,YAAM,YAAY,QAAQ;AAC1B,YAAM,WAAW,KAAK,MAAM,QAAQ,EAAE;AACtC,YAAM,OAAO,wBAAwB,QAAQ;AAC7C,YAAM,OAAO,OAAO,aAAa,IAAI,WAAW,CAAC,IAAI,SAAS;AAC9D,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AACA,QAAM,cAAc,SAAO,WAAW,KAAK,GAAG;AAC9C,QAAM,cAAc,SAAO,WAAW,KAAK,GAAG;AAC9C,QAAM,YAAY,SAAO,WAAW,KAAK,GAAG;AAC5C,QAAM,iBAAiB,WAAS;AAC9B,QAAI,UAAU,KAAK,GAAG;AACpB,aAAO;AAAA,IACT,WAAW,YAAY,KAAK,GAAG;AAC7B,aAAO;AAAA,IACT,WAAW,YAAY,KAAK,GAAG;AAC7B,aAAO;AAAA,IACT,WAAW,UAAU,KAAK,GAAG;AAC3B,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,kBAAkB,WAAS;AAC/B,YAAQ,eAAe,KAAK,GAAG;AAAA,MAC/B,KAAK;AACH,eAAO,SAAS,KAAK;AAAA,UACnB,eAAe,SAAS,KAAK;AAAA,UAC7B;AAAA,QACF,CAAC;AAAA,MACH,KAAK;AACH,eAAO,SAAS,KAAK;AAAA,UACnB,eAAe,SAAS,KAAK,aAAa;AAAA,UAC1C,OAAO,sBAAsB,KAAK,EAAE,SAAS;AAAA,QAC/C,CAAC;AAAA,MACH,KAAK;AACH,eAAO,SAAS,KAAK;AAAA,UACnB,eAAe,SAAS,KAAK,aAAa;AAAA,UAC1C,OAAO,sBAAsB,KAAK,EAAE,SAAS;AAAA,QAC/C,CAAC;AAAA,MACH,KAAK;AACH,eAAO,SAAS,KAAK;AAAA,UACnB,eAAe,SAAS,KAAK;AAAA,UAC7B,OAAO;AAAA,QACT,CAAC;AAAA,MACH,KAAK;AACH,eAAO,SAAS,KAAK;AAAA,IACvB;AAAA,EACF;AACA,QAAM,cAAc,YAAU;AAC5B,UAAM,QAAQ,SAAS,OAAO,OAAO,EAAE;AACvC,QAAI,KAAK,OAAO,eAAe,aAAa,GAAG;AAC7C,aAAO,wBAAwB,KAAK;AAAA,IACtC,WAAW,KAAK,OAAO,eAAe,aAAa,GAAG;AACpD,aAAO,wBAAwB,KAAK,EAAE,YAAY;AAAA,IACpD,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AAEA,QAAM,OAAO,YAAU;AACrB,UAAM,cAAc,cAAc,MAAM;AACxC,QAAI,CAAC,SAAS,WAAW,KAAK,wBAAwB,QAAQ,WAAW,GAAG;AAC1E;AAAA,IACF;AACA,WAAO,cAAc,KAAK;AAAA,MACxB,OAAO;AAAA,MACP,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,OAAO,CAAC;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,QACb,CAAC;AAAA,MACL;AAAA,MACA,aAAa;AAAA,QACX,OAAO,YAAY;AAAA,UACjB,OAAO,OAAO,IAAI,UAAU,aAAa,SAAS,GAAG;AAAA,UACrD,eAAe,SAAS,KAAK,OAAO,IAAI,SAAS,aAAa,iBAAiB,CAAC;AAAA,QAClF,CAAC;AAAA,MACH;AAAA,MACA,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,UAAU,SAAO;AACf,cAAM,OAAO,IAAI,QAAQ;AACzB,wBAAgB,KAAK,KAAK,EAAE,KAAK,YAAU;AACzC,iBAAO,YAAY,iBAAiB,OAAO;AAAA,YACzC,OAAO,EAAE,OAAO,OAAO,UAAU,MAAM,KAAK,OAAO,MAAM;AAAA,YACzD,QAAQ,EAAE,mBAAmB,OAAO,cAAc,MAAM,EAAE,EAAE;AAAA,UAC9D,CAAC;AAAA,QACH,CAAC;AACD,YAAI,MAAM;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,wBAAwB,CAAC,QAAQ,aAAa,MAAM;AACxD,UAAM,aAAa,cAAc,MAAM;AACvC,WAAO,cAAc,UAAU,KAAK,WAAW,aAAa;AAAA,EAC9D;AACA,QAAM,iBAAiB,YAAU;AAC/B,WAAO,WAAW,gBAAgB,MAAM;AACtC,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AACA,QAAM,aAAa,YAAU;AAC3B,WAAO,GAAG,qBAAqB,OAAK;AAClC,YAAM,MAAM,EAAE,QAAQ,YAAY;AAClC,UAAI,QAAQ,UAAU;AACpB,4BAAoB,MAAM;AAAA,MAC5B,WAAW,QAAQ,WAAW;AAC5B,6BAAqB,MAAM;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,WAAO,WAAW,uBAAuB,CAAC,IAAI,WAAW;AACvD,iBAAW,QAAQ,MAAM,MAAM;AAAA,IACjC,CAAC;AACD,WAAO,WAAW,qBAAqB,CAAC,IAAI,WAAW;AACrD,iBAAW,QAAQ,MAAM,MAAM;AAAA,IACjC,CAAC;AACD,WAAO,WAAW,wBAAwB,CAAC,IAAI,WAAW;AACxD,iBAAW,QAAQ,MAAM,MAAM;AAAA,IACjC,CAAC;AACD,WAAO,WAAW,cAAc,MAAM;AACpC,2BAAqB,MAAM;AAAA,IAC7B,CAAC;AACD,mBAAe,MAAM;AACrB,WAAO,WAAW,iBAAiB,CAAC,IAAI,WAAW;AACjD,UAAI,SAAS,MAAM,GAAG;AACpB,mBAAW,QAAQ,MAAM;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,WAAO,qBAAqB,uBAAuB,sBAAsB,QAAQ,IAAI,CAAC;AACtF,WAAO,qBAAqB,qBAAqB,sBAAsB,QAAQ,IAAI,CAAC;AACpF,WAAO,qBAAqB,wBAAwB,sBAAsB,QAAQ,IAAI,CAAC;AAAA,EACzF;AAEA,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,mBAAmB;AAE3D,QAAM,aAAa,UAAQ,KAAK,SAAS;AACzC,QAAM,UAAU,gBAAc,WAAW,WAAW;AACpD,QAAM,sBAAsB,UAAQ;AAClC,UAAM,iBAAiB,CAAC,QAAQ,YAAY;AAC1C,YAAM,KAAK,OAAO,OAAO,IAAI;AAC7B,aAAO,QAAQ,UAAQ,GAAG,OAAO,IAAI,CAAC;AACtC,UAAI,SAAS;AACX,aAAK,OAAO,IAAI,SAAS,IAAI;AAAA,MAC/B,OAAO;AACL,aAAK,OAAO,EAAE;AAAA,MAChB;AAAA,IACF;AACA,UAAM,UAAU,CAAC,QAAQ,SAAS;AAChC,UAAI,WAAW,IAAI,GAAG;AACpB,eAAO;AAAA,UACL,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF,WAAW,CAAC,QAAQ,MAAM,KAAK,CAAC,WAAW,IAAI,GAAG;AAChD,uBAAe,QAAQ,IAAI;AAC3B,eAAO,CAAC;AAAA,MACV,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,aAAa,MAAM,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC;AACrD,QAAI,CAAC,QAAQ,UAAU,GAAG;AACxB,qBAAe,UAAU;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,UAAU,YAAU;AACxB,WAAO,GAAG,WAAW,MAAM;AACzB,YAAM,EAAC,OAAM,IAAI;AACjB,aAAO,cAAc,SAAS,WAAS,OAAO,OAAO,mBAAmB,CAAC;AAAA,IAC3E,CAAC;AAAA,EACH;AAEA,QAAM,cAAc,YAAU;AAC5B,WAAO,GAAG,WAAW,OAAK;AACxB,UAAI,EAAE,YAAY,SAAS,OAAO,SAAS,eAAe,CAAC,GAAG;AAC5D;AAAA,MACF;AACA,aAAO,YAAY,SAAS,MAAM;AAChC,YAAI,EAAE,WAAW,qBAAqB,MAAM,IAAI,oBAAoB,MAAM,GAAG;AAC3E,YAAE,eAAe;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,QAAQ,YAAU;AACtB,QAAI,kBAAkB,MAAM,GAAG;AAC7B,kBAAY,MAAM;AAAA,IACpB;AACA,YAAQ,MAAM;AAAA,EAChB;AAEA,QAAM,2BAA2B,CAAC,QAAQ,aAAa,SAAO;AAC5D,UAAM,sBAAsB,OAAK;AAC/B,UAAI,UAAU,OAAO,EAAE,SAAS,QAAQ,CAAC;AACzC,UAAI,WAAW,CAAC,wBAAwB,QAAQ,EAAE,OAAO,KAAK,OAAO,UAAU,WAAW,CAAC;AAAA,IAC7F;AACA,QAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAC5C,WAAO,qBAAqB,QAAQ,mBAAmB;AAAA,EACzD;AACA,QAAM,aAAa,YAAU;AAC3B,UAAM,OAAO,aAAW,MAAM,OAAO,YAAY,OAAO;AACxD,QAAI,CAAC,OAAO,UAAU,SAAS,GAAG;AAChC,aAAO,GAAG,SAAS,gBAAgB,WAAW;AAAA,QAC5C,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU,KAAK,mBAAmB;AAAA,QAClC,SAAS,yBAAyB,QAAQ,IAAI;AAAA,MAChD,CAAC;AACD,aAAO,GAAG,SAAS,gBAAgB,WAAW;AAAA,QAC5C,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU,KAAK,qBAAqB;AAAA,QACpC,SAAS,yBAAyB,QAAQ,IAAI;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,yBAAyB,CAAC,QAAQ,aAAa,SAAO;AAC1D,UAAM,oBAAoB,OAAK,IAAI,WAAW,OAAO,EAAE,SAAS,QAAQ,KAAK,CAAC,wBAAwB,QAAQ,EAAE,OAAO,CAAC;AACxH,WAAO,qBAAqB,QAAQ,iBAAiB;AAAA,EACvD;AACA,QAAM,WAAW,YAAU;AACzB,UAAM,iBAAiB;AAAA,MACrB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU,MAAM,OAAO,YAAY,cAAc;AAAA,MACjD,SAAS,uBAAuB,QAAQ,IAAI;AAAA,IAC9C;AACA,WAAO,GAAG,SAAS,YAAY,aAAa,cAAc;AAC1D,WAAO,GAAG,SAAS,eAAe,SAAS;AAAA,MACzC,QAAQ,UAAQ;AACd,cAAM,aAAa,cAAc,QAAQ,IAAI;AAC7C,eAAO,SAAS,UAAU,IAAI,CAAC,WAAW,IAAI,CAAC;AAAA,MACjD;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,MAAM;AACjB,aAAS,IAAI,SAAS,YAAU;AAC9B,iBAAW,MAAM;AACjB,cAAQ,MAAM;AACd,UAAI,CAAC,OAAO,UAAU,OAAO,IAAI,GAAG;AAClC,cAAM,MAAM;AACZ,mBAAW,MAAM;AAAA,MACnB,OAAO;AACL,uBAAe,MAAM;AAAA,MACvB;AACA,iBAAW,MAAM;AACjB,eAAS,MAAM;AACf,aAAO,IAAI,MAAM;AAAA,IACnB,CAAC;AAAA,EACH;AAEA,SAAO;AAEX,GAAG;", "names": ["type", "name", "is", "ancestor", "selector", "parent", "empty", "elm", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isSelected", "child", "cast", "get", "set", "entries", "container", "nextS<PERSON>ling", "contains$1"]}