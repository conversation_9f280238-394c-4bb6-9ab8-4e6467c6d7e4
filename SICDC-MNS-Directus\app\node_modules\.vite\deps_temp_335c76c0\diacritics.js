import {
  __commonJS
} from "./chunk-TIUEEL27.js";

// ../node_modules/.pnpm/diacritics@1.3.0/node_modules/diacritics/index.js
var require_diacritics = __commonJS({
  "../node_modules/.pnpm/diacritics@1.3.0/node_modules/diacritics/index.js"(exports) {
    exports.remove = removeDiacritics;
    var replacementList = [
      {
        base: " ",
        chars: " "
      },
      {
        base: "0",
        chars: "߀"
      },
      {
        base: "A",
        chars: "ⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"
      },
      {
        base: "AA",
        chars: "Ꜳ"
      },
      {
        base: "AE",
        chars: "ÆǼǢ"
      },
      {
        base: "AO",
        chars: "Ꜵ"
      },
      {
        base: "AU",
        chars: "Ꜷ"
      },
      {
        base: "AV",
        chars: "ꜸꜺ"
      },
      {
        base: "AY",
        chars: "Ꜽ"
      },
      {
        base: "B",
        chars: "ⒷＢḂḄḆɃƁ"
      },
      {
        base: "C",
        chars: "ⒸＣꜾḈĆCĈĊČÇƇȻ"
      },
      {
        base: "D",
        chars: "ⒹＤḊĎḌḐḒḎĐƊƉᴅꝹ"
      },
      {
        base: "Dh",
        chars: "Ð"
      },
      {
        base: "DZ",
        chars: "ǱǄ"
      },
      {
        base: "Dz",
        chars: "ǲǅ"
      },
      {
        base: "E",
        chars: "ɛⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎᴇ"
      },
      {
        base: "F",
        chars: "ꝼⒻＦḞƑꝻ"
      },
      {
        base: "G",
        chars: "ⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾɢ"
      },
      {
        base: "H",
        chars: "ⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"
      },
      {
        base: "I",
        chars: "ⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"
      },
      {
        base: "J",
        chars: "ⒿＪĴɈȷ"
      },
      {
        base: "K",
        chars: "ⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"
      },
      {
        base: "L",
        chars: "ⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"
      },
      {
        base: "LJ",
        chars: "Ǉ"
      },
      {
        base: "Lj",
        chars: "ǈ"
      },
      {
        base: "M",
        chars: "ⓂＭḾṀṂⱮƜϻ"
      },
      {
        base: "N",
        chars: "ꞤȠⓃＮǸŃÑṄŇṆŅṊṈƝꞐᴎ"
      },
      {
        base: "NJ",
        chars: "Ǌ"
      },
      {
        base: "Nj",
        chars: "ǋ"
      },
      {
        base: "O",
        chars: "ⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"
      },
      {
        base: "OE",
        chars: "Œ"
      },
      {
        base: "OI",
        chars: "Ƣ"
      },
      {
        base: "OO",
        chars: "Ꝏ"
      },
      {
        base: "OU",
        chars: "Ȣ"
      },
      {
        base: "P",
        chars: "ⓅＰṔṖƤⱣꝐꝒꝔ"
      },
      {
        base: "Q",
        chars: "ⓆＱꝖꝘɊ"
      },
      {
        base: "R",
        chars: "ⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"
      },
      {
        base: "S",
        chars: "ⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"
      },
      {
        base: "T",
        chars: "ⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"
      },
      {
        base: "Th",
        chars: "Þ"
      },
      {
        base: "TZ",
        chars: "Ꜩ"
      },
      {
        base: "U",
        chars: "ⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"
      },
      {
        base: "V",
        chars: "ⓋＶṼṾƲꝞɅ"
      },
      {
        base: "VY",
        chars: "Ꝡ"
      },
      {
        base: "W",
        chars: "ⓌＷẀẂŴẆẄẈⱲ"
      },
      {
        base: "X",
        chars: "ⓍＸẊẌ"
      },
      {
        base: "Y",
        chars: "ⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"
      },
      {
        base: "Z",
        chars: "ⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"
      },
      {
        base: "a",
        chars: "ⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐɑ"
      },
      {
        base: "aa",
        chars: "ꜳ"
      },
      {
        base: "ae",
        chars: "æǽǣ"
      },
      {
        base: "ao",
        chars: "ꜵ"
      },
      {
        base: "au",
        chars: "ꜷ"
      },
      {
        base: "av",
        chars: "ꜹꜻ"
      },
      {
        base: "ay",
        chars: "ꜽ"
      },
      {
        base: "b",
        chars: "ⓑｂḃḅḇƀƃɓƂ"
      },
      {
        base: "c",
        chars: "ｃⓒćĉċčçḉƈȼꜿↄ"
      },
      {
        base: "d",
        chars: "ⓓｄḋďḍḑḓḏđƌɖɗƋᏧԁꞪ"
      },
      {
        base: "dh",
        chars: "ð"
      },
      {
        base: "dz",
        chars: "ǳǆ"
      },
      {
        base: "e",
        chars: "ⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇǝ"
      },
      {
        base: "f",
        chars: "ⓕｆḟƒ"
      },
      {
        base: "ff",
        chars: "ﬀ"
      },
      {
        base: "fi",
        chars: "ﬁ"
      },
      {
        base: "fl",
        chars: "ﬂ"
      },
      {
        base: "ffi",
        chars: "ﬃ"
      },
      {
        base: "ffl",
        chars: "ﬄ"
      },
      {
        base: "g",
        chars: "ⓖｇǵĝḡğġǧģǥɠꞡꝿᵹ"
      },
      {
        base: "h",
        chars: "ⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"
      },
      {
        base: "hv",
        chars: "ƕ"
      },
      {
        base: "i",
        chars: "ⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"
      },
      {
        base: "j",
        chars: "ⓙｊĵǰɉ"
      },
      {
        base: "k",
        chars: "ⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"
      },
      {
        base: "l",
        chars: "ⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇɭ"
      },
      {
        base: "lj",
        chars: "ǉ"
      },
      {
        base: "m",
        chars: "ⓜｍḿṁṃɱɯ"
      },
      {
        base: "n",
        chars: "ⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥлԉ"
      },
      {
        base: "nj",
        chars: "ǌ"
      },
      {
        base: "o",
        chars: "ⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿꝋꝍɵɔᴑ"
      },
      {
        base: "oe",
        chars: "œ"
      },
      {
        base: "oi",
        chars: "ƣ"
      },
      {
        base: "oo",
        chars: "ꝏ"
      },
      {
        base: "ou",
        chars: "ȣ"
      },
      {
        base: "p",
        chars: "ⓟｐṕṗƥᵽꝑꝓꝕρ"
      },
      {
        base: "q",
        chars: "ⓠｑɋꝗꝙ"
      },
      {
        base: "r",
        chars: "ⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"
      },
      {
        base: "s",
        chars: "ⓢｓśṥŝṡšṧṣṩșşȿꞩꞅẛʂ"
      },
      {
        base: "ss",
        chars: "ß"
      },
      {
        base: "t",
        chars: "ⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"
      },
      {
        base: "th",
        chars: "þ"
      },
      {
        base: "tz",
        chars: "ꜩ"
      },
      {
        base: "u",
        chars: "ⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"
      },
      {
        base: "v",
        chars: "ⓥｖṽṿʋꝟʌ"
      },
      {
        base: "vy",
        chars: "ꝡ"
      },
      {
        base: "w",
        chars: "ⓦｗẁẃŵẇẅẘẉⱳ"
      },
      {
        base: "x",
        chars: "ⓧｘẋẍ"
      },
      {
        base: "y",
        chars: "ⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"
      },
      {
        base: "z",
        chars: "ⓩｚźẑżžẓẕƶȥɀⱬꝣ"
      }
    ];
    var diacriticsMap = {};
    for (i = 0; i < replacementList.length; i += 1) {
      chars = replacementList[i].chars;
      for (j = 0; j < chars.length; j += 1) {
        diacriticsMap[chars[j]] = replacementList[i].base;
      }
    }
    var chars;
    var j;
    var i;
    function removeDiacritics(str) {
      return str.replace(/[^\u0000-\u007e]/g, function(c) {
        return diacriticsMap[c] || c;
      });
    }
    exports.replacementList = replacementList;
    exports.diacriticsMap = diacriticsMap;
  }
});
export default require_diacritics();
//# sourceMappingURL=diacritics.js.map
