#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0/node_modules/knex/bin/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0/node_modules/knex/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0/node_modules/knex/bin/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0/node_modules/knex/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/knex@3.1.0_mysql@2.18.1_pg@8.11.5_sqlite3@5.1.7_tedious@18.2.0/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../knex/bin/cli.js" "$@"
else
  exec node  "$basedir/../knex/bin/cli.js" "$@"
fi
