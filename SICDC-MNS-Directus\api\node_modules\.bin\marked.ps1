#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\marked@12.0.2\node_modules\marked\bin\node_modules;D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\marked@12.0.2\node_modules\marked\node_modules;D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\marked@12.0.2\node_modules;D:\Projects\SICDC\CUSTOM IMAGE DIRECTUS\SICDC-MNS-Directus\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/marked@12.0.2/node_modules/marked/bin/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/marked@12.0.2/node_modules/marked/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/marked@12.0.2/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../marked/bin/marked.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../marked/bin/marked.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../marked/bin/marked.js" $args
  } else {
    & "node$exe"  "$basedir/../marked/bin/marked.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
