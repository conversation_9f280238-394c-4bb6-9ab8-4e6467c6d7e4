import{g as N}from"./index-C0qcAVKU.js";import{e as T}from"./index.DUmRo3Ep.entry.js";function k(v,w){for(var c=0;c<w.length;c++){const p=w[c];if(typeof p!="string"&&!Array.isArray(p)){for(const u in p)if(u!=="default"&&!(u in v)){const h=Object.getOwnPropertyDescriptor(p,u);h&&Object.defineProperty(v,u,h.get?h:{enumerable:!0,get:()=>p[u]})}}}return Object.freeze(Object.defineProperty(v,Symbol.toStringTag,{value:"Module"}))}var D={exports:{}};(function(v,w){(function(c){c(T())})(function(c){var p=c.Pos;function u(e,n){for(var t=0,r=e.length;t<r;++t)n(e[t])}function h(e,n){if(!Array.prototype.indexOf){for(var t=e.length;t--;)if(e[t]===n)return!0;return!1}return e.indexOf(n)!=-1}function O(e,n,t,r){var a=e.getCursor(),i=t(e,a);if(!/\b(?:string|comment)\b/.test(i.type)){var o=c.innerMode(e.getMode(),i.state);if(o.mode.helperType!=="json"){i.state=o.state,/^[\w$_]*$/.test(i.string)?i.end>a.ch&&(i.end=a.ch,i.string=i.string.slice(0,a.ch-i.start)):i={start:a.ch,end:a.ch,string:"",state:i.state,type:i.string=="."?"property":null};for(var s=i;s.type=="property";){if(s=t(e,p(a.line,s.start)),s.string!=".")return;if(s=t(e,p(a.line,s.start)),!d)var d=[];d.push(s)}return{list:Q(i,d,n,r),from:p(a.line,i.start),to:p(a.line,i.end)}}}}function P(e,n){return O(e,$,function(t,r){return t.getTokenAt(r)},n)}c.registerHelper("hint","javascript",P);function A(e,n){var t=e.getTokenAt(n);return n.ch==t.start+1&&t.string.charAt(0)=="."?(t.end=t.start,t.string=".",t.type="property"):/^\.[\w$_]*$/.test(t.string)&&(t.type="property",t.start++,t.string=t.string.replace(/\./,"")),t}function C(e,n){return O(e,S,A,n)}c.registerHelper("hint","coffeescript",C);var _="charAt charCodeAt indexOf lastIndexOf substring substr slice trim trimLeft trimRight toUpperCase toLowerCase split concat match replace search".split(" "),j="length concat join splice push pop shift unshift slice reverse sort indexOf lastIndexOf every some filter forEach map reduce reduceRight ".split(" "),H="prototype apply call bind".split(" "),$="break case catch class const continue debugger default delete do else export extends false finally for function if in import instanceof new null return super switch this throw true try typeof var void while with yield".split(" "),S="and break catch class continue delete do else extends false finally for if in instanceof isnt new no not null of off on or return switch then throw true try typeof until void while with yes".split(" ");function E(e,n){if(!Object.getOwnPropertyNames||!Object.getPrototypeOf)for(var t in e)n(t);else for(var r=e;r;r=Object.getPrototypeOf(r))Object.getOwnPropertyNames(r).forEach(n)}function Q(e,n,t,r){var a=[],i=e.string,o=r&&r.globalScope||window;function s(y){y.lastIndexOf(i,0)==0&&!h(a,y)&&a.push(y)}function d(y){typeof y=="string"?u(_,s):y instanceof Array?u(j,s):y instanceof Function&&u(H,s),E(y,s)}if(n&&n.length){var g=n.pop(),l;for(g.type&&g.type.indexOf("variable")===0?(r&&r.additionalContext&&(l=r.additionalContext[g.string]),(!r||r.useGlobalScope!==!1)&&(l=l||o[g.string])):g.type=="string"?l="":g.type=="atom"?l=1:g.type=="function"&&(o.jQuery!=null&&(g.string=="$"||g.string=="jQuery")&&typeof o.jQuery=="function"?l=o.jQuery():o._!=null&&g.string=="_"&&typeof o._=="function"&&(l=o._()));l!=null&&n.length;)l=l[n.pop().string];l!=null&&d(l)}else{for(var f=e.state.localVars;f;f=f.next)s(f.name);for(var m=e.state.context;m;m=m.prev)for(var f=m.vars;f;f=f.next)s(f.name);for(var f=e.state.globalVars;f;f=f.next)s(f.name);if(r&&r.additionalContext!=null)for(var I in r.additionalContext)s(I);(!r||r.useGlobalScope!==!1)&&d(o),u(t,s)}return a}})})();var x=D.exports;const F=N(x),L=k({__proto__:null,default:F},[x]);export{L as j};
