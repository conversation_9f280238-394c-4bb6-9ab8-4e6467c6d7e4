import{T as We,w as Me,a as Ce,v as Ue}from"./vue.runtime.esm-bundler-D4Mhr4E9.js";import{q as Ee,u as $e,t as Ie,_ as F,v as ye,w as ue,x as we,M as Oe,y as ve,z as Le,A as Ae,B as je,C as Fe,D as Re,S as qe,E as ze,F as xe,G as He,H as Ze,I as Ge,J as Pe,K as Ye,V as Ne,L as Je}from"./index.DUmRo3Ep.entry.js";import{u as Ke,a as Qe,b as Xe,c as et}from"./index-C0qcAVKU.js";import{storeToRefs as ce}from"./pinia.DFmwLz6V.entry.js";import{useI18n as ae}from"./vue-i18n.8VWz_hPO.entry.js";import{useRouter as Be}from"./vue-router.YxiUBFHd.entry.js";import{d as R,c as k,e as $,bj as Se,w as ne,b5 as _,b6 as K,by as L,aY as s,ad as b,bw as l,af as x,I as oe,y as a,ae as C,ag as W,aN as A,b7 as De,u as v,ak as j,bf as U,b3 as se,aF as tt,b4 as J,aM as nt,n as ot,p as st,ai as at,aO as it,ax as lt,a$ as rt,aZ as ct}from"./runtime-core.esm-bundler-DhTmOO-X.js";const ut=["src"],dt=R({__name:"module-bar-logo",setup(i){const{t:e}=ae(),o=Ee(),r=$e(),n=k(()=>{var u;return r.settings===null||!((u=r.settings)!=null&&u.project_logo)?null:Ie(`${r.settings.project_logo}`)}),t=$(!1),{queueHasItems:f}=Se(o);ne(()=>f.value,u=>{u&&(t.value=!0)});const d=k(()=>{var u;return(u=r.settings)==null?void 0:u.project_url}),g=k(()=>{var u;return(u=r.settings)!=null&&u.project_url?e("view_project"):!1});function p(){f.value===!1&&(t.value=!1)}return(u,S)=>{const y=_("v-progress-linear"),N=K("tooltip");return L((s(),b(De(d.value?"a":"div"),{href:d.value,target:d.value?"_blank":void 0,rel:d.value?"noopener noreferrer":void 0,class:A(["module-bar-logo",{loading:t.value}])},{default:l(()=>[n.value?(s(),x(oe,{key:0},[a(We,{name:"fade"},{default:l(()=>[t.value?(s(),b(y,{key:0,indeterminate:"",rounded:"",onAnimationiteration:p})):C("",!0)]),_:1}),W("img",{class:"custom-logo",src:n.value,alt:"Project Logo"},null,8,ut)],64)):(s(),x("div",{key:1,class:A(["logo",{running:t.value}]),onAnimationiteration:p},null,34))]),_:1},8,["href","target","rel","class"])),[[N,g.value,void 0,{right:!0}]])}}}),vt=F(dt,[["__scopeId","data-v-01202865"]]),_t={class:"module-bar-avatar"},mt=["src","alt"],pt=R({__name:"module-bar-avatar",setup(i){const{t:e}=ae(),o=ye(),r=ue(),{notificationsDrawerOpen:n}=ce(o),{unread:t}=ce(r),f=we(),d=$(!1),g=k(()=>{var N;return!f.currentUser||!("avatar"in f.currentUser)||!((N=f.currentUser)!=null&&N.avatar)?null:Ie(`${f.currentUser.avatar.id}?key=system-medium-cover`)}),p=$(null),u=k(()=>`/users/${f.currentUser.id}`),S=k(()=>"/logout"),y=f.fullName??void 0;return(N,m)=>{const q=_("v-icon"),E=_("v-button"),T=_("v-badge"),Z=_("v-card-title"),Q=_("v-card-actions"),X=_("v-card"),ee=_("v-dialog"),G=_("v-avatar"),ie=_("router-link"),P=_("v-hover"),te=K("tooltip");return s(),x("div",_t,[a(T,{value:v(t),disabled:v(t)==0,class:"notifications-badge"},{default:l(()=>[L((s(),b(E,{tile:"",icon:"","x-large":"",class:"notifications",onClick:m[0]||(m[0]=H=>n.value=!0)},{default:l(()=>[a(q,{name:"notifications"})]),_:1})),[[te,v(e)("notifications"),void 0,{right:!0}]])]),_:1},8,["value","disabled"]),a(P,null,{default:l(({hover:H})=>[a(ee,{modelValue:d.value,"onUpdate:modelValue":m[2]||(m[2]=D=>d.value=D),onEsc:m[3]||(m[3]=D=>d.value=!1)},{activator:l(({on:D})=>[a(We,{name:"sign-out"},{default:l(()=>[H?L((s(),b(E,{key:0,tile:"",icon:"","x-large":"",class:"sign-out",onClick:D},{default:l(()=>[a(q,{name:"logout"})]),_:2},1032,["onClick"])),[[te,v(e)("sign_out"),void 0,{right:!0}]]):C("",!0)]),_:2},1024)]),default:l(()=>[a(X,null,{default:l(()=>[a(Z,null,{default:l(()=>[j(U(v(e)("sign_out_confirm")),1)]),_:1}),a(Q,null,{default:l(()=>[a(E,{secondary:"",onClick:m[1]||(m[1]=D=>d.value=!d.value)},{default:l(()=>[j(U(v(e)("cancel")),1)]),_:1}),a(E,{to:S.value},{default:l(()=>[j(U(v(e)("sign_out")),1)]),_:1},8,["to"])]),_:1})]),_:1})]),_:2},1032,["modelValue"]),a(ie,{to:u.value},{default:l(()=>[L((s(),b(G,{tile:"",large:"",class:A({"no-avatar":!g.value})},{default:l(()=>[g.value&&!p.value?(s(),x("img",{key:0,src:g.value,alt:v(y),class:"avatar-image",onError:m[4]||(m[4]=D=>p.value=D)},null,40,mt)):(s(),b(q,{key:1,name:"account_circle"}))]),_:1},8,["class"])),[[te,v(y),void 0,{right:!0}]])]),_:1},8,["to"])]),_:1})])}}}),ft=F(pt,[["__scopeId","data-v-f4c96e3e"]]),ht={class:"module-bar"},gt={class:"modules"},bt=R({__name:"module-bar",setup(i){const e=$e(),{modules:o}=Ae(),r=k(()=>o.value.map(t=>t.id)),n=k(()=>e.settings?(e.settings.module_bar??Oe).filter(t=>t.type==="link"?!0:t.enabled&&r.value.includes(t.id)).map(t=>{if(t.type==="link"){const d=ve.omit(t,["url"]);return t.url.startsWith("/")?d.to=t.url:d.href=t.url,Le(d)}const f=o.value.find(d=>d.id===t.id);return{...t,...o.value.find(d=>d.id===t.id),to:`/${f.id}`}}):[]);return(t,f)=>{const d=_("v-icon"),g=_("v-button"),p=K("tooltip");return s(),x("div",ht,[a(vt),W("div",gt,[(s(!0),x(oe,null,se(n.value,u=>L((s(),b(g,{key:u.id,icon:"","x-large":"",to:u.to,href:u.href,tile:""},{default:l(()=>[a(d,{name:u.icon},null,8,["name"])]),_:2},1032,["to","href"])),[[p,u.name,void 0,{right:!0}]])),128))]),a(ft)])}}}),yt=F(bt,[["__scopeId","data-v-8655fa43"]]),wt={class:"notification-dialogs"},kt=R({__name:"notification-dialogs",setup(i){const{t:e}=ae(),o=ue();we(),ce($e());const r=k(()=>o.dialogs),n=async t=>{t.dismissAction&&await t.dismissAction(),o.remove(t.id)};return(t,f)=>{const d=_("v-card-title"),g=_("v-error"),p=_("v-card-text"),u=_("v-button"),S=_("v-card-actions"),y=_("v-card"),N=_("v-dialog");return s(),x("div",wt,[(s(!0),x(oe,null,se(r.value,m=>(s(),b(N,{key:m.id,"model-value":"",persist:""},{default:l(()=>[a(y,{class:A([m.type])},{default:l(()=>[a(d,null,{default:l(()=>[j(U(m.title),1)]),_:2},1024),m.text||m.error?(s(),b(p,{key:0},{default:l(()=>[j(" Unable to establish connection. Please try again. "),m.error?(s(),b(g,{key:0,error:m.error},null,8,["error"])):C("",!0)]),_:2},1024)):C("",!0),a(S,null,{default:l(()=>[a(u,{onClick:q=>n(m)},{default:l(()=>[j(U(m.dismissText??v(e)("dismiss")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["class"])]),_:2},1024))),128))])}}}),$t=F(kt,[["__scopeId","data-v-ea288c6a"]]),St={key:1,class:"content"},Mt=["onClick"],xt={key:0,class:"message"},Nt=R({__name:"notifications-drawer",setup(i){const{t:e}=ae(),o=ye(),r=we(),n=je(),{setUnreadCount:t}=ue(),f=Be(),d=$([]),g=$(["inbox"]),p=$([]),u=$(1),S=$(25),y=$(null),N=$({}),m=$(null);ne(g,(M,V)=>{M[0]!==V[0]&&(u.value=1,d.value=[])}),ne([y,N],()=>{u.value=1});function q(M){d.value.includes(M)?d.value.splice(d.value.indexOf(M),1):d.value.push(M)}function E(M){p.value.includes(M)?p.value.splice(p.value.indexOf(M),1):p.value.push(M)}const{notificationsDrawerOpen:T}=ce(o),Z=k(()=>({_and:[{recipient:{_eq:r.currentUser.id}},{status:{_eq:g.value[0]}},N.value]})),{items:Q,loading:X,getItems:ee,totalPages:G,getItemCount:ie}=Ke($("directus_notifications"),{filter:Z,fields:$(["id","subject","message","collection","item","timestamp"]),sort:$(["-timestamp"]),search:y,limit:S,page:u}),P=k(()=>Q.value.map(M=>{var O;let V;if(M.collection){const I=n.getCollection(M.collection);(O=I==null?void 0:I.meta)!=null&&O.singleton||!M.item?V=Fe(M.collection):V=Re(M.collection,M.item)}else String(M.item).startsWith("/")&&(V=String(M.item));return{...M,to:V}}));async function te(){await xe.patch("/notifications",{query:{recipient:{_eq:r.currentUser.id}},data:{status:"archived"}}),await ie(),await ee(),t(0)}async function H(){await xe.patch("/notifications",{keys:d.value,data:{status:g.value[0]==="inbox"?"archived":"inbox"}}),await ie(),await ee(),d.value=[]}function D(M){f.push(M),T.value=!1}return(M,V)=>{const O=_("v-icon"),I=_("v-button"),de=_("v-list-item-icon"),_e=_("v-list-item-content"),me=_("v-tab"),pe=_("v-tabs"),fe=_("v-info"),he=_("v-skeleton-loader"),c=_("v-list"),h=_("v-checkbox"),z=_("v-text-overflow"),le=_("v-list-item"),ge=_("v-pagination"),B=_("v-drawer"),re=K("tooltip"),ke=K("md");return s(),b(B,{modelValue:v(T),"onUpdate:modelValue":V[5]||(V[5]=w=>tt(T)?T.value=w:null),icon:"notifications",title:v(e)("notifications"),"sidebar-label":v(e)("folders"),onCancel:V[6]||(V[6]=w=>T.value=!1)},{actions:l(()=>[a(qe,{modelValue:y.value,"onUpdate:modelValue":V[0]||(V[0]=w=>y.value=w),filter:N.value,"onUpdate:filter":V[1]||(V[1]=w=>N.value=w),collection:"directus_notifications"},null,8,["modelValue","filter"]),L((s(),b(I,{icon:"",rounded:"",disabled:d.value.length===0,secondary:"",onClick:H},{default:l(()=>[a(O,{name:g.value[0]==="inbox"?"archive":"move_to_inbox"},null,8,["name"])]),_:1},8,["disabled"])),[[re,g.value[0]==="inbox"?v(e)("archive"):v(e)("unarchive"),void 0,{bottom:!0}]]),g.value[0]==="inbox"?L((s(),b(I,{key:0,icon:"",rounded:"",disabled:P.value.length===0,onClick:te},{default:l(()=>[a(O,{name:"done_all"})]),_:1},8,["disabled"])),[[re,v(e)("archive_all"),void 0,{bottom:!0}]]):C("",!0)]),sidebar:l(()=>[a(pe,{modelValue:g.value,"onUpdate:modelValue":V[2]||(V[2]=w=>g.value=w),vertical:""},{default:l(()=>[a(me,{value:"inbox"},{default:l(()=>[a(de,null,{default:l(()=>[a(O,{name:"inbox"})]),_:1}),a(_e,null,{default:l(()=>[j(U(v(e)("inbox")),1)]),_:1})]),_:1}),a(me,{value:"archived"},{default:l(()=>[a(de,null,{default:l(()=>[a(O,{name:"archive"})]),_:1}),a(_e,null,{default:l(()=>[j(U(v(e)("archive")),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])]),default:l(()=>[!v(X)&&P.value.length===0?(s(),b(fe,{key:0,icon:"notifications",title:v(e)("no_notifications"),center:""},{default:l(()=>[j(U(v(e)("no_notifications_copy")),1)]),_:1},8,["title"])):(s(),x("div",St,[v(X)?(s(),b(c,{key:0,class:"notifications"},{default:l(()=>[(s(),x(oe,null,se(10,w=>a(he,{key:w,class:A({dense:v(G)>1})},null,8,["class"])),64))]),_:1})):(s(),b(c,{key:1,class:"notifications"},{default:l(()=>[(s(!0),x(oe,null,se(P.value,w=>(s(),b(le,{key:w.id,block:"",dense:v(G)>1,clickable:!!w.message,onMousedown:V[3]||(V[3]=Me(({target:Y})=>m.value=Y,["left","self"])),onMouseup:Me(({target:Y})=>{Y===m.value&&E(w.id),m.value=null},["left","self"])},{default:l(()=>[W("div",{class:"header",onClick:Y=>E(w.id)},[a(h,{"model-value":d.value.includes(w.id),"onUpdate:modelValue":Y=>q(w.id)},null,8,["model-value","onUpdate:modelValue"]),a(z,{class:"title",highlight:y.value,text:w.subject},null,8,["highlight","text"]),a(ze,{value:w.timestamp,type:"timestamp",relative:""},{default:l(({datetime:Y})=>[a(z,{class:"datetime",text:Y},null,8,["text"])]),_:2},1032,["value"]),w.to?L((s(),b(O,{key:0,clickable:"",name:"open_in_new",onClick:Y=>D(w.to)},null,8,["onClick"])),[[re,v(e)("goto_collection_content")]]):C("",!0),w.message?(s(),b(O,{key:1,clickable:"",name:p.value.includes(w.id)?"expand_less":"expand_more"},null,8,["name"])):C("",!0)],8,Mt),p.value.includes(w.id)&&w.message?L((s(),x("div",xt,null,512)),[[ke,w.message]]):C("",!0)]),_:2},1032,["dense","clickable","onMouseup"]))),128))]),_:1})),v(G)>1?(s(),b(ge,{key:2,modelValue:u.value,"onUpdate:modelValue":V[4]||(V[4]=w=>u.value=w),"total-visible":5,length:v(G)},null,8,["modelValue","length"])):C("",!0)]))]),_:1},8,["modelValue","title","sidebar-label"])}}}),Vt=F(Nt,[["__scopeId","data-v-a6f99c83"]]),Wt={key:0,class:"icon"},Ct={class:"content"},It={class:"title selectable"},Bt={key:0,class:"text selectable"},Dt=R({__name:"notification-item",props:{id:{},title:{},text:{},icon:{},type:{default:"info"},tail:{type:Boolean},dense:{type:Boolean},showClose:{type:Boolean},loading:{type:Boolean},progress:{},alwaysShowText:{type:Boolean},dismissText:{},dismissIcon:{},dismissAction:{}},setup(i){const e=i,o=ue(),r=async()=>{e.showClose===!0&&(e.dismissAction&&await e.dismissAction(),o.remove(e.id))};return(n,t)=>{const f=_("v-progress-circular"),d=_("v-icon"),g=K("tooltip");return s(),x("div",{class:A(["notification-item",[n.type,{tail:n.tail,dense:n.dense,"show-text":n.alwaysShowText}]]),onClick:r},[n.loading||n.progress||n.icon?(s(),x("div",Wt,[n.loading?(s(),b(f,{key:0,indeterminate:"",small:""})):n.progress?(s(),b(f,{key:1,small:"",value:n.progress},null,8,["value"])):(s(),b(d,{key:2,name:n.icon},null,8,["name"]))])):C("",!0),W("div",Ct,[W("p",It,U(n.title),1),n.text?(s(),x("p",Bt,U(n.text),1)):C("",!0)]),n.showClose?L((s(),b(d,{key:1,name:n.dismissIcon??"close",clickable:"",class:"close",onClick:r},null,8,["name"])),[[g,n.dismissText]]):C("",!0)],2)}}}),Te=F(Dt,[["__scopeId","data-v-a467eb79"]]),Tt=R({__name:"notifications-group",props:{sidebarOpen:{type:Boolean}},setup(i){const e=ue(),o=Se(e).queue;return(r,n)=>(s(),b(Ce,{class:A(["notifications-group",{"sidebar-open":r.sidebarOpen}]),name:"slide-fade",tag:"div"},{default:l(()=>[J(r.$slots,"default",{},void 0,!0),(s(!0),x(oe,null,se(v(o),(t,f)=>(s(),b(Te,{id:t.id,key:t.id,title:t.title,text:t.text,icon:t.icon,type:t.type,loading:t.loading,progress:t.progress,tail:f===v(o).length-1,dense:r.sidebarOpen===!1,"show-close":t.persist===!0&&t.closeable!==!1,"always-show-text":t.alwaysShowText,"dismiss-icon":t.dismissIcon,"dismiss-text":t.dismissText,"dismiss-action":t.dismissAction},null,8,["id","title","text","icon","type","loading","progress","tail","dense","show-close","always-show-text","dismiss-icon","dismiss-text","dismiss-action"]))),128))]),_:3},8,["class"]))}}),Ut=F(Tt,[["__scopeId","data-v-3c3866b1"]]),Et={class:"icon"},Ot={key:0,class:"title"},Lt=R({__name:"sidebar-button",props:{to:{},icon:{default:"box"},active:{type:Boolean}},emits:["click"],setup(i){const e=ye(),{sidebarOpen:o}=Se(e);return(r,n)=>{const t=_("v-icon");return s(),b(De(r.to?"router-link":"button"),{class:A(["sidebar-button",{active:r.active}]),onClick:n[0]||(n[0]=f=>r.$emit("click",f))},{default:l(()=>[W("div",Et,[a(t,{name:r.icon},null,8,["name"])]),v(o)?(s(),x("div",Ot,[J(r.$slots,"default",{},void 0,!0)])):C("",!0)]),_:3},8,["class"])}}}),At=F(Lt,[["__scopeId","data-v-13be5009"]]),jt={class:"notifications-preview"},Ft={key:0,class:"inline"},Rt={class:"padding-box"},qt=R({__name:"notifications-preview",props:{sidebarOpen:{type:Boolean},modelValue:{type:Boolean}},emits:["update:modelValue"],setup(i){const{t:e}=ae(),o=ue(),{lastFour:r}=ce(o);return(n,t)=>{const f=_("router-link"),d=_("transition-expand"),g=K("tooltip");return s(),x("div",jt,[a(d,{tag:"div"},{default:l(()=>[n.modelValue?(s(),x("div",Ft,[W("div",Rt,[a(f,{class:A(["link",{"has-items":v(r).length>0}]),to:"/activity"},{default:l(()=>[j(U(v(e)("show_all_activity")),1)]),_:1},8,["class"]),a(Ce,{tag:"div",name:"notification",class:"transition"},{default:l(()=>[(s(!0),x(oe,null,se(v(r),p=>(s(),b(Te,nt({key:p.id,ref_for:!0},p),null,16))),128))]),_:1})])])):C("",!0)]),_:1}),L((s(),b(At,{active:n.modelValue,class:"toggle",icon:"pending_actions",onClick:t[0]||(t[0]=p=>n.$emit("update:modelValue",!n.modelValue))},{default:l(()=>[j(U(v(e)("activity_log")),1)]),_:1},8,["active"])),[[g,!n.sidebarOpen&&v(e)("activity_log"),void 0,{left:!0}]])])}}}),zt=F(qt,[["__scopeId","data-v-630dad99"]]),Ve=i=>Number.isFinite(i)?i:0;function Ht(i){return{days:Math.trunc(i/864e5),hours:Math.trunc(i/36e5%24),minutes:Math.trunc(i/6e4%60),seconds:Math.trunc(i/1e3%60),milliseconds:Math.trunc(i%1e3),microseconds:Math.trunc(Ve(i*1e3)%1e3),nanoseconds:Math.trunc(Ve(i*1e6)%1e3)}}function Zt(i){return{days:i/86400000n,hours:i/3600000n%24n,minutes:i/60000n%60n,seconds:i/1000n%60n,milliseconds:i%1000n,microseconds:0n,nanoseconds:0n}}function Gt(i){switch(typeof i){case"number":{if(Number.isFinite(i))return Ht(i);break}case"bigint":return Zt(i)}throw new TypeError("Expected a finite number or bigint")}const Pt=i=>i===0||i===0n,Yt=(i,e)=>e===1||e===1n?i:`${i}s`,Jt=1e-7,Kt=24n*60n*60n*1000n;function be(i,e){const o=typeof i=="bigint";if(!o&&!Number.isFinite(i))throw new TypeError("Expected a finite number or bigint");e={...e},e.colonNotation&&(e.compact=!1,e.formatSubMilliseconds=!1,e.separateMilliseconds=!1,e.verbose=!1),e.compact&&(e.unitCount=1,e.secondsDecimalDigits=0,e.millisecondsDecimalDigits=0);let r=[];const n=(p,u)=>{const S=Math.floor(p*10**u+Jt);return(Math.round(S)/10**u).toFixed(u)},t=(p,u,S,y)=>{if(!((r.length===0||!e.colonNotation)&&Pt(p)&&!(e.colonNotation&&S==="m"))){if(y=y??String(p),e.colonNotation){const N=y.includes(".")?y.split(".")[0].length:y.length,m=r.length>0?2:1;y="0".repeat(Math.max(0,m-N))+y}else y+=e.verbose?" "+Yt(u,p):S;r.push(y)}},f=Gt(i),d=BigInt(f.days);if(t(d/365n,"year","y"),t(d%365n,"day","d"),t(Number(f.hours),"hour","h"),t(Number(f.minutes),"minute","m"),e.separateMilliseconds||e.formatSubMilliseconds||!e.colonNotation&&i<1e3){const p=Number(f.seconds),u=Number(f.milliseconds),S=Number(f.microseconds),y=Number(f.nanoseconds);if(t(p,"second","s"),e.formatSubMilliseconds)t(u,"millisecond","ms"),t(S,"microsecond","µs"),t(y,"nanosecond","ns");else{const N=u+S/1e3+y/1e6,m=typeof e.millisecondsDecimalDigits=="number"?e.millisecondsDecimalDigits:0,q=N>=1?Math.round(N):Math.ceil(N),E=m?N.toFixed(m):q;t(Number.parseFloat(E),"millisecond","ms",E)}}else{const p=(o?Number(i%Kt):i)/1e3%60,u=typeof e.secondsDecimalDigits=="number"?e.secondsDecimalDigits:1,S=n(p,u),y=e.keepDecimalsOnWholeSeconds?S:S.replace(/\.0+$/,"");t(Number.parseFloat(y),"second","s",y)}if(r.length===0)return"0"+(e.verbose?" milliseconds":"ms");const g=e.colonNotation?":":" ";return typeof e.unitCount=="number"&&(r=r.slice(0,Math.max(e.unitCount,1))),r.join(g)}const Qt={class:"latency-indicator"},Xt=R({__name:"latency-indicator",setup(i){const{t:e}=ae(),o=He(),r=k(()=>{const g=ve.sortBy(o.latency,["timestamp"]);return g[g.length-1]}),n=k(()=>{if(!o.latency||o.latency.length===0)return 0;const g=ve.sortBy(o.latency,["timestamp"]),p=g.slice(Math.max(g.length-5,0));let u=0;for(const{latency:S}of p)u+=S;return Math.round(u/p.length)}),t=k(()=>n.value<=250?4:n.value>250&&n.value<=500?3:n.value>500&&n.value<=750?2:1),f=k(()=>{switch(t.value){case 4:return`${e("connection_excellent")}
(${be(n.value)} ${e("latency")})`;case 3:return`${e("connection_good")}
(${be(n.value)} ${e("latency")})`;case 2:return`${e("connection_fair")}
(${be(n.value)} ${e("latency")})`;case 1:return`${e("connection_poor")}
(${be(n.value)} ${e("latency")})`;default:return null}}),d=k(()=>{switch(t.value){case 4:return"signal_wifi_4_bar";case 3:return"signal_wifi_3_bar";case 2:return"signal_wifi_2_bar";case 1:return"signal_wifi_1_bar";default:return null}});return(g,p)=>{const u=_("v-progress-circular"),S=_("v-icon"),y=K("tooltip");return L((s(),x("div",Qt,[r.value?(s(),b(S,{key:1,name:d.value},null,8,["name"])):(s(),b(u,{key:0,indeterminate:""}))])),[[y,f.value,void 0,{bottom:!0,end:!0}]])}}}),en=F(Xt,[["__scopeId","data-v-b16b807a"]]),tn={class:"project-info"},nn={class:"name-container"},on=R({__name:"project-info",setup(i){const e=Ze(),o=k(()=>{var n,t;return(t=(n=e.info)==null?void 0:n.project)==null?void 0:t.project_name}),r=k(()=>{var n,t;return(t=(n=e.info)==null?void 0:n.project)==null?void 0:t.project_descriptor});return(n,t)=>{const f=_("v-text-overflow");return s(),x("div",tn,[a(en),W("div",nn,[a(f,{placement:"right",class:"name",text:o.value},null,8,["text"]),r.value?(s(),b(f,{key:0,placement:"right",class:"descriptor",text:r.value},null,8,["text"])):C("",!0)])])}}}),sn=F(on,[["__scopeId","data-v-7837af12"]]),an=R({__name:"sidebar-detail-group",props:{sidebarOpen:{type:Boolean}},setup(i){const e=i,o=$([]),r=$(!1);return ne(()=>e.sidebarOpen,async n=>{n===!1?o.value=[]:(r.value=!0,await ot(),r.value=!1)}),(n,t)=>{const f=_("v-item-group");return s(),b(f,{modelValue:o.value,"onUpdate:modelValue":t[0]||(t[0]=d=>o.value=d),class:"sidebar-detail-group",scope:"sidebar-detail",mandatory:r.value},{default:l(()=>[J(n.$slots,"default",{},void 0,!0)]),_:3},8,["modelValue","mandatory"])}}}),ln=F(an,[["__scopeId","data-v-cb0557f3"]]),rn=i=>(rt("data-v-717e06b2"),i=i(),ct(),i),cn={class:"module-nav alt-colors"},un={class:"module-nav-content"},dn={class:"content-wrapper"},vn={class:"flex-container"},_n=rn(()=>W("div",{class:"spacer"},null,-1)),mn=R({__name:"private-view",props:{title:{},smallHeader:{type:Boolean},headerShadow:{type:Boolean,default:!0},splitView:{type:Boolean},splitViewMinWidth:{default:0},sidebarShadow:{type:Boolean}},emits:["update:splitView"],setup(i,{emit:e}){var fe,he;const o={moduleBarWidth:60,minModuleNavWidth:220,minContentWidth:590,collapsedSidebarWidth:60,overlaySpace:60},r=i,n=e,{t}=ae(),f=Be(),d=k(()=>r.title??null),g=Qe(r,"splitView",n),p=$(),u=$(),S=$();let y;const N=()=>{var c;clearTimeout(y),(c=p.value)==null||c.classList.remove("hide-overflow-x")};ne(g,()=>{if(!p.value||!u.value)return;p.value.classList.add("hide-overflow-x","hide-overflow-y"),y=setTimeout(N,1500);let c,h;const z=()=>{var le;clearTimeout(c),h==null||h(),(le=p.value)==null||le.classList.remove("hide-overflow-y")};c=setTimeout(z,1500),h=Ge(u.value,"transitionend",z)});const{width:m}=Pe(),{width:q}=Xe(S),E=k(()=>{if(!g.value)return!0;let c;return m.value>=1260?c=m.value-o.moduleBarWidth-o.minModuleNavWidth-o.minContentWidth-q.value:m.value>=960?c=m.value-o.moduleBarWidth-o.minModuleNavWidth-o.minContentWidth-o.collapsedSidebarWidth:c=m.value-o.minContentWidth,c>=r.splitViewMinWidth}),{data:T}=Ye("module-width",{}),Z=$(pe((fe=T.value)==null?void 0:fe.nav,o.minModuleNavWidth));ne(Z,ve.debounce(c=>{T.value={...T.value??{},nav:c}},300));const Q=$(pe((he=T.value)==null?void 0:he.main,o.minContentWidth));ne(Q,ve.debounce(c=>{T.value={...T.value??{},main:c}},300));const X=$(!1),ee=k(()=>{const c=g.value?r.splitViewMinWidth:0,h=E.value?o.minContentWidth+c:o.minContentWidth;let z;return m.value>=1260?z=m.value-o.moduleBarWidth-h-q.value:m.value>=960?z=m.value-o.moduleBarWidth-h-o.collapsedSidebarWidth:z=m.value-o.moduleBarWidth+o.overlaySpace,Math.max(z,o.minModuleNavWidth)}),G=k(()=>{const c=g.value?r.splitViewMinWidth:0;let h;return m.value>=1260?h=m.value-o.moduleBarWidth-Z.value-c-q.value:m.value>=960?h=m.value-o.moduleBarWidth-Z.value-c-o.collapsedSidebarWidth:h=m.value-c,Math.max(h,o.minContentWidth)}),ie=k(()=>({snapZones:[{width:40,snapPos:o.minModuleNavWidth,direction:"left"},{width:40,snapPos:ee.value,direction:"right"}]})),P=$(!1),te=k(()=>({snapZones:[{width:40,snapPos:o.minContentWidth,direction:"left"},{width:40,snapPos:G.value,direction:"right"}],alwaysShowHandle:!0,disableTransition:X.value})),H=$(!1),D=we(),M=ye(),V=k(()=>{var c,h;return D.currentUser?((h=(c=D.currentUser)==null?void 0:c.role)==null?void 0:h.app_access)||!1:!0}),O=$(!1),{sidebarOpen:I,fullScreen:de}=ce(M),_e=k(()=>D.currentUser&&"appearance"in D.currentUser?D.currentUser.appearance:"auto");st("main-element",p),f.afterEach(()=>{var c;(c=p.value)==null||c.scrollTo({top:0}),de.value=!1}),et({title:d});function me(c){c.target&&c.target.classList.contains("close")===!1&&(I.value=!0)}function pe(c,h){return c&&!Number.isNaN(c)?Number(c):h}return(c,h)=>{const z=_("v-button"),le=_("v-info"),ge=_("v-overlay");return V.value===!1?(s(),b(le,{key:0,center:"",title:v(t)("no_app_access"),type:"danger",icon:"block"},{append:l(()=>[a(z,{to:"/logout"},{default:l(()=>[j(U(v(t)("switch_user")),1)]),_:1})]),default:l(()=>[j(U(v(t)("no_app_access_copy"))+" ",1)]),_:1},8,["title"])):(s(),x("div",{key:1,class:A(["private-view",{appearance:_e.value,"full-screen":v(de),splitView:c.splitView}])},[W("aside",{id:"navigation",role:"navigation","aria-label":"Module Navigation",class:A({"is-open":H.value,"has-shadow":c.sidebarShadow})},[a(yt),a(Ne,{width:Z.value,"onUpdate:width":h[0]||(h[0]=B=>Z.value=B),"min-width":o.minModuleNavWidth,"max-width":ee.value,options:ie.value,onDragging:h[1]||(h[1]=B=>X.value=B),onTransitionEnd:N},{default:l(()=>[W("div",cn,[a(sn),W("div",un,[J(c.$slots,"navigation",{},void 0,!0)])])]),_:3},8,["width","min-width","max-width","options"])],2),W("div",{id:"main-content",ref_key:"contentEl",ref:p,class:"content"},[a(Je,{ref_key:"headerBarEl",ref:u,small:c.smallHeader||c.splitView,shadow:c.headerShadow||c.splitView,"show-sidebar-toggle":"",title:c.title,"onToggle:sidebar":h[2]||(h[2]=B=>I.value=!v(I)),onPrimary:h[3]||(h[3]=B=>H.value=!H.value)},at({_:2},[se(c.$slots,(B,re)=>({name:re,fn:l(ke=>[J(c.$slots,re,it(lt(ke)),void 0,!0)])}))]),1032,["small","shadow","title"]),W("div",dn,[a(Ne,{width:Q.value,"onUpdate:width":h[4]||(h[4]=B=>Q.value=B),"min-width":o.minContentWidth,"max-width":G.value,disabled:!v(g),options:te.value,onDragging:h[5]||(h[5]=B=>P.value=B)},{default:l(()=>[L(W("main",null,[J(c.$slots,"default",{},void 0,!0)],512),[[Ue,E.value]])]),_:3},8,["width","min-width","max-width","disabled","options"]),c.splitView?(s(),x("div",{key:0,id:"split-content",class:A({"is-dragging":P.value})},[J(c.$slots,"splitView",{},void 0,!0)],2)):C("",!0)])],512),W("aside",{id:"sidebar",ref_key:"sidebarEl",ref:S,role:"contentinfo",class:A(["alt-colors",{"is-open":v(I),"has-shadow":c.sidebarShadow}]),"aria-label":"Module Sidebar",onClick:me},[W("div",vn,[a(ln,{"sidebar-open":v(I)},{default:l(()=>[J(c.$slots,"sidebar",{},void 0,!0)]),_:3},8,["sidebar-open"]),_n,a(zt,{modelValue:O.value,"onUpdate:modelValue":h[6]||(h[6]=B=>O.value=B),"sidebar-open":v(I)},null,8,["modelValue","sidebar-open"])])],2),a(ge,{class:"nav-overlay",active:H.value,onClick:h[7]||(h[7]=B=>H.value=!1)},null,8,["active"]),a(ge,{class:"sidebar-overlay",active:v(I),onClick:h[8]||(h[8]=B=>I.value=!1)},null,8,["active"]),a(Vt),O.value===!1?(s(),b(Ut,{key:0,"sidebar-open":v(I)},null,8,["sidebar-open"])):C("",!0),a($t)],2))}}}),kn=F(mn,[["__scopeId","data-v-717e06b2"]]);export{kn as PrivateView,kn as default};
