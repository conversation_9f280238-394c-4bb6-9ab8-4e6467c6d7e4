import {
  __commonJS,
  __publicField
} from "./chunk-TIUEEL27.js";

// ../node_modules/.pnpm/editorjs-toggle-block@0.3.16/node_modules/editorjs-toggle-block/dist/bundle.js
var require_bundle = __commonJS({
  "../node_modules/.pnpm/editorjs-toggle-block@0.3.16/node_modules/editorjs-toggle-block/dist/bundle.js"(exports, module) {
    !function(t, e) {
      "object" == typeof exports && "object" == typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define([], e) : "object" == typeof exports ? exports.ToggleBlock = e() : t.ToggleBlock = e();
    }(self, () => (() => {
      var t = { 424: (t2, e2, o2) => {
        "use strict";
        o2.d(e2, { Z: () => l });
        var s2 = o2(81), i = o2.n(s2), r = o2(645), n = o2.n(r)()(i());
        n.push([t2.id, ".toggle-block__selector > div {\n  vertical-align: middle;\n  display: inline-block;\n  padding: 1% 0 1% 0;\n  outline: none;\n  border: none;\n  width: 90%;\n}\n\n.toggle-block__selector br {\n  display: none;\n}\n\n.toggle-block__icon > svg {\n  vertical-align: middle;\n  width: 15px;\n  height: auto;\n}\n\n.toggle-block__icon:hover {\n  color: #388ae5;\n  cursor: pointer;\n}\n\n.bi-play-fill {\n  width: 34px;\n  height: 34px;\n}\n\n.toggle-block__input {\n  margin-left: 5px;\n}\n\n.toggle-block__input:empty:before {\n  content: attr(placeholder);\n  color: gray;\n  background-color: transparent;\n}\n\n.toggle-block__content-default {\n  margin-left: 20px;\n}\n\n.toggle-block__item {\n  margin-left: 39px;\n}\n\n.toggle-block__content-default {\n  color: gray;\n  border-radius: 5px;\n}\n\n.toggle-block__content-default:hover {\n  cursor: pointer;\n  background: rgba(55, 53, 47, 0.08);\n}\n\ndiv.toggle-block__hidden {\n  display: none;\n}\n", ""]);
        const l = n;
      }, 645: (t2) => {
        "use strict";
        t2.exports = function(t3) {
          var e2 = [];
          return e2.toString = function() {
            return this.map(function(e3) {
              var o2 = "", s2 = void 0 !== e3[5];
              return e3[4] && (o2 += "@supports (".concat(e3[4], ") {")), e3[2] && (o2 += "@media ".concat(e3[2], " {")), s2 && (o2 += "@layer".concat(e3[5].length > 0 ? " ".concat(e3[5]) : "", " {")), o2 += t3(e3), s2 && (o2 += "}"), e3[2] && (o2 += "}"), e3[4] && (o2 += "}"), o2;
            }).join("");
          }, e2.i = function(t4, o2, s2, i, r) {
            "string" == typeof t4 && (t4 = [[null, t4, void 0]]);
            var n = {};
            if (s2)
              for (var l = 0; l < this.length; l++) {
                var c = this[l][0];
                null != c && (n[c] = true);
              }
            for (var d = 0; d < t4.length; d++) {
              var a = [].concat(t4[d]);
              s2 && n[a[0]] || (void 0 !== r && (void 0 === a[5] || (a[1] = "@layer".concat(a[5].length > 0 ? " ".concat(a[5]) : "", " {").concat(a[1], "}")), a[5] = r), o2 && (a[2] ? (a[1] = "@media ".concat(a[2], " {").concat(a[1], "}"), a[2] = o2) : a[2] = o2), i && (a[4] ? (a[1] = "@supports (".concat(a[4], ") {").concat(a[1], "}"), a[4] = i) : a[4] = "".concat(i)), e2.push(a));
            }
          }, e2;
        };
      }, 81: (t2) => {
        "use strict";
        t2.exports = function(t3) {
          return t3[1];
        };
      }, 379: (t2) => {
        "use strict";
        var e2 = [];
        function o2(t3) {
          for (var o3 = -1, s3 = 0; s3 < e2.length; s3++)
            if (e2[s3].identifier === t3) {
              o3 = s3;
              break;
            }
          return o3;
        }
        function s2(t3, s3) {
          for (var r = {}, n = [], l = 0; l < t3.length; l++) {
            var c = t3[l], d = s3.base ? c[0] + s3.base : c[0], a = r[d] || 0, h = "".concat(d, " ").concat(a);
            r[d] = a + 1;
            var g = o2(h), u = { css: c[1], media: c[2], sourceMap: c[3], supports: c[4], layer: c[5] };
            if (-1 !== g)
              e2[g].references++, e2[g].updater(u);
            else {
              var p = i(u, s3);
              s3.byIndex = l, e2.splice(l, 0, { identifier: h, updater: p, references: 1 });
            }
            n.push(h);
          }
          return n;
        }
        function i(t3, e3) {
          var o3 = e3.domAPI(e3);
          return o3.update(t3), function(e4) {
            if (e4) {
              if (e4.css === t3.css && e4.media === t3.media && e4.sourceMap === t3.sourceMap && e4.supports === t3.supports && e4.layer === t3.layer)
                return;
              o3.update(t3 = e4);
            } else
              o3.remove();
          };
        }
        t2.exports = function(t3, i2) {
          var r = s2(t3 = t3 || [], i2 = i2 || {});
          return function(t4) {
            t4 = t4 || [];
            for (var n = 0; n < r.length; n++) {
              var l = o2(r[n]);
              e2[l].references--;
            }
            for (var c = s2(t4, i2), d = 0; d < r.length; d++) {
              var a = o2(r[d]);
              0 === e2[a].references && (e2[a].updater(), e2.splice(a, 1));
            }
            r = c;
          };
        };
      }, 569: (t2) => {
        "use strict";
        var e2 = {};
        t2.exports = function(t3, o2) {
          var s2 = function(t4) {
            if (void 0 === e2[t4]) {
              var o3 = document.querySelector(t4);
              if (window.HTMLIFrameElement && o3 instanceof window.HTMLIFrameElement)
                try {
                  o3 = o3.contentDocument.head;
                } catch (t5) {
                  o3 = null;
                }
              e2[t4] = o3;
            }
            return e2[t4];
          }(t3);
          if (!s2)
            throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");
          s2.appendChild(o2);
        };
      }, 216: (t2) => {
        "use strict";
        t2.exports = function(t3) {
          var e2 = document.createElement("style");
          return t3.setAttributes(e2, t3.attributes), t3.insert(e2, t3.options), e2;
        };
      }, 565: (t2, e2, o2) => {
        "use strict";
        t2.exports = function(t3) {
          var e3 = o2.nc;
          e3 && t3.setAttribute("nonce", e3);
        };
      }, 795: (t2) => {
        "use strict";
        t2.exports = function(t3) {
          if ("undefined" == typeof document)
            return { update: function() {
            }, remove: function() {
            } };
          var e2 = t3.insertStyleElement(t3);
          return { update: function(o2) {
            !function(t4, e3, o3) {
              var s2 = "";
              o3.supports && (s2 += "@supports (".concat(o3.supports, ") {")), o3.media && (s2 += "@media ".concat(o3.media, " {"));
              var i = void 0 !== o3.layer;
              i && (s2 += "@layer".concat(o3.layer.length > 0 ? " ".concat(o3.layer) : "", " {")), s2 += o3.css, i && (s2 += "}"), o3.media && (s2 += "}"), o3.supports && (s2 += "}");
              var r = o3.sourceMap;
              r && "undefined" != typeof btoa && (s2 += "\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(r)))), " */")), e3.styleTagTransform(s2, t4, e3.options);
            }(e2, t3, o2);
          }, remove: function() {
            !function(t4) {
              if (null === t4.parentNode)
                return false;
              t4.parentNode.removeChild(t4);
            }(e2);
          } };
        };
      }, 589: (t2) => {
        "use strict";
        t2.exports = function(t3, e2) {
          if (e2.styleSheet)
            e2.styleSheet.cssText = t3;
          else {
            for (; e2.firstChild; )
              e2.removeChild(e2.firstChild);
            e2.appendChild(document.createTextNode(t3));
          }
        };
      }, 370: (t2) => {
        t2.exports = '<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-play-fill" viewBox="0 0 16 16"><path d="m11.596 8.697-6.363 3.692c-.54.313-1.233-.066-1.233-.697V4.308c0-.63.692-1.01 1.233-.696l6.363 3.692a.802.802 0 0 1 0 1.393z"></path></svg>';
      } }, e = {};
      function o(s2) {
        var i = e[s2];
        if (void 0 !== i)
          return i.exports;
        var r = e[s2] = { id: s2, exports: {} };
        return t[s2](r, r.exports, o), r.exports;
      }
      o.n = (t2) => {
        var e2 = t2 && t2.__esModule ? () => t2.default : () => t2;
        return o.d(e2, { a: e2 }), e2;
      }, o.d = (t2, e2) => {
        for (var s2 in e2)
          o.o(e2, s2) && !o.o(t2, s2) && Object.defineProperty(t2, s2, { enumerable: true, get: e2[s2] });
      }, o.o = (t2, e2) => Object.prototype.hasOwnProperty.call(t2, e2), o.nc = void 0;
      var s = {};
      return (() => {
        "use strict";
        o.d(s, { default: () => I });
        var t2 = o(379), e2 = o.n(t2), i = o(795), r = o.n(i), n = o(569), l = o.n(n), c = o(565), d = o.n(c), a = o(216), h = o.n(a), g = o(589), u = o.n(g), p = o(424), f = {};
        f.styleTagTransform = u(), f.setAttributes = d(), f.insert = l().bind(null, "head"), f.domAPI = r(), f.insertStyleElement = h(), e2()(p.Z, f), p.Z && p.Z.locals && p.Z.locals;
        const m = { randomUUID: "undefined" != typeof crypto && crypto.randomUUID && crypto.randomUUID.bind(crypto) };
        let b;
        const k = new Uint8Array(16);
        function y() {
          if (!b && (b = "undefined" != typeof crypto && crypto.getRandomValues && crypto.getRandomValues.bind(crypto), !b))
            throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
          return b(k);
        }
        const v = [];
        for (let t3 = 0; t3 < 256; ++t3)
          v.push((t3 + 256).toString(16).slice(1));
        const B = function(t3, e3, o2) {
          if (m.randomUUID && !e3 && !t3)
            return m.randomUUID();
          const s2 = (t3 = t3 || {}).random || (t3.rng || y)();
          if (s2[6] = 15 & s2[6] | 64, s2[8] = 63 & s2[8] | 128, e3) {
            o2 = o2 || 0;
            for (let t4 = 0; t4 < 16; ++t4)
              e3[o2 + t4] = s2[t4];
            return e3;
          }
          return function(t4, e4 = 0) {
            return (v[t4[e4 + 0]] + v[t4[e4 + 1]] + v[t4[e4 + 2]] + v[t4[e4 + 3]] + "-" + v[t4[e4 + 4]] + v[t4[e4 + 5]] + "-" + v[t4[e4 + 6]] + v[t4[e4 + 7]] + "-" + v[t4[e4 + 8]] + v[t4[e4 + 9]] + "-" + v[t4[e4 + 10]] + v[t4[e4 + 11]] + v[t4[e4 + 12]] + v[t4[e4 + 13]] + v[t4[e4 + 14]] + v[t4[e4 + 15]]).toLowerCase();
          }(s2);
        };
        var A = o(370), x = o.n(A);
        class I {
          constructor({ data: t3, api: e3, readOnly: o2, config: s2 }) {
            __publicField(this, "getIndex", (t3) => Array.from(t3.parentNode.children).indexOf(t3));
            __publicField(this, "isChild", (t3, e3) => !(!t3 || !e3) && (t3 === e3 || [...document.querySelectorAll(`div[foreignKey="${t3}"]`)].some((t4) => {
              const o2 = t4.querySelector(".toggle-block__selector");
              return !!o2 && this.isChild(o2.getAttribute("id"), e3);
            })));
            this.data = { text: t3.text || "", status: t3.status || "open", fk: t3.fk || `fk-${B()}`, items: t3.items || 0 }, this.itemsId = [], this.api = e3;
            const { toolbar: { close: i2 }, blocks: { getCurrentBlockIndex: r2, getBlockByIndex: n2, getBlocksCount: l2, move: c2 } } = this.api;
            this.close = i2, this.getCurrentBlockIndex = r2, this.getBlocksCount = l2, this.getBlockByIndex = n2, this.move = c2, this.wrapper = void 0, this.readOnly = o2 || false, this.placeholder = (s2 == null ? void 0 : s2.placeholder) ?? "Toggle", this.defaultContent = (s2 == null ? void 0 : s2.defaultContent) ?? "Empty toggle. Click or drop blocks inside.", this.addListeners(), this.addSupportForUndoAndRedoActions(), this.addSupportForDragAndDropActions(), this.addSupportForCopyAndPasteAction();
          }
          static get toolbox() {
            return { title: "Toggle", icon: x() };
          }
          static get enableLineBreaks() {
            return true;
          }
          static get isReadOnlySupported() {
            return true;
          }
          isAToggleItem(t3) {
            return t3.classList.contains("toggle-block__item");
          }
          isAToggleRoot(t3) {
            return t3.classList.contains("toggle-block__selector") || Boolean(t3.querySelector(".toggle-block__selector"));
          }
          createParagraphFromToggleRoot(t3) {
            if ("Enter" === t3.code) {
              const t4 = document.getSelection().focusOffset, e3 = this.api.blocks.getCurrentBlockIndex(), o2 = this.api.blocks.getBlockByIndex(e3), { holder: s2 } = o2, i2 = s2.firstChild.firstChild, r2 = i2.children[1].innerHTML, n2 = r2.indexOf("<br>"), l2 = -1 === n2 ? r2.length : n2;
              "closed" === this.data.status && (this.resolveToggleAction(), this.hideAndShowBlocks());
              const c2 = r2.slice(l2 + 4, t4.focusOffset);
              i2.children[1].innerHTML = r2.slice(t4.focusOffset, l2), this.api.blocks.insert("paragraph", { text: c2 }, {}, e3 + 1, 1), this.setAttributesToNewBlock();
            }
          }
          createParagraphFromIt() {
            this.setAttributesToNewBlock();
          }
          setAttributesToNewBlock(t3 = null, e3 = this.wrapper.id, o2 = null) {
            const s2 = null === t3 ? this.api.blocks.getCurrentBlockIndex() : t3, i2 = o2 || this.api.blocks.getBlockByIndex(s2), r2 = B();
            this.itemsId.includes(i2.id) || this.itemsId.splice(s2 - 1, 0, i2.id);
            const { holder: n2 } = i2, l2 = n2.firstChild.firstChild;
            n2.setAttribute("foreignKey", e3), n2.setAttribute("id", r2), setTimeout(() => n2.classList.add("toggle-block__item")), this.readOnly || (n2.onkeydown = this.setEventsToNestedBlock.bind(this), l2.focus());
          }
          setEventsToNestedBlock(t3) {
            if ("Enter" === t3.code)
              setTimeout(() => this.createParagraphFromIt());
            else {
              const e3 = this.getCurrentBlockIndex(), o2 = this.getBlockByIndex(e3), { holder: s2 } = o2;
              if ("Tab" === t3.code && t3.shiftKey && this.extractBlock(e3), "Backspace" === t3.code) {
                const t4 = document.getSelection().focusOffset;
                this.removeBlock(s2, o2.id, t4);
              }
            }
          }
          removeBlock(t3, e3, o2) {
            if (0 === o2) {
              const t4 = this.itemsId.indexOf(e3);
              this.itemsId.splice(t4, 1);
            }
          }
          removeAttributesFromNewBlock(t3) {
            const e3 = this.api.blocks.getBlockByIndex(t3), { holder: o2 } = e3;
            if (!this.itemsId.includes(e3.id)) {
              const t4 = this.itemsId.indexOf(e3.id);
              this.itemsId.splice(t4, 1);
            }
            o2.removeAttribute("foreignKey"), o2.removeAttribute("id"), o2.onkeydown = {}, o2.onkeyup = {}, o2.classList.remove("toggle-block__item");
          }
          createToggle() {
            this.wrapper = document.createElement("div"), this.wrapper.classList.add("toggle-block__selector"), this.wrapper.id = this.data.fk;
            const t3 = document.createElement("span"), e3 = document.createElement("div"), o2 = document.createElement("div");
            t3.classList.add("toggle-block__icon"), t3.innerHTML = x(), e3.classList.add("toggle-block__input"), e3.setAttribute("contentEditable", !this.readOnly), e3.innerHTML = this.data.text || "", this.readOnly || (e3.addEventListener("keyup", this.createParagraphFromToggleRoot.bind(this)), e3.addEventListener("keydown", this.removeToggle.bind(this)), e3.addEventListener("focusin", () => this.setFocusToggleRootAtTheEnd()), e3.addEventListener("keyup", this.setPlaceHolder.bind(this)), e3.setAttribute("placeholder", this.placeholder), e3.addEventListener("focus", this.setDefaultContent.bind(this)), e3.addEventListener("focusout", this.setDefaultContent.bind(this)), o2.addEventListener("click", this.clickInDefaultContent.bind(this)), e3.addEventListener("focus", this.setNestedBlockAttributes.bind(this))), o2.classList.add("toggle-block__content-default", "toggle-block__hidden"), o2.innerHTML = this.defaultContent, this.wrapper.appendChild(t3), this.wrapper.appendChild(e3), this.wrapper.appendChild(o2);
          }
          setFocusToggleRootAtTheEnd() {
            const t3 = document.activeElement, e3 = window.getSelection(), o2 = document.createRange();
            e3.removeAllRanges(), o2.selectNodeContents(t3), o2.collapse(false), e3.addRange(o2), t3.focus();
          }
          clickInDefaultContent() {
            this.api.blocks.insert(), this.setAttributesToNewBlock(), this.setDefaultContent();
          }
          setDefaultContent() {
            const t3 = document.querySelectorAll(`div[foreignKey="${this.wrapper.id}"]`), { firstChild: e3, lastChild: o2 } = this.wrapper, { status: s2 } = this.data, i2 = t3.length > 0 || "closed" === s2;
            o2.classList.toggle("toggle-block__hidden", i2), e3.style.color = 0 === t3.length ? "gray" : "black";
          }
          removeToggle(t3) {
            if ("Backspace" === t3.code) {
              const { children: t4 } = this.wrapper, e3 = t4[1].innerHTML;
              if (0 === document.getSelection().focusOffset) {
                const t5 = this.api.blocks.getCurrentBlockIndex(), o2 = e3.indexOf("<br>"), s2 = -1 === o2 ? e3.length : o2, i2 = document.querySelectorAll(`div[foreignKey="${this.wrapper.id}"]`);
                for (let e4 = 1; e4 < i2.length + 1; e4 += 1)
                  this.removeAttributesFromNewBlock(t5 + e4);
                this.api.blocks.delete(t5), this.api.blocks.insert("paragraph", { text: e3.slice(0, s2) }, {}, t5, 1), this.api.caret.setToBlock(t5);
              }
            }
          }
          findToggleRootIndex(t3, e3) {
            const o2 = this.getBlockByIndex(t3), { holder: s2 } = o2;
            return this.isAToggleRoot(s2) && e3 === s2.querySelector(".toggle-block__selector").getAttribute("id") ? t3 : t3 - 1 >= 0 ? this.findToggleRootIndex(t3 - 1, e3) : -1;
          }
          extractBlock(t3) {
            const e3 = this.getBlockByIndex(t3), { holder: o2 } = e3;
            if (this.isAToggleItem(o2)) {
              const e4 = o2.getAttribute("foreignKey"), s2 = this.findToggleRootIndex(t3, e4);
              if (s2 >= 0) {
                const o3 = this.getDescendantsNumber(e4), i2 = s2 + o3;
                o3 > 1 && this.api.blocks.move(i2, t3), setTimeout(() => this.removeAttributesFromNewBlock(i2), 200);
              }
            }
            this.api.caret.setToBlock(t3), this.api.toolbar.close();
          }
          setPlaceHolder(t3) {
            if ("Backspace" === t3.code || "Enter" === t3.code) {
              const { children: t4 } = this.wrapper, { length: e3 } = t4[1].textContent;
              0 === e3 && (t4[1].textContent = "");
            }
          }
          render() {
            return this.createToggle(), setTimeout(() => this.renderItems()), setTimeout(() => this.setInitialTransition()), this.wrapper;
          }
          setInitialTransition() {
            const { status: t3 } = this.data, e3 = this.wrapper.firstChild.firstChild;
            e3.style.transition = "0.1s", e3.style.transform = `rotate(${"closed" === t3 ? 0 : 90}deg)`;
          }
          renderItems() {
            const t3 = this.api.blocks.getBlocksCount(), e3 = this.wrapper.firstChild;
            let o2;
            if (this.readOnly) {
              const t4 = document.getElementsByClassName("codex-editor__redactor")[0], { children: e4 } = t4, { length: s2 } = e4;
              for (let t5 = 0; t5 < s2; t5 += 1) {
                const s3 = e4[t5].firstChild.firstChild, { id: i2 } = s3;
                if (i2 === this.wrapper.id) {
                  o2 = t5;
                  break;
                }
              }
            } else {
              const e4 = this.wrapper.children[1];
              let s2 = {}, i2 = this.api.blocks.getCurrentBlockIndex();
              const r2 = i2 === t3 - 1 ? -1 : 1;
              for (; s2[1] !== e4; ) {
                o2 = i2;
                const t4 = this.api.blocks.getBlockByIndex(o2);
                if (!t4)
                  break;
                const { holder: e5 } = t4;
                s2 = e5.firstChild.firstChild.children, i2 += r2;
              }
            }
            if (o2 + this.data.items < t3)
              for (let t4 = o2 + 1, e4 = 0; t4 <= o2 + this.data.items; t4 += 1) {
                const o3 = this.api.blocks.getBlockByIndex(t4), { holder: s2 } = o3, i2 = s2.firstChild.firstChild;
                if (this.isPartOfAToggle(i2)) {
                  this.data.items = e4;
                  break;
                }
                this.setAttributesToNewBlock(t4), e4 += 1;
              }
            else
              this.data.items = 0;
            e3.addEventListener("click", () => {
              this.resolveToggleAction(), setTimeout(() => {
                this.hideAndShowBlocks();
              });
            }), this.hideAndShowBlocks();
          }
          resolveToggleAction() {
            const t3 = this.wrapper.firstChild.firstChild;
            "closed" === this.data.status ? (this.data.status = "open", t3.style.transform = "rotate(90deg)") : (this.data.status = "closed", t3.style.transform = "rotate(0deg)"), this.api.blocks.getBlockByIndex(this.api.blocks.getCurrentBlockIndex()).holder.setAttribute("status", this.data.status);
          }
          hideAndShowBlocks(t3 = this.wrapper.id, e3 = this.data.status) {
            const o2 = document.querySelectorAll(`div[foreignKey="${t3}"]`), { length: s2 } = o2;
            if (s2 > 0)
              o2.forEach((t4) => {
                t4.hidden = "closed" === e3;
                const o3 = t4.querySelectorAll(".toggle-block__selector");
                if (o3.length > 0) {
                  const s3 = "closed" === e3 ? e3 : t4.getAttribute("status");
                  this.hideAndShowBlocks(o3[0].getAttribute("id"), s3);
                }
              });
            else if (t3 === this.wrapper.id) {
              const { lastChild: t4 } = this.wrapper;
              t4.classList.toggle("toggle-block__hidden", e3);
            }
          }
          save(t3) {
            const e3 = t3.getAttribute("id"), { children: o2 } = t3, s2 = o2[1].innerHTML, i2 = document.querySelectorAll(`div[foreignKey="${e3}"]`);
            return this.data.fk = e3, Object.assign(this.data, { text: s2, items: i2.length });
          }
          getDescendantsNumber(t3) {
            let e3 = 0;
            return document.querySelectorAll(`div[foreignKey="${t3}"]`).forEach((t4) => {
              if (t4.hasAttribute("status")) {
                const o2 = t4.querySelector(".toggle-block__selector").getAttribute("id");
                e3 += this.getDescendantsNumber(o2);
              }
              e3 += 1;
            }), e3;
          }
          highlightToggleItems(t3) {
            document.querySelectorAll(`div[foreignKey="${t3}"]`).forEach((t4) => {
              if (t4.classList.add("ce-block--selected"), t4.hasAttribute("status")) {
                const e3 = t4.querySelector(".toggle-block__selector").getAttribute("id");
                this.highlightToggleItems(e3);
              }
            });
          }
          renderSettings() {
            const t3 = document.getElementsByClassName("ce-settings")[0];
            return setTimeout(() => {
              const e3 = t3.lastChild, o2 = this.api.blocks.getCurrentBlockIndex();
              this.highlightToggleItems(this.wrapper.id);
              const s2 = e3.querySelector('[data-item-name="move-up"]') || e3.getElementsByClassName("ce-tune-move-up")[0], i2 = e3.querySelector('[data-item-name="move-down"]') || e3.getElementsByClassName("ce-tune-move-down")[0], r2 = e3.querySelector('[data-item-name="delete"]') || e3.getElementsByClassName("ce-settings__button--delete")[0];
              this.addEventsMoveButtons(i2, 0, o2), this.addEventsMoveButtons(s2, 1, o2), this.addEventDeleteButton(r2, o2);
            }), document.createElement("div");
          }
          addEventsMoveButtons(t3, e3, o2) {
            t3 && t3.addEventListener("click", () => {
              this.moveToggle(o2, e3);
            });
          }
          addEventDeleteButton(t3, e3) {
            t3 && t3.addEventListener("click", () => {
              const o2 = t3.classList;
              -1 === Object.values(o2).indexOf("clicked-to-destroy-toggle") ? t3.classList.add("clicked-to-destroy-toggle") : this.removeFullToggle(e3);
            });
          }
          moveToggle(t3, e3) {
            if (!this.readOnly) {
              this.close();
              const o2 = this.getCurrentBlockIndex(), s2 = this.getDescendantsNumber(this.wrapper.id), i2 = this.getBlocksCount(), r2 = t3 + s2;
              this.move(t3, o2), t3 >= 0 && r2 <= i2 - 1 && (0 === e3 ? this.moveDown(t3, r2) : this.moveUp(t3, r2));
            }
          }
          moveDown(t3, e3) {
            const o2 = e3 + 1, s2 = this.getBlockByIndex(o2), { holder: i2 } = s2;
            if (this.move(t3, o2), "toggle" === s2.name) {
              const e4 = i2.querySelector(".toggle-block__selector").getAttribute("id"), s3 = this.getDescendantsNumber(e4);
              this.moveDescendants(s3, t3 + 1, o2 + 1, 0);
            }
          }
          moveUp(t3, e3) {
            const o2 = t3 - 1, s2 = this.getBlockByIndex(o2);
            if ("toggle" === s2.name)
              return;
            const { holder: i2 } = s2;
            if (i2.hasAttribute("foreignKey")) {
              const o3 = this.getBlockByIndex(t3).holder.getAttribute("foreignKey"), s3 = i2.getAttribute("foreignKey");
              if (s3 !== o3) {
                const i3 = this.findIndexOfParentBlock(o3, s3, t3), r2 = this.getBlockByIndex(i3).holder.querySelector(".toggle-block__selector").getAttribute("id"), n2 = this.getDescendantsNumber(r2);
                return this.move(e3, i3), void this.moveDescendants(n2, e3, i3, 1);
              }
            }
            this.move(e3, o2);
          }
          findIndexOfParentBlock(t3, e3, o2) {
            const s2 = o2 - (this.getDescendantsNumber(e3) + 1), i2 = this.getBlockByIndex(s2).holder;
            if (i2.hasAttribute("foreignKey")) {
              const e4 = i2.getAttribute("foreignKey");
              if (e4 !== t3) {
                const o3 = this.getBlockByIndex(s2 - 1).holder;
                if (o3.hasAttribute("foreignKey")) {
                  const i3 = o3.getAttribute("foreignKey");
                  if (i3 !== e4)
                    return this.findIndexOfParentBlock(t3, i3, s2);
                }
              }
            }
            return s2;
          }
          moveDescendants(t3, e3, o2, s2) {
            let i2 = o2, r2 = e3;
            for (; t3; )
              this.move(r2, i2), 0 === s2 && (i2 += 1, r2 += 1), t3 -= 1;
          }
          removeFullToggle(t3) {
            const e3 = document.querySelectorAll(`div[foreignKey="${this.wrapper.id}"]`), { length: o2 } = e3;
            for (let e4 = t3; e4 < t3 + o2; e4 += 1)
              setTimeout(() => this.api.blocks.delete(t3));
          }
          addListeners() {
            this.readOnly || document.activeElement.addEventListener("keyup", (t3) => {
              const e3 = document.activeElement, o2 = this.getCurrentBlockIndex(), { holder: s2 } = this.getBlockByIndex(o2);
              "Space" === t3.code ? this.createToggleWithShortcut(e3) : o2 > 0 && !this.isPartOfAToggle(s2) && "Tab" === t3.code && this.nestBlock(s2);
            });
          }
          addSupportForUndoAndRedoActions() {
            if (!this.readOnly) {
              const t3 = document.querySelector("div.codex-editor__redactor"), e3 = { attributes: true, childList: true, characterData: true };
              new MutationObserver((t4) => {
                t4.forEach((t5) => {
                  "childList" === t5.type && setTimeout(this.restoreItemAttributes.bind(this, t5));
                });
              }).observe(t3, e3);
            }
          }
          addSupportForDragAndDropActions() {
            if (!this.readOnly) {
              if (void 0 === this.wrapper)
                return void setTimeout(() => this.addSupportForDragAndDropActions(), 250);
              document.querySelector(`#${this.wrapper.id}`).parentNode.parentNode.setAttribute("status", this.data.status);
              const t3 = document.querySelector(".ce-toolbar__settings-btn");
              t3.setAttribute("draggable", "true"), t3.addEventListener("dragstart", () => {
                this.startBlock = this.api.blocks.getCurrentBlockIndex(), this.nameDragged = this.api.blocks.getBlockByIndex(this.startBlock).name, this.holderDragged = this.api.blocks.getBlockByIndex(this.startBlock).holder;
              }), document.addEventListener("drop", (t4) => {
                const { target: e3 } = t4;
                if (document.contains(e3)) {
                  const t5 = e3.classList.contains("ce-block") ? e3 : e3.closest(".ce-block");
                  if (t5 && t5 !== this.holderDragged) {
                    let e4 = this.getIndex(t5);
                    e4 = this.startBlock < e4 ? e4 + 1 : e4;
                    const o2 = t5.querySelectorAll(".toggle-block__selector").length > 0 || null !== t5.getAttribute("foreignKey");
                    setTimeout(() => {
                      if ("toggle" === this.nameDragged) {
                        const s2 = this.holderDragged.querySelector(`#${this.wrapper.id}`);
                        if (s2)
                          if (this.isChild(s2.getAttribute("id"), t5.getAttribute("foreignKey"))) {
                            if (this.startBlock === e4 ? this.api.blocks.move(this.startBlock + 1, e4) : this.api.blocks.move(this.startBlock, e4), !o2) {
                              const t6 = this.getIndex(this.holderDragged);
                              this.removeAttributesFromNewBlock(t6);
                            }
                          } else
                            this.assignToggleItemAttributes(o2, t5), this.moveChildren(e4);
                      } else
                        this.nameDragged && this.assignToggleItemAttributes(o2, t5);
                      if (!o2) {
                        const t6 = this.getIndex(this.holderDragged);
                        this.removeAttributesFromNewBlock(t6);
                      }
                    });
                  }
                }
              });
            }
          }
          assignToggleItemAttributes(t3, e3) {
            if (t3) {
              const t4 = e3.getAttribute("foreignKey") ?? e3.querySelector(".toggle-block__selector").getAttribute("id"), o2 = this.getIndex(this.holderDragged);
              this.setAttributesToNewBlock(o2, t4);
            }
          }
          moveChildren(t3, e3 = this.wrapper.id) {
            let o2 = document.querySelectorAll(`div[foreignKey="${e3}"]`);
            o2 = this.startBlock >= t3 ? [...o2].reverse() : o2, o2.forEach((e4) => {
              const o3 = this.getIndex(e4);
              this.api.blocks.move(t3, o3);
              const s2 = e4.querySelectorAll(".toggle-block__selector");
              if (s2.length > 0) {
                const o4 = this.getIndex(e4), i2 = this.startBlock < t3 ? 0 : 1;
                s2.forEach((t4) => this.moveChildren(o4 + i2, t4.getAttribute("id")));
                const r2 = Math.abs(t3 - o4);
                t3 = this.startBlock < t3 ? t3 + r2 : t3 - r2;
              }
            });
          }
          restoreItemAttributes(t3) {
            if (void 0 !== this.wrapper) {
              const e3 = this.api.blocks.getCurrentBlockIndex(), o2 = this.api.blocks.getBlockByIndex(e3), { holder: s2 } = o2, i2 = !this.isPartOfAToggle(s2), { length: r2 } = this.itemsId, { length: n2 } = document.querySelectorAll(`div[foreignKey="${this.data.fk}"]`);
              if (this.itemsId.includes(o2.id) && i2)
                this.setAttributesToNewBlock(e3);
              else if (t3.addedNodes[0] && (t3 == null ? void 0 : t3.previousSibling) && this.isPartOfAToggle(t3.previousSibling) && !this.isPartOfAToggle(t3.addedNodes[0]) && r2 > n2) {
                const { id: s3 } = t3.addedNodes[0], i3 = this.api.blocks.getById(s3);
                this.setAttributesToNewBlock(null, this.wrapper.id, i3), this.itemsId[e3] = o2.id;
              }
            }
          }
          createToggleWithShortcut(t3) {
            const e3 = t3.textContent;
            if (">" === e3[0] && !this.isPartOfAToggle(t3)) {
              const t4 = this.api.blocks.getCurrentBlockIndex();
              this.api.blocks.insert("toggle", { text: e3.slice(2) }, this.api, t4, true), this.api.blocks.delete(t4 + 1), this.api.caret.setToBlock(t4);
            }
          }
          nestBlock(t3) {
            const e3 = t3.previousElementSibling, o2 = e3.firstChild.firstChild;
            if (this.isPartOfAToggle(o2) || this.isPartOfAToggle(e3)) {
              const s2 = e3.getAttribute("foreignKey"), i2 = o2.getAttribute("id"), r2 = s2 || i2;
              t3.setAttribute("will-be-a-nested-block", true), document.getElementById(r2).children[1].focus();
            }
          }
          setNestedBlockAttributes() {
            const t3 = this.api.blocks.getCurrentBlockIndex(), e3 = this.api.blocks.getBlockByIndex(t3), { holder: o2 } = e3;
            o2.getAttribute("will-be-a-nested-block") && (o2.removeAttribute("will-be-a-nested-block"), this.setAttributesToNewBlock(t3), this.api.toolbar.close());
          }
          isPartOfAToggle(t3) {
            const e3 = Array.from(t3.classList), o2 = ["toggle-block__item", "toggle-block__input", "toggle-block__selector"], s2 = o2.some((e4) => 0 !== t3.getElementsByClassName(e4).length);
            return o2.some((t4) => e3.includes(t4)) || s2;
          }
          addSupportForCopyAndPasteAction() {
            if (!this.readOnly) {
              const t3 = document.querySelector("div.codex-editor__redactor"), e3 = { attributes: true, childList: true, characterData: true };
              new MutationObserver((t4) => {
                t4.forEach((t5) => {
                  "childList" === t5.type && setTimeout(this.resetIdToCopiedBlock.bind(this, t5));
                });
              }).observe(t3, e3);
            }
          }
          resetIdToCopiedBlock() {
            if (void 0 !== this.wrapper) {
              const t3 = this.api.blocks.getCurrentBlockIndex(), { holder: e3 } = this.api.blocks.getBlockByIndex(t3);
              if (this.isPartOfAToggle(e3)) {
                const o2 = e3.getAttribute("foreignKey");
                if (document.querySelectorAll(`#${o2}`).length > 1) {
                  const e4 = this.findToggleRootIndex(t3, o2), s2 = B();
                  for (let o3 = e4; o3 <= t3; o3 += 1) {
                    const t4 = this.api.blocks.getBlockByIndex(o3), { holder: i2 } = t4;
                    o3 === e4 ? i2.firstChild.firstChild.setAttribute("id", `fk-${s2}`) : i2.setAttribute("foreignKey", `fk-${s2}`);
                  }
                }
              }
            }
          }
        }
      })(), s.default;
    })());
  }
});
export default require_bundle();
//# sourceMappingURL=editorjs-toggle-block.js.map
