{"version": 3, "sources": ["../../../../node_modules/.pnpm/editorjs-toggle-block@0.3.16/node_modules/editorjs-toggle-block/dist/bundle.js"], "sourcesContent": ["!function(t,e){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define([],e):\"object\"==typeof exports?exports.ToggleBlock=e():t.ToggleBlock=e()}(self,(()=>(()=>{var t={424:(t,e,o)=>{\"use strict\";o.d(e,{Z:()=>l});var s=o(81),i=o.n(s),r=o(645),n=o.n(r)()(i());n.push([t.id,\".toggle-block__selector > div {\\n  vertical-align: middle;\\n  display: inline-block;\\n  padding: 1% 0 1% 0;\\n  outline: none;\\n  border: none;\\n  width: 90%;\\n}\\n\\n.toggle-block__selector br {\\n  display: none;\\n}\\n\\n.toggle-block__icon > svg {\\n  vertical-align: middle;\\n  width: 15px;\\n  height: auto;\\n}\\n\\n.toggle-block__icon:hover {\\n  color: #388ae5;\\n  cursor: pointer;\\n}\\n\\n.bi-play-fill {\\n  width: 34px;\\n  height: 34px;\\n}\\n\\n.toggle-block__input {\\n  margin-left: 5px;\\n}\\n\\n.toggle-block__input:empty:before {\\n  content: attr(placeholder);\\n  color: gray;\\n  background-color: transparent;\\n}\\n\\n.toggle-block__content-default {\\n  margin-left: 20px;\\n}\\n\\n.toggle-block__item {\\n  margin-left: 39px;\\n}\\n\\n.toggle-block__content-default {\\n  color: gray;\\n  border-radius: 5px;\\n}\\n\\n.toggle-block__content-default:hover {\\n  cursor: pointer;\\n  background: rgba(55, 53, 47, 0.08);\\n}\\n\\ndiv.toggle-block__hidden {\\n  display: none;\\n}\\n\",\"\"]);const l=n},645:t=>{\"use strict\";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var o=\"\",s=void 0!==e[5];return e[4]&&(o+=\"@supports (\".concat(e[4],\") {\")),e[2]&&(o+=\"@media \".concat(e[2],\" {\")),s&&(o+=\"@layer\".concat(e[5].length>0?\" \".concat(e[5]):\"\",\" {\")),o+=t(e),s&&(o+=\"}\"),e[2]&&(o+=\"}\"),e[4]&&(o+=\"}\"),o})).join(\"\")},e.i=function(t,o,s,i,r){\"string\"==typeof t&&(t=[[null,t,void 0]]);var n={};if(s)for(var l=0;l<this.length;l++){var c=this[l][0];null!=c&&(n[c]=!0)}for(var d=0;d<t.length;d++){var a=[].concat(t[d]);s&&n[a[0]]||(void 0!==r&&(void 0===a[5]||(a[1]=\"@layer\".concat(a[5].length>0?\" \".concat(a[5]):\"\",\" {\").concat(a[1],\"}\")),a[5]=r),o&&(a[2]?(a[1]=\"@media \".concat(a[2],\" {\").concat(a[1],\"}\"),a[2]=o):a[2]=o),i&&(a[4]?(a[1]=\"@supports (\".concat(a[4],\") {\").concat(a[1],\"}\"),a[4]=i):a[4]=\"\".concat(i)),e.push(a))}},e}},81:t=>{\"use strict\";t.exports=function(t){return t[1]}},379:t=>{\"use strict\";var e=[];function o(t){for(var o=-1,s=0;s<e.length;s++)if(e[s].identifier===t){o=s;break}return o}function s(t,s){for(var r={},n=[],l=0;l<t.length;l++){var c=t[l],d=s.base?c[0]+s.base:c[0],a=r[d]||0,h=\"\".concat(d,\" \").concat(a);r[d]=a+1;var g=o(h),u={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==g)e[g].references++,e[g].updater(u);else{var p=i(u,s);s.byIndex=l,e.splice(l,0,{identifier:h,updater:p,references:1})}n.push(h)}return n}function i(t,e){var o=e.domAPI(e);return o.update(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;o.update(t=e)}else o.remove()}}t.exports=function(t,i){var r=s(t=t||[],i=i||{});return function(t){t=t||[];for(var n=0;n<r.length;n++){var l=o(r[n]);e[l].references--}for(var c=s(t,i),d=0;d<r.length;d++){var a=o(r[d]);0===e[a].references&&(e[a].updater(),e.splice(a,1))}r=c}}},569:t=>{\"use strict\";var e={};t.exports=function(t,o){var s=function(t){if(void 0===e[t]){var o=document.querySelector(t);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(t){o=null}e[t]=o}return e[t]}(t);if(!s)throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");s.appendChild(o)}},216:t=>{\"use strict\";t.exports=function(t){var e=document.createElement(\"style\");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},565:(t,e,o)=>{\"use strict\";t.exports=function(t){var e=o.nc;e&&t.setAttribute(\"nonce\",e)}},795:t=>{\"use strict\";t.exports=function(t){if(\"undefined\"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(o){!function(t,e,o){var s=\"\";o.supports&&(s+=\"@supports (\".concat(o.supports,\") {\")),o.media&&(s+=\"@media \".concat(o.media,\" {\"));var i=void 0!==o.layer;i&&(s+=\"@layer\".concat(o.layer.length>0?\" \".concat(o.layer):\"\",\" {\")),s+=o.css,i&&(s+=\"}\"),o.media&&(s+=\"}\"),o.supports&&(s+=\"}\");var r=o.sourceMap;r&&\"undefined\"!=typeof btoa&&(s+=\"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(r)))),\" */\")),e.styleTagTransform(s,t,e.options)}(e,t,o)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},589:t=>{\"use strict\";t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},370:t=>{t.exports='<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" class=\"bi bi-play-fill\" viewBox=\"0 0 16 16\"><path d=\"m11.596 8.697-6.363 3.692c-.54.313-1.233-.066-1.233-.697V4.308c0-.63.692-1.01 1.233-.696l6.363 3.692a.802.802 0 0 1 0 1.393z\"></path></svg>'}},e={};function o(s){var i=e[s];if(void 0!==i)return i.exports;var r=e[s]={id:s,exports:{}};return t[s](r,r.exports,o),r.exports}o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var s in e)o.o(e,s)&&!o.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.nc=void 0;var s={};return(()=>{\"use strict\";o.d(s,{default:()=>I});var t=o(379),e=o.n(t),i=o(795),r=o.n(i),n=o(569),l=o.n(n),c=o(565),d=o.n(c),a=o(216),h=o.n(a),g=o(589),u=o.n(g),p=o(424),f={};f.styleTagTransform=u(),f.setAttributes=d(),f.insert=l().bind(null,\"head\"),f.domAPI=r(),f.insertStyleElement=h(),e()(p.Z,f),p.Z&&p.Z.locals&&p.Z.locals;const m={randomUUID:\"undefined\"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let b;const k=new Uint8Array(16);function y(){if(!b&&(b=\"undefined\"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!b))throw new Error(\"crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported\");return b(k)}const v=[];for(let t=0;t<256;++t)v.push((t+256).toString(16).slice(1));const B=function(t,e,o){if(m.randomUUID&&!e&&!t)return m.randomUUID();const s=(t=t||{}).random||(t.rng||y)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,e){o=o||0;for(let t=0;t<16;++t)e[o+t]=s[t];return e}return function(t,e=0){return(v[t[e+0]]+v[t[e+1]]+v[t[e+2]]+v[t[e+3]]+\"-\"+v[t[e+4]]+v[t[e+5]]+\"-\"+v[t[e+6]]+v[t[e+7]]+\"-\"+v[t[e+8]]+v[t[e+9]]+\"-\"+v[t[e+10]]+v[t[e+11]]+v[t[e+12]]+v[t[e+13]]+v[t[e+14]]+v[t[e+15]]).toLowerCase()}(s)};var A=o(370),x=o.n(A);class I{static get toolbox(){return{title:\"Toggle\",icon:x()}}static get enableLineBreaks(){return!0}static get isReadOnlySupported(){return!0}constructor({data:t,api:e,readOnly:o,config:s}){this.data={text:t.text||\"\",status:t.status||\"open\",fk:t.fk||`fk-${B()}`,items:t.items||0},this.itemsId=[],this.api=e;const{toolbar:{close:i},blocks:{getCurrentBlockIndex:r,getBlockByIndex:n,getBlocksCount:l,move:c}}=this.api;this.close=i,this.getCurrentBlockIndex=r,this.getBlocksCount=l,this.getBlockByIndex=n,this.move=c,this.wrapper=void 0,this.readOnly=o||!1,this.placeholder=s?.placeholder??\"Toggle\",this.defaultContent=s?.defaultContent??\"Empty toggle. Click or drop blocks inside.\",this.addListeners(),this.addSupportForUndoAndRedoActions(),this.addSupportForDragAndDropActions(),this.addSupportForCopyAndPasteAction()}isAToggleItem(t){return t.classList.contains(\"toggle-block__item\")}isAToggleRoot(t){return t.classList.contains(\"toggle-block__selector\")||Boolean(t.querySelector(\".toggle-block__selector\"))}createParagraphFromToggleRoot(t){if(\"Enter\"===t.code){const t=document.getSelection().focusOffset,e=this.api.blocks.getCurrentBlockIndex(),o=this.api.blocks.getBlockByIndex(e),{holder:s}=o,i=s.firstChild.firstChild,r=i.children[1].innerHTML,n=r.indexOf(\"<br>\"),l=-1===n?r.length:n;\"closed\"===this.data.status&&(this.resolveToggleAction(),this.hideAndShowBlocks());const c=r.slice(l+4,t.focusOffset);i.children[1].innerHTML=r.slice(t.focusOffset,l),this.api.blocks.insert(\"paragraph\",{text:c},{},e+1,1),this.setAttributesToNewBlock()}}createParagraphFromIt(){this.setAttributesToNewBlock()}setAttributesToNewBlock(t=null,e=this.wrapper.id,o=null){const s=null===t?this.api.blocks.getCurrentBlockIndex():t,i=o||this.api.blocks.getBlockByIndex(s),r=B();this.itemsId.includes(i.id)||this.itemsId.splice(s-1,0,i.id);const{holder:n}=i,l=n.firstChild.firstChild;n.setAttribute(\"foreignKey\",e),n.setAttribute(\"id\",r),setTimeout((()=>n.classList.add(\"toggle-block__item\"))),this.readOnly||(n.onkeydown=this.setEventsToNestedBlock.bind(this),l.focus())}setEventsToNestedBlock(t){if(\"Enter\"===t.code)setTimeout((()=>this.createParagraphFromIt()));else{const e=this.getCurrentBlockIndex(),o=this.getBlockByIndex(e),{holder:s}=o;if(\"Tab\"===t.code&&t.shiftKey&&this.extractBlock(e),\"Backspace\"===t.code){const t=document.getSelection().focusOffset;this.removeBlock(s,o.id,t)}}}removeBlock(t,e,o){if(0===o){const t=this.itemsId.indexOf(e);this.itemsId.splice(t,1)}}removeAttributesFromNewBlock(t){const e=this.api.blocks.getBlockByIndex(t),{holder:o}=e;if(!this.itemsId.includes(e.id)){const t=this.itemsId.indexOf(e.id);this.itemsId.splice(t,1)}o.removeAttribute(\"foreignKey\"),o.removeAttribute(\"id\"),o.onkeydown={},o.onkeyup={},o.classList.remove(\"toggle-block__item\")}createToggle(){this.wrapper=document.createElement(\"div\"),this.wrapper.classList.add(\"toggle-block__selector\"),this.wrapper.id=this.data.fk;const t=document.createElement(\"span\"),e=document.createElement(\"div\"),o=document.createElement(\"div\");t.classList.add(\"toggle-block__icon\"),t.innerHTML=x(),e.classList.add(\"toggle-block__input\"),e.setAttribute(\"contentEditable\",!this.readOnly),e.innerHTML=this.data.text||\"\",this.readOnly||(e.addEventListener(\"keyup\",this.createParagraphFromToggleRoot.bind(this)),e.addEventListener(\"keydown\",this.removeToggle.bind(this)),e.addEventListener(\"focusin\",(()=>this.setFocusToggleRootAtTheEnd())),e.addEventListener(\"keyup\",this.setPlaceHolder.bind(this)),e.setAttribute(\"placeholder\",this.placeholder),e.addEventListener(\"focus\",this.setDefaultContent.bind(this)),e.addEventListener(\"focusout\",this.setDefaultContent.bind(this)),o.addEventListener(\"click\",this.clickInDefaultContent.bind(this)),e.addEventListener(\"focus\",this.setNestedBlockAttributes.bind(this))),o.classList.add(\"toggle-block__content-default\",\"toggle-block__hidden\"),o.innerHTML=this.defaultContent,this.wrapper.appendChild(t),this.wrapper.appendChild(e),this.wrapper.appendChild(o)}setFocusToggleRootAtTheEnd(){const t=document.activeElement,e=window.getSelection(),o=document.createRange();e.removeAllRanges(),o.selectNodeContents(t),o.collapse(!1),e.addRange(o),t.focus()}clickInDefaultContent(){this.api.blocks.insert(),this.setAttributesToNewBlock(),this.setDefaultContent()}setDefaultContent(){const t=document.querySelectorAll(`div[foreignKey=\"${this.wrapper.id}\"]`),{firstChild:e,lastChild:o}=this.wrapper,{status:s}=this.data,i=t.length>0||\"closed\"===s;o.classList.toggle(\"toggle-block__hidden\",i),e.style.color=0===t.length?\"gray\":\"black\"}removeToggle(t){if(\"Backspace\"===t.code){const{children:t}=this.wrapper,e=t[1].innerHTML;if(0===document.getSelection().focusOffset){const t=this.api.blocks.getCurrentBlockIndex(),o=e.indexOf(\"<br>\"),s=-1===o?e.length:o,i=document.querySelectorAll(`div[foreignKey=\"${this.wrapper.id}\"]`);for(let e=1;e<i.length+1;e+=1)this.removeAttributesFromNewBlock(t+e);this.api.blocks.delete(t),this.api.blocks.insert(\"paragraph\",{text:e.slice(0,s)},{},t,1),this.api.caret.setToBlock(t)}}}findToggleRootIndex(t,e){const o=this.getBlockByIndex(t),{holder:s}=o;return this.isAToggleRoot(s)&&e===s.querySelector(\".toggle-block__selector\").getAttribute(\"id\")?t:t-1>=0?this.findToggleRootIndex(t-1,e):-1}extractBlock(t){const e=this.getBlockByIndex(t),{holder:o}=e;if(this.isAToggleItem(o)){const e=o.getAttribute(\"foreignKey\"),s=this.findToggleRootIndex(t,e);if(s>=0){const o=this.getDescendantsNumber(e),i=s+o;o>1&&this.api.blocks.move(i,t),setTimeout((()=>this.removeAttributesFromNewBlock(i)),200)}}this.api.caret.setToBlock(t),this.api.toolbar.close()}setPlaceHolder(t){if(\"Backspace\"===t.code||\"Enter\"===t.code){const{children:t}=this.wrapper,{length:e}=t[1].textContent;0===e&&(t[1].textContent=\"\")}}render(){return this.createToggle(),setTimeout((()=>this.renderItems())),setTimeout((()=>this.setInitialTransition())),this.wrapper}setInitialTransition(){const{status:t}=this.data,e=this.wrapper.firstChild.firstChild;e.style.transition=\"0.1s\",e.style.transform=`rotate(${\"closed\"===t?0:90}deg)`}renderItems(){const t=this.api.blocks.getBlocksCount(),e=this.wrapper.firstChild;let o;if(this.readOnly){const t=document.getElementsByClassName(\"codex-editor__redactor\")[0],{children:e}=t,{length:s}=e;for(let t=0;t<s;t+=1){const s=e[t].firstChild.firstChild,{id:i}=s;if(i===this.wrapper.id){o=t;break}}}else{const e=this.wrapper.children[1];let s={},i=this.api.blocks.getCurrentBlockIndex();const r=i===t-1?-1:1;for(;s[1]!==e;){o=i;const t=this.api.blocks.getBlockByIndex(o);if(!t)break;const{holder:e}=t;s=e.firstChild.firstChild.children,i+=r}}if(o+this.data.items<t)for(let t=o+1,e=0;t<=o+this.data.items;t+=1){const o=this.api.blocks.getBlockByIndex(t),{holder:s}=o,i=s.firstChild.firstChild;if(this.isPartOfAToggle(i)){this.data.items=e;break}this.setAttributesToNewBlock(t),e+=1}else this.data.items=0;e.addEventListener(\"click\",(()=>{this.resolveToggleAction(),setTimeout((()=>{this.hideAndShowBlocks()}))})),this.hideAndShowBlocks()}resolveToggleAction(){const t=this.wrapper.firstChild.firstChild;\"closed\"===this.data.status?(this.data.status=\"open\",t.style.transform=\"rotate(90deg)\"):(this.data.status=\"closed\",t.style.transform=\"rotate(0deg)\"),this.api.blocks.getBlockByIndex(this.api.blocks.getCurrentBlockIndex()).holder.setAttribute(\"status\",this.data.status)}hideAndShowBlocks(t=this.wrapper.id,e=this.data.status){const o=document.querySelectorAll(`div[foreignKey=\"${t}\"]`),{length:s}=o;if(s>0)o.forEach((t=>{t.hidden=\"closed\"===e;const o=t.querySelectorAll(\".toggle-block__selector\");if(o.length>0){const s=\"closed\"===e?e:t.getAttribute(\"status\");this.hideAndShowBlocks(o[0].getAttribute(\"id\"),s)}}));else if(t===this.wrapper.id){const{lastChild:t}=this.wrapper;t.classList.toggle(\"toggle-block__hidden\",e)}}save(t){const e=t.getAttribute(\"id\"),{children:o}=t,s=o[1].innerHTML,i=document.querySelectorAll(`div[foreignKey=\"${e}\"]`);return this.data.fk=e,Object.assign(this.data,{text:s,items:i.length})}getDescendantsNumber(t){let e=0;return document.querySelectorAll(`div[foreignKey=\"${t}\"]`).forEach((t=>{if(t.hasAttribute(\"status\")){const o=t.querySelector(\".toggle-block__selector\").getAttribute(\"id\");e+=this.getDescendantsNumber(o)}e+=1})),e}highlightToggleItems(t){document.querySelectorAll(`div[foreignKey=\"${t}\"]`).forEach((t=>{if(t.classList.add(\"ce-block--selected\"),t.hasAttribute(\"status\")){const e=t.querySelector(\".toggle-block__selector\").getAttribute(\"id\");this.highlightToggleItems(e)}}))}renderSettings(){const t=document.getElementsByClassName(\"ce-settings\")[0];return setTimeout((()=>{const e=t.lastChild,o=this.api.blocks.getCurrentBlockIndex();this.highlightToggleItems(this.wrapper.id);const s=e.querySelector('[data-item-name=\"move-up\"]')||e.getElementsByClassName(\"ce-tune-move-up\")[0],i=e.querySelector('[data-item-name=\"move-down\"]')||e.getElementsByClassName(\"ce-tune-move-down\")[0],r=e.querySelector('[data-item-name=\"delete\"]')||e.getElementsByClassName(\"ce-settings__button--delete\")[0];this.addEventsMoveButtons(i,0,o),this.addEventsMoveButtons(s,1,o),this.addEventDeleteButton(r,o)})),document.createElement(\"div\")}addEventsMoveButtons(t,e,o){t&&t.addEventListener(\"click\",(()=>{this.moveToggle(o,e)}))}addEventDeleteButton(t,e){t&&t.addEventListener(\"click\",(()=>{const o=t.classList;-1===Object.values(o).indexOf(\"clicked-to-destroy-toggle\")?t.classList.add(\"clicked-to-destroy-toggle\"):this.removeFullToggle(e)}))}moveToggle(t,e){if(!this.readOnly){this.close();const o=this.getCurrentBlockIndex(),s=this.getDescendantsNumber(this.wrapper.id),i=this.getBlocksCount(),r=t+s;this.move(t,o),t>=0&&r<=i-1&&(0===e?this.moveDown(t,r):this.moveUp(t,r))}}moveDown(t,e){const o=e+1,s=this.getBlockByIndex(o),{holder:i}=s;if(this.move(t,o),\"toggle\"===s.name){const e=i.querySelector(\".toggle-block__selector\").getAttribute(\"id\"),s=this.getDescendantsNumber(e);this.moveDescendants(s,t+1,o+1,0)}}moveUp(t,e){const o=t-1,s=this.getBlockByIndex(o);if(\"toggle\"===s.name)return;const{holder:i}=s;if(i.hasAttribute(\"foreignKey\")){const o=this.getBlockByIndex(t).holder.getAttribute(\"foreignKey\"),s=i.getAttribute(\"foreignKey\");if(s!==o){const i=this.findIndexOfParentBlock(o,s,t),r=this.getBlockByIndex(i).holder.querySelector(\".toggle-block__selector\").getAttribute(\"id\"),n=this.getDescendantsNumber(r);return this.move(e,i),void this.moveDescendants(n,e,i,1)}}this.move(e,o)}findIndexOfParentBlock(t,e,o){const s=o-(this.getDescendantsNumber(e)+1),i=this.getBlockByIndex(s).holder;if(i.hasAttribute(\"foreignKey\")){const e=i.getAttribute(\"foreignKey\");if(e!==t){const o=this.getBlockByIndex(s-1).holder;if(o.hasAttribute(\"foreignKey\")){const i=o.getAttribute(\"foreignKey\");if(i!==e)return this.findIndexOfParentBlock(t,i,s)}}}return s}moveDescendants(t,e,o,s){let i=o,r=e;for(;t;)this.move(r,i),0===s&&(i+=1,r+=1),t-=1}removeFullToggle(t){const e=document.querySelectorAll(`div[foreignKey=\"${this.wrapper.id}\"]`),{length:o}=e;for(let e=t;e<t+o;e+=1)setTimeout((()=>this.api.blocks.delete(t)))}addListeners(){this.readOnly||document.activeElement.addEventListener(\"keyup\",(t=>{const e=document.activeElement,o=this.getCurrentBlockIndex(),{holder:s}=this.getBlockByIndex(o);\"Space\"===t.code?this.createToggleWithShortcut(e):o>0&&!this.isPartOfAToggle(s)&&\"Tab\"===t.code&&this.nestBlock(s)}))}addSupportForUndoAndRedoActions(){if(!this.readOnly){const t=document.querySelector(\"div.codex-editor__redactor\"),e={attributes:!0,childList:!0,characterData:!0};new MutationObserver((t=>{t.forEach((t=>{\"childList\"===t.type&&setTimeout(this.restoreItemAttributes.bind(this,t))}))})).observe(t,e)}}getIndex=t=>Array.from(t.parentNode.children).indexOf(t);isChild=(t,e)=>!(!t||!e)&&(t===e||[...document.querySelectorAll(`div[foreignKey=\"${t}\"]`)].some((t=>{const o=t.querySelector(\".toggle-block__selector\");return!!o&&this.isChild(o.getAttribute(\"id\"),e)})));addSupportForDragAndDropActions(){if(!this.readOnly){if(void 0===this.wrapper)return void setTimeout((()=>this.addSupportForDragAndDropActions()),250);document.querySelector(`#${this.wrapper.id}`).parentNode.parentNode.setAttribute(\"status\",this.data.status);const t=document.querySelector(\".ce-toolbar__settings-btn\");t.setAttribute(\"draggable\",\"true\"),t.addEventListener(\"dragstart\",(()=>{this.startBlock=this.api.blocks.getCurrentBlockIndex(),this.nameDragged=this.api.blocks.getBlockByIndex(this.startBlock).name,this.holderDragged=this.api.blocks.getBlockByIndex(this.startBlock).holder})),document.addEventListener(\"drop\",(t=>{const{target:e}=t;if(document.contains(e)){const t=e.classList.contains(\"ce-block\")?e:e.closest(\".ce-block\");if(t&&t!==this.holderDragged){let e=this.getIndex(t);e=this.startBlock<e?e+1:e;const o=t.querySelectorAll(\".toggle-block__selector\").length>0||null!==t.getAttribute(\"foreignKey\");setTimeout((()=>{if(\"toggle\"===this.nameDragged){const s=this.holderDragged.querySelector(`#${this.wrapper.id}`);if(s)if(this.isChild(s.getAttribute(\"id\"),t.getAttribute(\"foreignKey\"))){if(this.startBlock===e?this.api.blocks.move(this.startBlock+1,e):this.api.blocks.move(this.startBlock,e),!o){const t=this.getIndex(this.holderDragged);this.removeAttributesFromNewBlock(t)}}else this.assignToggleItemAttributes(o,t),this.moveChildren(e)}else this.nameDragged&&this.assignToggleItemAttributes(o,t);if(!o){const t=this.getIndex(this.holderDragged);this.removeAttributesFromNewBlock(t)}}))}}}))}}assignToggleItemAttributes(t,e){if(t){const t=e.getAttribute(\"foreignKey\")??e.querySelector(\".toggle-block__selector\").getAttribute(\"id\"),o=this.getIndex(this.holderDragged);this.setAttributesToNewBlock(o,t)}}moveChildren(t,e=this.wrapper.id){let o=document.querySelectorAll(`div[foreignKey=\"${e}\"]`);o=this.startBlock>=t?[...o].reverse():o,o.forEach((e=>{const o=this.getIndex(e);this.api.blocks.move(t,o);const s=e.querySelectorAll(\".toggle-block__selector\");if(s.length>0){const o=this.getIndex(e),i=this.startBlock<t?0:1;s.forEach((t=>this.moveChildren(o+i,t.getAttribute(\"id\"))));const r=Math.abs(t-o);t=this.startBlock<t?t+r:t-r}}))}restoreItemAttributes(t){if(void 0!==this.wrapper){const e=this.api.blocks.getCurrentBlockIndex(),o=this.api.blocks.getBlockByIndex(e),{holder:s}=o,i=!this.isPartOfAToggle(s),{length:r}=this.itemsId,{length:n}=document.querySelectorAll(`div[foreignKey=\"${this.data.fk}\"]`);if(this.itemsId.includes(o.id)&&i)this.setAttributesToNewBlock(e);else if(t.addedNodes[0]&&t?.previousSibling&&this.isPartOfAToggle(t.previousSibling)&&!this.isPartOfAToggle(t.addedNodes[0])&&r>n){const{id:s}=t.addedNodes[0],i=this.api.blocks.getById(s);this.setAttributesToNewBlock(null,this.wrapper.id,i),this.itemsId[e]=o.id}}}createToggleWithShortcut(t){const e=t.textContent;if(\">\"===e[0]&&!this.isPartOfAToggle(t)){const t=this.api.blocks.getCurrentBlockIndex();this.api.blocks.insert(\"toggle\",{text:e.slice(2)},this.api,t,!0),this.api.blocks.delete(t+1),this.api.caret.setToBlock(t)}}nestBlock(t){const e=t.previousElementSibling,o=e.firstChild.firstChild;if(this.isPartOfAToggle(o)||this.isPartOfAToggle(e)){const s=e.getAttribute(\"foreignKey\"),i=o.getAttribute(\"id\"),r=s||i;t.setAttribute(\"will-be-a-nested-block\",!0),document.getElementById(r).children[1].focus()}}setNestedBlockAttributes(){const t=this.api.blocks.getCurrentBlockIndex(),e=this.api.blocks.getBlockByIndex(t),{holder:o}=e;o.getAttribute(\"will-be-a-nested-block\")&&(o.removeAttribute(\"will-be-a-nested-block\"),this.setAttributesToNewBlock(t),this.api.toolbar.close())}isPartOfAToggle(t){const e=Array.from(t.classList),o=[\"toggle-block__item\",\"toggle-block__input\",\"toggle-block__selector\"],s=o.some((e=>0!==t.getElementsByClassName(e).length));return o.some((t=>e.includes(t)))||s}addSupportForCopyAndPasteAction(){if(!this.readOnly){const t=document.querySelector(\"div.codex-editor__redactor\"),e={attributes:!0,childList:!0,characterData:!0};new MutationObserver((t=>{t.forEach((t=>{\"childList\"===t.type&&setTimeout(this.resetIdToCopiedBlock.bind(this,t))}))})).observe(t,e)}}resetIdToCopiedBlock(){if(void 0!==this.wrapper){const t=this.api.blocks.getCurrentBlockIndex(),{holder:e}=this.api.blocks.getBlockByIndex(t);if(this.isPartOfAToggle(e)){const o=e.getAttribute(\"foreignKey\");if(document.querySelectorAll(`#${o}`).length>1){const e=this.findToggleRootIndex(t,o),s=B();for(let o=e;o<=t;o+=1){const t=this.api.blocks.getBlockByIndex(o),{holder:i}=t;o===e?i.firstChild.firstChild.setAttribute(\"id\",`fk-${s}`):i.setAttribute(\"foreignKey\",`fk-${s}`)}}}}}}})(),s.default})()));"], "mappings": ";;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,YAAU,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,GAAE,CAAC,IAAE,YAAU,OAAO,UAAQ,QAAQ,cAAY,EAAE,IAAE,EAAE,cAAY,EAAE;AAAA,IAAC,EAAE,MAAM,OAAK,MAAI;AAAC,UAAI,IAAE,EAAC,KAAI,CAACA,IAAEC,IAAEC,OAAI;AAAC;AAAa,QAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,MAAI,EAAC,CAAC;AAAE,YAAIE,KAAED,GAAE,EAAE,GAAE,IAAEA,GAAE,EAAEC,EAAC,GAAE,IAAED,GAAE,GAAG,GAAE,IAAEA,GAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;AAAE,UAAE,KAAK,CAACF,GAAE,IAAG,87BAA67B,EAAE,CAAC;AAAE,cAAM,IAAE;AAAA,MAAC,GAAE,KAAI,CAAAA,OAAG;AAAC;AAAa,QAAAA,GAAE,UAAQ,SAASA,IAAE;AAAC,cAAIC,KAAE,CAAC;AAAE,iBAAOA,GAAE,WAAS,WAAU;AAAC,mBAAO,KAAK,IAAK,SAASA,IAAE;AAAC,kBAAIC,KAAE,IAAGC,KAAE,WAASF,GAAE,CAAC;AAAE,qBAAOA,GAAE,CAAC,MAAIC,MAAG,cAAc,OAAOD,GAAE,CAAC,GAAE,KAAK,IAAGA,GAAE,CAAC,MAAIC,MAAG,UAAU,OAAOD,GAAE,CAAC,GAAE,IAAI,IAAGE,OAAID,MAAG,SAAS,OAAOD,GAAE,CAAC,EAAE,SAAO,IAAE,IAAI,OAAOA,GAAE,CAAC,CAAC,IAAE,IAAG,IAAI,IAAGC,MAAGF,GAAEC,EAAC,GAAEE,OAAID,MAAG,MAAKD,GAAE,CAAC,MAAIC,MAAG,MAAKD,GAAE,CAAC,MAAIC,MAAG,MAAKA;AAAA,YAAC,CAAE,EAAE,KAAK,EAAE;AAAA,UAAC,GAAED,GAAE,IAAE,SAASD,IAAEE,IAAEC,IAAE,GAAE,GAAE;AAAC,wBAAU,OAAOH,OAAIA,KAAE,CAAC,CAAC,MAAKA,IAAE,MAAM,CAAC;AAAG,gBAAI,IAAE,CAAC;AAAE,gBAAGG;AAAE,uBAAQ,IAAE,GAAE,IAAE,KAAK,QAAO,KAAI;AAAC,oBAAI,IAAE,KAAK,CAAC,EAAE,CAAC;AAAE,wBAAM,MAAI,EAAE,CAAC,IAAE;AAAA,cAAG;AAAC,qBAAQ,IAAE,GAAE,IAAEH,GAAE,QAAO,KAAI;AAAC,kBAAI,IAAE,CAAC,EAAE,OAAOA,GAAE,CAAC,CAAC;AAAE,cAAAG,MAAG,EAAE,EAAE,CAAC,CAAC,MAAI,WAAS,MAAI,WAAS,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,SAAS,OAAO,EAAE,CAAC,EAAE,SAAO,IAAE,IAAI,OAAO,EAAE,CAAC,CAAC,IAAE,IAAG,IAAI,EAAE,OAAO,EAAE,CAAC,GAAE,GAAG,IAAG,EAAE,CAAC,IAAE,IAAGD,OAAI,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,UAAU,OAAO,EAAE,CAAC,GAAE,IAAI,EAAE,OAAO,EAAE,CAAC,GAAE,GAAG,GAAE,EAAE,CAAC,IAAEA,MAAG,EAAE,CAAC,IAAEA,KAAG,MAAI,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,cAAc,OAAO,EAAE,CAAC,GAAE,KAAK,EAAE,OAAO,EAAE,CAAC,GAAE,GAAG,GAAE,EAAE,CAAC,IAAE,KAAG,EAAE,CAAC,IAAE,GAAG,OAAO,CAAC,IAAGD,GAAE,KAAK,CAAC;AAAA,YAAE;AAAA,UAAC,GAAEA;AAAA,QAAC;AAAA,MAAC,GAAE,IAAG,CAAAD,OAAG;AAAC;AAAa,QAAAA,GAAE,UAAQ,SAASA,IAAE;AAAC,iBAAOA,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAAAA,OAAG;AAAC;AAAa,YAAIC,KAAE,CAAC;AAAE,iBAASC,GAAEF,IAAE;AAAC,mBAAQE,KAAE,IAAGC,KAAE,GAAEA,KAAEF,GAAE,QAAOE;AAAI,gBAAGF,GAAEE,EAAC,EAAE,eAAaH,IAAE;AAAC,cAAAE,KAAEC;AAAE;AAAA,YAAK;AAAC,iBAAOD;AAAA,QAAC;AAAC,iBAASC,GAAEH,IAAEG,IAAE;AAAC,mBAAQ,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAEH,GAAE,QAAO,KAAI;AAAC,gBAAI,IAAEA,GAAE,CAAC,GAAE,IAAEG,GAAE,OAAK,EAAE,CAAC,IAAEA,GAAE,OAAK,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,KAAG,GAAE,IAAE,GAAG,OAAO,GAAE,GAAG,EAAE,OAAO,CAAC;AAAE,cAAE,CAAC,IAAE,IAAE;AAAE,gBAAI,IAAED,GAAE,CAAC,GAAE,IAAE,EAAC,KAAI,EAAE,CAAC,GAAE,OAAM,EAAE,CAAC,GAAE,WAAU,EAAE,CAAC,GAAE,UAAS,EAAE,CAAC,GAAE,OAAM,EAAE,CAAC,EAAC;AAAE,gBAAG,OAAK;AAAE,cAAAD,GAAE,CAAC,EAAE,cAAaA,GAAE,CAAC,EAAE,QAAQ,CAAC;AAAA,iBAAM;AAAC,kBAAI,IAAE,EAAE,GAAEE,EAAC;AAAE,cAAAA,GAAE,UAAQ,GAAEF,GAAE,OAAO,GAAE,GAAE,EAAC,YAAW,GAAE,SAAQ,GAAE,YAAW,EAAC,CAAC;AAAA,YAAC;AAAC,cAAE,KAAK,CAAC;AAAA,UAAC;AAAC,iBAAO;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAEC,IAAE;AAAC,cAAIC,KAAED,GAAE,OAAOA,EAAC;AAAE,iBAAOC,GAAE,OAAOF,EAAC,GAAE,SAASC,IAAE;AAAC,gBAAGA,IAAE;AAAC,kBAAGA,GAAE,QAAMD,GAAE,OAAKC,GAAE,UAAQD,GAAE,SAAOC,GAAE,cAAYD,GAAE,aAAWC,GAAE,aAAWD,GAAE,YAAUC,GAAE,UAAQD,GAAE;AAAM;AAAO,cAAAE,GAAE,OAAOF,KAAEC,EAAC;AAAA,YAAC;AAAM,cAAAC,GAAE,OAAO;AAAA,UAAC;AAAA,QAAC;AAAC,QAAAF,GAAE,UAAQ,SAASA,IAAEI,IAAE;AAAC,cAAI,IAAED,GAAEH,KAAEA,MAAG,CAAC,GAAEI,KAAEA,MAAG,CAAC,CAAC;AAAE,iBAAO,SAASJ,IAAE;AAAC,YAAAA,KAAEA,MAAG,CAAC;AAAE,qBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,kBAAI,IAAEE,GAAE,EAAE,CAAC,CAAC;AAAE,cAAAD,GAAE,CAAC,EAAE;AAAA,YAAY;AAAC,qBAAQ,IAAEE,GAAEH,IAAEI,EAAC,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,kBAAI,IAAEF,GAAE,EAAE,CAAC,CAAC;AAAE,oBAAID,GAAE,CAAC,EAAE,eAAaA,GAAE,CAAC,EAAE,QAAQ,GAAEA,GAAE,OAAO,GAAE,CAAC;AAAA,YAAE;AAAC,gBAAE;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAAAD,OAAG;AAAC;AAAa,YAAIC,KAAE,CAAC;AAAE,QAAAD,GAAE,UAAQ,SAASA,IAAEE,IAAE;AAAC,cAAIC,KAAE,SAASH,IAAE;AAAC,gBAAG,WAASC,GAAED,EAAC,GAAE;AAAC,kBAAIE,KAAE,SAAS,cAAcF,EAAC;AAAE,kBAAG,OAAO,qBAAmBE,cAAa,OAAO;AAAkB,oBAAG;AAAC,kBAAAA,KAAEA,GAAE,gBAAgB;AAAA,gBAAI,SAAOF,IAAE;AAAC,kBAAAE,KAAE;AAAA,gBAAI;AAAC,cAAAD,GAAED,EAAC,IAAEE;AAAA,YAAC;AAAC,mBAAOD,GAAED,EAAC;AAAA,UAAC,EAAEA,EAAC;AAAE,cAAG,CAACG;AAAE,kBAAM,IAAI,MAAM,yGAAyG;AAAE,UAAAA,GAAE,YAAYD,EAAC;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAAAF,OAAG;AAAC;AAAa,QAAAA,GAAE,UAAQ,SAASA,IAAE;AAAC,cAAIC,KAAE,SAAS,cAAc,OAAO;AAAE,iBAAOD,GAAE,cAAcC,IAAED,GAAE,UAAU,GAAEA,GAAE,OAAOC,IAAED,GAAE,OAAO,GAAEC;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAACD,IAAEC,IAAEC,OAAI;AAAC;AAAa,QAAAF,GAAE,UAAQ,SAASA,IAAE;AAAC,cAAIC,KAAEC,GAAE;AAAG,UAAAD,MAAGD,GAAE,aAAa,SAAQC,EAAC;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAAAD,OAAG;AAAC;AAAa,QAAAA,GAAE,UAAQ,SAASA,IAAE;AAAC,cAAG,eAAa,OAAO;AAAS,mBAAM,EAAC,QAAO,WAAU;AAAA,YAAC,GAAE,QAAO,WAAU;AAAA,YAAC,EAAC;AAAE,cAAIC,KAAED,GAAE,mBAAmBA,EAAC;AAAE,iBAAM,EAAC,QAAO,SAASE,IAAE;AAAC,aAAC,SAASF,IAAEC,IAAEC,IAAE;AAAC,kBAAIC,KAAE;AAAG,cAAAD,GAAE,aAAWC,MAAG,cAAc,OAAOD,GAAE,UAAS,KAAK,IAAGA,GAAE,UAAQC,MAAG,UAAU,OAAOD,GAAE,OAAM,IAAI;AAAG,kBAAI,IAAE,WAASA,GAAE;AAAM,oBAAIC,MAAG,SAAS,OAAOD,GAAE,MAAM,SAAO,IAAE,IAAI,OAAOA,GAAE,KAAK,IAAE,IAAG,IAAI,IAAGC,MAAGD,GAAE,KAAI,MAAIC,MAAG,MAAKD,GAAE,UAAQC,MAAG,MAAKD,GAAE,aAAWC,MAAG;AAAK,kBAAI,IAAED,GAAE;AAAU,mBAAG,eAAa,OAAO,SAAOC,MAAG,uDAAuD,OAAO,KAAK,SAAS,mBAAmB,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,GAAE,KAAK,IAAGF,GAAE,kBAAkBE,IAAEH,IAAEC,GAAE,OAAO;AAAA,YAAC,EAAEA,IAAED,IAAEE,EAAC;AAAA,UAAC,GAAE,QAAO,WAAU;AAAC,aAAC,SAASF,IAAE;AAAC,kBAAG,SAAOA,GAAE;AAAW,uBAAM;AAAG,cAAAA,GAAE,WAAW,YAAYA,EAAC;AAAA,YAAC,EAAEC,EAAC;AAAA,UAAC,EAAC;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAAAD,OAAG;AAAC;AAAa,QAAAA,GAAE,UAAQ,SAASA,IAAEC,IAAE;AAAC,cAAGA,GAAE;AAAW,YAAAA,GAAE,WAAW,UAAQD;AAAA,eAAM;AAAC,mBAAKC,GAAE;AAAY,cAAAA,GAAE,YAAYA,GAAE,UAAU;AAAE,YAAAA,GAAE,YAAY,SAAS,eAAeD,EAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAAAA,OAAG;AAAC,QAAAA,GAAE,UAAQ;AAAA,MAA8P,EAAC,GAAE,IAAE,CAAC;AAAE,eAAS,EAAEG,IAAE;AAAC,YAAI,IAAE,EAAEA,EAAC;AAAE,YAAG,WAAS;AAAE,iBAAO,EAAE;AAAQ,YAAI,IAAE,EAAEA,EAAC,IAAE,EAAC,IAAGA,IAAE,SAAQ,CAAC,EAAC;AAAE,eAAO,EAAEA,EAAC,EAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAE;AAAA,MAAO;AAAC,QAAE,IAAE,CAAAH,OAAG;AAAC,YAAIC,KAAED,MAAGA,GAAE,aAAW,MAAIA,GAAE,UAAQ,MAAIA;AAAE,eAAO,EAAE,EAAEC,IAAE,EAAC,GAAEA,GAAC,CAAC,GAAEA;AAAA,MAAC,GAAE,EAAE,IAAE,CAACD,IAAEC,OAAI;AAAC,iBAAQE,MAAKF;AAAE,YAAE,EAAEA,IAAEE,EAAC,KAAG,CAAC,EAAE,EAAEH,IAAEG,EAAC,KAAG,OAAO,eAAeH,IAAEG,IAAE,EAAC,YAAW,MAAG,KAAIF,GAAEE,EAAC,EAAC,CAAC;AAAA,MAAC,GAAE,EAAE,IAAE,CAACH,IAAEC,OAAI,OAAO,UAAU,eAAe,KAAKD,IAAEC,EAAC,GAAE,EAAE,KAAG;AAAO,UAAI,IAAE,CAAC;AAAE,cAAO,MAAI;AAAC;AAAa,UAAE,EAAE,GAAE,EAAC,SAAQ,MAAI,EAAC,CAAC;AAAE,YAAID,KAAE,EAAE,GAAG,GAAEC,KAAE,EAAE,EAAED,EAAC,GAAE,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,EAAE,GAAG,GAAE,IAAE,CAAC;AAAE,UAAE,oBAAkB,EAAE,GAAE,EAAE,gBAAc,EAAE,GAAE,EAAE,SAAO,EAAE,EAAE,KAAK,MAAK,MAAM,GAAE,EAAE,SAAO,EAAE,GAAE,EAAE,qBAAmB,EAAE,GAAEC,GAAE,EAAE,EAAE,GAAE,CAAC,GAAE,EAAE,KAAG,EAAE,EAAE,UAAQ,EAAE,EAAE;AAAO,cAAM,IAAE,EAAC,YAAW,eAAa,OAAO,UAAQ,OAAO,cAAY,OAAO,WAAW,KAAK,MAAM,EAAC;AAAE,YAAI;AAAE,cAAM,IAAE,IAAI,WAAW,EAAE;AAAE,iBAAS,IAAG;AAAC,cAAG,CAAC,MAAI,IAAE,eAAa,OAAO,UAAQ,OAAO,mBAAiB,OAAO,gBAAgB,KAAK,MAAM,GAAE,CAAC;AAAG,kBAAM,IAAI,MAAM,0GAA0G;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAC;AAAC,cAAM,IAAE,CAAC;AAAE,iBAAQD,KAAE,GAAEA,KAAE,KAAI,EAAEA;AAAE,YAAE,MAAMA,KAAE,KAAK,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;AAAE,cAAM,IAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,cAAG,EAAE,cAAY,CAACD,MAAG,CAACD;AAAE,mBAAO,EAAE,WAAW;AAAE,gBAAMG,MAAGH,KAAEA,MAAG,CAAC,GAAG,WAASA,GAAE,OAAK,GAAG;AAAE,cAAGG,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,IAAE,IAAGA,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,IAAE,KAAIF,IAAE;AAAC,YAAAC,KAAEA,MAAG;AAAE,qBAAQF,KAAE,GAAEA,KAAE,IAAG,EAAEA;AAAE,cAAAC,GAAEC,KAAEF,EAAC,IAAEG,GAAEH,EAAC;AAAE,mBAAOC;AAAA,UAAC;AAAC,iBAAO,SAASD,IAAEC,KAAE,GAAE;AAAC,oBAAO,EAAED,GAAEC,KAAE,CAAC,CAAC,IAAE,EAAED,GAAEC,KAAE,CAAC,CAAC,IAAE,EAAED,GAAEC,KAAE,CAAC,CAAC,IAAE,EAAED,GAAEC,KAAE,CAAC,CAAC,IAAE,MAAI,EAAED,GAAEC,KAAE,CAAC,CAAC,IAAE,EAAED,GAAEC,KAAE,CAAC,CAAC,IAAE,MAAI,EAAED,GAAEC,KAAE,CAAC,CAAC,IAAE,EAAED,GAAEC,KAAE,CAAC,CAAC,IAAE,MAAI,EAAED,GAAEC,KAAE,CAAC,CAAC,IAAE,EAAED,GAAEC,KAAE,CAAC,CAAC,IAAE,MAAI,EAAED,GAAEC,KAAE,EAAE,CAAC,IAAE,EAAED,GAAEC,KAAE,EAAE,CAAC,IAAE,EAAED,GAAEC,KAAE,EAAE,CAAC,IAAE,EAAED,GAAEC,KAAE,EAAE,CAAC,IAAE,EAAED,GAAEC,KAAE,EAAE,CAAC,IAAE,EAAED,GAAEC,KAAE,EAAE,CAAC,GAAG,YAAY;AAAA,UAAC,EAAEE,EAAC;AAAA,QAAC;AAAE,YAAI,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,EAAE,CAAC;AAAA,QAAE,MAAM,EAAC;AAAA,UAAuI,YAAY,EAAC,MAAKH,IAAE,KAAIC,IAAE,UAASC,IAAE,QAAOC,GAAC,GAAE;AAAglW,4CAAS,CAAAH,OAAG,MAAM,KAAKA,GAAE,WAAW,QAAQ,EAAE,QAAQA,EAAC;AAAE,2CAAQ,CAACA,IAAEC,OAAI,EAAE,CAACD,MAAG,CAACC,QAAKD,OAAIC,MAAG,CAAC,GAAG,SAAS,iBAAiB,mBAAmBD,EAAC,IAAI,CAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,oBAAME,KAAEF,GAAE,cAAc,yBAAyB;AAAE,qBAAM,CAAC,CAACE,MAAG,KAAK,QAAQA,GAAE,aAAa,IAAI,GAAED,EAAC;AAAA,YAAC,CAAE;AAAj1W,iBAAK,OAAK,EAAC,MAAKD,GAAE,QAAM,IAAG,QAAOA,GAAE,UAAQ,QAAO,IAAGA,GAAE,MAAI,MAAM,EAAE,CAAC,IAAG,OAAMA,GAAE,SAAO,EAAC,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,MAAIC;AAAE,kBAAK,EAAC,SAAQ,EAAC,OAAMG,GAAC,GAAE,QAAO,EAAC,sBAAqBC,IAAE,iBAAgBC,IAAE,gBAAeC,IAAE,MAAKC,GAAC,EAAC,IAAE,KAAK;AAAI,iBAAK,QAAMJ,IAAE,KAAK,uBAAqBC,IAAE,KAAK,iBAAeE,IAAE,KAAK,kBAAgBD,IAAE,KAAK,OAAKE,IAAE,KAAK,UAAQ,QAAO,KAAK,WAASN,MAAG,OAAG,KAAK,eAAYC,MAAA,gBAAAA,GAAG,gBAAa,UAAS,KAAK,kBAAeA,MAAA,gBAAAA,GAAG,mBAAgB,8CAA6C,KAAK,aAAa,GAAE,KAAK,gCAAgC,GAAE,KAAK,gCAAgC,GAAE,KAAK,gCAAgC;AAAA,UAAC;AAAA,UAAvyB,WAAW,UAAS;AAAC,mBAAM,EAAC,OAAM,UAAS,MAAK,EAAE,EAAC;AAAA,UAAC;AAAA,UAAC,WAAW,mBAAkB;AAAC,mBAAM;AAAA,UAAE;AAAA,UAAC,WAAW,sBAAqB;AAAC,mBAAM;AAAA,UAAE;AAAA,UAAmqB,cAAcH,IAAE;AAAC,mBAAOA,GAAE,UAAU,SAAS,oBAAoB;AAAA,UAAC;AAAA,UAAC,cAAcA,IAAE;AAAC,mBAAOA,GAAE,UAAU,SAAS,wBAAwB,KAAG,QAAQA,GAAE,cAAc,yBAAyB,CAAC;AAAA,UAAC;AAAA,UAAC,8BAA8BA,IAAE;AAAC,gBAAG,YAAUA,GAAE,MAAK;AAAC,oBAAMA,KAAE,SAAS,aAAa,EAAE,aAAYC,KAAE,KAAK,IAAI,OAAO,qBAAqB,GAAEC,KAAE,KAAK,IAAI,OAAO,gBAAgBD,EAAC,GAAE,EAAC,QAAOE,GAAC,IAAED,IAAEE,KAAED,GAAE,WAAW,YAAWE,KAAED,GAAE,SAAS,CAAC,EAAE,WAAUE,KAAED,GAAE,QAAQ,MAAM,GAAEE,KAAE,OAAKD,KAAED,GAAE,SAAOC;AAAE,2BAAW,KAAK,KAAK,WAAS,KAAK,oBAAoB,GAAE,KAAK,kBAAkB;AAAG,oBAAME,KAAEH,GAAE,MAAME,KAAE,GAAEP,GAAE,WAAW;AAAE,cAAAI,GAAE,SAAS,CAAC,EAAE,YAAUC,GAAE,MAAML,GAAE,aAAYO,EAAC,GAAE,KAAK,IAAI,OAAO,OAAO,aAAY,EAAC,MAAKC,GAAC,GAAE,CAAC,GAAEP,KAAE,GAAE,CAAC,GAAE,KAAK,wBAAwB;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,wBAAuB;AAAC,iBAAK,wBAAwB;AAAA,UAAC;AAAA,UAAC,wBAAwBD,KAAE,MAAKC,KAAE,KAAK,QAAQ,IAAGC,KAAE,MAAK;AAAC,kBAAMC,KAAE,SAAOH,KAAE,KAAK,IAAI,OAAO,qBAAqB,IAAEA,IAAEI,KAAEF,MAAG,KAAK,IAAI,OAAO,gBAAgBC,EAAC,GAAEE,KAAE,EAAE;AAAE,iBAAK,QAAQ,SAASD,GAAE,EAAE,KAAG,KAAK,QAAQ,OAAOD,KAAE,GAAE,GAAEC,GAAE,EAAE;AAAE,kBAAK,EAAC,QAAOE,GAAC,IAAEF,IAAEG,KAAED,GAAE,WAAW;AAAW,YAAAA,GAAE,aAAa,cAAaL,EAAC,GAAEK,GAAE,aAAa,MAAKD,EAAC,GAAE,WAAY,MAAIC,GAAE,UAAU,IAAI,oBAAoB,CAAE,GAAE,KAAK,aAAWA,GAAE,YAAU,KAAK,uBAAuB,KAAK,IAAI,GAAEC,GAAE,MAAM;AAAA,UAAE;AAAA,UAAC,uBAAuBP,IAAE;AAAC,gBAAG,YAAUA,GAAE;AAAK,yBAAY,MAAI,KAAK,sBAAsB,CAAE;AAAA,iBAAM;AAAC,oBAAMC,KAAE,KAAK,qBAAqB,GAAEC,KAAE,KAAK,gBAAgBD,EAAC,GAAE,EAAC,QAAOE,GAAC,IAAED;AAAE,kBAAG,UAAQF,GAAE,QAAMA,GAAE,YAAU,KAAK,aAAaC,EAAC,GAAE,gBAAcD,GAAE,MAAK;AAAC,sBAAMA,KAAE,SAAS,aAAa,EAAE;AAAY,qBAAK,YAAYG,IAAED,GAAE,IAAGF,EAAC;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,gBAAG,MAAIA,IAAE;AAAC,oBAAMF,KAAE,KAAK,QAAQ,QAAQC,EAAC;AAAE,mBAAK,QAAQ,OAAOD,IAAE,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,6BAA6BA,IAAE;AAAC,kBAAMC,KAAE,KAAK,IAAI,OAAO,gBAAgBD,EAAC,GAAE,EAAC,QAAOE,GAAC,IAAED;AAAE,gBAAG,CAAC,KAAK,QAAQ,SAASA,GAAE,EAAE,GAAE;AAAC,oBAAMD,KAAE,KAAK,QAAQ,QAAQC,GAAE,EAAE;AAAE,mBAAK,QAAQ,OAAOD,IAAE,CAAC;AAAA,YAAC;AAAC,YAAAE,GAAE,gBAAgB,YAAY,GAAEA,GAAE,gBAAgB,IAAI,GAAEA,GAAE,YAAU,CAAC,GAAEA,GAAE,UAAQ,CAAC,GAAEA,GAAE,UAAU,OAAO,oBAAoB;AAAA,UAAC;AAAA,UAAC,eAAc;AAAC,iBAAK,UAAQ,SAAS,cAAc,KAAK,GAAE,KAAK,QAAQ,UAAU,IAAI,wBAAwB,GAAE,KAAK,QAAQ,KAAG,KAAK,KAAK;AAAG,kBAAMF,KAAE,SAAS,cAAc,MAAM,GAAEC,KAAE,SAAS,cAAc,KAAK,GAAEC,KAAE,SAAS,cAAc,KAAK;AAAE,YAAAF,GAAE,UAAU,IAAI,oBAAoB,GAAEA,GAAE,YAAU,EAAE,GAAEC,GAAE,UAAU,IAAI,qBAAqB,GAAEA,GAAE,aAAa,mBAAkB,CAAC,KAAK,QAAQ,GAAEA,GAAE,YAAU,KAAK,KAAK,QAAM,IAAG,KAAK,aAAWA,GAAE,iBAAiB,SAAQ,KAAK,8BAA8B,KAAK,IAAI,CAAC,GAAEA,GAAE,iBAAiB,WAAU,KAAK,aAAa,KAAK,IAAI,CAAC,GAAEA,GAAE,iBAAiB,WAAW,MAAI,KAAK,2BAA2B,CAAE,GAAEA,GAAE,iBAAiB,SAAQ,KAAK,eAAe,KAAK,IAAI,CAAC,GAAEA,GAAE,aAAa,eAAc,KAAK,WAAW,GAAEA,GAAE,iBAAiB,SAAQ,KAAK,kBAAkB,KAAK,IAAI,CAAC,GAAEA,GAAE,iBAAiB,YAAW,KAAK,kBAAkB,KAAK,IAAI,CAAC,GAAEC,GAAE,iBAAiB,SAAQ,KAAK,sBAAsB,KAAK,IAAI,CAAC,GAAED,GAAE,iBAAiB,SAAQ,KAAK,yBAAyB,KAAK,IAAI,CAAC,IAAGC,GAAE,UAAU,IAAI,iCAAgC,sBAAsB,GAAEA,GAAE,YAAU,KAAK,gBAAe,KAAK,QAAQ,YAAYF,EAAC,GAAE,KAAK,QAAQ,YAAYC,EAAC,GAAE,KAAK,QAAQ,YAAYC,EAAC;AAAA,UAAC;AAAA,UAAC,6BAA4B;AAAC,kBAAMF,KAAE,SAAS,eAAcC,KAAE,OAAO,aAAa,GAAEC,KAAE,SAAS,YAAY;AAAE,YAAAD,GAAE,gBAAgB,GAAEC,GAAE,mBAAmBF,EAAC,GAAEE,GAAE,SAAS,KAAE,GAAED,GAAE,SAASC,EAAC,GAAEF,GAAE,MAAM;AAAA,UAAC;AAAA,UAAC,wBAAuB;AAAC,iBAAK,IAAI,OAAO,OAAO,GAAE,KAAK,wBAAwB,GAAE,KAAK,kBAAkB;AAAA,UAAC;AAAA,UAAC,oBAAmB;AAAC,kBAAMA,KAAE,SAAS,iBAAiB,mBAAmB,KAAK,QAAQ,EAAE,IAAI,GAAE,EAAC,YAAWC,IAAE,WAAUC,GAAC,IAAE,KAAK,SAAQ,EAAC,QAAOC,GAAC,IAAE,KAAK,MAAKC,KAAEJ,GAAE,SAAO,KAAG,aAAWG;AAAE,YAAAD,GAAE,UAAU,OAAO,wBAAuBE,EAAC,GAAEH,GAAE,MAAM,QAAM,MAAID,GAAE,SAAO,SAAO;AAAA,UAAO;AAAA,UAAC,aAAaA,IAAE;AAAC,gBAAG,gBAAcA,GAAE,MAAK;AAAC,oBAAK,EAAC,UAASA,GAAC,IAAE,KAAK,SAAQC,KAAED,GAAE,CAAC,EAAE;AAAU,kBAAG,MAAI,SAAS,aAAa,EAAE,aAAY;AAAC,sBAAMA,KAAE,KAAK,IAAI,OAAO,qBAAqB,GAAEE,KAAED,GAAE,QAAQ,MAAM,GAAEE,KAAE,OAAKD,KAAED,GAAE,SAAOC,IAAEE,KAAE,SAAS,iBAAiB,mBAAmB,KAAK,QAAQ,EAAE,IAAI;AAAE,yBAAQH,KAAE,GAAEA,KAAEG,GAAE,SAAO,GAAEH,MAAG;AAAE,uBAAK,6BAA6BD,KAAEC,EAAC;AAAE,qBAAK,IAAI,OAAO,OAAOD,EAAC,GAAE,KAAK,IAAI,OAAO,OAAO,aAAY,EAAC,MAAKC,GAAE,MAAM,GAAEE,EAAC,EAAC,GAAE,CAAC,GAAEH,IAAE,CAAC,GAAE,KAAK,IAAI,MAAM,WAAWA,EAAC;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,oBAAoBA,IAAEC,IAAE;AAAC,kBAAMC,KAAE,KAAK,gBAAgBF,EAAC,GAAE,EAAC,QAAOG,GAAC,IAAED;AAAE,mBAAO,KAAK,cAAcC,EAAC,KAAGF,OAAIE,GAAE,cAAc,yBAAyB,EAAE,aAAa,IAAI,IAAEH,KAAEA,KAAE,KAAG,IAAE,KAAK,oBAAoBA,KAAE,GAAEC,EAAC,IAAE;AAAA,UAAE;AAAA,UAAC,aAAaD,IAAE;AAAC,kBAAMC,KAAE,KAAK,gBAAgBD,EAAC,GAAE,EAAC,QAAOE,GAAC,IAAED;AAAE,gBAAG,KAAK,cAAcC,EAAC,GAAE;AAAC,oBAAMD,KAAEC,GAAE,aAAa,YAAY,GAAEC,KAAE,KAAK,oBAAoBH,IAAEC,EAAC;AAAE,kBAAGE,MAAG,GAAE;AAAC,sBAAMD,KAAE,KAAK,qBAAqBD,EAAC,GAAEG,KAAED,KAAED;AAAE,gBAAAA,KAAE,KAAG,KAAK,IAAI,OAAO,KAAKE,IAAEJ,EAAC,GAAE,WAAY,MAAI,KAAK,6BAA6BI,EAAC,GAAG,GAAG;AAAA,cAAC;AAAA,YAAC;AAAC,iBAAK,IAAI,MAAM,WAAWJ,EAAC,GAAE,KAAK,IAAI,QAAQ,MAAM;AAAA,UAAC;AAAA,UAAC,eAAeA,IAAE;AAAC,gBAAG,gBAAcA,GAAE,QAAM,YAAUA,GAAE,MAAK;AAAC,oBAAK,EAAC,UAASA,GAAC,IAAE,KAAK,SAAQ,EAAC,QAAOC,GAAC,IAAED,GAAE,CAAC,EAAE;AAAY,oBAAIC,OAAID,GAAE,CAAC,EAAE,cAAY;AAAA,YAAG;AAAA,UAAC;AAAA,UAAC,SAAQ;AAAC,mBAAO,KAAK,aAAa,GAAE,WAAY,MAAI,KAAK,YAAY,CAAE,GAAE,WAAY,MAAI,KAAK,qBAAqB,CAAE,GAAE,KAAK;AAAA,UAAO;AAAA,UAAC,uBAAsB;AAAC,kBAAK,EAAC,QAAOA,GAAC,IAAE,KAAK,MAAKC,KAAE,KAAK,QAAQ,WAAW;AAAW,YAAAA,GAAE,MAAM,aAAW,QAAOA,GAAE,MAAM,YAAU,UAAU,aAAWD,KAAE,IAAE,EAAE;AAAA,UAAM;AAAA,UAAC,cAAa;AAAC,kBAAMA,KAAE,KAAK,IAAI,OAAO,eAAe,GAAEC,KAAE,KAAK,QAAQ;AAAW,gBAAIC;AAAE,gBAAG,KAAK,UAAS;AAAC,oBAAMF,KAAE,SAAS,uBAAuB,wBAAwB,EAAE,CAAC,GAAE,EAAC,UAASC,GAAC,IAAED,IAAE,EAAC,QAAOG,GAAC,IAAEF;AAAE,uBAAQD,KAAE,GAAEA,KAAEG,IAAEH,MAAG,GAAE;AAAC,sBAAMG,KAAEF,GAAED,EAAC,EAAE,WAAW,YAAW,EAAC,IAAGI,GAAC,IAAED;AAAE,oBAAGC,OAAI,KAAK,QAAQ,IAAG;AAAC,kBAAAF,KAAEF;AAAE;AAAA,gBAAK;AAAA,cAAC;AAAA,YAAC,OAAK;AAAC,oBAAMC,KAAE,KAAK,QAAQ,SAAS,CAAC;AAAE,kBAAIE,KAAE,CAAC,GAAEC,KAAE,KAAK,IAAI,OAAO,qBAAqB;AAAE,oBAAMC,KAAED,OAAIJ,KAAE,IAAE,KAAG;AAAE,qBAAKG,GAAE,CAAC,MAAIF,MAAG;AAAC,gBAAAC,KAAEE;AAAE,sBAAMJ,KAAE,KAAK,IAAI,OAAO,gBAAgBE,EAAC;AAAE,oBAAG,CAACF;AAAE;AAAM,sBAAK,EAAC,QAAOC,GAAC,IAAED;AAAE,gBAAAG,KAAEF,GAAE,WAAW,WAAW,UAASG,MAAGC;AAAA,cAAC;AAAA,YAAC;AAAC,gBAAGH,KAAE,KAAK,KAAK,QAAMF;AAAE,uBAAQA,KAAEE,KAAE,GAAED,KAAE,GAAED,MAAGE,KAAE,KAAK,KAAK,OAAMF,MAAG,GAAE;AAAC,sBAAME,KAAE,KAAK,IAAI,OAAO,gBAAgBF,EAAC,GAAE,EAAC,QAAOG,GAAC,IAAED,IAAEE,KAAED,GAAE,WAAW;AAAW,oBAAG,KAAK,gBAAgBC,EAAC,GAAE;AAAC,uBAAK,KAAK,QAAMH;AAAE;AAAA,gBAAK;AAAC,qBAAK,wBAAwBD,EAAC,GAAEC,MAAG;AAAA,cAAC;AAAA;AAAM,mBAAK,KAAK,QAAM;AAAE,YAAAA,GAAE,iBAAiB,SAAS,MAAI;AAAC,mBAAK,oBAAoB,GAAE,WAAY,MAAI;AAAC,qBAAK,kBAAkB;AAAA,cAAC,CAAE;AAAA,YAAC,CAAE,GAAE,KAAK,kBAAkB;AAAA,UAAC;AAAA,UAAC,sBAAqB;AAAC,kBAAMD,KAAE,KAAK,QAAQ,WAAW;AAAW,yBAAW,KAAK,KAAK,UAAQ,KAAK,KAAK,SAAO,QAAOA,GAAE,MAAM,YAAU,oBAAkB,KAAK,KAAK,SAAO,UAASA,GAAE,MAAM,YAAU,iBAAgB,KAAK,IAAI,OAAO,gBAAgB,KAAK,IAAI,OAAO,qBAAqB,CAAC,EAAE,OAAO,aAAa,UAAS,KAAK,KAAK,MAAM;AAAA,UAAC;AAAA,UAAC,kBAAkBA,KAAE,KAAK,QAAQ,IAAGC,KAAE,KAAK,KAAK,QAAO;AAAC,kBAAMC,KAAE,SAAS,iBAAiB,mBAAmBF,EAAC,IAAI,GAAE,EAAC,QAAOG,GAAC,IAAED;AAAE,gBAAGC,KAAE;AAAE,cAAAD,GAAE,QAAS,CAAAF,OAAG;AAAC,gBAAAA,GAAE,SAAO,aAAWC;AAAE,sBAAMC,KAAEF,GAAE,iBAAiB,yBAAyB;AAAE,oBAAGE,GAAE,SAAO,GAAE;AAAC,wBAAMC,KAAE,aAAWF,KAAEA,KAAED,GAAE,aAAa,QAAQ;AAAE,uBAAK,kBAAkBE,GAAE,CAAC,EAAE,aAAa,IAAI,GAAEC,EAAC;AAAA,gBAAC;AAAA,cAAC,CAAE;AAAA,qBAAUH,OAAI,KAAK,QAAQ,IAAG;AAAC,oBAAK,EAAC,WAAUA,GAAC,IAAE,KAAK;AAAQ,cAAAA,GAAE,UAAU,OAAO,wBAAuBC,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,KAAKD,IAAE;AAAC,kBAAMC,KAAED,GAAE,aAAa,IAAI,GAAE,EAAC,UAASE,GAAC,IAAEF,IAAEG,KAAED,GAAE,CAAC,EAAE,WAAUE,KAAE,SAAS,iBAAiB,mBAAmBH,EAAC,IAAI;AAAE,mBAAO,KAAK,KAAK,KAAGA,IAAE,OAAO,OAAO,KAAK,MAAK,EAAC,MAAKE,IAAE,OAAMC,GAAE,OAAM,CAAC;AAAA,UAAC;AAAA,UAAC,qBAAqBJ,IAAE;AAAC,gBAAIC,KAAE;AAAE,mBAAO,SAAS,iBAAiB,mBAAmBD,EAAC,IAAI,EAAE,QAAS,CAAAA,OAAG;AAAC,kBAAGA,GAAE,aAAa,QAAQ,GAAE;AAAC,sBAAME,KAAEF,GAAE,cAAc,yBAAyB,EAAE,aAAa,IAAI;AAAE,gBAAAC,MAAG,KAAK,qBAAqBC,EAAC;AAAA,cAAC;AAAC,cAAAD,MAAG;AAAA,YAAC,CAAE,GAAEA;AAAA,UAAC;AAAA,UAAC,qBAAqBD,IAAE;AAAC,qBAAS,iBAAiB,mBAAmBA,EAAC,IAAI,EAAE,QAAS,CAAAA,OAAG;AAAC,kBAAGA,GAAE,UAAU,IAAI,oBAAoB,GAAEA,GAAE,aAAa,QAAQ,GAAE;AAAC,sBAAMC,KAAED,GAAE,cAAc,yBAAyB,EAAE,aAAa,IAAI;AAAE,qBAAK,qBAAqBC,EAAC;AAAA,cAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,iBAAgB;AAAC,kBAAMD,KAAE,SAAS,uBAAuB,aAAa,EAAE,CAAC;AAAE,mBAAO,WAAY,MAAI;AAAC,oBAAMC,KAAED,GAAE,WAAUE,KAAE,KAAK,IAAI,OAAO,qBAAqB;AAAE,mBAAK,qBAAqB,KAAK,QAAQ,EAAE;AAAE,oBAAMC,KAAEF,GAAE,cAAc,4BAA4B,KAAGA,GAAE,uBAAuB,iBAAiB,EAAE,CAAC,GAAEG,KAAEH,GAAE,cAAc,8BAA8B,KAAGA,GAAE,uBAAuB,mBAAmB,EAAE,CAAC,GAAEI,KAAEJ,GAAE,cAAc,2BAA2B,KAAGA,GAAE,uBAAuB,6BAA6B,EAAE,CAAC;AAAE,mBAAK,qBAAqBG,IAAE,GAAEF,EAAC,GAAE,KAAK,qBAAqBC,IAAE,GAAED,EAAC,GAAE,KAAK,qBAAqBG,IAAEH,EAAC;AAAA,YAAC,CAAE,GAAE,SAAS,cAAc,KAAK;AAAA,UAAC;AAAA,UAAC,qBAAqBF,IAAEC,IAAEC,IAAE;AAAC,YAAAF,MAAGA,GAAE,iBAAiB,SAAS,MAAI;AAAC,mBAAK,WAAWE,IAAED,EAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,qBAAqBD,IAAEC,IAAE;AAAC,YAAAD,MAAGA,GAAE,iBAAiB,SAAS,MAAI;AAAC,oBAAME,KAAEF,GAAE;AAAU,qBAAK,OAAO,OAAOE,EAAC,EAAE,QAAQ,2BAA2B,IAAEF,GAAE,UAAU,IAAI,2BAA2B,IAAE,KAAK,iBAAiBC,EAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,WAAWD,IAAEC,IAAE;AAAC,gBAAG,CAAC,KAAK,UAAS;AAAC,mBAAK,MAAM;AAAE,oBAAMC,KAAE,KAAK,qBAAqB,GAAEC,KAAE,KAAK,qBAAqB,KAAK,QAAQ,EAAE,GAAEC,KAAE,KAAK,eAAe,GAAEC,KAAEL,KAAEG;AAAE,mBAAK,KAAKH,IAAEE,EAAC,GAAEF,MAAG,KAAGK,MAAGD,KAAE,MAAI,MAAIH,KAAE,KAAK,SAASD,IAAEK,EAAC,IAAE,KAAK,OAAOL,IAAEK,EAAC;AAAA,YAAE;AAAA,UAAC;AAAA,UAAC,SAASL,IAAEC,IAAE;AAAC,kBAAMC,KAAED,KAAE,GAAEE,KAAE,KAAK,gBAAgBD,EAAC,GAAE,EAAC,QAAOE,GAAC,IAAED;AAAE,gBAAG,KAAK,KAAKH,IAAEE,EAAC,GAAE,aAAWC,GAAE,MAAK;AAAC,oBAAMF,KAAEG,GAAE,cAAc,yBAAyB,EAAE,aAAa,IAAI,GAAED,KAAE,KAAK,qBAAqBF,EAAC;AAAE,mBAAK,gBAAgBE,IAAEH,KAAE,GAAEE,KAAE,GAAE,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,OAAOF,IAAEC,IAAE;AAAC,kBAAMC,KAAEF,KAAE,GAAEG,KAAE,KAAK,gBAAgBD,EAAC;AAAE,gBAAG,aAAWC,GAAE;AAAK;AAAO,kBAAK,EAAC,QAAOC,GAAC,IAAED;AAAE,gBAAGC,GAAE,aAAa,YAAY,GAAE;AAAC,oBAAMF,KAAE,KAAK,gBAAgBF,EAAC,EAAE,OAAO,aAAa,YAAY,GAAEG,KAAEC,GAAE,aAAa,YAAY;AAAE,kBAAGD,OAAID,IAAE;AAAC,sBAAME,KAAE,KAAK,uBAAuBF,IAAEC,IAAEH,EAAC,GAAEK,KAAE,KAAK,gBAAgBD,EAAC,EAAE,OAAO,cAAc,yBAAyB,EAAE,aAAa,IAAI,GAAEE,KAAE,KAAK,qBAAqBD,EAAC;AAAE,uBAAO,KAAK,KAAKJ,IAAEG,EAAC,GAAE,KAAK,KAAK,gBAAgBE,IAAEL,IAAEG,IAAE,CAAC;AAAA,cAAC;AAAA,YAAC;AAAC,iBAAK,KAAKH,IAAEC,EAAC;AAAA,UAAC;AAAA,UAAC,uBAAuBF,IAAEC,IAAEC,IAAE;AAAC,kBAAMC,KAAED,MAAG,KAAK,qBAAqBD,EAAC,IAAE,IAAGG,KAAE,KAAK,gBAAgBD,EAAC,EAAE;AAAO,gBAAGC,GAAE,aAAa,YAAY,GAAE;AAAC,oBAAMH,KAAEG,GAAE,aAAa,YAAY;AAAE,kBAAGH,OAAID,IAAE;AAAC,sBAAME,KAAE,KAAK,gBAAgBC,KAAE,CAAC,EAAE;AAAO,oBAAGD,GAAE,aAAa,YAAY,GAAE;AAAC,wBAAME,KAAEF,GAAE,aAAa,YAAY;AAAE,sBAAGE,OAAIH;AAAE,2BAAO,KAAK,uBAAuBD,IAAEI,IAAED,EAAC;AAAA,gBAAC;AAAA,cAAC;AAAA,YAAC;AAAC,mBAAOA;AAAA,UAAC;AAAA,UAAC,gBAAgBH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAEF,IAAEG,KAAEJ;AAAE,mBAAKD;AAAG,mBAAK,KAAKK,IAAED,EAAC,GAAE,MAAID,OAAIC,MAAG,GAAEC,MAAG,IAAGL,MAAG;AAAA,UAAC;AAAA,UAAC,iBAAiBA,IAAE;AAAC,kBAAMC,KAAE,SAAS,iBAAiB,mBAAmB,KAAK,QAAQ,EAAE,IAAI,GAAE,EAAC,QAAOC,GAAC,IAAED;AAAE,qBAAQA,KAAED,IAAEC,KAAED,KAAEE,IAAED,MAAG;AAAE,yBAAY,MAAI,KAAK,IAAI,OAAO,OAAOD,EAAC,CAAE;AAAA,UAAC;AAAA,UAAC,eAAc;AAAC,iBAAK,YAAU,SAAS,cAAc,iBAAiB,SAAS,CAAAA,OAAG;AAAC,oBAAMC,KAAE,SAAS,eAAcC,KAAE,KAAK,qBAAqB,GAAE,EAAC,QAAOC,GAAC,IAAE,KAAK,gBAAgBD,EAAC;AAAE,0BAAUF,GAAE,OAAK,KAAK,yBAAyBC,EAAC,IAAEC,KAAE,KAAG,CAAC,KAAK,gBAAgBC,EAAC,KAAG,UAAQH,GAAE,QAAM,KAAK,UAAUG,EAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,kCAAiC;AAAC,gBAAG,CAAC,KAAK,UAAS;AAAC,oBAAMH,KAAE,SAAS,cAAc,4BAA4B,GAAEC,KAAE,EAAC,YAAW,MAAG,WAAU,MAAG,eAAc,KAAE;AAAE,kBAAI,iBAAkB,CAAAD,OAAG;AAAC,gBAAAA,GAAE,QAAS,CAAAA,OAAG;AAAC,kCAAcA,GAAE,QAAM,WAAW,KAAK,sBAAsB,KAAK,MAAKA,EAAC,CAAC;AAAA,gBAAC,CAAE;AAAA,cAAC,CAAE,EAAE,QAAQA,IAAEC,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAsQ,kCAAiC;AAAC,gBAAG,CAAC,KAAK,UAAS;AAAC,kBAAG,WAAS,KAAK;AAAQ,uBAAO,KAAK,WAAY,MAAI,KAAK,gCAAgC,GAAG,GAAG;AAAE,uBAAS,cAAc,IAAI,KAAK,QAAQ,EAAE,EAAE,EAAE,WAAW,WAAW,aAAa,UAAS,KAAK,KAAK,MAAM;AAAE,oBAAMD,KAAE,SAAS,cAAc,2BAA2B;AAAE,cAAAA,GAAE,aAAa,aAAY,MAAM,GAAEA,GAAE,iBAAiB,aAAa,MAAI;AAAC,qBAAK,aAAW,KAAK,IAAI,OAAO,qBAAqB,GAAE,KAAK,cAAY,KAAK,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE,MAAK,KAAK,gBAAc,KAAK,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;AAAA,cAAM,CAAE,GAAE,SAAS,iBAAiB,QAAQ,CAAAA,OAAG;AAAC,sBAAK,EAAC,QAAOC,GAAC,IAAED;AAAE,oBAAG,SAAS,SAASC,EAAC,GAAE;AAAC,wBAAMD,KAAEC,GAAE,UAAU,SAAS,UAAU,IAAEA,KAAEA,GAAE,QAAQ,WAAW;AAAE,sBAAGD,MAAGA,OAAI,KAAK,eAAc;AAAC,wBAAIC,KAAE,KAAK,SAASD,EAAC;AAAE,oBAAAC,KAAE,KAAK,aAAWA,KAAEA,KAAE,IAAEA;AAAE,0BAAMC,KAAEF,GAAE,iBAAiB,yBAAyB,EAAE,SAAO,KAAG,SAAOA,GAAE,aAAa,YAAY;AAAE,+BAAY,MAAI;AAAC,0BAAG,aAAW,KAAK,aAAY;AAAC,8BAAMG,KAAE,KAAK,cAAc,cAAc,IAAI,KAAK,QAAQ,EAAE,EAAE;AAAE,4BAAGA;AAAE,8BAAG,KAAK,QAAQA,GAAE,aAAa,IAAI,GAAEH,GAAE,aAAa,YAAY,CAAC,GAAE;AAAC,gCAAG,KAAK,eAAaC,KAAE,KAAK,IAAI,OAAO,KAAK,KAAK,aAAW,GAAEA,EAAC,IAAE,KAAK,IAAI,OAAO,KAAK,KAAK,YAAWA,EAAC,GAAE,CAACC,IAAE;AAAC,oCAAMF,KAAE,KAAK,SAAS,KAAK,aAAa;AAAE,mCAAK,6BAA6BA,EAAC;AAAA,4BAAC;AAAA,0BAAC;AAAM,iCAAK,2BAA2BE,IAAEF,EAAC,GAAE,KAAK,aAAaC,EAAC;AAAA,sBAAC;AAAM,6BAAK,eAAa,KAAK,2BAA2BC,IAAEF,EAAC;AAAE,0BAAG,CAACE,IAAE;AAAC,8BAAMF,KAAE,KAAK,SAAS,KAAK,aAAa;AAAE,6BAAK,6BAA6BA,EAAC;AAAA,sBAAC;AAAA,oBAAC,CAAE;AAAA,kBAAC;AAAA,gBAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,2BAA2BA,IAAEC,IAAE;AAAC,gBAAGD,IAAE;AAAC,oBAAMA,KAAEC,GAAE,aAAa,YAAY,KAAGA,GAAE,cAAc,yBAAyB,EAAE,aAAa,IAAI,GAAEC,KAAE,KAAK,SAAS,KAAK,aAAa;AAAE,mBAAK,wBAAwBA,IAAEF,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,aAAaA,IAAEC,KAAE,KAAK,QAAQ,IAAG;AAAC,gBAAIC,KAAE,SAAS,iBAAiB,mBAAmBD,EAAC,IAAI;AAAE,YAAAC,KAAE,KAAK,cAAYF,KAAE,CAAC,GAAGE,EAAC,EAAE,QAAQ,IAAEA,IAAEA,GAAE,QAAS,CAAAD,OAAG;AAAC,oBAAMC,KAAE,KAAK,SAASD,EAAC;AAAE,mBAAK,IAAI,OAAO,KAAKD,IAAEE,EAAC;AAAE,oBAAMC,KAAEF,GAAE,iBAAiB,yBAAyB;AAAE,kBAAGE,GAAE,SAAO,GAAE;AAAC,sBAAMD,KAAE,KAAK,SAASD,EAAC,GAAEG,KAAE,KAAK,aAAWJ,KAAE,IAAE;AAAE,gBAAAG,GAAE,QAAS,CAAAH,OAAG,KAAK,aAAaE,KAAEE,IAAEJ,GAAE,aAAa,IAAI,CAAC,CAAE;AAAE,sBAAMK,KAAE,KAAK,IAAIL,KAAEE,EAAC;AAAE,gBAAAF,KAAE,KAAK,aAAWA,KAAEA,KAAEK,KAAEL,KAAEK;AAAA,cAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,UAAC,sBAAsBL,IAAE;AAAC,gBAAG,WAAS,KAAK,SAAQ;AAAC,oBAAMC,KAAE,KAAK,IAAI,OAAO,qBAAqB,GAAEC,KAAE,KAAK,IAAI,OAAO,gBAAgBD,EAAC,GAAE,EAAC,QAAOE,GAAC,IAAED,IAAEE,KAAE,CAAC,KAAK,gBAAgBD,EAAC,GAAE,EAAC,QAAOE,GAAC,IAAE,KAAK,SAAQ,EAAC,QAAOC,GAAC,IAAE,SAAS,iBAAiB,mBAAmB,KAAK,KAAK,EAAE,IAAI;AAAE,kBAAG,KAAK,QAAQ,SAASJ,GAAE,EAAE,KAAGE;AAAE,qBAAK,wBAAwBH,EAAC;AAAA,uBAAUD,GAAE,WAAW,CAAC,MAAGA,MAAA,gBAAAA,GAAG,oBAAiB,KAAK,gBAAgBA,GAAE,eAAe,KAAG,CAAC,KAAK,gBAAgBA,GAAE,WAAW,CAAC,CAAC,KAAGK,KAAEC,IAAE;AAAC,sBAAK,EAAC,IAAGH,GAAC,IAAEH,GAAE,WAAW,CAAC,GAAEI,KAAE,KAAK,IAAI,OAAO,QAAQD,EAAC;AAAE,qBAAK,wBAAwB,MAAK,KAAK,QAAQ,IAAGC,EAAC,GAAE,KAAK,QAAQH,EAAC,IAAEC,GAAE;AAAA,cAAE;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,yBAAyBF,IAAE;AAAC,kBAAMC,KAAED,GAAE;AAAY,gBAAG,QAAMC,GAAE,CAAC,KAAG,CAAC,KAAK,gBAAgBD,EAAC,GAAE;AAAC,oBAAMA,KAAE,KAAK,IAAI,OAAO,qBAAqB;AAAE,mBAAK,IAAI,OAAO,OAAO,UAAS,EAAC,MAAKC,GAAE,MAAM,CAAC,EAAC,GAAE,KAAK,KAAID,IAAE,IAAE,GAAE,KAAK,IAAI,OAAO,OAAOA,KAAE,CAAC,GAAE,KAAK,IAAI,MAAM,WAAWA,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,UAAUA,IAAE;AAAC,kBAAMC,KAAED,GAAE,wBAAuBE,KAAED,GAAE,WAAW;AAAW,gBAAG,KAAK,gBAAgBC,EAAC,KAAG,KAAK,gBAAgBD,EAAC,GAAE;AAAC,oBAAME,KAAEF,GAAE,aAAa,YAAY,GAAEG,KAAEF,GAAE,aAAa,IAAI,GAAEG,KAAEF,MAAGC;AAAE,cAAAJ,GAAE,aAAa,0BAAyB,IAAE,GAAE,SAAS,eAAeK,EAAC,EAAE,SAAS,CAAC,EAAE,MAAM;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,2BAA0B;AAAC,kBAAML,KAAE,KAAK,IAAI,OAAO,qBAAqB,GAAEC,KAAE,KAAK,IAAI,OAAO,gBAAgBD,EAAC,GAAE,EAAC,QAAOE,GAAC,IAAED;AAAE,YAAAC,GAAE,aAAa,wBAAwB,MAAIA,GAAE,gBAAgB,wBAAwB,GAAE,KAAK,wBAAwBF,EAAC,GAAE,KAAK,IAAI,QAAQ,MAAM;AAAA,UAAE;AAAA,UAAC,gBAAgBA,IAAE;AAAC,kBAAMC,KAAE,MAAM,KAAKD,GAAE,SAAS,GAAEE,KAAE,CAAC,sBAAqB,uBAAsB,wBAAwB,GAAEC,KAAED,GAAE,KAAM,CAAAD,OAAG,MAAID,GAAE,uBAAuBC,EAAC,EAAE,MAAO;AAAE,mBAAOC,GAAE,KAAM,CAAAF,OAAGC,GAAE,SAASD,EAAC,CAAE,KAAGG;AAAA,UAAC;AAAA,UAAC,kCAAiC;AAAC,gBAAG,CAAC,KAAK,UAAS;AAAC,oBAAMH,KAAE,SAAS,cAAc,4BAA4B,GAAEC,KAAE,EAAC,YAAW,MAAG,WAAU,MAAG,eAAc,KAAE;AAAE,kBAAI,iBAAkB,CAAAD,OAAG;AAAC,gBAAAA,GAAE,QAAS,CAAAA,OAAG;AAAC,kCAAcA,GAAE,QAAM,WAAW,KAAK,qBAAqB,KAAK,MAAKA,EAAC,CAAC;AAAA,gBAAC,CAAE;AAAA,cAAC,CAAE,EAAE,QAAQA,IAAEC,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,uBAAsB;AAAC,gBAAG,WAAS,KAAK,SAAQ;AAAC,oBAAMD,KAAE,KAAK,IAAI,OAAO,qBAAqB,GAAE,EAAC,QAAOC,GAAC,IAAE,KAAK,IAAI,OAAO,gBAAgBD,EAAC;AAAE,kBAAG,KAAK,gBAAgBC,EAAC,GAAE;AAAC,sBAAMC,KAAED,GAAE,aAAa,YAAY;AAAE,oBAAG,SAAS,iBAAiB,IAAIC,EAAC,EAAE,EAAE,SAAO,GAAE;AAAC,wBAAMD,KAAE,KAAK,oBAAoBD,IAAEE,EAAC,GAAEC,KAAE,EAAE;AAAE,2BAAQD,KAAED,IAAEC,MAAGF,IAAEE,MAAG,GAAE;AAAC,0BAAMF,KAAE,KAAK,IAAI,OAAO,gBAAgBE,EAAC,GAAE,EAAC,QAAOE,GAAC,IAAEJ;AAAE,oBAAAE,OAAID,KAAEG,GAAE,WAAW,WAAW,aAAa,MAAK,MAAMD,EAAC,EAAE,IAAEC,GAAE,aAAa,cAAa,MAAMD,EAAC,EAAE;AAAA,kBAAC;AAAA,gBAAC;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,GAAG,GAAE,EAAE;AAAA,IAAO,GAAG,CAAE;AAAA;AAAA;", "names": ["t", "e", "o", "s", "i", "r", "n", "l", "c"]}