{"version": 3, "sources": ["../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/uk/_lib/formatDistance.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/uk/_lib/formatLong.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/uk/_lib/formatRelative.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/uk/_lib/localize.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/uk/_lib/match.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/uk.mjs"], "sourcesContent": ["function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\n\nfunction buildLocalizeTokenFn(scheme) {\n  return (count, options) => {\n    if (options && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return \"за \" + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + \" тому\";\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\n\nconst halfAtMinute = (_, options) => {\n  if (options && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"за півхвилини\";\n    } else {\n      return \"півхвилини тому\";\n    }\n  }\n\n  return \"півхвилини\";\n};\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: \"менше секунди\",\n      singularNominative: \"менше {{count}} секунди\",\n      singularGenitive: \"менше {{count}} секунд\",\n      pluralGenitive: \"менше {{count}} секунд\",\n    },\n    future: {\n      one: \"менше, ніж за секунду\",\n      singularNominative: \"менше, ніж за {{count}} секунду\",\n      singularGenitive: \"менше, ніж за {{count}} секунди\",\n      pluralGenitive: \"менше, ніж за {{count}} секунд\",\n    },\n  }),\n\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} секунда\",\n      singularGenitive: \"{{count}} секунди\",\n      pluralGenitive: \"{{count}} секунд\",\n    },\n    past: {\n      singularNominative: \"{{count}} секунду тому\",\n      singularGenitive: \"{{count}} секунди тому\",\n      pluralGenitive: \"{{count}} секунд тому\",\n    },\n    future: {\n      singularNominative: \"за {{count}} секунду\",\n      singularGenitive: \"за {{count}} секунди\",\n      pluralGenitive: \"за {{count}} секунд\",\n    },\n  }),\n\n  halfAMinute: halfAtMinute,\n\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: \"менше хвилини\",\n      singularNominative: \"менше {{count}} хвилини\",\n      singularGenitive: \"менше {{count}} хвилин\",\n      pluralGenitive: \"менше {{count}} хвилин\",\n    },\n    future: {\n      one: \"менше, ніж за хвилину\",\n      singularNominative: \"менше, ніж за {{count}} хвилину\",\n      singularGenitive: \"менше, ніж за {{count}} хвилини\",\n      pluralGenitive: \"менше, ніж за {{count}} хвилин\",\n    },\n  }),\n\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} хвилина\",\n      singularGenitive: \"{{count}} хвилини\",\n      pluralGenitive: \"{{count}} хвилин\",\n    },\n    past: {\n      singularNominative: \"{{count}} хвилину тому\",\n      singularGenitive: \"{{count}} хвилини тому\",\n      pluralGenitive: \"{{count}} хвилин тому\",\n    },\n    future: {\n      singularNominative: \"за {{count}} хвилину\",\n      singularGenitive: \"за {{count}} хвилини\",\n      pluralGenitive: \"за {{count}} хвилин\",\n    },\n  }),\n\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} години\",\n      singularGenitive: \"близько {{count}} годин\",\n      pluralGenitive: \"близько {{count}} годин\",\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} годину\",\n      singularGenitive: \"приблизно за {{count}} години\",\n      pluralGenitive: \"приблизно за {{count}} годин\",\n    },\n  }),\n\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} годину\",\n      singularGenitive: \"{{count}} години\",\n      pluralGenitive: \"{{count}} годин\",\n    },\n  }),\n\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} день\",\n      singularGenitive: \"{{count}} днi\",\n      pluralGenitive: \"{{count}} днів\",\n    },\n  }),\n\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} тижня\",\n      singularGenitive: \"близько {{count}} тижнів\",\n      pluralGenitive: \"близько {{count}} тижнів\",\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} тиждень\",\n      singularGenitive: \"приблизно за {{count}} тижні\",\n      pluralGenitive: \"приблизно за {{count}} тижнів\",\n    },\n  }),\n\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} тиждень\",\n      singularGenitive: \"{{count}} тижні\",\n      pluralGenitive: \"{{count}} тижнів\",\n    },\n  }),\n\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} місяця\",\n      singularGenitive: \"близько {{count}} місяців\",\n      pluralGenitive: \"близько {{count}} місяців\",\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} місяць\",\n      singularGenitive: \"приблизно за {{count}} місяці\",\n      pluralGenitive: \"приблизно за {{count}} місяців\",\n    },\n  }),\n\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} місяць\",\n      singularGenitive: \"{{count}} місяці\",\n      pluralGenitive: \"{{count}} місяців\",\n    },\n  }),\n\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} року\",\n      singularGenitive: \"близько {{count}} років\",\n      pluralGenitive: \"близько {{count}} років\",\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} рік\",\n      singularGenitive: \"приблизно за {{count}} роки\",\n      pluralGenitive: \"приблизно за {{count}} років\",\n    },\n  }),\n\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} рік\",\n      singularGenitive: \"{{count}} роки\",\n      pluralGenitive: \"{{count}} років\",\n    },\n  }),\n\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"більше {{count}} року\",\n      singularGenitive: \"більше {{count}} років\",\n      pluralGenitive: \"більше {{count}} років\",\n    },\n    future: {\n      singularNominative: \"більше, ніж за {{count}} рік\",\n      singularGenitive: \"більше, ніж за {{count}} роки\",\n      pluralGenitive: \"більше, ніж за {{count}} років\",\n    },\n  }),\n\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"майже {{count}} рік\",\n      singularGenitive: \"майже {{count}} роки\",\n      pluralGenitive: \"майже {{count}} років\",\n    },\n    future: {\n      singularNominative: \"майже за {{count}} рік\",\n      singularGenitive: \"майже за {{count}} роки\",\n      pluralGenitive: \"майже за {{count}} років\",\n    },\n  }),\n};\n\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n\n  return formatDistanceLocale[token](count, options);\n};\n", "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.mjs\";\n\nconst dateFormats = {\n  full: \"EEEE, do MMMM y 'р.'\",\n  long: \"do MMMM y 'р.'\",\n  medium: \"d MMM y 'р.'\",\n  short: \"dd.MM.y\",\n};\n\nconst timeFormats = {\n  full: \"H:mm:ss zzzz\",\n  long: \"H:mm:ss z\",\n  medium: \"H:mm:ss\",\n  short: \"H:mm\",\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} 'о' {{time}}\",\n  long: \"{{date}} 'о' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n", "import { isSameWeek } from \"../../../isSameWeek.mjs\";\nimport { toDate } from \"../../../toDate.mjs\";\n\nconst accusativeWeekdays = [\n  \"неділю\",\n  \"понеділок\",\n  \"вівторок\",\n  \"середу\",\n  \"четвер\",\n  \"п’ятницю\",\n  \"суботу\",\n];\n\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у минулу \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у минулий \" + weekday + \" о' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  return \"'у \" + weekday + \" о' p\";\n}\n\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у наступну \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у наступний \" + weekday + \" о' p\";\n  }\n}\n\nconst lastWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\n\nconst nextWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\n\nconst formatRelativeLocale = {\n  lastWeek: lastWeekFormat,\n  yesterday: \"'вчора о' p\",\n  today: \"'сьогодні о' p\",\n  tomorrow: \"'завтра о' p\",\n  nextWeek: nextWeekFormat,\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n", "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"до н.е.\", \"н.е.\"],\n  abbreviated: [\"до н. е.\", \"н. е.\"],\n  wide: [\"до нашої ери\", \"нашої ери\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-й кв.\", \"2-й кв.\", \"3-й кв.\", \"4-й кв.\"],\n  wide: [\"1-й квартал\", \"2-й квартал\", \"3-й квартал\", \"4-й квартал\"],\n};\n\nconst monthValues = {\n  // ДСТУ 3582:2013\n  narrow: [\"С\", \"Л\", \"Б\", \"К\", \"Т\", \"Ч\", \"Л\", \"С\", \"В\", \"Ж\", \"Л\", \"Г\"],\n  abbreviated: [\n    \"січ.\",\n    \"лют.\",\n    \"берез.\",\n    \"квіт.\",\n    \"трав.\",\n    \"черв.\",\n    \"лип.\",\n    \"серп.\",\n    \"верес.\",\n    \"жовт.\",\n    \"листоп.\",\n    \"груд.\",\n  ],\n\n  wide: [\n    \"січень\",\n    \"лютий\",\n    \"березень\",\n    \"квітень\",\n    \"травень\",\n    \"червень\",\n    \"липень\",\n    \"серпень\",\n    \"вересень\",\n    \"жовтень\",\n    \"листопад\",\n    \"грудень\",\n  ],\n};\n\nconst formattingMonthValues = {\n  narrow: [\"С\", \"Л\", \"Б\", \"К\", \"Т\", \"Ч\", \"Л\", \"С\", \"В\", \"Ж\", \"Л\", \"Г\"],\n  abbreviated: [\n    \"січ.\",\n    \"лют.\",\n    \"берез.\",\n    \"квіт.\",\n    \"трав.\",\n    \"черв.\",\n    \"лип.\",\n    \"серп.\",\n    \"верес.\",\n    \"жовт.\",\n    \"листоп.\",\n    \"груд.\",\n  ],\n\n  wide: [\n    \"січня\",\n    \"лютого\",\n    \"березня\",\n    \"квітня\",\n    \"травня\",\n    \"червня\",\n    \"липня\",\n    \"серпня\",\n    \"вересня\",\n    \"жовтня\",\n    \"листопада\",\n    \"грудня\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"нед\", \"пон\", \"вів\", \"сер\", \"чтв\", \"птн\", \"суб\"],\n  wide: [\n    \"неділя\",\n    \"понеділок\",\n    \"вівторок\",\n    \"середа\",\n    \"четвер\",\n    \"п’ятниця\",\n    \"субота\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ніч\",\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ніч\",\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"північ\",\n    noon: \"полудень\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"вечір\",\n    night: \"ніч\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\",\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\",\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"північ\",\n    noon: \"полудень\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const unit = String(options?.unit);\n  const number = Number(dirtyNumber);\n  let suffix;\n\n  if (unit === \"date\") {\n    if (number === 3 || number === 23) {\n      suffix = \"-є\";\n    } else {\n      suffix = \"-е\";\n    }\n  } else if (unit === \"minute\" || unit === \"second\" || unit === \"hour\") {\n    suffix = \"-а\";\n  } else {\n    suffix = \"-й\";\n  }\n\n  return number + suffix;\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n", "import { buildMatchFn } from \"../../_lib/buildMatchFn.mjs\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.mjs\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(е|й|є|а|я))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^((до )?н\\.?\\s?е\\.?)/i,\n  abbreviated: /^((до )?н\\.?\\s?е\\.?)/i,\n  wide: /^(до нашої ери|нашої ери|наша ера)/i,\n};\n\nconst parseEraPatterns = {\n  any: [/^д/i, /^н/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?[иі]?й?)? кв.?/i,\n  wide: /^[1234](-?[иі]?й?)? квартал/i,\n};\n\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^[слбктчвжг]/i,\n  abbreviated:\n    /^(січ|лют|бер(ез)?|квіт|трав|черв|лип|серп|вер(ес)?|жовт|лис(топ)?|груд)\\.?/i,\n  wide: /^(січень|січня|лютий|лютого|березень|березня|квітень|квітня|травень|травня|червня|червень|липень|липня|серпень|серпня|вересень|вересня|жовтень|жовтня|листопад[а]?|грудень|грудня)/i,\n};\n\nconst parseMonthPatterns = {\n  narrow: [\n    /^с/i,\n    /^л/i,\n    /^б/i,\n    /^к/i,\n    /^т/i,\n    /^ч/i,\n    /^л/i,\n    /^с/i,\n    /^в/i,\n    /^ж/i,\n    /^л/i,\n    /^г/i,\n  ],\n\n  any: [\n    /^сі/i,\n    /^лю/i,\n    /^б/i,\n    /^к/i,\n    /^т/i,\n    /^ч/i,\n    /^лип/i,\n    /^се/i,\n    /^в/i,\n    /^ж/i,\n    /^лис/i,\n    /^г/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[нпвсч]/i,\n  short: /^(нд|пн|вт|ср|чт|пт|сб)\\.?/i,\n  abbreviated: /^(нед|пон|вів|сер|че?тв|птн?|суб)\\.?/i,\n  wide: /^(неділ[яі]|понеділ[ок][ка]|вівтор[ок][ка]|серед[аи]|четвер(га)?|п\\W*?ятниц[яі]|субот[аи])/i,\n};\n\nconst parseDayPatterns = {\n  narrow: [/^н/i, /^п/i, /^в/i, /^с/i, /^ч/i, /^п/i, /^с/i],\n  any: [/^н/i, /^п[он]/i, /^в/i, /^с[ер]/i, /^ч/i, /^п\\W*?[ят]/i, /^с[уб]/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n  abbreviated: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n  wide: /^([дп]п|північ|полудень|ранок|ранку|день|дня|вечір|вечора|ніч|ночі)/i,\n};\n\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^дп/i,\n    pm: /^пп/i,\n    midnight: /^півн/i,\n    noon: /^пол/i,\n    morning: /^р/i,\n    afternoon: /^д[ен]/i,\n    evening: /^в/i,\n    night: /^н/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n", "import { formatDistance } from \"./uk/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./uk/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./uk/_lib/formatRelative.mjs\";\nimport { localize } from \"./uk/_lib/localize.mjs\";\nimport { match } from \"./uk/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Ukrainian locale.\n * @language Ukrainian\n * @iso-639-2 ukr\n * <AUTHOR> [@korzhyk](https://github.com/korzhyk)\n * <AUTHOR> [@shcherbyakdev](https://github.com/shcherbyakdev)\n */\nexport const uk = {\n  code: \"uk\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default uk;\n"], "mappings": ";;;;;;;;;;;;;AAAA,SAAS,WAAW,QAAQ,OAAO;AAEjC,MAAI,OAAO,QAAQ,UAAa,UAAU,GAAG;AAC3C,WAAO,OAAO;AAAA,EAChB;AAEA,QAAM,QAAQ,QAAQ;AACtB,QAAM,SAAS,QAAQ;AAGvB,MAAI,UAAU,KAAK,WAAW,IAAI;AAChC,WAAO,OAAO,mBAAmB,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAGrE,WAAW,SAAS,KAAK,SAAS,MAAM,SAAS,MAAM,SAAS,KAAK;AACnE,WAAO,OAAO,iBAAiB,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAGnE,OAAO;AACL,WAAO,OAAO,eAAe,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EACjE;AACF;AAEA,SAAS,qBAAqB,QAAQ;AACpC,SAAO,CAAC,OAAO,YAAY;AACzB,QAAI,WAAW,QAAQ,WAAW;AAChC,UAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,YAAI,OAAO,QAAQ;AACjB,iBAAO,WAAW,OAAO,QAAQ,KAAK;AAAA,QACxC,OAAO;AACL,iBAAO,QAAQ,WAAW,OAAO,SAAS,KAAK;AAAA,QACjD;AAAA,MACF,OAAO;AACL,YAAI,OAAO,MAAM;AACf,iBAAO,WAAW,OAAO,MAAM,KAAK;AAAA,QACtC,OAAO;AACL,iBAAO,WAAW,OAAO,SAAS,KAAK,IAAI;AAAA,QAC7C;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,WAAW,OAAO,SAAS,KAAK;AAAA,IACzC;AAAA,EACF;AACF;AAEA,IAAM,eAAe,CAAC,GAAG,YAAY;AACnC,MAAI,WAAW,QAAQ,WAAW;AAChC,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAM,uBAAuB;AAAA,EAC3B,kBAAkB,qBAAqB;AAAA,IACrC,SAAS;AAAA,MACP,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,UAAU,qBAAqB;AAAA,IAC7B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,aAAa;AAAA,EAEb,kBAAkB,qBAAqB;AAAA,IACrC,SAAS;AAAA,MACP,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,UAAU,qBAAqB;AAAA,IAC7B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,OAAO,qBAAqB;AAAA,IAC1B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,cAAc,qBAAqB;AAAA,IACjC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,SAAS,qBAAqB;AAAA,IAC5B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,YAAY,qBAAqB;AAAA,IAC/B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EAED,cAAc,qBAAqB;AAAA,IACjC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AAEO,IAAM,iBAAiB,CAAC,OAAO,OAAO,YAAY;AACvD,YAAU,WAAW,CAAC;AAEtB,SAAO,qBAAqB,KAAK,EAAE,OAAO,OAAO;AACnD;;;ACvPA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEO,IAAM,aAAa;AAAA,EACxB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;;;ACnCA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,SAAS,KAAK;AACrB,QAAM,UAAU,mBAAmB,GAAG;AAEtC,UAAQ,KAAK;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,eAAe,UAAU;AAAA,IAClC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,gBAAgB,UAAU;AAAA,EACrC;AACF;AAEA,SAAS,SAAS,KAAK;AACrB,QAAM,UAAU,mBAAmB,GAAG;AAEtC,SAAO,QAAQ,UAAU;AAC3B;AAEA,SAAS,SAAS,KAAK;AACrB,QAAM,UAAU,mBAAmB,GAAG;AAEtC,UAAQ,KAAK;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,iBAAiB,UAAU;AAAA,IACpC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,kBAAkB,UAAU;AAAA,EACvC;AACF;AAEA,IAAM,iBAAiB,CAAC,WAAW,UAAU,YAAY;AACvD,QAAM,OAAO,OAAO,SAAS;AAC7B,QAAM,MAAM,KAAK,OAAO;AAExB,MAAI,WAAW,MAAM,UAAU,OAAO,GAAG;AACvC,WAAO,SAAS,GAAG;AAAA,EACrB,OAAO;AACL,WAAO,SAAS,GAAG;AAAA,EACrB;AACF;AAEA,IAAM,iBAAiB,CAAC,WAAW,UAAU,YAAY;AACvD,QAAM,OAAO,OAAO,SAAS;AAC7B,QAAM,MAAM,KAAK,OAAO;AACxB,MAAI,WAAW,MAAM,UAAU,OAAO,GAAG;AACvC,WAAO,SAAS,GAAG;AAAA,EACrB,OAAO;AACL,WAAO,SAAS,GAAG;AAAA,EACrB;AACF;AAEA,IAAM,uBAAuB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AAEO,IAAM,iBAAiB,CAAC,OAAO,MAAM,UAAU,YAAY;AAChE,QAAM,SAAS,qBAAqB,KAAK;AAEzC,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,OAAO,MAAM,UAAU,OAAO;AAAA,EACvC;AAEA,SAAO;AACT;;;ACvFA,IAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,WAAW,MAAM;AAAA,EAC1B,aAAa,CAAC,YAAY,OAAO;AAAA,EACjC,MAAM,CAAC,gBAAgB,WAAW;AACpC;AAEA,IAAM,gBAAgB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,WAAW,WAAW,WAAW,SAAS;AAAA,EACxD,MAAM,CAAC,eAAe,eAAe,eAAe,aAAa;AACnE;AAEA,IAAM,cAAc;AAAA;AAAA,EAElB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,wBAAwB;AAAA,EAC5B,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEA,IAAM,4BAA4B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEA,IAAM,gBAAgB,CAAC,aAAa,YAAY;AAC9C,QAAM,OAAO,OAAO,mCAAS,IAAI;AACjC,QAAM,SAAS,OAAO,WAAW;AACjC,MAAI;AAEJ,MAAI,SAAS,QAAQ;AACnB,QAAI,WAAW,KAAK,WAAW,IAAI;AACjC,eAAS;AAAA,IACX,OAAO;AACL,eAAS;AAAA,IACX;AAAA,EACF,WAAW,SAAS,YAAY,SAAS,YAAY,SAAS,QAAQ;AACpE,aAAS;AAAA,EACX,OAAO;AACL,aAAS;AAAA,EACX;AAEA,SAAO,SAAS;AAClB;AAEO,IAAM,WAAW;AAAA,EACtB;AAAA,EAEA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,CAAC,YAAY,UAAU;AAAA,EAC3C,CAAC;AAAA,EAED,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AAAA,EAED,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;;;ACnNA,IAAM,4BAA4B;AAClC,IAAM,4BAA4B;AAElC,IAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AAEA,IAAM,mBAAmB;AAAA,EACvB,KAAK,CAAC,OAAO,KAAK;AACpB;AAEA,IAAM,uBAAuB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AAEA,IAAM,uBAAuB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AAEA,IAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,EACR,aACE;AAAA,EACF,MAAM;AACR;AAEA,IAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,KAAK;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AAEA,IAAM,mBAAmB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,OAAO,WAAW,OAAO,WAAW,OAAO,eAAe,SAAS;AAC3E;AAEA,IAAM,yBAAyB;AAAA,EAC7B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AAEA,IAAM,yBAAyB;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEO,IAAM,QAAQ;AAAA,EACnB,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,CAAC,UAAU,SAAS,OAAO,EAAE;AAAA,EAC9C,CAAC;AAAA,EAED,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,CAAC,UAAU,QAAQ;AAAA,EACpC,CAAC;AAAA,EAED,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;;;AC5HO,IAAM,KAAK;AAAA,EAChB,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AACF;AAGA,IAAO,aAAQ;", "names": []}