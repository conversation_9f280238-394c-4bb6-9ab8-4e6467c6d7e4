import{g as s}from"./index-C0qcAVKU.js";import{e as y}from"./index.DUmRo3Ep.entry.js";import{r as x}from"./htmlmixed-BGdafJOX.js";import{r as z}from"./overlay-8ot5Sopc.js";function j(f,p){for(var n=0;n<p.length;n++){const i=p[n];if(typeof i!="string"&&!Array.isArray(i)){for(const t in i)if(t!=="default"&&!(t in f)){const o=Object.getOwnPropertyDescriptor(i,t);o&&Object.defineProperty(f,t,o.get?o:{enumerable:!0,get:()=>i[t]})}}}return Object.freeze(Object.defineProperty(f,Symbol.toStringTag,{value:"Module"}))}var P={exports:{}};(function(f,p){(function(n){n(y(),x(),z())})(function(n){n.defineMode("django:inner",function(){var i=["block","endblock","for","endfor","true","false","filter","endfilter","loop","none","self","super","if","elif","endif","as","else","import","with","endwith","without","context","ifequal","endifequal","ifnotequal","endifnotequal","extends","include","load","comment","endcomment","empty","url","static","trans","blocktrans","endblocktrans","now","regroup","lorem","ifchanged","endifchanged","firstof","debug","cycle","csrf_token","autoescape","endautoescape","spaceless","endspaceless","ssi","templatetag","verbatim","endverbatim","widthratio"],t=["add","addslashes","capfirst","center","cut","date","default","default_if_none","dictsort","dictsortreversed","divisibleby","escape","escapejs","filesizeformat","first","floatformat","force_escape","get_digit","iriencode","join","last","length","length_is","linebreaks","linebreaksbr","linenumbers","ljust","lower","make_list","phone2numeric","pluralize","pprint","random","removetags","rjust","safe","safeseq","slice","slugify","stringformat","striptags","time","timesince","timeuntil","title","truncatechars","truncatechars_html","truncatewords","truncatewords_html","unordered_list","upper","urlencode","urlize","urlizetrunc","wordcount","wordwrap","yesno"],o=["==","!=","<",">","<=",">="],d=["in","not","or","and"];i=new RegExp("^\\b("+i.join("|")+")\\b"),t=new RegExp("^\\b("+t.join("|")+")\\b"),o=new RegExp("^\\b("+o.join("|")+")\\b"),d=new RegExp("^\\b("+d.join("|")+")\\b");function a(r,e){if(r.match("{{"))return e.tokenize=g,"tag";if(r.match("{%"))return e.tokenize=w,"tag";if(r.match("{#"))return e.tokenize=h,"comment";for(;r.next()!=null&&!r.match(/\{[{%#]/,!1););return null}function c(r,e){return function(l,u){if(!u.escapeNext&&l.eat(r))u.tokenize=e;else{u.escapeNext&&(u.escapeNext=!1);var k=l.next();k=="\\"&&(u.escapeNext=!0)}return"string"}}function g(r,e){if(e.waitDot){if(e.waitDot=!1,r.peek()!=".")return"null";if(r.match(/\.\W+/))return"error";if(r.eat("."))return e.waitProperty=!0,"null";throw Error("Unexpected error while waiting for property.")}if(e.waitPipe){if(e.waitPipe=!1,r.peek()!="|")return"null";if(r.match(/\.\W+/))return"error";if(r.eat("|"))return e.waitFilter=!0,"null";throw Error("Unexpected error while waiting for filter.")}return e.waitProperty&&(e.waitProperty=!1,r.match(/\b(\w+)\b/))?(e.waitDot=!0,e.waitPipe=!0,"property"):e.waitFilter&&(e.waitFilter=!1,r.match(t))?"variable-2":r.eatSpace()?(e.waitProperty=!1,"null"):r.match(/\b\d+(\.\d+)?\b/)?"number":r.match("'")?(e.tokenize=c("'",e.tokenize),"string"):r.match('"')?(e.tokenize=c('"',e.tokenize),"string"):r.match(/\b(\w+)\b/)&&!e.foundVariable?(e.waitDot=!0,e.waitPipe=!0,"variable"):r.match("}}")?(e.waitProperty=null,e.waitFilter=null,e.waitDot=null,e.waitPipe=null,e.tokenize=a,"tag"):(r.next(),"null")}function w(r,e){if(e.waitDot){if(e.waitDot=!1,r.peek()!=".")return"null";if(r.match(/\.\W+/))return"error";if(r.eat("."))return e.waitProperty=!0,"null";throw Error("Unexpected error while waiting for property.")}if(e.waitPipe){if(e.waitPipe=!1,r.peek()!="|")return"null";if(r.match(/\.\W+/))return"error";if(r.eat("|"))return e.waitFilter=!0,"null";throw Error("Unexpected error while waiting for filter.")}if(e.waitProperty&&(e.waitProperty=!1,r.match(/\b(\w+)\b/)))return e.waitDot=!0,e.waitPipe=!0,"property";if(e.waitFilter&&(e.waitFilter=!1,r.match(t)))return"variable-2";if(r.eatSpace())return e.waitProperty=!1,"null";if(r.match(/\b\d+(\.\d+)?\b/))return"number";if(r.match("'"))return e.tokenize=c("'",e.tokenize),"string";if(r.match('"'))return e.tokenize=c('"',e.tokenize),"string";if(r.match(o))return"operator";if(r.match(d))return"keyword";var l=r.match(i);return l?(l[0]=="comment"&&(e.blockCommentTag=!0),"keyword"):r.match(/\b(\w+)\b/)?(e.waitDot=!0,e.waitPipe=!0,"variable"):r.match("%}")?(e.waitProperty=null,e.waitFilter=null,e.waitDot=null,e.waitPipe=null,e.blockCommentTag?(e.blockCommentTag=!1,e.tokenize=b):e.tokenize=a,"tag"):(r.next(),"null")}function h(r,e){return r.match(/^.*?#\}/)?e.tokenize=a:r.skipToEnd(),"comment"}function b(r,e){return r.match(/\{%\s*endcomment\s*%\}/,!1)?(e.tokenize=w,r.match("{%"),"tag"):(r.next(),"comment")}return{startState:function(){return{tokenize:a}},token:function(r,e){return e.tokenize(r,e)},blockCommentStart:"{% comment %}",blockCommentEnd:"{% endcomment %}"}}),n.defineMode("django",function(i){var t=n.getMode(i,"text/html"),o=n.getMode(i,"django:inner");return n.overlayMode(t,o)}),n.defineMIME("text/x-django","django")})})();var m=P.exports;const v=s(m),q=j({__proto__:null,default:v},[m]);export{q as d};
