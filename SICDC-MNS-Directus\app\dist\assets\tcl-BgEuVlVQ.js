import{g as x}from"./index-C0qcAVKU.js";import{e as y}from"./index.DUmRo3Ep.entry.js";function v(u,s){for(var o=0;o<s.length;o++){const i=s[o];if(typeof i!="string"&&!Array.isArray(i)){for(const a in i)if(a!=="default"&&!(a in u)){const l=Object.getOwnPropertyDescriptor(i,a);l&&Object.defineProperty(u,a,l.get?l:{enumerable:!0,get:()=>i[a]})}}}return Object.freeze(Object.defineProperty(u,Symbol.toStringTag,{value:"Module"}))}var _={exports:{}};(function(u,s){(function(o){o(y())})(function(o){o.defineMode("tcl",function(){function i(r){for(var t={},n=r.split(" "),e=0;e<n.length;++e)t[n[e]]=!0;return t}var a=i("Tcl safe after append array auto_execok auto_import auto_load auto_mkindex auto_mkindex_old auto_qualify auto_reset bgerror binary break catch cd close concat continue dde eof encoding error eval exec exit expr fblocked fconfigure fcopy file fileevent filename filename flush for foreach format gets glob global history http if incr info interp join lappend lindex linsert list llength load lrange lreplace lsearch lset lsort memory msgcat namespace open package parray pid pkg::create pkg_mkIndex proc puts pwd re_syntax read regex regexp registry regsub rename resource return scan seek set socket source split string subst switch tcl_endOfWord tcl_findLibrary tcl_startOfNextWord tcl_wordBreakAfter tcl_startOfPreviousWord tcl_wordBreakBefore tcltest tclvars tell time trace unknown unset update uplevel upvar variable vwait"),l=i("if elseif else and not or eq ne in ni for foreach while switch"),d=/[+\-*&%=<>!?^\/\|]/;function p(r,t,n){return t.tokenize=n,n(r,t)}function c(r,t){var n=t.beforeParams;t.beforeParams=!1;var e=r.next();if((e=='"'||e=="'")&&t.inParams)return p(r,t,b(e));if(/[\[\]{}\(\),;\.]/.test(e))return e=="("&&n?t.inParams=!0:e==")"&&(t.inParams=!1),null;if(/\d/.test(e))return r.eatWhile(/[\w\.]/),"number";if(e=="#")return r.eat("*")?p(r,t,g):e=="#"&&r.match(/ *\[ *\[/)?p(r,t,h):(r.skipToEnd(),"comment");if(e=='"')return r.skipTo(/"/),"comment";if(e=="$")return r.eatWhile(/[$_a-z0-9A-Z\.{:]/),r.eatWhile(/}/),t.beforeParams=!0,"builtin";if(d.test(e))return r.eatWhile(d),"comment";r.eatWhile(/[\w\$_{}\xa1-\uffff]/);var f=r.current().toLowerCase();return a&&a.propertyIsEnumerable(f)?"keyword":l&&l.propertyIsEnumerable(f)?(t.beforeParams=!0,"keyword"):null}function b(r){return function(t,n){for(var e=!1,f,m=!1;(f=t.next())!=null;){if(f==r&&!e){m=!0;break}e=!e&&f=="\\"}return m&&(n.tokenize=c),"string"}}function g(r,t){for(var n=!1,e;e=r.next();){if(e=="#"&&n){t.tokenize=c;break}n=e=="*"}return"comment"}function h(r,t){for(var n=0,e;e=r.next();){if(e=="#"&&n==2){t.tokenize=c;break}e=="]"?n++:e!=" "&&(n=0)}return"meta"}return{startState:function(){return{tokenize:c,beforeParams:!1,inParams:!1}},token:function(r,t){return r.eatSpace()?null:t.tokenize(r,t)},lineComment:"#"}}),o.defineMIME("text/x-tcl","tcl")})})();var k=_.exports;const w=x(k),W=v({__proto__:null,default:w},[k]);export{W as t};
