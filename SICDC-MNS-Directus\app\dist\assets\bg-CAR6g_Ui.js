import{c,b as i,i as f,j as s}from"./index.DUmRo3Ep.entry.js";import{i as d}from"./isSameWeek-DOt1VTFz.js";import{t as m}from"./index-C0qcAVKU.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";const y={lessThanXSeconds:{one:"по-малко от секунда",other:"по-малко от {{count}} секунди"},xSeconds:{one:"1 секунда",other:"{{count}} секунди"},halfAMinute:"половин минута",lessThanXMinutes:{one:"по-малко от минута",other:"по-малко от {{count}} минути"},xMinutes:{one:"1 минута",other:"{{count}} минути"},aboutXHours:{one:"около час",other:"около {{count}} часа"},xHours:{one:"1 час",other:"{{count}} часа"},xDays:{one:"1 ден",other:"{{count}} дни"},aboutXWeeks:{one:"около седмица",other:"около {{count}} седмици"},xWeeks:{one:"1 седмица",other:"{{count}} седмици"},aboutXMonths:{one:"около месец",other:"около {{count}} месеца"},xMonths:{one:"1 месец",other:"{{count}} месеца"},aboutXYears:{one:"около година",other:"около {{count}} години"},xYears:{one:"1 година",other:"{{count}} години"},overXYears:{one:"над година",other:"над {{count}} години"},almostXYears:{one:"почти година",other:"почти {{count}} години"}},b=(e,t,a)=>{let r;const n=y[e];return typeof n=="string"?r=n:t===1?r=n.one:r=n.other.replace("{{count}}",String(t)),a!=null&&a.addSuffix?a.comparison&&a.comparison>0?"след "+r:"преди "+r:r},P={full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"dd/MM/yyyy"},w={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"H:mm"},W={any:"{{date}} {{time}}"},p={date:c({formats:P,defaultWidth:"full"}),time:c({formats:w,defaultWidth:"full"}),dateTime:c({formats:W,defaultWidth:"any"})},u=["неделя","понеделник","вторник","сряда","четвъртък","петък","събота"];function M(e){const t=u[e];switch(e){case 0:case 3:case 6:return"'миналата "+t+" в' p";case 1:case 2:case 4:case 5:return"'миналия "+t+" в' p"}}function l(e){const t=u[e];return e===2?"'във "+t+" в' p":"'в "+t+" в' p"}function k(e){const t=u[e];switch(e){case 0:case 3:case 6:return"'следващата "+t+" в' p";case 1:case 2:case 4:case 5:return"'следващия "+t+" в' p"}}const v=(e,t,a)=>{const r=m(e),n=r.getDay();return d(r,t,a)?l(n):M(n)},g=(e,t,a)=>{const r=m(e),n=r.getDay();return d(r,t,a)?l(n):k(n)},x={lastWeek:v,yesterday:"'вчера в' p",today:"'днес в' p",tomorrow:"'утре в' p",nextWeek:g,other:"P"},D=(e,t,a,r)=>{const n=x[e];return typeof n=="function"?n(t,a,r):n},F={narrow:["пр.н.е.","н.е."],abbreviated:["преди н. е.","н. е."],wide:["преди новата ера","новата ера"]},H={narrow:["1","2","3","4"],abbreviated:["1-во тримес.","2-ро тримес.","3-то тримес.","4-то тримес."],wide:["1-во тримесечие","2-ро тримесечие","3-то тримесечие","4-то тримесечие"]},z={abbreviated:["яну","фев","мар","апр","май","юни","юли","авг","сеп","окт","ное","дек"],wide:["януари","февруари","март","април","май","юни","юли","август","септември","октомври","ноември","декември"]},X={narrow:["Н","П","В","С","Ч","П","С"],short:["нд","пн","вт","ср","чт","пт","сб"],abbreviated:["нед","пон","вто","сря","чет","пет","съб"],wide:["неделя","понеделник","вторник","сряда","четвъртък","петък","събота"]},S={wide:{am:"преди обяд",pm:"след обяд",midnight:"в полунощ",noon:"на обяд",morning:"сутринта",afternoon:"следобед",evening:"вечерта",night:"през нощта"}};function E(e){return e==="year"||e==="week"||e==="minute"||e==="second"}function L(e){return e==="quarter"}function o(e,t,a,r,n){const h=L(t)?n:E(t)?r:a;return e+"-"+h}const N=(e,t)=>{const a=Number(e),r=t==null?void 0:t.unit;if(a===0)return o(0,r,"ев","ева","ево");if(a%1e3===0)return o(a,r,"ен","на","но");if(a%100===0)return o(a,r,"тен","тна","тно");const n=a%100;if(n>20||n<10)switch(n%10){case 1:return o(a,r,"ви","ва","во");case 2:return o(a,r,"ри","ра","ро");case 7:case 8:return o(a,r,"ми","ма","мо")}return o(a,r,"ти","та","то")},T={ordinalNumber:N,era:i({values:F,defaultWidth:"wide"}),quarter:i({values:H,defaultWidth:"wide",argumentCallback:e=>e-1}),month:i({values:z,defaultWidth:"wide"}),day:i({values:X,defaultWidth:"wide"}),dayPeriod:i({values:S,defaultWidth:"wide"})},V=/^(\d+)(-?[врмт][аи]|-?т?(ен|на)|-?(ев|ева))?/i,q=/\d+/i,C={narrow:/^((пр)?н\.?\s?е\.?)/i,abbreviated:/^((пр)?н\.?\s?е\.?)/i,wide:/^(преди новата ера|новата ера|нова ера)/i},Y={any:[/^п/i,/^н/i]},O={narrow:/^[1234]/i,abbreviated:/^[1234](-?[врт]?o?)? тримес.?/i,wide:/^[1234](-?[врт]?о?)? тримесечие/i},R={any:[/1/i,/2/i,/3/i,/4/i]},Q={narrow:/^[нпвсч]/i,short:/^(нд|пн|вт|ср|чт|пт|сб)/i,abbreviated:/^(нед|пон|вто|сря|чет|пет|съб)/i,wide:/^(неделя|понеделник|вторник|сряда|четвъртък|петък|събота)/i},j={narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н[ед]/i,/^п[он]/i,/^вт/i,/^ср/i,/^ч[ет]/i,/^п[ет]/i,/^с[ъб]/i]},A={abbreviated:/^(яну|фев|мар|апр|май|юни|юли|авг|сеп|окт|ное|дек)/i,wide:/^(януари|февруари|март|април|май|юни|юли|август|септември|октомври|ноември|декември)/i},I={any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^май/i,/^юн/i,/^юл/i,/^ав/i,/^се/i,/^окт/i,/^но/i,/^де/i]},B={any:/^(преди о|след о|в по|на о|през|веч|сут|следо)/i},G={any:{am:/^преди о/i,pm:/^след о/i,midnight:/^в пол/i,noon:/^на об/i,morning:/^сут/i,afternoon:/^следо/i,evening:/^веч/i,night:/^през н/i}},J={ordinalNumber:f({matchPattern:V,parsePattern:q,valueCallback:e=>parseInt(e,10)}),era:s({matchPatterns:C,defaultMatchWidth:"wide",parsePatterns:Y,defaultParseWidth:"any"}),quarter:s({matchPatterns:O,defaultMatchWidth:"wide",parsePatterns:R,defaultParseWidth:"any",valueCallback:e=>e+1}),month:s({matchPatterns:A,defaultMatchWidth:"wide",parsePatterns:I,defaultParseWidth:"any"}),day:s({matchPatterns:Q,defaultMatchWidth:"wide",parsePatterns:j,defaultParseWidth:"any"}),dayPeriod:s({matchPatterns:B,defaultMatchWidth:"any",parsePatterns:G,defaultParseWidth:"any"})},re={code:"bg",formatDistance:b,formatLong:p,formatRelative:D,localize:T,match:J,options:{weekStartsOn:1,firstWeekContainsDate:1}};export{re as bg,re as default};
