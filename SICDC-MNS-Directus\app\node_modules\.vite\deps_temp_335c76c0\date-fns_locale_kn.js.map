{"version": 3, "sources": ["../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kn/_lib/formatDistance.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kn/_lib/formatLong.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kn/_lib/formatRelative.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kn/_lib/localize.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kn/_lib/match.mjs", "../../../../node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/locale/kn.mjs"], "sourcesContent": ["// note: no implementation for weeks\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      default: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      future: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      past: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n    },\n    other: {\n      default: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      future: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      past: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n    },\n  },\n\n  xSeconds: {\n    one: {\n      default: \"1 ಸೆಕೆಂಡ್\",\n      future: \"1 ಸೆಕೆಂಡ್‌ನಲ್ಲಿ\",\n      past: \"1 ಸೆಕೆಂಡ್ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ಸೆಕೆಂಡುಗಳು\",\n      future: \"{{count}} ಸೆಕೆಂಡ್‌ಗಳಲ್ಲಿ\",\n      past: \"{{count}} ಸೆಕೆಂಡ್ ಹಿಂದೆ\",\n    },\n  },\n\n  halfAMinute: {\n    other: {\n      default: \"ಅರ್ಧ ನಿಮಿಷ\",\n      future: \"ಅರ್ಧ ನಿಮಿಷದಲ್ಲಿ\",\n      past: \"ಅರ್ಧ ನಿಮಿಷದ ಹಿಂದೆ\",\n    },\n  },\n\n  lessThanXMinutes: {\n    one: {\n      default: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      future: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      past: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n    },\n    other: {\n      default: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      future: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      past: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n    },\n  },\n\n  xMinutes: {\n    one: {\n      default: \"1 ನಿಮಿಷ\",\n      future: \"1 ನಿಮಿಷದಲ್ಲಿ\",\n      past: \"1 ನಿಮಿಷದ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ನಿಮಿಷಗಳು\",\n      future: \"{{count}} ನಿಮಿಷಗಳಲ್ಲಿ\",\n      past: \"{{count}} ನಿಮಿಷಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  aboutXHours: {\n    one: {\n      default: \"ಸುಮಾರು 1 ಗಂಟೆ\",\n      future: \"ಸುಮಾರು 1 ಗಂಟೆಯಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ಗಂಟೆ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  xHours: {\n    one: {\n      default: \"1 ಗಂಟೆ\",\n      future: \"1 ಗಂಟೆಯಲ್ಲಿ\",\n      past: \"1 ಗಂಟೆ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ಗಂಟೆಗಳು\",\n      future: \"{{count}} ಗಂಟೆಗಳಲ್ಲಿ\",\n      past: \"{{count}} ಗಂಟೆಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  xDays: {\n    one: {\n      default: \"1 ದಿನ\",\n      future: \"1 ದಿನದಲ್ಲಿ\",\n      past: \"1 ದಿನದ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ದಿನಗಳು\",\n      future: \"{{count}} ದಿನಗಳಲ್ಲಿ\",\n      past: \"{{count}} ದಿನಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  // TODO\n  // aboutXWeeks: {},\n\n  // TODO\n  // xWeeks: {},\n\n  aboutXMonths: {\n    one: {\n      default: \"ಸುಮಾರು 1 ತಿಂಗಳು\",\n      future: \"ಸುಮಾರು 1 ತಿಂಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ತಿಂಗಳ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ತಿಂಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ತಿಂಗಳುಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ತಿಂಗಳುಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  xMonths: {\n    one: {\n      default: \"1 ತಿಂಗಳು\",\n      future: \"1 ತಿಂಗಳಲ್ಲಿ\",\n      past: \"1 ತಿಂಗಳ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ತಿಂಗಳು\",\n      future: \"{{count}} ತಿಂಗಳುಗಳಲ್ಲಿ\",\n      past: \"{{count}} ತಿಂಗಳುಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  aboutXYears: {\n    one: {\n      default: \"ಸುಮಾರು 1 ವರ್ಷ\",\n      future: \"ಸುಮಾರು 1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ವರ್ಷದ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  xYears: {\n    one: {\n      default: \"1 ವರ್ಷ\",\n      future: \"1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"1 ವರ್ಷದ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ವರ್ಷಗಳು\",\n      future: \"{{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"{{count}} ವರ್ಷಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  overXYears: {\n    one: {\n      default: \"1 ವರ್ಷದ ಮೇಲೆ\",\n      future: \"1 ವರ್ಷದ ಮೇಲೆ\",\n      past: \"1 ವರ್ಷದ ಮೇಲೆ\",\n    },\n    other: {\n      default: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\",\n      future: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\",\n      past: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\",\n    },\n  },\n\n  almostXYears: {\n    one: {\n      default: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\",\n      future: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\",\n    },\n    other: {\n      default: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      future: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n    },\n  },\n};\n\nfunction getResultByTense(parentToken, options) {\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return parentToken.future;\n    } else {\n      return parentToken.past;\n    }\n  }\n  return parentToken.default;\n}\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n\n  if (tokenValue.one && count === 1) {\n    result = getResultByTense(tokenValue.one, options);\n  } else {\n    result = getResultByTense(tokenValue.other, options);\n  }\n\n  return result.replace(\"{{count}}\", String(count));\n};\n", "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.mjs\";\n\n// Reference: https://www.unicode.org/cldr/charts/32/summary/kn.html\n\nconst dateFormats = {\n  full: \"EEEE, MMMM d, y\", // CLDR 1816\n  long: \"MMMM d, y\", // CLDR 1817\n  medium: \"MMM d, y\", // CLDR 1818\n  short: \"d/M/yy\", // CLDR 1819\n};\n\nconst timeFormats = {\n  full: \"hh:mm:ss a zzzz\", // CLDR 1820\n  long: \"hh:mm:ss a z\", // CLDR 1821\n  medium: \"hh:mm:ss a\", // CLDR 1822\n  short: \"hh:mm a\", // CLDR 1823\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} {{time}}\", // CLDR 1824\n  long: \"{{date}} {{time}}\", // CLDR 1825\n  medium: \"{{date}} {{time}}\", // CLDR 1826\n  short: \"{{date}} {{time}}\", // CLDR 1827\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n", "const formatRelativeLocale = {\n  lastWeek: \"'ಕಳೆದ' eeee p 'ಕ್ಕೆ'\",\n  yesterday: \"'ನಿನ್ನೆ' p 'ಕ್ಕೆ'\",\n  today: \"'ಇಂದು' p 'ಕ್ಕೆ'\",\n  tomorrow: \"'ನಾಳೆ' p 'ಕ್ಕೆ'\",\n  nextWeek: \"eeee p 'ಕ್ಕೆ'\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n", "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\n// Reference: https://www.unicode.org/cldr/charts/32/summary/kn.html\n\nconst eraValues = {\n  narrow: [\"ಕ್ರಿ.ಪೂ\", \"ಕ್ರಿ.ಶ\"],\n  abbreviated: [\"ಕ್ರಿ.ಪೂ\", \"ಕ್ರಿ.ಶ\"], // CLDR #1618, #1620\n  wide: [\"ಕ್ರಿಸ್ತ ಪೂರ್ವ\", \"ಕ್ರಿಸ್ತ ಶಕ\"], // CLDR #1614, #1616\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ತ್ರೈ 1\", \"ತ್ರೈ 2\", \"ತ್ರೈ 3\", \"ತ್ರೈ 4\"], // CLDR #1630 - #1638\n  wide: [\"1ನೇ ತ್ರೈಮಾಸಿಕ\", \"2ನೇ ತ್ರೈಮಾಸಿಕ\", \"3ನೇ ತ್ರೈಮಾಸಿಕ\", \"4ನೇ ತ್ರೈಮಾಸಿಕ\"],\n  // CLDR #1622 - #1629\n};\n\n// CLDR #1646 - #1717\nconst monthValues = {\n  narrow: [\"ಜ\", \"ಫೆ\", \"ಮಾ\", \"ಏ\", \"ಮೇ\", \"ಜೂ\", \"ಜು\", \"ಆ\", \"ಸೆ\", \"ಅ\", \"ನ\", \"ಡಿ\"],\n\n  abbreviated: [\n    \"ಜನ\",\n    \"ಫೆಬ್ರ\",\n    \"ಮಾರ್ಚ್\",\n    \"ಏಪ್ರಿ\",\n    \"ಮೇ\",\n    \"ಜೂನ್\",\n    \"ಜುಲೈ\",\n    \"ಆಗ\",\n    \"ಸೆಪ್ಟೆಂ\",\n    \"ಅಕ್ಟೋ\",\n    \"ನವೆಂ\",\n    \"ಡಿಸೆಂ\",\n  ],\n\n  wide: [\n    \"ಜನವರಿ\",\n    \"ಫೆಬ್ರವರಿ\",\n    \"ಮಾರ್ಚ್\",\n    \"ಏಪ್ರಿಲ್\",\n    \"ಮೇ\",\n    \"ಜೂನ್\",\n    \"ಜುಲೈ\",\n    \"ಆಗಸ್ಟ್\",\n    \"ಸೆಪ್ಟೆಂಬರ್\",\n    \"ಅಕ್ಟೋಬರ್\",\n    \"ನವೆಂಬರ್\",\n    \"ಡಿಸೆಂಬರ್\",\n  ],\n};\n\n// CLDR #1718 - #1773\nconst dayValues = {\n  narrow: [\"ಭಾ\", \"ಸೋ\", \"ಮಂ\", \"ಬು\", \"ಗು\", \"ಶು\", \"ಶ\"],\n  short: [\"ಭಾನು\", \"ಸೋಮ\", \"ಮಂಗಳ\", \"ಬುಧ\", \"ಗುರು\", \"ಶುಕ್ರ\", \"ಶನಿ\"],\n  abbreviated: [\"ಭಾನು\", \"ಸೋಮ\", \"ಮಂಗಳ\", \"ಬುಧ\", \"ಗುರು\", \"ಶುಕ್ರ\", \"ಶನಿ\"],\n  wide: [\n    \"ಭಾನುವಾರ\",\n    \"ಸೋಮವಾರ\",\n    \"ಮಂಗಳವಾರ\",\n    \"ಬುಧವಾರ\",\n    \"ಗುರುವಾರ\",\n    \"ಶುಕ್ರವಾರ\",\n    \"ಶನಿವಾರ\",\n  ],\n};\n\n// CLDR #1774 - #1815\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾಹ್ನ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾಹ್ನ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n  abbreviated: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n  wide: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ಪೂ\",\n    pm: \"ಅ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n  abbreviated: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯ ರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n  wide: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯ ರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"ನೇ\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n", "import { buildMatchFn } from \"../../_lib/buildMatchFn.mjs\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.mjs\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(ನೇ|ನೆ)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(ಕ್ರಿ.ಪೂ|ಕ್ರಿ.ಶ)/i,\n  abbreviated: /^(ಕ್ರಿ\\.?\\s?ಪೂ\\.?|ಕ್ರಿ\\.?\\s?ಶ\\.?|ಪ್ರ\\.?\\s?ಶ\\.?)/i,\n  wide: /^(ಕ್ರಿಸ್ತ ಪೂರ್ವ|ಕ್ರಿಸ್ತ ಶಕ|ಪ್ರಸಕ್ತ ಶಕ)/i,\n};\nconst parseEraPatterns = {\n  any: [/^ಪೂ/i, /^(ಶ|ಪ್ರ)/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^ತ್ರೈ[1234]|ತ್ರೈ [1234]| [1234]ತ್ರೈ/i,\n  wide: /^[1234](ನೇ)? ತ್ರೈಮಾಸಿಕ/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^(ಜೂ|ಜು|ಜ|ಫೆ|ಮಾ|ಏ|ಮೇ|ಆ|ಸೆ|ಅ|ನ|ಡಿ)/i,\n  abbreviated:\n    /^(ಜನ|ಫೆಬ್ರ|ಮಾರ್ಚ್|ಏಪ್ರಿ|ಮೇ|ಜೂನ್|ಜುಲೈ|ಆಗ|ಸೆಪ್ಟೆಂ|ಅಕ್ಟೋ|ನವೆಂ|ಡಿಸೆಂ)/i,\n  wide: /^(ಜನವರಿ|ಫೆಬ್ರವರಿ|ಮಾರ್ಚ್|ಏಪ್ರಿಲ್|ಮೇ|ಜೂನ್|ಜುಲೈ|ಆಗಸ್ಟ್|ಸೆಪ್ಟೆಂಬರ್|ಅಕ್ಟೋಬರ್|ನವೆಂಬರ್|ಡಿಸೆಂಬರ್)/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /^ಜ$/i,\n    /^ಫೆ/i,\n    /^ಮಾ/i,\n    /^ಏ/i,\n    /^ಮೇ/i,\n    /^ಜೂ/i,\n    /^ಜು$/i,\n    /^ಆ/i,\n    /^ಸೆ/i,\n    /^ಅ/i,\n    /^ನ/i,\n    /^ಡಿ/i,\n  ],\n\n  any: [\n    /^ಜನ/i,\n    /^ಫೆ/i,\n    /^ಮಾ/i,\n    /^ಏ/i,\n    /^ಮೇ/i,\n    /^ಜೂನ್/i,\n    /^ಜುಲೈ/i,\n    /^ಆ/i,\n    /^ಸೆ/i,\n    /^ಅ/i,\n    /^ನ/i,\n    /^ಡಿ/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^(ಭಾ|ಸೋ|ಮ|ಬು|ಗು|ಶು|ಶ)/i,\n  short: /^(ಭಾನು|ಸೋಮ|ಮಂಗಳ|ಬುಧ|ಗುರು|ಶುಕ್ರ|ಶನಿ)/i,\n  abbreviated: /^(ಭಾನು|ಸೋಮ|ಮಂಗಳ|ಬುಧ|ಗುರು|ಶುಕ್ರ|ಶನಿ)/i,\n  wide: /^(ಭಾನುವಾರ|ಸೋಮವಾರ|ಮಂಗಳವಾರ|ಬುಧವಾರ|ಗುರುವಾರ|ಶುಕ್ರವಾರ|ಶನಿವಾರ)/i,\n};\nconst parseDayPatterns = {\n  narrow: [/^ಭಾ/i, /^ಸೋ/i, /^ಮ/i, /^ಬು/i, /^ಗು/i, /^ಶು/i, /^ಶ/i],\n  any: [/^ಭಾ/i, /^ಸೋ/i, /^ಮ/i, /^ಬು/i, /^ಗು/i, /^ಶು/i, /^ಶ/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow: /^(ಪೂ|ಅ|ಮಧ್ಯರಾತ್ರಿ|ಮಧ್ಯಾನ್ಹ|ಬೆಳಗ್ಗೆ|ಸಂಜೆ|ರಾತ್ರಿ)/i,\n  any: /^(ಪೂರ್ವಾಹ್ನ|ಅಪರಾಹ್ನ|ಮಧ್ಯರಾತ್ರಿ|ಮಧ್ಯಾನ್ಹ|ಬೆಳಗ್ಗೆ|ಸಂಜೆ|ರಾತ್ರಿ)/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^ಪೂ/i,\n    pm: /^ಅ/i,\n    midnight: /ಮಧ್ಯರಾತ್ರಿ/i,\n    noon: /ಮಧ್ಯಾನ್ಹ/i,\n    morning: /ಬೆಳಗ್ಗೆ/i,\n    afternoon: /ಮಧ್ಯಾನ್ಹ/i,\n    evening: /ಸಂಜೆ/i,\n    night: /ರಾತ್ರಿ/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n", "import { formatDistance } from \"./kn/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./kn/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./kn/_lib/formatRelative.mjs\";\nimport { localize } from \"./kn/_lib/localize.mjs\";\nimport { match } from \"./kn/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Kannada locale (India).\n * @language Kannada\n * @iso-639-2 kan\n * <AUTHOR> [@developergouli](https://github.com/developergouli)\n */\nexport const kn = {\n  code: \"kn\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default kn;\n"], "mappings": ";;;;;;;;;AAEA,IAAM,uBAAuB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,UAAU;AAAA,IACR,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,aAAa;AAAA,IACX,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,kBAAkB;AAAA,IAChB,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,UAAU;AAAA,IACR,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,aAAa;AAAA,IACX,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,QAAQ;AAAA,IACN,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,IACL,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AAAA,IACZ,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,IACP,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,aAAa;AAAA,IACX,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,QAAQ;AAAA,IACN,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,YAAY;AAAA,IACV,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,cAAc;AAAA,IACZ,KAAK;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,aAAa,SAAS;AAC9C,MAAI,mCAAS,WAAW;AACtB,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,YAAY;AAAA,IACrB,OAAO;AACL,aAAO,YAAY;AAAA,IACrB;AAAA,EACF;AACA,SAAO,YAAY;AACrB;AAEO,IAAM,iBAAiB,CAAC,OAAO,OAAO,YAAY;AACvD,MAAI;AAEJ,QAAM,aAAa,qBAAqB,KAAK;AAE7C,MAAI,WAAW,OAAO,UAAU,GAAG;AACjC,aAAS,iBAAiB,WAAW,KAAK,OAAO;AAAA,EACnD,OAAO;AACL,aAAS,iBAAiB,WAAW,OAAO,OAAO;AAAA,EACrD;AAEA,SAAO,OAAO,QAAQ,aAAa,OAAO,KAAK,CAAC;AAClD;;;AC9MA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA;AAAA,EACN,MAAM;AAAA;AAAA,EACN,QAAQ;AAAA;AAAA,EACR,OAAO;AAAA;AACT;AAEA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA;AAAA,EACN,MAAM;AAAA;AAAA,EACN,QAAQ;AAAA;AAAA,EACR,OAAO;AAAA;AACT;AAEA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA;AAAA,EACN,MAAM;AAAA;AAAA,EACN,QAAQ;AAAA;AAAA,EACR,OAAO;AAAA;AACT;AAEO,IAAM,aAAa;AAAA,EACxB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;;;ACxCA,IAAM,uBAAuB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AAEO,IAAM,iBAAiB,CAAC,OAAO,OAAO,WAAW,aACtD,qBAAqB,KAAK;;;ACN5B,IAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,WAAW,QAAQ;AAAA,EAC5B,aAAa,CAAC,WAAW,QAAQ;AAAA;AAAA,EACjC,MAAM,CAAC,iBAAiB,YAAY;AAAA;AACtC;AAEA,IAAM,gBAAgB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,UAAU,UAAU,UAAU,QAAQ;AAAA;AAAA,EACpD,MAAM,CAAC,iBAAiB,iBAAiB,iBAAiB,eAAe;AAAA;AAE3E;AAGA,IAAM,cAAc;AAAA,EAClB,QAAQ,CAAC,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,KAAK,KAAK,IAAI;AAAA,EAE1E,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,IAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,GAAG;AAAA,EAChD,OAAO,CAAC,QAAQ,OAAO,QAAQ,OAAO,QAAQ,SAAS,KAAK;AAAA,EAC5D,aAAa,CAAC,QAAQ,OAAO,QAAQ,OAAO,QAAQ,SAAS,KAAK;AAAA,EAClE,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEA,IAAM,4BAA4B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEA,IAAM,gBAAgB,CAAC,aAAa,aAAa;AAC/C,QAAM,SAAS,OAAO,WAAW;AACjC,SAAO,SAAS;AAClB;AAEO,IAAM,WAAW;AAAA,EACtB;AAAA,EAEA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,CAAC,YAAY,UAAU;AAAA,EAC3C,CAAC;AAAA,EAED,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EAED,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;;;ACvKA,IAAM,4BAA4B;AAClC,IAAM,4BAA4B;AAElC,IAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAM,mBAAmB;AAAA,EACvB,KAAK,CAAC,QAAQ,WAAW;AAC3B;AAEA,IAAM,uBAAuB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAM,uBAAuB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AAEA,IAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,EACR,aACE;AAAA,EACF,MAAM;AACR;AACA,IAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EAEA,KAAK;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAM,mBAAmB;AAAA,EACvB,QAAQ,CAAC,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,KAAK;AAAA,EAC7D,KAAK,CAAC,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,KAAK;AAC5D;AAEA,IAAM,yBAAyB;AAAA,EAC7B,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAM,yBAAyB;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AAEO,IAAM,QAAQ;AAAA,EACnB,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,CAAC,UAAU,SAAS,OAAO,EAAE;AAAA,EAC9C,CAAC;AAAA,EAED,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,CAAC,UAAU,QAAQ;AAAA,EACpC,CAAC;AAAA,EAED,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EAED,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;;;ACvHO,IAAM,KAAK;AAAA,EAChB,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AACF;AAGA,IAAO,aAAQ;", "names": []}