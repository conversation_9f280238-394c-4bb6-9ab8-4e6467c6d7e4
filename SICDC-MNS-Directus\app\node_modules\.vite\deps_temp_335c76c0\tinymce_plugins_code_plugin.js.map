{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/code/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const setContent = (editor, html) => {\n      editor.focus();\n      editor.undoManager.transact(() => {\n        editor.setContent(html);\n      });\n      editor.selection.setCursorLocation();\n      editor.nodeChanged();\n    };\n    const getContent = editor => {\n      return editor.getContent({ source_view: true });\n    };\n\n    const open = editor => {\n      const editorContent = getContent(editor);\n      editor.windowManager.open({\n        title: 'Source Code',\n        size: 'large',\n        body: {\n          type: 'panel',\n          items: [{\n              type: 'textarea',\n              name: 'code'\n            }]\n        },\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData: { code: editorContent },\n        onSubmit: api => {\n          setContent(editor, api.getData().code);\n          api.close();\n        }\n      });\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mceCodeEditor', () => {\n        open(editor);\n      });\n    };\n\n    const register = editor => {\n      const onAction = () => editor.execCommand('mceCodeEditor');\n      editor.ui.registry.addButton('code', {\n        icon: 'sourcecode',\n        tooltip: 'Source code',\n        onAction\n      });\n      editor.ui.registry.addMenuItem('code', {\n        icon: 'sourcecode',\n        text: 'Source code',\n        onAction\n      });\n    };\n\n    var Plugin = () => {\n      global.add('code', editor => {\n        register$1(editor);\n        register(editor);\n        return {};\n      });\n    };\n\n    Plugin();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAE/D,QAAM,aAAa,CAAC,QAAQ,SAAS;AACnC,WAAO,MAAM;AACb,WAAO,YAAY,SAAS,MAAM;AAChC,aAAO,WAAW,IAAI;AAAA,IACxB,CAAC;AACD,WAAO,UAAU,kBAAkB;AACnC,WAAO,YAAY;AAAA,EACrB;AACA,QAAM,aAAa,YAAU;AAC3B,WAAO,OAAO,WAAW,EAAE,aAAa,KAAK,CAAC;AAAA,EAChD;AAEA,QAAM,OAAO,YAAU;AACrB,UAAM,gBAAgB,WAAW,MAAM;AACvC,WAAO,cAAc,KAAK;AAAA,MACxB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,OAAO,CAAC;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,QACR,CAAC;AAAA,MACL;AAAA,MACA,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,aAAa,EAAE,MAAM,cAAc;AAAA,MACnC,UAAU,SAAO;AACf,mBAAW,QAAQ,IAAI,QAAQ,EAAE,IAAI;AACrC,YAAI,MAAM;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,aAAa,YAAU;AAC3B,WAAO,WAAW,iBAAiB,MAAM;AACvC,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,YAAU;AACzB,UAAM,WAAW,MAAM,OAAO,YAAY,eAAe;AACzD,WAAO,GAAG,SAAS,UAAU,QAAQ;AAAA,MACnC,MAAM;AAAA,MACN,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AACD,WAAO,GAAG,SAAS,YAAY,QAAQ;AAAA,MACrC,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,MAAM;AACjB,WAAO,IAAI,QAAQ,YAAU;AAC3B,iBAAW,MAAM;AACjB,eAAS,MAAM;AACf,aAAO,CAAC;AAAA,IACV,CAAC;AAAA,EACH;AAEA,SAAO;AAEX,GAAG;", "names": []}