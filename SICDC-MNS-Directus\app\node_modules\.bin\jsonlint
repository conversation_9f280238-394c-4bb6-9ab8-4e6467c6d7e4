#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/jsonlint-mod@1.7.6/node_modules/jsonlint-mod/lib/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/jsonlint-mod@1.7.6/node_modules/jsonlint-mod/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/jsonlint-mod@1.7.6/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/jsonlint-mod@1.7.6/node_modules/jsonlint-mod/lib/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/jsonlint-mod@1.7.6/node_modules/jsonlint-mod/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/jsonlint-mod@1.7.6/node_modules:/mnt/d/Projects/SICDC/CUSTOM IMAGE DIRECTUS/SICDC-MNS-Directus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jsonlint-mod/lib/cli.js" "$@"
else
  exec node  "$basedir/../jsonlint-mod/lib/cli.js" "$@"
fi
