import{c as s,b as r,i as d,j as i}from"./index.DUmRo3Ep.entry.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";const m={lessThanXSeconds:{one:"少於 1 秒",other:"少於 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分鐘",lessThanXMinutes:{one:"少於 1 分鐘",other:"少於 {{count}} 分鐘"},xMinutes:{one:"1 分鐘",other:"{{count}} 分鐘"},xHours:{one:"1 小時",other:"{{count}} 小時"},aboutXHours:{one:"大約 1 小時",other:"大約 {{count}} 小時"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大約 1 個星期",other:"大約 {{count}} 個星期"},xWeeks:{one:"1 個星期",other:"{{count}} 個星期"},aboutXMonths:{one:"大約 1 個月",other:"大約 {{count}} 個月"},xMonths:{one:"1 個月",other:"{{count}} 個月"},aboutXYears:{one:"大約 1 年",other:"大約 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超過 1 年",other:"超過 {{count}} 年"},almostXYears:{one:"將近 1 年",other:"將近 {{count}} 年"}},u=(e,a,t)=>{let n;const o=m[e];return typeof o=="string"?n=o:a===1?n=o.one:n=o.other.replace("{{count}}",String(a)),t!=null&&t.addSuffix?t.comparison&&t.comparison>0?n+"內":n+"前":n},h={full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},c={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},l={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},f={date:s({formats:h,defaultWidth:"full"}),time:s({formats:c,defaultWidth:"full"}),dateTime:s({formats:l,defaultWidth:"full"})},g={lastWeek:"'上個'eeee p",yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:"'下個'eeee p",other:"P"},b=(e,a,t,n)=>g[e],P={narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},y={narrow:["1","2","3","4"],abbreviated:["第一刻","第二刻","第三刻","第四刻"],wide:["第一刻鐘","第二刻鐘","第三刻鐘","第四刻鐘"]},w={narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},p={narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["週日","週一","週二","週三","週四","週五","週六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},v={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜間"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜間"}},W={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜間"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜間"}},M=(e,a)=>{const t=Number(e);switch(a==null?void 0:a.unit){case"date":return t+"日";case"hour":return t+"時";case"minute":return t+"分";case"second":return t+"秒";default:return"第 "+t}},D={ordinalNumber:M,era:r({values:P,defaultWidth:"wide"}),quarter:r({values:y,defaultWidth:"wide",argumentCallback:e=>e-1}),month:r({values:w,defaultWidth:"wide"}),day:r({values:p,defaultWidth:"wide"}),dayPeriod:r({values:v,defaultWidth:"wide",formattingValues:W,defaultFormattingWidth:"wide"})},k=/^(第\s*)?\d+(日|時|分|秒)?/i,x=/\d+/i,z={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},F={any:[/^(前)/i,/^(公元)/i]},V={narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻鐘/i},X={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},E={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},L={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},T={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^週[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},N={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},S={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨)/i},C={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},Y={ordinalNumber:d({matchPattern:k,parsePattern:x,valueCallback:e=>parseInt(e,10)}),era:i({matchPatterns:z,defaultMatchWidth:"wide",parsePatterns:F,defaultParseWidth:"any"}),quarter:i({matchPatterns:V,defaultMatchWidth:"wide",parsePatterns:X,defaultParseWidth:"any",valueCallback:e=>e+1}),month:i({matchPatterns:E,defaultMatchWidth:"wide",parsePatterns:L,defaultParseWidth:"any"}),day:i({matchPatterns:T,defaultMatchWidth:"wide",parsePatterns:N,defaultParseWidth:"any"}),dayPeriod:i({matchPatterns:S,defaultMatchWidth:"any",parsePatterns:C,defaultParseWidth:"any"})},A={code:"zh-TW",formatDistance:u,formatLong:f,formatRelative:b,localize:D,match:Y,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{A as default,A as zhTW};
