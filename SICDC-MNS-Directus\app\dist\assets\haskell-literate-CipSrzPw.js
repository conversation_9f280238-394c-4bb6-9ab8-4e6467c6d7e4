import{g as f}from"./index-C0qcAVKU.js";import{e as u}from"./index.DUmRo3Ep.entry.js";import{a as c}from"./haskell-BeLxD8LD.js";function d(a,l){for(var e=0;e<l.length;e++){const r=l[e];if(typeof r!="string"&&!Array.isArray(r)){for(const t in r)if(t!=="default"&&!(t in a)){const o=Object.getOwnPropertyDescriptor(r,t);o&&Object.defineProperty(a,t,o.get?o:{enumerable:!0,get:()=>r[t]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}var p={exports:{}};(function(a,l){(function(e){e(u(),c)})(function(e){e.defineMode("haskell-literate",function(r,t){var o=e.getMode(r,t&&t.base||"haskell");return{startState:function(){return{inCode:!1,baseState:e.startState(o)}},token:function(n,i){return n.sol()&&(i.inCode=n.eat(">"))?"meta":i.inCode?o.token(n,i.baseState):(n.skipToEnd(),"comment")},innerMode:function(n){return n.inCode?{state:n.baseState,mode:o}:null}}},"haskell"),e.defineMIME("text/x-literate-haskell","haskell-literate")})})();var s=p.exports;const k=f(s),g=d({__proto__:null,default:k},[s]);export{g as h};
