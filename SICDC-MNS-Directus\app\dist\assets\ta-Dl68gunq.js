import{c as d,b as e,i as u,j as n}from"./index.DUmRo3Ep.entry.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";function s(t){return t.one!==void 0}const c={lessThanXSeconds:{one:{default:"ஒரு வினாடிக்கு குறைவாக",in:"ஒரு வினாடிக்குள்",ago:"ஒரு வினாடிக்கு முன்பு"},other:{default:"{{count}} வினாடிகளுக்கு குறைவாக",in:"{{count}} வினாடிகளுக்குள்",ago:"{{count}} வினாடிகளுக்கு முன்பு"}},xSeconds:{one:{default:"1 வினாடி",in:"1 வினாடியில்",ago:"1 வினாடி முன்பு"},other:{default:"{{count}} விநாடிகள்",in:"{{count}} வினாடிகளில்",ago:"{{count}} விநாடிகளுக்கு முன்பு"}},halfAMinute:{default:"அரை நிமிடம்",in:"அரை நிமிடத்தில்",ago:"அரை நிமிடம் முன்பு"},lessThanXMinutes:{one:{default:"ஒரு நிமிடத்திற்கும் குறைவாக",in:"ஒரு நிமிடத்திற்குள்",ago:"ஒரு நிமிடத்திற்கு முன்பு"},other:{default:"{{count}} நிமிடங்களுக்கும் குறைவாக",in:"{{count}} நிமிடங்களுக்குள்",ago:"{{count}} நிமிடங்களுக்கு முன்பு"}},xMinutes:{one:{default:"1 நிமிடம்",in:"1 நிமிடத்தில்",ago:"1 நிமிடம் முன்பு"},other:{default:"{{count}} நிமிடங்கள்",in:"{{count}} நிமிடங்களில்",ago:"{{count}} நிமிடங்களுக்கு முன்பு"}},aboutXHours:{one:{default:"சுமார் 1 மணி நேரம்",in:"சுமார் 1 மணி நேரத்தில்",ago:"சுமார் 1 மணி நேரத்திற்கு முன்பு"},other:{default:"சுமார் {{count}} மணி நேரம்",in:"சுமார் {{count}} மணி நேரத்திற்கு முன்பு",ago:"சுமார் {{count}} மணி நேரத்தில்"}},xHours:{one:{default:"1 மணி நேரம்",in:"1 மணி நேரத்தில்",ago:"1 மணி நேரத்திற்கு முன்பு"},other:{default:"{{count}} மணி நேரம்",in:"{{count}} மணி நேரத்தில்",ago:"{{count}} மணி நேரத்திற்கு முன்பு"}},xDays:{one:{default:"1 நாள்",in:"1 நாளில்",ago:"1 நாள் முன்பு"},other:{default:"{{count}} நாட்கள்",in:"{{count}} நாட்களில்",ago:"{{count}} நாட்களுக்கு முன்பு"}},aboutXWeeks:{one:{default:"சுமார் 1 வாரம்",in:"சுமார் 1 வாரத்தில்",ago:"சுமார் 1 வாரம் முன்பு"},other:{default:"சுமார் {{count}} வாரங்கள்",in:"சுமார் {{count}} வாரங்களில்",ago:"சுமார் {{count}} வாரங்களுக்கு முன்பு"}},xWeeks:{one:{default:"1 வாரம்",in:"1 வாரத்தில்",ago:"1 வாரம் முன்பு"},other:{default:"{{count}} வாரங்கள்",in:"{{count}} வாரங்களில்",ago:"{{count}} வாரங்களுக்கு முன்பு"}},aboutXMonths:{one:{default:"சுமார் 1 மாதம்",in:"சுமார் 1 மாதத்தில்",ago:"சுமார் 1 மாதத்திற்கு முன்பு"},other:{default:"சுமார் {{count}} மாதங்கள்",in:"சுமார் {{count}} மாதங்களில்",ago:"சுமார் {{count}} மாதங்களுக்கு முன்பு"}},xMonths:{one:{default:"1 மாதம்",in:"1 மாதத்தில்",ago:"1 மாதம் முன்பு"},other:{default:"{{count}} மாதங்கள்",in:"{{count}} மாதங்களில்",ago:"{{count}} மாதங்களுக்கு முன்பு"}},aboutXYears:{one:{default:"சுமார் 1 வருடம்",in:"சுமார் 1 ஆண்டில்",ago:"சுமார் 1 வருடம் முன்பு"},other:{default:"சுமார் {{count}} ஆண்டுகள்",in:"சுமார் {{count}} ஆண்டுகளில்",ago:"சுமார் {{count}} ஆண்டுகளுக்கு முன்பு"}},xYears:{one:{default:"1 வருடம்",in:"1 ஆண்டில்",ago:"1 வருடம் முன்பு"},other:{default:"{{count}} ஆண்டுகள்",in:"{{count}} ஆண்டுகளில்",ago:"{{count}} ஆண்டுகளுக்கு முன்பு"}},overXYears:{one:{default:"1 வருடத்திற்கு மேல்",in:"1 வருடத்திற்கும் மேலாக",ago:"1 வருடம் முன்பு"},other:{default:"{{count}} ஆண்டுகளுக்கும் மேலாக",in:"{{count}} ஆண்டுகளில்",ago:"{{count}} ஆண்டுகளுக்கு முன்பு"}},almostXYears:{one:{default:"கிட்டத்தட்ட 1 வருடம்",in:"கிட்டத்தட்ட 1 ஆண்டில்",ago:"கிட்டத்தட்ட 1 வருடம் முன்பு"},other:{default:"கிட்டத்தட்ட {{count}} ஆண்டுகள்",in:"கிட்டத்தட்ட {{count}} ஆண்டுகளில்",ago:"கிட்டத்தட்ட {{count}} ஆண்டுகளுக்கு முன்பு"}}},l=(t,o,a)=>{const i=a!=null&&a.addSuffix?a.comparison&&a.comparison>0?"in":"ago":"default",r=c[t];return s(r)?o===1?r.one[i]:r.other[i].replace("{{count}}",String(o)):r[i]},m={full:"EEEE, d MMMM, y",long:"d MMMM, y",medium:"d MMM, y",short:"d/M/yy"},h={full:"a h:mm:ss zzzz",long:"a h:mm:ss z",medium:"a h:mm:ss",short:"a h:mm"},f={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},g={date:d({formats:m,defaultWidth:"full"}),time:d({formats:h,defaultWidth:"full"}),dateTime:d({formats:f,defaultWidth:"full"})},P={lastWeek:"'கடந்த' eeee p 'மணிக்கு'",yesterday:"'நேற்று ' p 'மணிக்கு'",today:"'இன்று ' p 'மணிக்கு'",tomorrow:"'நாளை ' p 'மணிக்கு'",nextWeek:"eeee p 'மணிக்கு'",other:"P"},b=(t,o,a,i)=>P[t],w={narrow:["கி.மு.","கி.பி."],abbreviated:["கி.மு.","கி.பி."],wide:["கிறிஸ்துவுக்கு முன்","அன்னோ டோமினி"]},p={narrow:["1","2","3","4"],abbreviated:["காலா.1","காலா.2","காலா.3","காலா.4"],wide:["ஒன்றாம் காலாண்டு","இரண்டாம் காலாண்டு","மூன்றாம் காலாண்டு","நான்காம் காலாண்டு"]},y={narrow:["ஜ","பி","மா","ஏ","மே","ஜூ","ஜூ","ஆ","செ","அ","ந","டி"],abbreviated:["ஜன.","பிப்.","மார்.","ஏப்.","மே","ஜூன்","ஜூலை","ஆக.","செப்.","அக்.","நவ.","டிச."],wide:["ஜனவரி","பிப்ரவரி","மார்ச்","ஏப்ரல்","மே","ஜூன்","ஜூலை","ஆகஸ்ட்","செப்டம்பர்","அக்டோபர்","நவம்பர்","டிசம்பர்"]},v={narrow:["ஞா","தி","செ","பு","வி","வெ","ச"],short:["ஞா","தி","செ","பு","வி","வெ","ச"],abbreviated:["ஞாயி.","திங்.","செவ்.","புத.","வியா.","வெள்.","சனி"],wide:["ஞாயிறு","திங்கள்","செவ்வாய்","புதன்","வியாழன்","வெள்ளி","சனி"]},M={narrow:{am:"மு.ப",pm:"பி.ப",midnight:"நள்.",noon:"நண்.",morning:"கா.",afternoon:"மதி.",evening:"மா.",night:"இர."},abbreviated:{am:"முற்பகல்",pm:"பிற்பகல்",midnight:"நள்ளிரவு",noon:"நண்பகல்",morning:"காலை",afternoon:"மதியம்",evening:"மாலை",night:"இரவு"},wide:{am:"முற்பகல்",pm:"பிற்பகல்",midnight:"நள்ளிரவு",noon:"நண்பகல்",morning:"காலை",afternoon:"மதியம்",evening:"மாலை",night:"இரவு"}},W={narrow:{am:"மு.ப",pm:"பி.ப",midnight:"நள்.",noon:"நண்.",morning:"கா.",afternoon:"மதி.",evening:"மா.",night:"இர."},abbreviated:{am:"முற்பகல்",pm:"பிற்பகல்",midnight:"நள்ளிரவு",noon:"நண்பகல்",morning:"காலை",afternoon:"மதியம்",evening:"மாலை",night:"இரவு"},wide:{am:"முற்பகல்",pm:"பிற்பகல்",midnight:"நள்ளிரவு",noon:"நண்பகல்",morning:"காலை",afternoon:"மதியம்",evening:"மாலை",night:"இரவு"}},D=(t,o)=>String(t),k={ordinalNumber:D,era:e({values:w,defaultWidth:"wide"}),quarter:e({values:p,defaultWidth:"wide",argumentCallback:t=>t-1}),month:e({values:y,defaultWidth:"wide"}),day:e({values:v,defaultWidth:"wide"}),dayPeriod:e({values:M,defaultWidth:"wide",formattingValues:W,defaultFormattingWidth:"wide"})},x=/^(\d+)(வது)?/i,F=/\d+/i,z={narrow:/^(கி.மு.|கி.பி.)/i,abbreviated:/^(கி\.?\s?மு\.?|கி\.?\s?பி\.?)/,wide:/^(கிறிஸ்துவுக்கு\sமுன்|அன்னோ\sடோமினி)/i},V={any:[/கி\.?\s?மு\.?/,/கி\.?\s?பி\.?/]},X={narrow:/^[1234]/i,abbreviated:/^காலா.[1234]/i,wide:/^(ஒன்றாம்|இரண்டாம்|மூன்றாம்|நான்காம்) காலாண்டு/i},E={narrow:[/1/i,/2/i,/3/i,/4/i],any:[/(1|காலா.1|ஒன்றாம்)/i,/(2|காலா.2|இரண்டாம்)/i,/(3|காலா.3|மூன்றாம்)/i,/(4|காலா.4|நான்காம்)/i]},L={narrow:/^(ஜ|பி|மா|ஏ|மே|ஜூ|ஆ|செ|அ|ந|டி)$/i,abbreviated:/^(ஜன.|பிப்.|மார்.|ஏப்.|மே|ஜூன்|ஜூலை|ஆக.|செப்.|அக்.|நவ.|டிச.)/i,wide:/^(ஜனவரி|பிப்ரவரி|மார்ச்|ஏப்ரல்|மே|ஜூன்|ஜூலை|ஆகஸ்ட்|செப்டம்பர்|அக்டோபர்|நவம்பர்|டிசம்பர்)/i},S={narrow:[/^ஜ$/i,/^பி/i,/^மா/i,/^ஏ/i,/^மே/i,/^ஜூ/i,/^ஜூ/i,/^ஆ/i,/^செ/i,/^அ/i,/^ந/i,/^டி/i],any:[/^ஜன/i,/^பி/i,/^மா/i,/^ஏ/i,/^மே/i,/^ஜூன்/i,/^ஜூலை/i,/^ஆ/i,/^செ/i,/^அ/i,/^ந/i,/^டி/i]},T={narrow:/^(ஞா|தி|செ|பு|வி|வெ|ச)/i,short:/^(ஞா|தி|செ|பு|வி|வெ|ச)/i,abbreviated:/^(ஞாயி.|திங்.|செவ்.|புத.|வியா.|வெள்.|சனி)/i,wide:/^(ஞாயிறு|திங்கள்|செவ்வாய்|புதன்|வியாழன்|வெள்ளி|சனி)/i},C={narrow:[/^ஞா/i,/^தி/i,/^செ/i,/^பு/i,/^வி/i,/^வெ/i,/^ச/i],any:[/^ஞா/i,/^தி/i,/^செ/i,/^பு/i,/^வி/i,/^வெ/i,/^ச/i]},N={narrow:/^(மு.ப|பி.ப|நள்|நண்|காலை|மதியம்|மாலை|இரவு)/i,any:/^(மு.ப|பி.ப|முற்பகல்|பிற்பகல்|நள்ளிரவு|நண்பகல்|காலை|மதியம்|மாலை|இரவு)/i},Y={any:{am:/^மு/i,pm:/^பி/i,midnight:/^நள்/i,noon:/^நண்/i,morning:/காலை/i,afternoon:/மதியம்/i,evening:/மாலை/i,night:/இரவு/i}},_={ordinalNumber:u({matchPattern:x,parsePattern:F,valueCallback:t=>parseInt(t,10)}),era:n({matchPatterns:z,defaultMatchWidth:"wide",parsePatterns:V,defaultParseWidth:"any"}),quarter:n({matchPatterns:X,defaultMatchWidth:"wide",parsePatterns:E,defaultParseWidth:"any",valueCallback:t=>t+1}),month:n({matchPatterns:L,defaultMatchWidth:"wide",parsePatterns:S,defaultParseWidth:"any"}),day:n({matchPatterns:T,defaultMatchWidth:"wide",parsePatterns:C,defaultParseWidth:"any"}),dayPeriod:n({matchPatterns:N,defaultMatchWidth:"any",parsePatterns:Y,defaultParseWidth:"any"})},A={code:"ta",formatDistance:l,formatLong:g,formatRelative:b,localize:k,match:_,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{A as default,A as ta};
