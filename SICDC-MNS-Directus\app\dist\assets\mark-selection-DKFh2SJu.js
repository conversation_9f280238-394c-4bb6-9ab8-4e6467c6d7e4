import{g as j}from"./index-C0qcAVKU.js";import{e as m}from"./index.DUmRo3Ep.entry.js";function C(p,S){for(var a=0;a<S.length;a++){const l=S[a];if(typeof l!="string"&&!Array.isArray(l)){for(const f in l)if(f!=="default"&&!(f in p)){const s=Object.getOwnPropertyDescriptor(l,f);s&&Object.defineProperty(p,f,s.get?s:{enumerable:!0,get:()=>l[f]})}}}return Object.freeze(Object.defineProperty(p,Symbol.toStringTag,{value:"Module"}))}var A={exports:{}};(function(p,S){(function(a){a(m())})(function(a){a.defineOption("styleSelectedText",!1,function(e,t,r){var o=r&&r!=a.Init;t&&!o?(e.state.markedSelection=[],e.state.markedSelectionStyle=typeof t=="string"?t:"CodeMirror-selectedtext",d(e),e.on("cursorActivity",l),e.on("change",f)):!t&&o&&(e.off("cursorActivity",l),e.off("change",f),g(e),e.state.markedSelection=e.state.markedSelectionStyle=null)});function l(e){e.state.markedSelection&&e.operation(function(){O(e)})}function f(e){e.state.markedSelection&&e.state.markedSelection.length&&e.operation(function(){g(e)})}var s=8,y=a.Pos,u=a.cmpPos;function c(e,t,r,o){if(u(t,r)!=0)for(var n=e.state.markedSelection,i=e.state.markedSelectionStyle,v=t.line;;){var P=v==t.line?t:y(v,0),h=v+s,k=h>=r.line,_=k?r:y(h,0),x=e.markText(P,_,{className:i});if(o==null?n.push(x):n.splice(o++,0,x),k)break;v=h}}function g(e){for(var t=e.state.markedSelection,r=0;r<t.length;++r)t[r].clear();t.length=0}function d(e){g(e);for(var t=e.listSelections(),r=0;r<t.length;r++)c(e,t[r].from(),t[r].to())}function O(e){if(!e.somethingSelected())return g(e);if(e.listSelections().length>1)return d(e);var t=e.getCursor("start"),r=e.getCursor("end"),o=e.state.markedSelection;if(!o.length)return c(e,t,r);var n=o[0].find(),i=o[o.length-1].find();if(!n||!i||r.line-t.line<=s||u(t,i.to)>=0||u(r,n.from)<=0)return d(e);for(;u(t,n.from)>0;)o.shift().clear(),n=o[0].find();for(u(t,n.from)<0&&(n.to.line-t.line<s?(o.shift().clear(),c(e,t,n.to,0)):c(e,t,n.from,0));u(r,i.to)<0;)o.pop().clear(),i=o[o.length-1].find();u(r,i.to)>0&&(r.line-i.from.line<s?(o.pop().clear(),c(e,i.from,r)):c(e,i.to,r))}})})();var b=A.exports;const w=j(b),T=C({__proto__:null,default:w},[b]);export{T as m};
