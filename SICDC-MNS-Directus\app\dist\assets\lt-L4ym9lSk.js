import{c as l,b as u,i as f,j as m}from"./index.DUmRo3Ep.entry.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";const g={xseconds_other:"sekundė_sekundžių_sekundes",xminutes_one:"minutė_minutės_minutę",xminutes_other:"minutės_minučių_minutes",xhours_one:"valanda_valandos_valandą",xhours_other:"valandos_valandų_valandas",xdays_one:"diena_dienos_dieną",xdays_other:"dienos_dienų_dienas",xweeks_one:"savaitė_savaitės_savaitę",xweeks_other:"savaitės_savaičių_savaites",xmonths_one:"mėnuo_mėnesio_mėnesį",xmonths_other:"mėnesiai_mėnesių_mėnesius",xyears_one:"metai_metų_metus",xyears_other:"metai_metų_metus",about:"apie",over:"daugiau nei",almost:"beveik",lessthan:"mažiau nei"},v=(a,r,e,s)=>r?s?"kelių sekundžių":"kelias sekundes":"kelios sekundės",t=(a,r,e,s)=>r?s?o(e)[1]:o(e)[2]:o(e)[0],i=(a,r,e,s)=>{const d=a+" ";return a===1?d+t(a,r,e,s):r?s?d+o(e)[1]:d+(k(a)?o(e)[1]:o(e)[2]):d+(k(a)?o(e)[1]:o(e)[0])};function k(a){return a%10===0||a>10&&a<20}function o(a){return g[a].split("_")}const b={lessThanXSeconds:{one:v,other:i},xSeconds:{one:v,other:i},halfAMinute:"pusė minutės",lessThanXMinutes:{one:t,other:i},xMinutes:{one:t,other:i},aboutXHours:{one:t,other:i},xHours:{one:t,other:i},xDays:{one:t,other:i},aboutXWeeks:{one:t,other:i},xWeeks:{one:t,other:i},aboutXMonths:{one:t,other:i},xMonths:{one:t,other:i},aboutXYears:{one:t,other:i},xYears:{one:t,other:i},overXYears:{one:t,other:i},almostXYears:{one:t,other:i}},w=(a,r,e)=>{const s=a.match(/about|over|almost|lessthan/i),d=s?a.replace(s[0],""):a,h=(e==null?void 0:e.comparison)!==void 0&&e.comparison>0;let n;const p=b[a];if(typeof p=="string"?n=p:r===1?n=p.one(r,(e==null?void 0:e.addSuffix)===!0,d.toLowerCase()+"_one",h):n=p.other(r,(e==null?void 0:e.addSuffix)===!0,d.toLowerCase()+"_other",h),s){const c=s[0].toLowerCase();n=g[c]+" "+n}return e!=null&&e.addSuffix?e.comparison&&e.comparison>0?"po "+n:"prieš "+n:n},I={full:"y 'm'. MMMM d 'd'., EEEE",long:"y 'm'. MMMM d 'd'.",medium:"y-MM-dd",short:"y-MM-dd"},_={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},P={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},y={date:l({formats:I,defaultWidth:"full"}),time:l({formats:_,defaultWidth:"full"}),dateTime:l({formats:P,defaultWidth:"full"})},M={lastWeek:"'Praėjusį' eeee p",yesterday:"'Vakar' p",today:"'Šiandien' p",tomorrow:"'Rytoj' p",nextWeek:"eeee p",other:"P"},W=(a,r,e,s)=>M[a],x={narrow:["pr. Kr.","po Kr."],abbreviated:["pr. Kr.","po Kr."],wide:["prieš Kristų","po Kristaus"]},V={narrow:["1","2","3","4"],abbreviated:["I ketv.","II ketv.","III ketv.","IV ketv."],wide:["I ketvirtis","II ketvirtis","III ketvirtis","IV ketvirtis"]},S={narrow:["1","2","3","4"],abbreviated:["I k.","II k.","III k.","IV k."],wide:["I ketvirtis","II ketvirtis","III ketvirtis","IV ketvirtis"]},K={narrow:["S","V","K","B","G","B","L","R","R","S","L","G"],abbreviated:["saus.","vas.","kov.","bal.","geg.","birž.","liep.","rugp.","rugs.","spal.","lapkr.","gruod."],wide:["sausis","vasaris","kovas","balandis","gegužė","birželis","liepa","rugpjūtis","rugsėjis","spalis","lapkritis","gruodis"]},j={narrow:["S","V","K","B","G","B","L","R","R","S","L","G"],abbreviated:["saus.","vas.","kov.","bal.","geg.","birž.","liep.","rugp.","rugs.","spal.","lapkr.","gruod."],wide:["sausio","vasario","kovo","balandžio","gegužės","birželio","liepos","rugpjūčio","rugsėjo","spalio","lapkričio","gruodžio"]},L={narrow:["S","P","A","T","K","P","Š"],short:["Sk","Pr","An","Tr","Kt","Pn","Št"],abbreviated:["sk","pr","an","tr","kt","pn","št"],wide:["sekmadienis","pirmadienis","antradienis","trečiadienis","ketvirtadienis","penktadienis","šeštadienis"]},D={narrow:["S","P","A","T","K","P","Š"],short:["Sk","Pr","An","Tr","Kt","Pn","Št"],abbreviated:["sk","pr","an","tr","kt","pn","št"],wide:["sekmadienį","pirmadienį","antradienį","trečiadienį","ketvirtadienį","penktadienį","šeštadienį"]},F={narrow:{am:"pr. p.",pm:"pop.",midnight:"vidurnaktis",noon:"vidurdienis",morning:"rytas",afternoon:"diena",evening:"vakaras",night:"naktis"},abbreviated:{am:"priešpiet",pm:"popiet",midnight:"vidurnaktis",noon:"vidurdienis",morning:"rytas",afternoon:"diena",evening:"vakaras",night:"naktis"},wide:{am:"priešpiet",pm:"popiet",midnight:"vidurnaktis",noon:"vidurdienis",morning:"rytas",afternoon:"diena",evening:"vakaras",night:"naktis"}},H={narrow:{am:"pr. p.",pm:"pop.",midnight:"vidurnaktis",noon:"perpiet",morning:"rytas",afternoon:"popietė",evening:"vakaras",night:"naktis"},abbreviated:{am:"priešpiet",pm:"popiet",midnight:"vidurnaktis",noon:"perpiet",morning:"rytas",afternoon:"popietė",evening:"vakaras",night:"naktis"},wide:{am:"priešpiet",pm:"popiet",midnight:"vidurnaktis",noon:"perpiet",morning:"rytas",afternoon:"popietė",evening:"vakaras",night:"naktis"}},z=(a,r)=>Number(a)+"-oji",R={ordinalNumber:z,era:u({values:x,defaultWidth:"wide"}),quarter:u({values:V,defaultWidth:"wide",formattingValues:S,defaultFormattingWidth:"wide",argumentCallback:a=>a-1}),month:u({values:K,defaultWidth:"wide",formattingValues:j,defaultFormattingWidth:"wide"}),day:u({values:L,defaultWidth:"wide",formattingValues:D,defaultFormattingWidth:"wide"}),dayPeriod:u({values:F,defaultWidth:"wide",formattingValues:H,defaultFormattingWidth:"wide"})},T=/^(\d+)(-oji)?/i,X=/\d+/i,C={narrow:/^p(r|o)\.?\s?(kr\.?|me)/i,abbreviated:/^(pr\.\s?(kr\.|m\.\s?e\.)|po\s?kr\.|mūsų eroje)/i,wide:/^(prieš Kristų|prieš mūsų erą|po Kristaus|mūsų eroje)/i},E={wide:[/prieš/i,/(po|mūsų)/i],any:[/^pr/i,/^(po|m)/i]},A={narrow:/^([1234])/i,abbreviated:/^(I|II|III|IV)\s?ketv?\.?/i,wide:/^(I|II|III|IV)\s?ketvirtis/i},N={narrow:[/1/i,/2/i,/3/i,/4/i],any:[/I$/i,/II$/i,/III/i,/IV/i]},B={narrow:/^[svkbglr]/i,abbreviated:/^(saus\.|vas\.|kov\.|bal\.|geg\.|birž\.|liep\.|rugp\.|rugs\.|spal\.|lapkr\.|gruod\.)/i,wide:/^(sausi(s|o)|vasari(s|o)|kov(a|o)s|balandž?i(s|o)|gegužės?|birželi(s|o)|liep(a|os)|rugpjū(t|č)i(s|o)|rugsėj(is|o)|spali(s|o)|lapkri(t|č)i(s|o)|gruodž?i(s|o))/i},G={narrow:[/^s/i,/^v/i,/^k/i,/^b/i,/^g/i,/^b/i,/^l/i,/^r/i,/^r/i,/^s/i,/^l/i,/^g/i],any:[/^saus/i,/^vas/i,/^kov/i,/^bal/i,/^geg/i,/^birž/i,/^liep/i,/^rugp/i,/^rugs/i,/^spal/i,/^lapkr/i,/^gruod/i]},Y={narrow:/^[spatkš]/i,short:/^(sk|pr|an|tr|kt|pn|št)/i,abbreviated:/^(sk|pr|an|tr|kt|pn|št)/i,wide:/^(sekmadien(is|į)|pirmadien(is|į)|antradien(is|į)|trečiadien(is|į)|ketvirtadien(is|į)|penktadien(is|į)|šeštadien(is|į))/i},$={narrow:[/^s/i,/^p/i,/^a/i,/^t/i,/^k/i,/^p/i,/^š/i],wide:[/^se/i,/^pi/i,/^an/i,/^tr/i,/^ke/i,/^pe/i,/^še/i],any:[/^sk/i,/^pr/i,/^an/i,/^tr/i,/^kt/i,/^pn/i,/^št/i]},q={narrow:/^(pr.\s?p.|pop.|vidurnaktis|(vidurdienis|perpiet)|rytas|(diena|popietė)|vakaras|naktis)/i,any:/^(priešpiet|popiet$|vidurnaktis|(vidurdienis|perpiet)|rytas|(diena|popietė)|vakaras|naktis)/i},O={narrow:{am:/^pr/i,pm:/^pop./i,midnight:/^vidurnaktis/i,noon:/^(vidurdienis|perp)/i,morning:/rytas/i,afternoon:/(die|popietė)/i,evening:/vakaras/i,night:/naktis/i},any:{am:/^pr/i,pm:/^popiet$/i,midnight:/^vidurnaktis/i,noon:/^(vidurdienis|perp)/i,morning:/rytas/i,afternoon:/(die|popietė)/i,evening:/vakaras/i,night:/naktis/i}},Q={ordinalNumber:f({matchPattern:T,parsePattern:X,valueCallback:a=>parseInt(a,10)}),era:m({matchPatterns:C,defaultMatchWidth:"wide",parsePatterns:E,defaultParseWidth:"any"}),quarter:m({matchPatterns:A,defaultMatchWidth:"wide",parsePatterns:N,defaultParseWidth:"any",valueCallback:a=>a+1}),month:m({matchPatterns:B,defaultMatchWidth:"wide",parsePatterns:G,defaultParseWidth:"any"}),day:m({matchPatterns:Y,defaultMatchWidth:"wide",parsePatterns:$,defaultParseWidth:"any"}),dayPeriod:m({matchPatterns:q,defaultMatchWidth:"any",parsePatterns:O,defaultParseWidth:"any"})},re={code:"lt",formatDistance:w,formatLong:y,formatRelative:W,localize:R,match:Q,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{re as default,re as lt};
