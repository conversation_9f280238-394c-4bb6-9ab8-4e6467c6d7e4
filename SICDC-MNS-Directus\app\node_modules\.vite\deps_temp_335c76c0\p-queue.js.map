{"version": 3, "sources": ["../../../../node_modules/.pnpm/eventemitter3@5.0.1/node_modules/eventemitter3/index.js", "../../../../node_modules/.pnpm/eventemitter3@5.0.1/node_modules/eventemitter3/index.mjs", "../../../../node_modules/.pnpm/p-timeout@6.1.2/node_modules/p-timeout/index.js", "../../../../node_modules/.pnpm/p-queue@8.0.1/node_modules/p-queue/dist/lower-bound.js", "../../../../node_modules/.pnpm/p-queue@8.0.1/node_modules/p-queue/dist/priority-queue.js", "../../../../node_modules/.pnpm/p-queue@8.0.1/node_modules/p-queue/dist/index.js"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "import EventEmitter from './index.js'\n\nexport { EventEmitter }\nexport default EventEmitter\n", "export class TimeoutError extends Error {\n\tconstructor(message) {\n\t\tsuper(message);\n\t\tthis.name = 'TimeoutError';\n\t}\n}\n\n/**\nAn error to be thrown when the request is aborted by AbortController.\nDOMException is thrown instead of this Error when DOMException is available.\n*/\nexport class AbortError extends Error {\n\tconstructor(message) {\n\t\tsuper();\n\t\tthis.name = 'AbortError';\n\t\tthis.message = message;\n\t}\n}\n\n/**\nTODO: Remove AbortError and just throw DOMException when targeting Node 18.\n*/\nconst getDOMException = errorMessage => globalThis.DOMException === undefined\n\t? new AbortError(errorMessage)\n\t: new DOMException(errorMessage);\n\n/**\nTODO: Remove below function and just 'reject(signal.reason)' when targeting Node 18.\n*/\nconst getAbortedReason = signal => {\n\tconst reason = signal.reason === undefined\n\t\t? getDOMException('This operation was aborted.')\n\t\t: signal.reason;\n\n\treturn reason instanceof Error ? reason : getDOMException(reason);\n};\n\nexport default function pTimeout(promise, options) {\n\tconst {\n\t\tmilliseconds,\n\t\tfallback,\n\t\tmessage,\n\t\tcustomTimers = {setTimeout, clearTimeout},\n\t} = options;\n\n\tlet timer;\n\n\tconst wrappedPromise = new Promise((resolve, reject) => {\n\t\tif (typeof milliseconds !== 'number' || Math.sign(milliseconds) !== 1) {\n\t\t\tthrow new TypeError(`Expected \\`milliseconds\\` to be a positive number, got \\`${milliseconds}\\``);\n\t\t}\n\n\t\tif (options.signal) {\n\t\t\tconst {signal} = options;\n\t\t\tif (signal.aborted) {\n\t\t\t\treject(getAbortedReason(signal));\n\t\t\t}\n\n\t\t\tsignal.addEventListener('abort', () => {\n\t\t\t\treject(getAbortedReason(signal));\n\t\t\t});\n\t\t}\n\n\t\tif (milliseconds === Number.POSITIVE_INFINITY) {\n\t\t\tpromise.then(resolve, reject);\n\t\t\treturn;\n\t\t}\n\n\t\t// We create the error outside of `setTimeout` to preserve the stack trace.\n\t\tconst timeoutError = new TimeoutError();\n\n\t\ttimer = customTimers.setTimeout.call(undefined, () => {\n\t\t\tif (fallback) {\n\t\t\t\ttry {\n\t\t\t\t\tresolve(fallback());\n\t\t\t\t} catch (error) {\n\t\t\t\t\treject(error);\n\t\t\t\t}\n\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (typeof promise.cancel === 'function') {\n\t\t\t\tpromise.cancel();\n\t\t\t}\n\n\t\t\tif (message === false) {\n\t\t\t\tresolve();\n\t\t\t} else if (message instanceof Error) {\n\t\t\t\treject(message);\n\t\t\t} else {\n\t\t\t\ttimeoutError.message = message ?? `Promise timed out after ${milliseconds} milliseconds`;\n\t\t\t\treject(timeoutError);\n\t\t\t}\n\t\t}, milliseconds);\n\n\t\t(async () => {\n\t\t\ttry {\n\t\t\t\tresolve(await promise);\n\t\t\t} catch (error) {\n\t\t\t\treject(error);\n\t\t\t}\n\t\t})();\n\t});\n\n\tconst cancelablePromise = wrappedPromise.finally(() => {\n\t\tcancelablePromise.clear();\n\t});\n\n\tcancelablePromise.clear = () => {\n\t\tcustomTimers.clearTimeout.call(undefined, timer);\n\t\ttimer = undefined;\n\t};\n\n\treturn cancelablePromise;\n}\n", "// Port of lower_bound from https://en.cppreference.com/w/cpp/algorithm/lower_bound\n// Used to compute insertion index to keep queue sorted after insertion\nexport default function lowerBound(array, value, comparator) {\n    let first = 0;\n    let count = array.length;\n    while (count > 0) {\n        const step = Math.trunc(count / 2);\n        let it = first + step;\n        if (comparator(array[it], value) <= 0) {\n            first = ++it;\n            count -= step + 1;\n        }\n        else {\n            count = step;\n        }\n    }\n    return first;\n}\n", "import lowerBound from './lower-bound.js';\nexport default class PriorityQueue {\n    #queue = [];\n    enqueue(run, options) {\n        options = {\n            priority: 0,\n            ...options,\n        };\n        const element = {\n            priority: options.priority,\n            run,\n        };\n        if (this.size && this.#queue[this.size - 1].priority >= options.priority) {\n            this.#queue.push(element);\n            return;\n        }\n        const index = lowerBound(this.#queue, element, (a, b) => b.priority - a.priority);\n        this.#queue.splice(index, 0, element);\n    }\n    dequeue() {\n        const item = this.#queue.shift();\n        return item?.run;\n    }\n    filter(options) {\n        return this.#queue.filter((element) => element.priority === options.priority).map((element) => element.run);\n    }\n    get size() {\n        return this.#queue.length;\n    }\n}\n", "import { EventEmitter } from 'eventemitter3';\nimport pTimeout, { TimeoutError } from 'p-timeout';\nimport PriorityQueue from './priority-queue.js';\n/**\nPromise queue with concurrency control.\n*/\nexport default class PQueue extends EventEmitter {\n    #carryoverConcurrencyCount;\n    #isIntervalIgnored;\n    #intervalCount = 0;\n    #intervalCap;\n    #interval;\n    #intervalEnd = 0;\n    #intervalId;\n    #timeoutId;\n    #queue;\n    #queueClass;\n    #pending = 0;\n    // The `!` is needed because of https://github.com/microsoft/TypeScript/issues/32194\n    #concurrency;\n    #isPaused;\n    #throwOnTimeout;\n    /**\n    Per-operation timeout in milliseconds. Operations fulfill once `timeout` elapses if they haven't already.\n\n    Applies to each future operation.\n    */\n    timeout;\n    // TODO: The `throwOnTimeout` option should affect the return types of `add()` and `addAll()`\n    constructor(options) {\n        super();\n        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions\n        options = {\n            carryoverConcurrencyCount: false,\n            intervalCap: Number.POSITIVE_INFINITY,\n            interval: 0,\n            concurrency: Number.POSITIVE_INFINITY,\n            autoStart: true,\n            queueClass: PriorityQueue,\n            ...options,\n        };\n        if (!(typeof options.intervalCap === 'number' && options.intervalCap >= 1)) {\n            throw new TypeError(`Expected \\`intervalCap\\` to be a number from 1 and up, got \\`${options.intervalCap?.toString() ?? ''}\\` (${typeof options.intervalCap})`);\n        }\n        if (options.interval === undefined || !(Number.isFinite(options.interval) && options.interval >= 0)) {\n            throw new TypeError(`Expected \\`interval\\` to be a finite number >= 0, got \\`${options.interval?.toString() ?? ''}\\` (${typeof options.interval})`);\n        }\n        this.#carryoverConcurrencyCount = options.carryoverConcurrencyCount;\n        this.#isIntervalIgnored = options.intervalCap === Number.POSITIVE_INFINITY || options.interval === 0;\n        this.#intervalCap = options.intervalCap;\n        this.#interval = options.interval;\n        this.#queue = new options.queueClass();\n        this.#queueClass = options.queueClass;\n        this.concurrency = options.concurrency;\n        this.timeout = options.timeout;\n        this.#throwOnTimeout = options.throwOnTimeout === true;\n        this.#isPaused = options.autoStart === false;\n    }\n    get #doesIntervalAllowAnother() {\n        return this.#isIntervalIgnored || this.#intervalCount < this.#intervalCap;\n    }\n    get #doesConcurrentAllowAnother() {\n        return this.#pending < this.#concurrency;\n    }\n    #next() {\n        this.#pending--;\n        this.#tryToStartAnother();\n        this.emit('next');\n    }\n    #onResumeInterval() {\n        this.#onInterval();\n        this.#initializeIntervalIfNeeded();\n        this.#timeoutId = undefined;\n    }\n    get #isIntervalPaused() {\n        const now = Date.now();\n        if (this.#intervalId === undefined) {\n            const delay = this.#intervalEnd - now;\n            if (delay < 0) {\n                // Act as the interval was done\n                // We don't need to resume it here because it will be resumed on line 160\n                this.#intervalCount = (this.#carryoverConcurrencyCount) ? this.#pending : 0;\n            }\n            else {\n                // Act as the interval is pending\n                if (this.#timeoutId === undefined) {\n                    this.#timeoutId = setTimeout(() => {\n                        this.#onResumeInterval();\n                    }, delay);\n                }\n                return true;\n            }\n        }\n        return false;\n    }\n    #tryToStartAnother() {\n        if (this.#queue.size === 0) {\n            // We can clear the interval (\"pause\")\n            // Because we can redo it later (\"resume\")\n            if (this.#intervalId) {\n                clearInterval(this.#intervalId);\n            }\n            this.#intervalId = undefined;\n            this.emit('empty');\n            if (this.#pending === 0) {\n                this.emit('idle');\n            }\n            return false;\n        }\n        if (!this.#isPaused) {\n            const canInitializeInterval = !this.#isIntervalPaused;\n            if (this.#doesIntervalAllowAnother && this.#doesConcurrentAllowAnother) {\n                const job = this.#queue.dequeue();\n                if (!job) {\n                    return false;\n                }\n                this.emit('active');\n                job();\n                if (canInitializeInterval) {\n                    this.#initializeIntervalIfNeeded();\n                }\n                return true;\n            }\n        }\n        return false;\n    }\n    #initializeIntervalIfNeeded() {\n        if (this.#isIntervalIgnored || this.#intervalId !== undefined) {\n            return;\n        }\n        this.#intervalId = setInterval(() => {\n            this.#onInterval();\n        }, this.#interval);\n        this.#intervalEnd = Date.now() + this.#interval;\n    }\n    #onInterval() {\n        if (this.#intervalCount === 0 && this.#pending === 0 && this.#intervalId) {\n            clearInterval(this.#intervalId);\n            this.#intervalId = undefined;\n        }\n        this.#intervalCount = this.#carryoverConcurrencyCount ? this.#pending : 0;\n        this.#processQueue();\n    }\n    /**\n    Executes all queued functions until it reaches the limit.\n    */\n    #processQueue() {\n        // eslint-disable-next-line no-empty\n        while (this.#tryToStartAnother()) { }\n    }\n    get concurrency() {\n        return this.#concurrency;\n    }\n    set concurrency(newConcurrency) {\n        if (!(typeof newConcurrency === 'number' && newConcurrency >= 1)) {\n            throw new TypeError(`Expected \\`concurrency\\` to be a number from 1 and up, got \\`${newConcurrency}\\` (${typeof newConcurrency})`);\n        }\n        this.#concurrency = newConcurrency;\n        this.#processQueue();\n    }\n    async #throwOnAbort(signal) {\n        return new Promise((_resolve, reject) => {\n            signal.addEventListener('abort', () => {\n                reject(signal.reason);\n            }, { once: true });\n        });\n    }\n    async add(function_, options = {}) {\n        options = {\n            timeout: this.timeout,\n            throwOnTimeout: this.#throwOnTimeout,\n            ...options,\n        };\n        return new Promise((resolve, reject) => {\n            this.#queue.enqueue(async () => {\n                this.#pending++;\n                this.#intervalCount++;\n                try {\n                    options.signal?.throwIfAborted();\n                    let operation = function_({ signal: options.signal });\n                    if (options.timeout) {\n                        operation = pTimeout(Promise.resolve(operation), { milliseconds: options.timeout });\n                    }\n                    if (options.signal) {\n                        operation = Promise.race([operation, this.#throwOnAbort(options.signal)]);\n                    }\n                    const result = await operation;\n                    resolve(result);\n                    this.emit('completed', result);\n                }\n                catch (error) {\n                    if (error instanceof TimeoutError && !options.throwOnTimeout) {\n                        resolve();\n                        return;\n                    }\n                    reject(error);\n                    this.emit('error', error);\n                }\n                finally {\n                    this.#next();\n                }\n            }, options);\n            this.emit('add');\n            this.#tryToStartAnother();\n        });\n    }\n    async addAll(functions, options) {\n        return Promise.all(functions.map(async (function_) => this.add(function_, options)));\n    }\n    /**\n    Start (or resume) executing enqueued tasks within concurrency limit. No need to call this if queue is not paused (via `options.autoStart = false` or by `.pause()` method.)\n    */\n    start() {\n        if (!this.#isPaused) {\n            return this;\n        }\n        this.#isPaused = false;\n        this.#processQueue();\n        return this;\n    }\n    /**\n    Put queue execution on hold.\n    */\n    pause() {\n        this.#isPaused = true;\n    }\n    /**\n    Clear the queue.\n    */\n    clear() {\n        this.#queue = new this.#queueClass();\n    }\n    /**\n    Can be called multiple times. Useful if you for example add additional items at a later time.\n\n    @returns A promise that settles when the queue becomes empty.\n    */\n    async onEmpty() {\n        // Instantly resolve if the queue is empty\n        if (this.#queue.size === 0) {\n            return;\n        }\n        await this.#onEvent('empty');\n    }\n    /**\n    @returns A promise that settles when the queue size is less than the given limit: `queue.size < limit`.\n\n    If you want to avoid having the queue grow beyond a certain size you can `await queue.onSizeLessThan()` before adding a new item.\n\n    Note that this only limits the number of items waiting to start. There could still be up to `concurrency` jobs already running that this call does not include in its calculation.\n    */\n    async onSizeLessThan(limit) {\n        // Instantly resolve if the queue is empty.\n        if (this.#queue.size < limit) {\n            return;\n        }\n        await this.#onEvent('next', () => this.#queue.size < limit);\n    }\n    /**\n    The difference with `.onEmpty` is that `.onIdle` guarantees that all work from the queue has finished. `.onEmpty` merely signals that the queue is empty, but it could mean that some promises haven't completed yet.\n\n    @returns A promise that settles when the queue becomes empty, and all promises have completed; `queue.size === 0 && queue.pending === 0`.\n    */\n    async onIdle() {\n        // Instantly resolve if none pending and if nothing else is queued\n        if (this.#pending === 0 && this.#queue.size === 0) {\n            return;\n        }\n        await this.#onEvent('idle');\n    }\n    async #onEvent(event, filter) {\n        return new Promise(resolve => {\n            const listener = () => {\n                if (filter && !filter()) {\n                    return;\n                }\n                this.off(event, listener);\n                resolve();\n            };\n            this.on(event, listener);\n        });\n    }\n    /**\n    Size of the queue, the number of queued items waiting to run.\n    */\n    get size() {\n        return this.#queue.size;\n    }\n    /**\n    Size of the queue, filtered by the given options.\n\n    For example, this can be used to find the number of items remaining in the queue with a specific priority level.\n    */\n    sizeBy(options) {\n        // eslint-disable-next-line unicorn/no-array-callback-reference\n        return this.#queue.filter(options).length;\n    }\n    /**\n    Number of running items (no longer in the queue).\n    */\n    get pending() {\n        return this.#pending;\n    }\n    /**\n    Whether the queue is currently paused.\n    */\n    get isPaused() {\n        return this.#isPaused;\n    }\n}\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE;AAAW,iBAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG;AAAG,gBAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE;AAAI,gBAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA;AAChE,gBAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB;AAAG,gBAAQ,UAAU,IAAI,OAAO;AAAA;AAC1D,eAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB;AAAG,eAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI;AAAG,gBAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC;AAAU,eAAO,CAAC;AACvB,UAAI,SAAS;AAAI,eAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC;AAAW,eAAO;AACvB,UAAI,UAAU;AAAI,eAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU;AAAM,eAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE;AAAM,iBAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC;AAAM,qBAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,uBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,gBAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO;AAAQ,eAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA;AACpE,qBAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG;AAAG,qBAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;AC/UA,mBAAyB;;;ACAlB,IAAM,eAAN,cAA2B,MAAM;AAAA,EACvC,YAAY,SAAS;AACpB,UAAM,OAAO;AACb,SAAK,OAAO;AAAA,EACb;AACD;AAMO,IAAM,aAAN,cAAyB,MAAM;AAAA,EACrC,YAAY,SAAS;AACpB,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EAChB;AACD;AAKA,IAAM,kBAAkB,kBAAgB,WAAW,iBAAiB,SACjE,IAAI,WAAW,YAAY,IAC3B,IAAI,aAAa,YAAY;AAKhC,IAAM,mBAAmB,YAAU;AAClC,QAAM,SAAS,OAAO,WAAW,SAC9B,gBAAgB,6BAA6B,IAC7C,OAAO;AAEV,SAAO,kBAAkB,QAAQ,SAAS,gBAAgB,MAAM;AACjE;AAEe,SAAR,SAA0B,SAAS,SAAS;AAClD,QAAM;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe,EAAC,YAAY,aAAY;AAAA,EACzC,IAAI;AAEJ,MAAI;AAEJ,QAAM,iBAAiB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvD,QAAI,OAAO,iBAAiB,YAAY,KAAK,KAAK,YAAY,MAAM,GAAG;AACtE,YAAM,IAAI,UAAU,4DAA4D,YAAY,IAAI;AAAA,IACjG;AAEA,QAAI,QAAQ,QAAQ;AACnB,YAAM,EAAC,OAAM,IAAI;AACjB,UAAI,OAAO,SAAS;AACnB,eAAO,iBAAiB,MAAM,CAAC;AAAA,MAChC;AAEA,aAAO,iBAAiB,SAAS,MAAM;AACtC,eAAO,iBAAiB,MAAM,CAAC;AAAA,MAChC,CAAC;AAAA,IACF;AAEA,QAAI,iBAAiB,OAAO,mBAAmB;AAC9C,cAAQ,KAAK,SAAS,MAAM;AAC5B;AAAA,IACD;AAGA,UAAM,eAAe,IAAI,aAAa;AAEtC,YAAQ,aAAa,WAAW,KAAK,QAAW,MAAM;AACrD,UAAI,UAAU;AACb,YAAI;AACH,kBAAQ,SAAS,CAAC;AAAA,QACnB,SAAS,OAAO;AACf,iBAAO,KAAK;AAAA,QACb;AAEA;AAAA,MACD;AAEA,UAAI,OAAO,QAAQ,WAAW,YAAY;AACzC,gBAAQ,OAAO;AAAA,MAChB;AAEA,UAAI,YAAY,OAAO;AACtB,gBAAQ;AAAA,MACT,WAAW,mBAAmB,OAAO;AACpC,eAAO,OAAO;AAAA,MACf,OAAO;AACN,qBAAa,UAAU,WAAW,2BAA2B,YAAY;AACzE,eAAO,YAAY;AAAA,MACpB;AAAA,IACD,GAAG,YAAY;AAEf,KAAC,YAAY;AACZ,UAAI;AACH,gBAAQ,MAAM,OAAO;AAAA,MACtB,SAAS,OAAO;AACf,eAAO,KAAK;AAAA,MACb;AAAA,IACD,GAAG;AAAA,EACJ,CAAC;AAED,QAAM,oBAAoB,eAAe,QAAQ,MAAM;AACtD,sBAAkB,MAAM;AAAA,EACzB,CAAC;AAED,oBAAkB,QAAQ,MAAM;AAC/B,iBAAa,aAAa,KAAK,QAAW,KAAK;AAC/C,YAAQ;AAAA,EACT;AAEA,SAAO;AACR;;;ACjHe,SAAR,WAA4B,OAAO,OAAO,YAAY;AACzD,MAAI,QAAQ;AACZ,MAAI,QAAQ,MAAM;AAClB,SAAO,QAAQ,GAAG;AACd,UAAM,OAAO,KAAK,MAAM,QAAQ,CAAC;AACjC,QAAI,KAAK,QAAQ;AACjB,QAAI,WAAW,MAAM,EAAE,GAAG,KAAK,KAAK,GAAG;AACnC,cAAQ,EAAE;AACV,eAAS,OAAO;AAAA,IACpB,OACK;AACD,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,SAAO;AACX;;;ACjBA;AACA,IAAqB,gBAArB,MAAmC;AAAA,EAAnC;AACI,+BAAS,CAAC;AAAA;AAAA,EACV,QAAQ,KAAK,SAAS;AAClB,cAAU;AAAA,MACN,UAAU;AAAA,MACV,GAAG;AAAA,IACP;AACA,UAAM,UAAU;AAAA,MACZ,UAAU,QAAQ;AAAA,MAClB;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,mBAAK,QAAO,KAAK,OAAO,CAAC,EAAE,YAAY,QAAQ,UAAU;AACtE,yBAAK,QAAO,KAAK,OAAO;AACxB;AAAA,IACJ;AACA,UAAM,QAAQ,WAAW,mBAAK,SAAQ,SAAS,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ;AAChF,uBAAK,QAAO,OAAO,OAAO,GAAG,OAAO;AAAA,EACxC;AAAA,EACA,UAAU;AACN,UAAM,OAAO,mBAAK,QAAO,MAAM;AAC/B,WAAO,6BAAM;AAAA,EACjB;AAAA,EACA,OAAO,SAAS;AACZ,WAAO,mBAAK,QAAO,OAAO,CAAC,YAAY,QAAQ,aAAa,QAAQ,QAAQ,EAAE,IAAI,CAAC,YAAY,QAAQ,GAAG;AAAA,EAC9G;AAAA,EACA,IAAI,OAAO;AACP,WAAO,mBAAK,QAAO;AAAA,EACvB;AACJ;AA3BI;;;ACFJ,oIAAAC,SAAA;AAMA,IAAqB,SAArB,cAAoC,aAAAC,QAAa;AAAA;AAAA,EAuB7C,YAAY,SAAS;AA7BzB;AA8BQ,UAAM;AA4BV,uBAAI;AAGJ,uBAAI;AAGJ;AAKA;AAKA,uBAAI;AAqBJ;AA+BA;AASA;AAWA;AAAA;AAAA;AAAA;AAcA,uBAAM;AA8GN,uBAAM;AAvQN;AACA;AACA,uCAAiB;AACjB;AACA;AACA,qCAAe;AACf;AACA;AACA,uBAAAD,SAAA;AACA;AACA,iCAAW;AAEX;AAAA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKI,cAAU;AAAA,MACN,2BAA2B;AAAA,MAC3B,aAAa,OAAO;AAAA,MACpB,UAAU;AAAA,MACV,aAAa,OAAO;AAAA,MACpB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,GAAG;AAAA,IACP;AACA,QAAI,EAAE,OAAO,QAAQ,gBAAgB,YAAY,QAAQ,eAAe,IAAI;AACxE,YAAM,IAAI,UAAU,kEAAgE,aAAQ,gBAAR,mBAAqB,eAAc,EAAE,OAAO,OAAO,QAAQ,WAAW,GAAG;AAAA,IACjK;AACA,QAAI,QAAQ,aAAa,UAAa,EAAE,OAAO,SAAS,QAAQ,QAAQ,KAAK,QAAQ,YAAY,IAAI;AACjG,YAAM,IAAI,UAAU,6DAA2D,aAAQ,aAAR,mBAAkB,eAAc,EAAE,OAAO,OAAO,QAAQ,QAAQ,GAAG;AAAA,IACtJ;AACA,uBAAK,4BAA6B,QAAQ;AAC1C,uBAAK,oBAAqB,QAAQ,gBAAgB,OAAO,qBAAqB,QAAQ,aAAa;AACnG,uBAAK,cAAe,QAAQ;AAC5B,uBAAK,WAAY,QAAQ;AACzB,uBAAKA,SAAS,IAAI,QAAQ,WAAW;AACrC,uBAAK,aAAc,QAAQ;AAC3B,SAAK,cAAc,QAAQ;AAC3B,SAAK,UAAU,QAAQ;AACvB,uBAAK,iBAAkB,QAAQ,mBAAmB;AAClD,uBAAK,WAAY,QAAQ,cAAc;AAAA,EAC3C;AAAA,EA6FA,IAAI,cAAc;AACd,WAAO,mBAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY,gBAAgB;AAC5B,QAAI,EAAE,OAAO,mBAAmB,YAAY,kBAAkB,IAAI;AAC9D,YAAM,IAAI,UAAU,gEAAgE,cAAc,OAAO,OAAO,cAAc,GAAG;AAAA,IACrI;AACA,uBAAK,cAAe;AACpB,0BAAK,gCAAL;AAAA,EACJ;AAAA,EAQA,MAAM,IAAI,WAAW,UAAU,CAAC,GAAG;AAC/B,cAAU;AAAA,MACN,SAAS,KAAK;AAAA,MACd,gBAAgB,mBAAK;AAAA,MACrB,GAAG;AAAA,IACP;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,yBAAKA,SAAO,QAAQ,YAAY;AA9K5C;AA+KgB,+BAAK,UAAL;AACA,+BAAK,gBAAL;AACA,YAAI;AACA,wBAAQ,WAAR,mBAAgB;AAChB,cAAI,YAAY,UAAU,EAAE,QAAQ,QAAQ,OAAO,CAAC;AACpD,cAAI,QAAQ,SAAS;AACjB,wBAAY,SAAS,QAAQ,QAAQ,SAAS,GAAG,EAAE,cAAc,QAAQ,QAAQ,CAAC;AAAA,UACtF;AACA,cAAI,QAAQ,QAAQ;AAChB,wBAAY,QAAQ,KAAK,CAAC,WAAW,sBAAK,gCAAL,WAAmB,QAAQ,OAAO,CAAC;AAAA,UAC5E;AACA,gBAAM,SAAS,MAAM;AACrB,kBAAQ,MAAM;AACd,eAAK,KAAK,aAAa,MAAM;AAAA,QACjC,SACO,OAAO;AACV,cAAI,iBAAiB,gBAAgB,CAAC,QAAQ,gBAAgB;AAC1D,oBAAQ;AACR;AAAA,UACJ;AACA,iBAAO,KAAK;AACZ,eAAK,KAAK,SAAS,KAAK;AAAA,QAC5B,UACA;AACI,gCAAK,gBAAL;AAAA,QACJ;AAAA,MACJ,GAAG,OAAO;AACV,WAAK,KAAK,KAAK;AACf,4BAAK,0CAAL;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,OAAO,WAAW,SAAS;AAC7B,WAAO,QAAQ,IAAI,UAAU,IAAI,OAAO,cAAc,KAAK,IAAI,WAAW,OAAO,CAAC,CAAC;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,QAAI,CAAC,mBAAK,YAAW;AACjB,aAAO;AAAA,IACX;AACA,uBAAK,WAAY;AACjB,0BAAK,gCAAL;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,uBAAK,WAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,uBAAKA,SAAS,KAAI,mBAAK,cAAY;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,UAAU;AAEZ,QAAI,mBAAKA,SAAO,SAAS,GAAG;AACxB;AAAA,IACJ;AACA,UAAM,sBAAK,sBAAL,WAAc;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,eAAe,OAAO;AAExB,QAAI,mBAAKA,SAAO,OAAO,OAAO;AAC1B;AAAA,IACJ;AACA,UAAM,sBAAK,sBAAL,WAAc,QAAQ,MAAM,mBAAKA,SAAO,OAAO;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,SAAS;AAEX,QAAI,mBAAK,cAAa,KAAK,mBAAKA,SAAO,SAAS,GAAG;AAC/C;AAAA,IACJ;AACA,UAAM,sBAAK,sBAAL,WAAc;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAgBA,IAAI,OAAO;AACP,WAAO,mBAAKA,SAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,SAAS;AAEZ,WAAO,mBAAKA,SAAO,OAAO,OAAO,EAAE;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACV,WAAO,mBAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW;AACX,WAAO,mBAAK;AAAA,EAChB;AACJ;AA9SI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,UAAA;AACA;AACA;AAEA;AACA;AACA;AAqCI;AAAA,+BAAyB,WAAG;AAC5B,SAAO,mBAAK,uBAAsB,mBAAK,kBAAiB,mBAAK;AACjE;AACI;AAAA,iCAA2B,WAAG;AAC9B,SAAO,mBAAK,YAAW,mBAAK;AAChC;AACA;AAAA,UAAK,WAAG;AACJ,yBAAK,UAAL;AACA,wBAAK,0CAAL;AACA,OAAK,KAAK,MAAM;AACpB;AACA;AAAA,sBAAiB,WAAG;AAChB,wBAAK,4BAAL;AACA,wBAAK,4DAAL;AACA,qBAAK,YAAa;AACtB;AACI;AAAA,uBAAiB,WAAG;AACpB,QAAM,MAAM,KAAK,IAAI;AACrB,MAAI,mBAAK,iBAAgB,QAAW;AAChC,UAAM,QAAQ,mBAAK,gBAAe;AAClC,QAAI,QAAQ,GAAG;AAGX,yBAAK,gBAAkB,mBAAK,8BAA8B,mBAAK,YAAW;AAAA,IAC9E,OACK;AAED,UAAI,mBAAK,gBAAe,QAAW;AAC/B,2BAAK,YAAa,WAAW,MAAM;AAC/B,gCAAK,wCAAL;AAAA,QACJ,GAAG,KAAK;AAAA,MACZ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA;AAAA,uBAAkB,WAAG;AACjB,MAAI,mBAAKA,SAAO,SAAS,GAAG;AAGxB,QAAI,mBAAK,cAAa;AAClB,oBAAc,mBAAK,YAAW;AAAA,IAClC;AACA,uBAAK,aAAc;AACnB,SAAK,KAAK,OAAO;AACjB,QAAI,mBAAK,cAAa,GAAG;AACrB,WAAK,KAAK,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,EACX;AACA,MAAI,CAAC,mBAAK,YAAW;AACjB,UAAM,wBAAwB,CAAC,mBAAK;AACpC,QAAI,mBAAK,4DAA6B,mBAAK,8DAA6B;AACpE,YAAM,MAAM,mBAAKA,SAAO,QAAQ;AAChC,UAAI,CAAC,KAAK;AACN,eAAO;AAAA,MACX;AACA,WAAK,KAAK,QAAQ;AAClB,UAAI;AACJ,UAAI,uBAAuB;AACvB,8BAAK,4DAAL;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA;AAAA,gCAA2B,WAAG;AAC1B,MAAI,mBAAK,uBAAsB,mBAAK,iBAAgB,QAAW;AAC3D;AAAA,EACJ;AACA,qBAAK,aAAc,YAAY,MAAM;AACjC,0BAAK,4BAAL;AAAA,EACJ,GAAG,mBAAK,UAAS;AACjB,qBAAK,cAAe,KAAK,IAAI,IAAI,mBAAK;AAC1C;AACA;AAAA,gBAAW,WAAG;AACV,MAAI,mBAAK,oBAAmB,KAAK,mBAAK,cAAa,KAAK,mBAAK,cAAa;AACtE,kBAAc,mBAAK,YAAW;AAC9B,uBAAK,aAAc;AAAA,EACvB;AACA,qBAAK,gBAAiB,mBAAK,8BAA6B,mBAAK,YAAW;AACxE,wBAAK,gCAAL;AACJ;AAIA;AAAA,kBAAa,WAAG;AAEZ,SAAO,sBAAK,0CAAL,YAA2B;AAAA,EAAE;AACxC;AAWM;AAAA,kBAAa,eAAC,QAAQ;AACxB,SAAO,IAAI,QAAQ,CAAC,UAAU,WAAW;AACrC,WAAO,iBAAiB,SAAS,MAAM;AACnC,aAAO,OAAO,MAAM;AAAA,IACxB,GAAG,EAAE,MAAM,KAAK,CAAC;AAAA,EACrB,CAAC;AACL;AAwGM;AAAA,aAAQ,eAAC,OAAO,QAAQ;AAC1B,SAAO,IAAI,QAAQ,aAAW;AAC1B,UAAM,WAAW,MAAM;AACnB,UAAI,UAAU,CAAC,OAAO,GAAG;AACrB;AAAA,MACJ;AACA,WAAK,IAAI,OAAO,QAAQ;AACxB,cAAQ;AAAA,IACZ;AACA,SAAK,GAAG,OAAO,QAAQ;AAAA,EAC3B,CAAC;AACL;", "names": ["EventEmitter", "_queue", "EventEmitter"]}