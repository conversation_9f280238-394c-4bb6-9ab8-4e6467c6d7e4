import{g as un}from"./index-C0qcAVKU.js";import{e as cn}from"./index.DUmRo3Ep.entry.js";import{a as fn}from"./javascript-C7QCAkh7.js";import{a as ln}from"./css-Bx1xD9zI.js";import{r as on}from"./htmlmixed-BGdafJOX.js";function an(l,o){for(var r=0;r<o.length;r++){const u=o[r];if(typeof u!="string"&&!Array.isArray(u)){for(const t in u)if(t!=="default"&&!(t in l)){const f=Object.getOwnPropertyDescriptor(u,t);f&&Object.defineProperty(l,t,f.get?f:{enumerable:!0,get:()=>u[t]})}}}return Object.freeze(Object.defineProperty(l,Symbol.toStringTag,{value:"Module"}))}var pn={exports:{}};(function(l,o){(function(r){r(cn(),fn,ln,on())})(function(r){r.defineMode("pug",function(u){var t="keyword",f="meta",j="builtin",b="qualifier",h={"{":"}","(":")","[":"]"},c=r.getMode(u,"javascript");function a(){this.javaScriptLine=!1,this.javaScriptLineExcludesColon=!1,this.javaScriptArguments=!1,this.javaScriptArgumentsDepth=0,this.isInterpolating=!1,this.interpolationNesting=0,this.jsState=r.startState(c),this.restOfLine="",this.isIncludeFiltered=!1,this.isEach=!1,this.lastTag="",this.scriptType="",this.isAttrs=!1,this.attrsNest=[],this.inAttributeName=!0,this.attributeIsType=!1,this.attrValue="",this.indentOf=1/0,this.indentToken="",this.innerMode=null,this.innerState=null,this.innerModeForLine=!1}a.prototype.copy=function(){var n=new a;return n.javaScriptLine=this.javaScriptLine,n.javaScriptLineExcludesColon=this.javaScriptLineExcludesColon,n.javaScriptArguments=this.javaScriptArguments,n.javaScriptArgumentsDepth=this.javaScriptArgumentsDepth,n.isInterpolating=this.isInterpolating,n.interpolationNesting=this.interpolationNesting,n.jsState=r.copyState(c,this.jsState),n.innerMode=this.innerMode,this.innerMode&&this.innerState&&(n.innerState=r.copyState(this.innerMode,this.innerState)),n.restOfLine=this.restOfLine,n.isIncludeFiltered=this.isIncludeFiltered,n.isEach=this.isEach,n.lastTag=this.lastTag,n.scriptType=this.scriptType,n.isAttrs=this.isAttrs,n.attrsNest=this.attrsNest.slice(),n.inAttributeName=this.inAttributeName,n.attributeIsType=this.attributeIsType,n.attrValue=this.attrValue,n.indentOf=this.indentOf,n.indentToken=this.indentToken,n.innerModeForLine=this.innerModeForLine,n};function L(n,i){if(n.sol()&&(i.javaScriptLine=!1,i.javaScriptLineExcludesColon=!1),i.javaScriptLine){if(i.javaScriptLineExcludesColon&&n.peek()===":"){i.javaScriptLine=!1,i.javaScriptLineExcludesColon=!1;return}var e=c.token(n,i.jsState);return n.eol()&&(i.javaScriptLine=!1),e||!0}}function y(n,i){if(i.javaScriptArguments){if(i.javaScriptArgumentsDepth===0&&n.peek()!=="("){i.javaScriptArguments=!1;return}if(n.peek()==="("?i.javaScriptArgumentsDepth++:n.peek()===")"&&i.javaScriptArgumentsDepth--,i.javaScriptArgumentsDepth===0){i.javaScriptArguments=!1;return}var e=c.token(n,i.jsState);return e||!0}}function A(n){if(n.match(/^yield\b/))return"keyword"}function x(n){if(n.match(/^(?:doctype) *([^\n]+)?/))return f}function s(n,i){if(n.match("#{"))return i.isInterpolating=!0,i.interpolationNesting=0,"punctuation"}function T(n,i){if(i.isInterpolating){if(n.peek()==="}"){if(i.interpolationNesting--,i.interpolationNesting<0)return n.next(),i.isInterpolating=!1,"punctuation"}else n.peek()==="{"&&i.interpolationNesting++;return c.token(n,i.jsState)||!0}}function k(n,i){if(n.match(/^case\b/))return i.javaScriptLine=!0,t}function O(n,i){if(n.match(/^when\b/))return i.javaScriptLine=!0,i.javaScriptLineExcludesColon=!0,t}function N(n){if(n.match(/^default\b/))return t}function w(n,i){if(n.match(/^extends?\b/))return i.restOfLine="string",t}function E(n,i){if(n.match(/^append\b/))return i.restOfLine="variable",t}function I(n,i){if(n.match(/^prepend\b/))return i.restOfLine="variable",t}function F(n,i){if(n.match(/^block\b *(?:(prepend|append)\b)?/))return i.restOfLine="variable",t}function D(n,i){if(n.match(/^include\b/))return i.restOfLine="string",t}function M(n,i){if(n.match(/^include:([a-zA-Z0-9\-]+)/,!1)&&n.match("include"))return i.isIncludeFiltered=!0,t}function C(n,i){if(i.isIncludeFiltered){var e=d(n,i);return i.isIncludeFiltered=!1,i.restOfLine="string",e}}function m(n,i){if(n.match(/^mixin\b/))return i.javaScriptLine=!0,t}function V(n,i){if(n.match(/^\+([-\w]+)/))return n.match(/^\( *[-\w]+ *=/,!1)||(i.javaScriptArguments=!0,i.javaScriptArgumentsDepth=0),"variable";if(n.match("+#{",!1))return n.next(),i.mixinCallAfter=!0,s(n,i)}function _(n,i){if(i.mixinCallAfter)return i.mixinCallAfter=!1,n.match(/^\( *[-\w]+ *=/,!1)||(i.javaScriptArguments=!0,i.javaScriptArgumentsDepth=0),!0}function P(n,i){if(n.match(/^(if|unless|else if|else)\b/))return i.javaScriptLine=!0,t}function q(n,i){if(n.match(/^(- *)?(each|for)\b/))return i.isEach=!0,t}function $(n,i){if(i.isEach){if(n.match(/^ in\b/))return i.javaScriptLine=!0,i.isEach=!1,t;if(n.sol()||n.eol())i.isEach=!1;else if(n.next()){for(;!n.match(/^ in\b/,!1)&&n.next(););return"variable"}}}function z(n,i){if(n.match(/^while\b/))return i.javaScriptLine=!0,t}function R(n,i){var e;if(e=n.match(/^(\w(?:[-:\w]*\w)?)\/?/))return i.lastTag=e[1].toLowerCase(),i.lastTag==="script"&&(i.scriptType="application/javascript"),"tag"}function d(n,i){if(n.match(/^:([\w\-]+)/)){var e;return u&&u.innerModes&&(e=u.innerModes(n.current().substring(1))),e||(e=n.current().substring(1)),typeof e=="string"&&(e=r.getMode(u,e)),p(n,i,e),"atom"}}function Y(n,i){if(n.match(/^(!?=|-)/))return i.javaScriptLine=!0,"punctuation"}function B(n){if(n.match(/^#([\w-]+)/))return j}function H(n){if(n.match(/^\.([\w-]+)/))return b}function K(n,i){if(n.peek()=="(")return n.next(),i.isAttrs=!0,i.attrsNest=[],i.inAttributeName=!0,i.attrValue="",i.attributeIsType=!1,"punctuation"}function S(n,i){if(i.isAttrs){if(h[n.peek()]&&i.attrsNest.push(h[n.peek()]),i.attrsNest[i.attrsNest.length-1]===n.peek())i.attrsNest.pop();else if(n.eat(")"))return i.isAttrs=!1,"punctuation";if(i.inAttributeName&&n.match(/^[^=,\)!]+/))return(n.peek()==="="||n.peek()==="!")&&(i.inAttributeName=!1,i.jsState=r.startState(c),i.lastTag==="script"&&n.current().trim().toLowerCase()==="type"?i.attributeIsType=!0:i.attributeIsType=!1),"attribute";var e=c.token(n,i.jsState);if(i.attributeIsType&&e==="string"&&(i.scriptType=n.current().toString()),i.attrsNest.length===0&&(e==="string"||e==="variable"||e==="keyword"))try{return Function("","var x "+i.attrValue.replace(/,\s*$/,"").replace(/^!/,"")),i.inAttributeName=!0,i.attrValue="",n.backUp(n.current().length),S(n,i)}catch{}return i.attrValue+=n.current(),e||!0}}function U(n,i){if(n.match(/^&attributes\b/))return i.javaScriptArguments=!0,i.javaScriptArgumentsDepth=0,"keyword"}function W(n){if(n.sol()&&n.eatSpace())return"indent"}function Z(n,i){if(n.match(/^ *\/\/(-)?([^\n]*)/))return i.indentOf=n.indentation(),i.indentToken="comment","comment"}function G(n){if(n.match(/^: */))return"colon"}function J(n,i){if(n.match(/^(?:\| ?| )([^\n]+)/))return"string";if(n.match(/^(<[^\n]*)/,!1))return p(n,i,"htmlmixed"),i.innerModeForLine=!0,v(n,i,!0)}function Q(n,i){if(n.eat(".")){var e=null;return i.lastTag==="script"&&i.scriptType.toLowerCase().indexOf("javascript")!=-1?e=i.scriptType.toLowerCase().replace(/"|'/g,""):i.lastTag==="style"&&(e="css"),p(n,i,e),"dot"}}function X(n){return n.next(),null}function p(n,i,e){e=r.mimeModes[e]||e,e=u.innerModes&&u.innerModes(e)||e,e=r.mimeModes[e]||e,e=r.getMode(u,e),i.indentOf=n.indentation(),e&&e.name!=="null"?i.innerMode=e:i.indentToken="string"}function v(n,i,e){if(n.indentation()>i.indentOf||i.innerModeForLine&&!n.sol()||e)return i.innerMode?(i.innerState||(i.innerState=i.innerMode.startState?r.startState(i.innerMode,n.indentation()):{}),n.hideFirstChars(i.indentOf+2,function(){return i.innerMode.token(n,i.innerState)||!0})):(n.skipToEnd(),i.indentToken);n.sol()&&(i.indentOf=1/0,i.indentToken=null,i.innerMode=null,i.innerState=null)}function nn(n,i){if(n.sol()&&(i.restOfLine=""),i.restOfLine){n.skipToEnd();var e=i.restOfLine;return i.restOfLine="",e}}function en(){return new a}function tn(n){return n.copy()}function rn(n,i){var e=v(n,i)||nn(n,i)||T(n,i)||C(n,i)||$(n,i)||S(n,i)||L(n,i)||y(n,i)||_(n,i)||A(n)||x(n)||s(n,i)||k(n,i)||O(n,i)||N(n)||w(n,i)||E(n,i)||I(n,i)||F(n,i)||D(n,i)||M(n,i)||m(n,i)||V(n,i)||P(n,i)||q(n,i)||z(n,i)||R(n,i)||d(n,i)||Y(n,i)||B(n)||H(n)||K(n,i)||U(n,i)||W(n)||J(n,i)||Z(n,i)||G(n)||Q(n,i)||X(n);return e===!0?null:e}return{startState:en,copyState:tn,token:rn}},"javascript","css","htmlmixed"),r.defineMIME("text/x-pug","pug"),r.defineMIME("text/x-jade","pug")})})();var g=pn.exports;const hn=un(g),bn=an({__proto__:null,default:hn},[g]);export{g as a,bn as p};
