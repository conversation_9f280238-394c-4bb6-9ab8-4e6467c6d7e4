import{f as e,a as i,b as r,m as o}from"./match-FWbQ--A_.js";import{b as t}from"./index.DUmRo3Ep.entry.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";const m={narrow:["v.Chr.","n.Chr."],abbreviated:["v.Chr.","n.Chr."],wide:["vor <PERSON><PERSON>","nach <PERSON><PERSON>"]},g={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. Quartal","2. Quartal","3. Quartal","4. Quartal"]},n={narrow:["J","F","M","A","<PERSON>","<PERSON>","J","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>"],abbreviated:["<PERSON><PERSON>n","<PERSON>","<PERSON><PERSON>r","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>t","<PERSON>","<PERSON>z"],wide:["<PERSON><PERSON><PERSON>","<PERSON>ruar","<PERSON><PERSON>rz","April","<PERSON>","<PERSON>i","<PERSON>i","August","September","Oktober","November","Dezember"]},s={narrow:n.narrow,abbreviated:["Jän.","Feb.","März","Apr.","Mai","Juni","Juli","Aug.","Sep.","Okt.","Nov.","Dez."],wide:n.wide},d={narrow:["S","M","D","M","D","F","S"],short:["So","Mo","Di","Mi","Do","Fr","Sa"],abbreviated:["So.","Mo.","Di.","Mi.","Do.","Fr.","Sa."],wide:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"]},h={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachm.",evening:"Abend",night:"Nacht"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"}},u={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachm.",evening:"abends",night:"nachts"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"}},c=a=>Number(a)+".",l={ordinalNumber:c,era:t({values:m,defaultWidth:"wide"}),quarter:t({values:g,defaultWidth:"wide",argumentCallback:a=>a-1}),month:t({values:n,formattingValues:s,defaultWidth:"wide"}),day:t({values:d,defaultWidth:"wide"}),dayPeriod:t({values:h,defaultWidth:"wide",formattingValues:u,defaultFormattingWidth:"wide"})},J={code:"de-AT",formatDistance:e,formatLong:i,formatRelative:r,localize:l,match:o,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{J as deAT,J as default};
