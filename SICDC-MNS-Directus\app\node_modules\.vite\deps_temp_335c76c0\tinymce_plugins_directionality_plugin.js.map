{"version": 3, "sources": ["../../../../node_modules/.pnpm/tinymce@7.1.1/node_modules/tinymce/plugins/directionality/plugin.js"], "sourcesContent": ["/**\n * TinyMCE version 7.1.1 (2024-05-22)\n */\n\n(function () {\n    'use strict';\n\n    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const isString = isType$1('string');\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const compose1 = (fbc, fab) => a => fbc(fab(a));\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const never = constant(false);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const filter = (xs, pred) => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    const fromDom = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom,\n      fromPoint\n    };\n\n    const is = (element, selector) => {\n      const dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        const elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const name = element => {\n      const r = element.dom.nodeName;\n      return r.toLowerCase();\n    };\n    const type = element => element.dom.nodeType;\n    const isType = t => element => type(element) === t;\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocument = isType(DOCUMENT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n    const isTag = tag => e => isElement(e) && name(e) === tag;\n\n    const owner = element => SugarElement.fromDom(element.dom.ownerDocument);\n    const documentOrOwner = dos => isDocument(dos) ? dos : owner(dos);\n    const parent = element => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const children$2 = element => map(element.dom.childNodes, SugarElement.fromDom);\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const set = (element, key, value) => {\n      rawSet(element.dom, key, value);\n    };\n    const remove = (element, key) => {\n      element.dom.removeAttribute(key);\n    };\n\n    const isShadowRoot = dos => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    const getRootNode = supported ? e => SugarElement.fromDom(e.dom.getRootNode()) : documentOrOwner;\n    const getShadowRoot = e => {\n      const r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    const getShadowHost = e => SugarElement.fromDom(e.dom.host);\n\n    const inBody = element => {\n      const dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      const doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n\n    const ancestor$1 = (scope, predicate, isRoot) => {\n      let element = scope.dom;\n      const stop = isFunction(isRoot) ? isRoot : never;\n      while (element.parentNode) {\n        element = element.parentNode;\n        const el = SugarElement.fromDom(element);\n        if (predicate(el)) {\n          return Optional.some(el);\n        } else if (stop(el)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n\n    const ancestor = (scope, selector, isRoot) => ancestor$1(scope, e => is(e, selector), isRoot);\n\n    const isSupported = dom => dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    const get = (element, property) => {\n      const dom = element.dom;\n      const styles = window.getComputedStyle(dom);\n      const r = styles.getPropertyValue(property);\n      return r === '' && !inBody(element) ? getUnsafeProperty(dom, property) : r;\n    };\n    const getUnsafeProperty = (dom, property) => isSupported(dom) ? dom.style.getPropertyValue(property) : '';\n\n    const getDirection = element => get(element, 'direction') === 'rtl' ? 'rtl' : 'ltr';\n\n    const children$1 = (scope, predicate) => filter(children$2(scope), predicate);\n\n    const children = (scope, selector) => children$1(scope, e => is(e, selector));\n\n    const getParentElement = element => parent(element).filter(isElement);\n    const getNormalizedBlock = (element, isListItem) => {\n      const normalizedElement = isListItem ? ancestor(element, 'ol,ul') : Optional.some(element);\n      return normalizedElement.getOr(element);\n    };\n    const isListItem = isTag('li');\n    const setDirOnElements = (dom, blocks, dir) => {\n      each(blocks, block => {\n        const blockElement = SugarElement.fromDom(block);\n        const isBlockElementListItem = isListItem(blockElement);\n        const normalizedBlock = getNormalizedBlock(blockElement, isBlockElementListItem);\n        const normalizedBlockParent = getParentElement(normalizedBlock);\n        normalizedBlockParent.each(parent => {\n          dom.setStyle(normalizedBlock.dom, 'direction', null);\n          const parentDirection = getDirection(parent);\n          if (parentDirection === dir) {\n            remove(normalizedBlock, 'dir');\n          } else {\n            set(normalizedBlock, 'dir', dir);\n          }\n          if (getDirection(normalizedBlock) !== dir) {\n            dom.setStyle(normalizedBlock.dom, 'direction', dir);\n          }\n          if (isBlockElementListItem) {\n            const listItems = children(normalizedBlock, 'li[dir],li[style]');\n            each(listItems, listItem => {\n              remove(listItem, 'dir');\n              dom.setStyle(listItem.dom, 'direction', null);\n            });\n          }\n        });\n      });\n    };\n    const setDir = (editor, dir) => {\n      if (editor.selection.isEditable()) {\n        setDirOnElements(editor.dom, editor.selection.getSelectedBlocks(), dir);\n        editor.nodeChanged();\n      }\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mceDirectionLTR', () => {\n        setDir(editor, 'ltr');\n      });\n      editor.addCommand('mceDirectionRTL', () => {\n        setDir(editor, 'rtl');\n      });\n    };\n\n    const getNodeChangeHandler = (editor, dir) => api => {\n      const nodeChangeHandler = e => {\n        const element = SugarElement.fromDom(e.element);\n        api.setActive(getDirection(element) === dir);\n        api.setEnabled(editor.selection.isEditable());\n      };\n      editor.on('NodeChange', nodeChangeHandler);\n      api.setEnabled(editor.selection.isEditable());\n      return () => editor.off('NodeChange', nodeChangeHandler);\n    };\n    const register = editor => {\n      editor.ui.registry.addToggleButton('ltr', {\n        tooltip: 'Left to right',\n        icon: 'ltr',\n        onAction: () => editor.execCommand('mceDirectionLTR'),\n        onSetup: getNodeChangeHandler(editor, 'ltr')\n      });\n      editor.ui.registry.addToggleButton('rtl', {\n        tooltip: 'Right to left',\n        icon: 'rtl',\n        onAction: () => editor.execCommand('mceDirectionRTL'),\n        onSetup: getNodeChangeHandler(editor, 'rtl')\n      });\n    };\n\n    var Plugin = () => {\n      global.add('directionality', editor => {\n        register$1(editor);\n        register(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n"], "mappings": ";CAIC,WAAY;AACT;AAEA,MAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAE/D,QAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,QAAI;AACJ,QAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,aAAO;AAAA,IACT,OAAO;AACL,eAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,IAC7F;AAAA,EACF;AACA,QAAM,SAAS,OAAK;AAClB,UAAM,IAAI,OAAO;AACjB,QAAI,MAAM,MAAM;AACd,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,aAAO;AAAA,IACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,WAAW,CAAAA,UAAQ,WAAS,OAAO,KAAK,MAAMA;AACpD,QAAM,eAAe,CAAAA,UAAQ,WAAS,OAAO,UAAUA;AACvD,QAAM,WAAW,SAAS,QAAQ;AAClC,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,QAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,QAAM,aAAa,aAAa,UAAU;AAC1C,QAAM,WAAW,aAAa,QAAQ;AAEtC,QAAM,WAAW,CAAC,KAAK,QAAQ,OAAK,IAAI,IAAI,CAAC,CAAC;AAC9C,QAAM,WAAW,WAAS;AACxB,WAAO,MAAM;AACX,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,QAAQ,SAAS,KAAK;AAAA,EAE5B,MAAM,SAAS;AAAA,IACb,YAAY,KAAK,OAAO;AACtB,WAAK,MAAM;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,IACA,OAAO,KAAK,OAAO;AACjB,aAAO,IAAI,SAAS,MAAM,KAAK;AAAA,IACjC;AAAA,IACA,OAAO,OAAO;AACZ,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,KAAK,QAAQ,QAAQ;AACnB,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AACP,aAAO,KAAK;AAAA,IACd;AAAA,IACA,SAAS;AACP,aAAO,CAAC,KAAK;AAAA,IACf;AAAA,IACA,IAAI,QAAQ;AACV,UAAI,KAAK,KAAK;AACZ,eAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,MACzC,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IACzC;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,IAC1C;AAAA,IACA,OAAO,WAAW;AAChB,UAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,SAAS,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,MAAM,aAAa;AACjB,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,GAAG,aAAa;AACd,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B;AAAA,IACA,WAAW,OAAO;AAChB,aAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,IACvC;AAAA,IACA,QAAQ,OAAO;AACb,aAAO,KAAK,MAAM,OAAO,MAAM;AAAA,IACjC;AAAA,IACA,SAAS,SAAS;AAChB,UAAI,CAAC,KAAK,KAAK;AACb,cAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,MAC9F,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,IACA,OAAO,KAAK,OAAO;AACjB,aAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,IACrE;AAAA,IACA,YAAY;AACV,aAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,IACjC;AAAA,IACA,iBAAiB;AACf,aAAO,KAAK;AAAA,IACd;AAAA,IACA,KAAK,QAAQ;AACX,UAAI,KAAK,KAAK;AACZ,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,IACA,UAAU;AACR,aAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,IACpC;AAAA,IACA,WAAW;AACT,aAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,IAC9C;AAAA,EACF;AACA,WAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,QAAM,MAAM,CAAC,IAAI,MAAM;AACrB,UAAM,MAAM,GAAG;AACf,UAAM,IAAI,IAAI,MAAM,GAAG;AACvB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,IACf;AACA,WAAO;AAAA,EACT;AACA,QAAM,OAAO,CAAC,IAAI,MAAM;AACtB,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,QAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACA,QAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,KAAK,GAAG,CAAC,GAAG;AACd,UAAE,KAAK,CAAC;AAAA,MACV;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,QAAM,WAAW;AACjB,QAAM,oBAAoB;AAC1B,QAAM,UAAU;AAChB,QAAM,OAAO;AAEb,QAAM,WAAW,CAAC,MAAM,UAAU;AAChC,UAAM,MAAM,SAAS;AACrB,UAAM,MAAM,IAAI,cAAc,KAAK;AACnC,QAAI,YAAY;AAChB,QAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,YAAM,UAAU;AAChB,cAAQ,MAAM,SAAS,IAAI;AAC3B,YAAM,IAAI,MAAM,OAAO;AAAA,IACzB;AACA,WAAO,QAAQ,IAAI,WAAW,CAAC,CAAC;AAAA,EAClC;AACA,QAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,UAAM,MAAM,SAAS;AACrB,UAAM,OAAO,IAAI,cAAc,GAAG;AAClC,WAAO,QAAQ,IAAI;AAAA,EACrB;AACA,QAAM,WAAW,CAAC,MAAM,UAAU;AAChC,UAAM,MAAM,SAAS;AACrB,UAAM,OAAO,IAAI,eAAe,IAAI;AACpC,WAAO,QAAQ,IAAI;AAAA,EACrB;AACA,QAAM,UAAU,UAAQ;AACtB,QAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,WAAO,EAAE,KAAK,KAAK;AAAA,EACrB;AACA,QAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,OAAO;AAChG,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,KAAK,CAAC,SAAS,aAAa;AAChC,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI,aAAa,SAAS;AAC5B,aAAO;AAAA,IACT,OAAO;AACL,YAAM,OAAO;AACb,UAAI,KAAK,YAAY,QAAW;AAC9B,eAAO,KAAK,QAAQ,QAAQ;AAAA,MAC9B,WAAW,KAAK,sBAAsB,QAAW;AAC/C,eAAO,KAAK,kBAAkB,QAAQ;AAAA,MACxC,WAAW,KAAK,0BAA0B,QAAW;AACnD,eAAO,KAAK,sBAAsB,QAAQ;AAAA,MAC5C,WAAW,KAAK,uBAAuB,QAAW;AAChD,eAAO,KAAK,mBAAmB,QAAQ;AAAA,MACzC,OAAO;AACL,cAAM,IAAI,MAAM,gCAAgC;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAElE,QAAM,OAAO,aAAW;AACtB,UAAM,IAAI,QAAQ,IAAI;AACtB,WAAO,EAAE,YAAY;AAAA,EACvB;AACA,QAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,QAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,QAAM,YAAY,OAAO,OAAO;AAChC,QAAM,SAAS,OAAO,IAAI;AAC1B,QAAM,aAAa,OAAO,QAAQ;AAClC,QAAM,qBAAqB,OAAO,iBAAiB;AACnD,QAAM,QAAQ,SAAO,OAAK,UAAU,CAAC,KAAK,KAAK,CAAC,MAAM;AAEtD,QAAM,QAAQ,aAAW,aAAa,QAAQ,QAAQ,IAAI,aAAa;AACvE,QAAM,kBAAkB,SAAO,WAAW,GAAG,IAAI,MAAM,MAAM,GAAG;AAChE,QAAM,SAAS,aAAW,SAAS,KAAK,QAAQ,IAAI,UAAU,EAAE,IAAI,aAAa,OAAO;AACxF,QAAM,aAAa,aAAW,IAAI,QAAQ,IAAI,YAAY,aAAa,OAAO;AAE9E,QAAM,SAAS,CAAC,KAAK,KAAK,UAAU;AAClC,QAAI,SAAS,KAAK,KAAK,UAAU,KAAK,KAAK,SAAS,KAAK,GAAG;AAC1D,UAAI,aAAa,KAAK,QAAQ,EAAE;AAAA,IAClC,OAAO;AACL,cAAQ,MAAM,uCAAuC,KAAK,aAAa,OAAO,eAAe,GAAG;AAChG,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AAAA,EACF;AACA,QAAM,MAAM,CAAC,SAAS,KAAK,UAAU;AACnC,WAAO,QAAQ,KAAK,KAAK,KAAK;AAAA,EAChC;AACA,QAAM,SAAS,CAAC,SAAS,QAAQ;AAC/B,YAAQ,IAAI,gBAAgB,GAAG;AAAA,EACjC;AAEA,QAAM,eAAe,SAAO,mBAAmB,GAAG,KAAK,cAAc,IAAI,IAAI,IAAI;AACjF,QAAM,YAAY,WAAW,QAAQ,UAAU,YAAY,KAAK,WAAW,KAAK,UAAU,WAAW;AACrG,QAAM,cAAc,YAAY,OAAK,aAAa,QAAQ,EAAE,IAAI,YAAY,CAAC,IAAI;AACjF,QAAM,gBAAgB,OAAK;AACzB,UAAM,IAAI,YAAY,CAAC;AACvB,WAAO,aAAa,CAAC,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAAA,EAC5D;AACA,QAAM,gBAAgB,OAAK,aAAa,QAAQ,EAAE,IAAI,IAAI;AAE1D,QAAM,SAAS,aAAW;AACxB,UAAM,MAAM,OAAO,OAAO,IAAI,QAAQ,IAAI,aAAa,QAAQ;AAC/D,QAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,aAAO;AAAA,IACT;AACA,UAAM,MAAM,IAAI;AAChB,WAAO,cAAc,aAAa,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG,SAAS,QAAQ,aAAa,CAAC;AAAA,EACpH;AAEA,QAAM,aAAa,CAAC,OAAO,WAAW,WAAW;AAC/C,QAAI,UAAU,MAAM;AACpB,UAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,WAAO,QAAQ,YAAY;AACzB,gBAAU,QAAQ;AAClB,YAAM,KAAK,aAAa,QAAQ,OAAO;AACvC,UAAI,UAAU,EAAE,GAAG;AACjB,eAAO,SAAS,KAAK,EAAE;AAAA,MACzB,WAAW,KAAK,EAAE,GAAG;AACnB;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,KAAK;AAAA,EACvB;AAEA,QAAM,WAAW,CAAC,OAAO,UAAU,WAAW,WAAW,OAAO,OAAK,GAAG,GAAG,QAAQ,GAAG,MAAM;AAE5F,QAAM,cAAc,SAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM,gBAAgB;AAE3F,QAAM,MAAM,CAAC,SAAS,aAAa;AACjC,UAAM,MAAM,QAAQ;AACpB,UAAM,SAAS,OAAO,iBAAiB,GAAG;AAC1C,UAAM,IAAI,OAAO,iBAAiB,QAAQ;AAC1C,WAAO,MAAM,MAAM,CAAC,OAAO,OAAO,IAAI,kBAAkB,KAAK,QAAQ,IAAI;AAAA,EAC3E;AACA,QAAM,oBAAoB,CAAC,KAAK,aAAa,YAAY,GAAG,IAAI,IAAI,MAAM,iBAAiB,QAAQ,IAAI;AAEvG,QAAM,eAAe,aAAW,IAAI,SAAS,WAAW,MAAM,QAAQ,QAAQ;AAE9E,QAAM,aAAa,CAAC,OAAO,cAAc,OAAO,WAAW,KAAK,GAAG,SAAS;AAE5E,QAAM,WAAW,CAAC,OAAO,aAAa,WAAW,OAAO,OAAK,GAAG,GAAG,QAAQ,CAAC;AAE5E,QAAM,mBAAmB,aAAW,OAAO,OAAO,EAAE,OAAO,SAAS;AACpE,QAAM,qBAAqB,CAAC,SAASC,gBAAe;AAClD,UAAM,oBAAoBA,cAAa,SAAS,SAAS,OAAO,IAAI,SAAS,KAAK,OAAO;AACzF,WAAO,kBAAkB,MAAM,OAAO;AAAA,EACxC;AACA,QAAM,aAAa,MAAM,IAAI;AAC7B,QAAM,mBAAmB,CAAC,KAAK,QAAQ,QAAQ;AAC7C,SAAK,QAAQ,WAAS;AACpB,YAAM,eAAe,aAAa,QAAQ,KAAK;AAC/C,YAAM,yBAAyB,WAAW,YAAY;AACtD,YAAM,kBAAkB,mBAAmB,cAAc,sBAAsB;AAC/E,YAAM,wBAAwB,iBAAiB,eAAe;AAC9D,4BAAsB,KAAK,CAAAC,YAAU;AACnC,YAAI,SAAS,gBAAgB,KAAK,aAAa,IAAI;AACnD,cAAM,kBAAkB,aAAaA,OAAM;AAC3C,YAAI,oBAAoB,KAAK;AAC3B,iBAAO,iBAAiB,KAAK;AAAA,QAC/B,OAAO;AACL,cAAI,iBAAiB,OAAO,GAAG;AAAA,QACjC;AACA,YAAI,aAAa,eAAe,MAAM,KAAK;AACzC,cAAI,SAAS,gBAAgB,KAAK,aAAa,GAAG;AAAA,QACpD;AACA,YAAI,wBAAwB;AAC1B,gBAAM,YAAY,SAAS,iBAAiB,mBAAmB;AAC/D,eAAK,WAAW,cAAY;AAC1B,mBAAO,UAAU,KAAK;AACtB,gBAAI,SAAS,SAAS,KAAK,aAAa,IAAI;AAAA,UAC9C,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,SAAS,CAAC,QAAQ,QAAQ;AAC9B,QAAI,OAAO,UAAU,WAAW,GAAG;AACjC,uBAAiB,OAAO,KAAK,OAAO,UAAU,kBAAkB,GAAG,GAAG;AACtE,aAAO,YAAY;AAAA,IACrB;AAAA,EACF;AAEA,QAAM,aAAa,YAAU;AAC3B,WAAO,WAAW,mBAAmB,MAAM;AACzC,aAAO,QAAQ,KAAK;AAAA,IACtB,CAAC;AACD,WAAO,WAAW,mBAAmB,MAAM;AACzC,aAAO,QAAQ,KAAK;AAAA,IACtB,CAAC;AAAA,EACH;AAEA,QAAM,uBAAuB,CAAC,QAAQ,QAAQ,SAAO;AACnD,UAAM,oBAAoB,OAAK;AAC7B,YAAM,UAAU,aAAa,QAAQ,EAAE,OAAO;AAC9C,UAAI,UAAU,aAAa,OAAO,MAAM,GAAG;AAC3C,UAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,IAC9C;AACA,WAAO,GAAG,cAAc,iBAAiB;AACzC,QAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAC5C,WAAO,MAAM,OAAO,IAAI,cAAc,iBAAiB;AAAA,EACzD;AACA,QAAM,WAAW,YAAU;AACzB,WAAO,GAAG,SAAS,gBAAgB,OAAO;AAAA,MACxC,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU,MAAM,OAAO,YAAY,iBAAiB;AAAA,MACpD,SAAS,qBAAqB,QAAQ,KAAK;AAAA,IAC7C,CAAC;AACD,WAAO,GAAG,SAAS,gBAAgB,OAAO;AAAA,MACxC,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU,MAAM,OAAO,YAAY,iBAAiB;AAAA,MACpD,SAAS,qBAAqB,QAAQ,KAAK;AAAA,IAC7C,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,MAAM;AACjB,WAAO,IAAI,kBAAkB,YAAU;AACrC,iBAAW,MAAM;AACjB,eAAS,MAAM;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,SAAO;AAEX,GAAG;", "names": ["type", "isListItem", "parent"]}