import{c as l,b as e,i as p,j as i}from"./index.DUmRo3Ep.entry.js";import"./vue.runtime.esm-bundler-D4Mhr4E9.js";import"./runtime-core.esm-bundler-DhTmOO-X.js";import"./index-C0qcAVKU.js";import"./pinia.DFmwLz6V.entry.js";import"./vue-i18n.8VWz_hPO.entry.js";import"./vue-router.YxiUBFHd.entry.js";function m(a){return a.replace(/sekuntia?/,"sekunnin")}function d(a){return a.replace(/minuuttia?/,"minuutin")}function h(a){return a.replace(/tuntia?/,"tunnin")}function v(a){return a.replace(/päivää?/,"päivän")}function c(a){return a.replace(/(viikko|viikkoa)/,"viikon")}function f(a){return a.replace(/(kuukausi|kuukautta)/,"kuukauden")}function o(a){return a.replace(/(vuosi|vuotta)/,"vuoden")}const y={lessThanXSeconds:{one:"alle sekunti",other:"alle {{count}} sekuntia",futureTense:m},xSeconds:{one:"sekunti",other:"{{count}} sekuntia",futureTense:m},halfAMinute:{one:"puoli minuuttia",other:"puoli minuuttia",futureTense:a=>"puolen minuutin"},lessThanXMinutes:{one:"alle minuutti",other:"alle {{count}} minuuttia",futureTense:d},xMinutes:{one:"minuutti",other:"{{count}} minuuttia",futureTense:d},aboutXHours:{one:"noin tunti",other:"noin {{count}} tuntia",futureTense:h},xHours:{one:"tunti",other:"{{count}} tuntia",futureTense:h},xDays:{one:"päivä",other:"{{count}} päivää",futureTense:v},aboutXWeeks:{one:"noin viikko",other:"noin {{count}} viikkoa",futureTense:c},xWeeks:{one:"viikko",other:"{{count}} viikkoa",futureTense:c},aboutXMonths:{one:"noin kuukausi",other:"noin {{count}} kuukautta",futureTense:f},xMonths:{one:"kuukausi",other:"{{count}} kuukautta",futureTense:f},aboutXYears:{one:"noin vuosi",other:"noin {{count}} vuotta",futureTense:o},xYears:{one:"vuosi",other:"{{count}} vuotta",futureTense:o},overXYears:{one:"yli vuosi",other:"yli {{count}} vuotta",futureTense:o},almostXYears:{one:"lähes vuosi",other:"lähes {{count}} vuotta",futureTense:o}},b=(a,n,t)=>{const u=y[a],s=n===1?u.one:u.other.replace("{{count}}",String(n));return t!=null&&t.addSuffix?t.comparison&&t.comparison>0?u.futureTense(s)+" kuluttua":s+" sitten":s},w={full:"eeee d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"d.M.y"},P={full:"HH.mm.ss zzzz",long:"HH.mm.ss z",medium:"HH.mm.ss",short:"HH.mm"},M={full:"{{date}} 'klo' {{time}}",long:"{{date}} 'klo' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},g={date:l({formats:w,defaultWidth:"full"}),time:l({formats:P,defaultWidth:"full"}),dateTime:l({formats:M,defaultWidth:"full"})},W={lastWeek:"'viime' eeee 'klo' p",yesterday:"'eilen klo' p",today:"'tänään klo' p",tomorrow:"'huomenna klo' p",nextWeek:"'ensi' eeee 'klo' p",other:"P"},j=(a,n,t,u)=>W[a],T={narrow:["eaa.","jaa."],abbreviated:["eaa.","jaa."],wide:["ennen ajanlaskun alkua","jälkeen ajanlaskun alun"]},H={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. kvartaali","2. kvartaali","3. kvartaali","4. kvartaali"]},k={narrow:["T","H","M","H","T","K","H","E","S","L","M","J"],abbreviated:["tammi","helmi","maalis","huhti","touko","kesä","heinä","elo","syys","loka","marras","joulu"],wide:["tammikuu","helmikuu","maaliskuu","huhtikuu","toukokuu","kesäkuu","heinäkuu","elokuu","syyskuu","lokakuu","marraskuu","joulukuu"]},D={narrow:k.narrow,abbreviated:k.abbreviated,wide:["tammikuuta","helmikuuta","maaliskuuta","huhtikuuta","toukokuuta","kesäkuuta","heinäkuuta","elokuuta","syyskuuta","lokakuuta","marraskuuta","joulukuuta"]},r={narrow:["S","M","T","K","T","P","L"],short:["su","ma","ti","ke","to","pe","la"],abbreviated:["sunn.","maan.","tiis.","kesk.","torst.","perj.","la"],wide:["sunnuntai","maanantai","tiistai","keskiviikko","torstai","perjantai","lauantai"]},x={narrow:r.narrow,short:r.short,abbreviated:r.abbreviated,wide:["sunnuntaina","maanantaina","tiistaina","keskiviikkona","torstaina","perjantaina","lauantaina"]},F={narrow:{am:"ap",pm:"ip",midnight:"keskiyö",noon:"keskipäivä",morning:"ap",afternoon:"ip",evening:"illalla",night:"yöllä"},abbreviated:{am:"ap",pm:"ip",midnight:"keskiyö",noon:"keskipäivä",morning:"ap",afternoon:"ip",evening:"illalla",night:"yöllä"},wide:{am:"ap",pm:"ip",midnight:"keskiyöllä",noon:"keskipäivällä",morning:"aamupäivällä",afternoon:"iltapäivällä",evening:"illalla",night:"yöllä"}},V=(a,n)=>Number(a)+".",z={ordinalNumber:V,era:e({values:T,defaultWidth:"wide"}),quarter:e({values:H,defaultWidth:"wide",argumentCallback:a=>a-1}),month:e({values:k,defaultWidth:"wide",formattingValues:D,defaultFormattingWidth:"wide"}),day:e({values:r,defaultWidth:"wide",formattingValues:x,defaultFormattingWidth:"wide"}),dayPeriod:e({values:F,defaultWidth:"wide"})},L=/^(\d+)(\.)/i,S=/\d+/i,X={narrow:/^(e|j)/i,abbreviated:/^(eaa.|jaa.)/i,wide:/^(ennen ajanlaskun alkua|jälkeen ajanlaskun alun)/i},Q={any:[/^e/i,/^j/i]},N={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]\.? kvartaali/i},Y={any:[/1/i,/2/i,/3/i,/4/i]},_={narrow:/^[thmkeslj]/i,abbreviated:/^(tammi|helmi|maalis|huhti|touko|kesä|heinä|elo|syys|loka|marras|joulu)/i,wide:/^(tammikuu|helmikuu|maaliskuu|huhtikuu|toukokuu|kesäkuu|heinäkuu|elokuu|syyskuu|lokakuu|marraskuu|joulukuu)(ta)?/i},q={narrow:[/^t/i,/^h/i,/^m/i,/^h/i,/^t/i,/^k/i,/^h/i,/^e/i,/^s/i,/^l/i,/^m/i,/^j/i],any:[/^ta/i,/^hel/i,/^maa/i,/^hu/i,/^to/i,/^k/i,/^hei/i,/^e/i,/^s/i,/^l/i,/^mar/i,/^j/i]},C={narrow:/^[smtkpl]/i,short:/^(su|ma|ti|ke|to|pe|la)/i,abbreviated:/^(sunn.|maan.|tiis.|kesk.|torst.|perj.|la)/i,wide:/^(sunnuntai|maanantai|tiistai|keskiviikko|torstai|perjantai|lauantai)(na)?/i},E={narrow:[/^s/i,/^m/i,/^t/i,/^k/i,/^t/i,/^p/i,/^l/i],any:[/^s/i,/^m/i,/^ti/i,/^k/i,/^to/i,/^p/i,/^l/i]},O={narrow:/^(ap|ip|keskiyö|keskipäivä|aamupäivällä|iltapäivällä|illalla|yöllä)/i,any:/^(ap|ip|keskiyöllä|keskipäivällä|aamupäivällä|iltapäivällä|illalla|yöllä)/i},R={any:{am:/^ap/i,pm:/^ip/i,midnight:/^keskiyö/i,noon:/^keskipäivä/i,morning:/aamupäivällä/i,afternoon:/iltapäivällä/i,evening:/illalla/i,night:/yöllä/i}},K={ordinalNumber:p({matchPattern:L,parsePattern:S,valueCallback:a=>parseInt(a,10)}),era:i({matchPatterns:X,defaultMatchWidth:"wide",parsePatterns:Q,defaultParseWidth:"any"}),quarter:i({matchPatterns:N,defaultMatchWidth:"wide",parsePatterns:Y,defaultParseWidth:"any",valueCallback:a=>a+1}),month:i({matchPatterns:_,defaultMatchWidth:"wide",parsePatterns:q,defaultParseWidth:"any"}),day:i({matchPatterns:C,defaultMatchWidth:"wide",parsePatterns:E,defaultParseWidth:"any"}),dayPeriod:i({matchPatterns:O,defaultMatchWidth:"any",parsePatterns:R,defaultParseWidth:"any"})},$={code:"fi",formatDistance:b,formatLong:g,formatRelative:j,localize:z,match:K,options:{weekStartsOn:1,firstWeekContainsDate:4}};export{$ as default,$ as fi};
